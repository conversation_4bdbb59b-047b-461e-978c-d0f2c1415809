
# 代码结构化 - 类/方法/字段等结构化信息

- todo
  - ！！ 需不需要分类构建索引？？？ 不同模块的文件放在不同的索引里，是否会提高检索效率？看看iwiki有没有优化方案
  - 业务场景tag怎么打更快（问问大模型、git blame？）----- 5. 领域自适应策略
  - **结构化提示（Structured Prompt）**    --- 是否可以作为一个索引（类名-类名包含的方法名、字段名等等）？和链式调用有什么不同
   将检索到的代码、注释、文件路径按模板组织，引导生成准确答案：  
   ```plaintext
   // 模板示例
   文件路径: src/main/java/com/example/RecommendRequest.kt
   类名: RecommendRequest
   字段定义: 
     - public String userId; // 用户ID（来自Javadoc）
   关联方法: 
     - validateUserId() // 校验userId合法性
   ```
   - ！！！ 可不可以对agent进行分类处理。不同类型的问题进行不同的处理
     - 自由问答
     - 场景查找问题
     - 协议查找问题

总体流程 -- 自动合并检索器（或父文档检索器）（利用自定义插件进行 上下文合并）

1. 对用户问题prompt进行优化。第一层prompt（用户的问题如果转化成代码实现，可能是怎样的？如“推荐页”可能是“RecommendFragment”。那么最后的prompt就是 推荐页，猜测可能的代码模块有RecommendFragment，具体还要根据代码库查询）
2. 先检索子文档（代码注释 - 子文档（chunk）），然后再根据子文档存放的父文档的id，检索父文档。
3. 结合 合并的上下文，调整prompt【可以学学模型回答，搜的时候看到过；或者再问问模型】
4. todo：可以调整一下输出的格式（在 `场景探索-ds.md` 文档中，有后处理步骤） 如
    ```plaintext
    {字段名} 存在于 {类名} 中，类型为 {类型}。  
    定义位置：{文件路径}。
    ```
    ```plaintext
    App详情页的代码位于：  
    文件路径：{文件路径}  
    包含类：{类名}  
    主要方法：{方法列表}
    ```

示例流程 -- 针对API（可以再结合：2. API使用分析 -- 解析API 生成知识卡片）
- 推荐协议有userid字段吗？
- 优化用户prompt 列出可能tag [][][]
- 通过可能得tag，找到对应子块的场景标识tag，找到可能得推荐协议 子块
- 根据推荐协议子块 -- 所属类的tag -- 找到类
- 合并上下文，让模型解析是否有 userid



代码解析与分块 -- 混合分块 ---- 查看：工具使用-代码分块.md
1. **先用 Tree-sitter/JavaParser 提取完整方法、字段**。
2. **对超长方法使用 RecursiveTextSplitter 二次分割**。
3. **通过 `chunk_overlap` 保留上下文**。

```py
from langchain_community.document_loaders import DirectoryLoader
from langchain_text_splitters import Language, RecursiveCharacterTextSplitter

# 加载代码库
loader = DirectoryLoader("src/main/java/", glob="**/*.java")
java_docs = loader.load()

# 分块器（Java/Kotlin专用）
splitter = RecursiveCharacterTextSplitter.from_language(
    language=Language.JAVA,
    chunk_size=400,
    chunk_overlap=50,
    is_separator_regex=False,
)
chunks = splitter.split_documents(java_docs)
```

构建索引
- 代码注释 - 子文档（chunk）--- 需要存放父文档的id（类名） --- 需要 元数据增强（链式调用可行的话，可以辅助，想想场景（问问模型））
   - 方法/函数体（如 RecommendService.getRecommendations()）。
   - 字段定义（如 private String userId;）。
   - 关键代码段（如网络请求、动画初始化逻辑）。
- 类名/接口名 - 父文档 （文件级别的全局上下文）

- 元数据增强 --- 为每个代码块添加业务标签（如 `#支付`、`#推荐`），通过聚类或规则生成。
为每个代码块附加：
```json
{
  "file_path": "com/example/app/ui/recommend/RecommendRequest.kt",
  "class": "RecommendRequest",
  "method": "parseResponse",
  "last_modified": "2024-03-15",
  "git_blame": "author: Alice"
}
```

- 上下文增强分块
   - **分块策略**：  
     - **垂直分块**：按业务模块分割（如订单、支付、用户管理）。  --- git blame？
     - **水平分块**：按技术层级分割（如 API 层、Service 层、DAO 层）。  
   - **元数据注入**：  
     - 为每个代码块添加业务标签（如 `#支付`、`#推荐`），通过聚类或规则生成。




**检索器架构**  
采用 **混合检索器（Hybrid Retriever）** + **自动合并上下文**，确保精准匹配与语义理解。

1. **混合检索器**  
   - **关键词检索（Elasticsearch）**：  
     - 索引字段名（如 `userId`）、方法名（如 `loadAnimation`）、文件路径。  
     - 匹配精确术语（如“推荐页协议”对应类名 `RecommendRequest`）。  
   - **向量检索（CodeBERT/UniXcoder + FAISS）**：  
     - 将代码和注释编码为向量，支持语义搜索（如“引导动画”匹配 `setupGuideAnimation` 方法）。  
   - **图检索（Neo4j，可选）**：  
     - 存储类-方法-字段的调用关系，辅助回答复杂逻辑（如“哪些类调用了 `userId` 字段”）。

2. **自动合并上下文**  
   - **步骤**：  
     1. 检索到相关子文档（如字段 `userId` 的定义）。  
     2. 关联父文档（如 `RecommendRequest` 类的完整代码）。  
     3. 合并结果，确保生成时能访问完整协议结构。  
   - **示例**：  
     - 用户问“推荐页请求协议是否有 `userId`？”  
     - 子文档命中 `RecommendRequest` 类中的 `userId` 字段。  
     - 父文档提供完整类定义，确认 `userId` 的存在及类型（如 `String`）。



**5. 针对不同问题的优化策略**
#### **(1) 字段存在性检查（如“是否有userId字段”）**
- **检索优化**：  
  - 关键词检索优先匹配字段名 (`userId`)。  
  - 向量检索补充语义相似词（如 `user_id`、`userIdentifier`）。  
- **生成模板**：  
  ```plaintext
  {字段名} 存在于 {类名} 中，类型为 {类型}。  
  定义位置：{文件路径}。
  ```

#### **(2) 功能逻辑查询（如“引导动画实现”）**
- **检索优化**：  
  - 向量检索匹配代码中的动画关键词（如 `Animation`、`startAnimation`）。  
  - 关联父文档（如 `RecommendFragment` 类）提供完整生命周期逻辑。  
- **生成模板**：  
  ```plaintext
  引导动画在 {方法名} 中实现，使用了 {动画类型}。  
  关键代码片段：  
  {代码块}
  ```

#### **(3) 代码定位（如“app详情页的代码在哪里”）**
- **检索优化**：  
  - 关键词检索匹配文件路径（如 `DetailActivity.java`）。  
  - 元数据筛选（如 `type: class` 且包含 `详情页` 注释）。  
- **生成模板**：  
  ```plaintext
  App详情页的代码位于：  
  文件路径：{文件路径}  
  包含类：{类名}  
  主要方法：{方法列表}
  ```




完整架构

用户问题 → 混合检索 → 自动合并父文档 → 生成模型 → 后处理 → 最终答案
        (BM25 + FAISS)   (类/文件级上下文)   (CodeLlama)  (语法/路径验证)





## 1. 借助模型对代码生成注释
```py
def generate_code_comment(code_snippet):
    prompt = f"""
    你是一个资深的代码文档工程师，请为以下代码生成注释：
    
    {code_snippet}
    
    要求：
    1. 说明方法的主要功能
    2. 列出关键参数的含义
    3. 指出可能的异常情况
    """
    return llm.invoke(prompt)
```

## 2. API使用分析 -- 解析API 生成知识卡片

Retrofit API分析示例：

```kotlin
// 检测网络接口定义
interface RecommendService {
    @GET("/v3/recommend")
    suspend fun getRecommendList(
        @Query("user_id") userId: String
    ): Response<RecommendResponse>
}
```

生成的API知识卡：
```json
{
  "api_name": "getRecommendList",
  "method": "GET",
  "path": "/v3/recommend",
  "parameters": [
    {
      "name": "user_id",
      "type": "String",
      "required": true
    }
  ],
  "used_in": ["RecommendActivity", "RecommendViewModel"]
}
```

## 3. 代码调用链分析 -- 建立方法之间的调用关系图，帮助模型理解代码逻辑流

```py
# 使用Javalang进行Java调用链分析
import javalang

def extract_call_chains(code):
    tree = javalang.parse.parse(code)
    call_graph = {}
    
    for path, node in tree.filter(javalang.tree.MethodInvocation):
        current_method = get_current_method_name(path)  # 获取所在方法
        callee = node.qualifier + '.' + node.member if node.qualifier else node.member
        
        if current_method not in call_graph:
            call_graph[current_method] = []
        call_graph[current_method].append(callee)
    
    return call_graph

# 示例输出
{
  "RecommendService.loadData()": [
    "HttpClient.execute()",
    "RecommendParser.parse()",
    "Animator.start()"
  ]
}
```

应用场景：

当用户问"推荐页数据加载流程是怎样的？"时，系统可展示完整的调用链路：

```py
1. RecommendService.loadData() 发起请求
2. → HttpClient.execute() 发送网络请求
3. → RecommendParser.parse() 解析响应
4. → Animator.start() 启动加载动画
```



## 4. 元数据注入：为每个代码块添加业务标签（如 #支付、#推荐），通过聚类或规则生成。
```json
    {
  "chunk_id": "OrderService_createOrder",
  "content": "public Order createOrder(OrderRequest request) { ... }",
  "metadata": {
    "class": "OrderService",
    "module": "订单",
    "dependencies": ["PaymentGateway", "InventoryService"],
    "file_path": "src/main/java/com/example/order/OrderService.java"
  }
}
```



## 5. 领域自适应策略
(1) 代码术语词典
```json
// code_terms.json
{
  "推荐页": ["RecommendActivity", "RecommendFragment", "recommend_"],
  "用户ID": ["userId", "user_id", "mUid"],
  "网络请求": ["Retrofit", "OkHttp", "ApiService"]
}
```

(2) 查询重写模块
```python
# 使用术语词典扩展查询
def expand_query(query):
    expanded = [query]
    for term in code_terms:
        if term in query:
            expanded.extend(code_terms[term])
    return " OR ".join(f'"{w}"' for w in expanded)

# 示例输入："推荐页怎么获取用户ID？"
# 扩展后："推荐页" OR "RecommendActivity" OR "RecommendFragment" OR "userID" OR "user_id" OR "mUid"
```


## 更多优化方案 如图谱、链式调用、更新、git blame的利用 待续...





# 工具

## (1) LangChain
用途：构建 RAG 流水线，集成代码解析、检索与生成。
代码分块示例：
```py
from langchain_text_splitters import Language, RecursiveCharacterTextSplitter

# 专为 Java/Kotlin 设计的分块器
splitter = RecursiveCharacterTextSplitter.from_language(
    language=Language.JAVA,
    chunk_size=200,
    chunk_overlap=20
)
chunks = splitter.split_text(java_code)
```
## (2) Haystack
用途：支持自定义代码处理管道（如解析→分块→嵌入→检索）。
集成工具：可与 Elasticsearch、FAISS 和生成模型（如 GPT-4）结合。


