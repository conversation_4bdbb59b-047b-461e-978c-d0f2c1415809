
# 参考链接
- 混元Embedding 使用说明手册：https://iwiki.woa.com/p/4008466380

# 代码索引
- v1
  - DiscoverRecommendFragment
  - DiscoverRecommendAdapter
  - BaseVideoViewHolder
  - DiscoverVideoView
  - DiscoverSeekBar

# 索引格式

- query + value
- value


# 业务场景映射，以兼容不用用户的问题，比如有不同的推荐页（索引映射、正则匹配）


代码
- 选择指定的代码，ai分析
- 分析代码
- 回答


- 模型怎么根据知识库进行搜索？
  - 问题
    - 推荐页的代码在哪一块
    - 怎么修改版本号
    - 搜索的时候支持语音输入吗
  - 模型处理链路
    - 找到相关代码（根据什么找相关代码？人工标注？）
      - 全文检索
        - 分割输入模型回答（让模型根据上下文回答）
      - 为知识库分类，不同场景的放一个知识库。代码量会小一点。
        - 分割输入模型回答（让模型根据上下文回答）
      - 索引检索
        - 根据问题检索（如何确保索引检索的准确性）
          - 人工标注、模型标注？
        - 得到一些代码片段输入给模型，模型根据索引内容回答
    - 分析代码回答


- 既然可以切割，那切割给模型可以吗？



# TODO

- 索引优化（标签的内容怎么优化？）

- 逻辑分流
  - class全文（添加更多tag。类名和方法名）
    - 需要提供的更多信息
  - chunk总结
    - 更适合细分的模块（可以一步锁定）
  - 场景全文（优化索引，更详细的场景标签，如推荐tab的视频模块）
    - 锁定场景-提供场景对应代码

- 查找代码流程（关键的类、方法、字符串 -- 分析代码中的模块）
  - 定位到主页面的代码
  - 根据主页面的代码查找所需模块
    - 可以根据 方法获取更多的信息

- 接口查找策略：文档关联-提取文档的接口描述



- agent
          prompt -> 模型
用户问题
          索引

# 目标（cs能否做到？）
- 帮助新同学查找代码块在哪
- 分析代码 - 定位bug
- 查找 协议 弹窗的某个字段 是否是下发的？
- 关联tapd单子、设计稿
- 智能IDE插件
  - 实现特征：
   * 悬停提示：显示当前函数的业务含义及关联案例
   * 智能重构：检测到相似业务逻辑时自动推荐重用方案
   * 路径导航：输入自然语言跳转到对应代码位置


# 思考

- ！！！ 再看看模型的回答

- 代码知识增强有哪些方案（偏业务场景）

- 需要尝试里面的方法！！！！ 大模型RAG（检索增强生成）含高级方法（https://zhuanlan.zhihu.com/p/675509396）

- 元数据注入：为每个代码块添加业务标签（如 #支付、#推荐），通过聚类或规则生成。
```json
    {
  "chunk_id": "OrderService_createOrder",
  "content": "public Order createOrder(OrderRequest request) { ... }",
  "metadata": {
    "class": "OrderService",
    "module": "订单",
    "dependencies": ["PaymentGateway", "InventoryService"],
    "file_path": "src/main/java/com/example/order/OrderService.java"
  }
}
```

- 检索增强技巧 
  - 同义词扩展：userId → user_id/userIdentifier
  - API路径联想：推荐页协议 → RecommendService.proto
  - 跨文件关联：检索到RecommendActivity.kt时，自动关联recommend_layout.xml

- API使用分析 -- 解析API使用模式生成知识卡片

- gradle 解析（依赖版本解析等）

- 变更历史分析 -- 利用Git历史增强代码理解  -- git关联tapd、文档

- 领域自适应策略
(1) 代码术语词典
```json
// code_terms.json
{
  "推荐页": ["RecommendActivity", "RecommendFragment", "recommend_"],
  "用户ID": ["userId", "user_id", "mUid"],
  "网络请求": ["Retrofit", "OkHttp", "ApiService"]
}
```

(2) 查询重写模块
```python
# 使用术语词典扩展查询
def expand_query(query):
    expanded = [query]
    for term in code_terms:
        if term in query:
            expanded.extend(code_terms[term])
    return " OR ".join(f'"{w}"' for w in expanded)

# 示例输入："推荐页怎么获取用户ID？"
# 扩展后："推荐页" OR "RecommendActivity" OR "RecommendFragment" OR "userID" OR "user_id" OR "mUid"
```

- 目前有没有面向代码的agent/RAG

- 高效的RAG方法有哪些？（论文综述看看）

- 混元 GraphRAG 是否有帮助？

- 面向 代码摘要 的 RAG，现在的最新方式是什么？（https://zhuanlan.zhihu.com/p/688723054）

- 是否要增加更多的 索引字段 -- 保留更详细的元数据 帮助检索

- 向量模型再训练 是否会提升效果

- LangChain 工具 尝试：构建 RAG 流水线，集成代码解析、检索与生成。（https://www.langchain.com.cn/）
  - 

- 如何让上下相邻分块关联起来？
- git blame 能不能提取来帮助
- 分块的话 多大合适？重叠块，多大合适？

- 摘要代码索引 和 代码分块能不能有效合作（分层索引）
  - 先通过摘要代码定位
  - 再根据定位到的代码value 作为答案，来检索到对应的代码块？

- 可以抽取出代码的方法名、变量、字符型常量 来进行辅助吗？

- 如何让大模型理解我们的需求（prompt）
  - 所谓的一致性问题，或者叫“对齐问题”，是尤瓦尔·赫拉利在《智人之上——从石器时代到 AI 时代的信息网络简史》里提到的一个困扰人类社会数千年的难题。