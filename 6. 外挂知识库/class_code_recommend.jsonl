{"query": "DiscoverVideoComponentCacheManager.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport android.content.Context\nimport com.tencent.assistant.component.video.VideoViewManager\nimport com.tencent.assistant.component.video.view.VideoViewComponentV2\nimport com.tencent.assistant.utils.HandlerUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.concurrentHashMapOf\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport java.util.concurrent.ConcurrentLinkedQueue\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/15 10:54\n * Description:\n */\nclass DiscoverVideoComponentCacheManager(val scene: Int) {\n\n    companion object {\n\n        private const val MAX_PRE_CREATE_COUNT = 4\n        private val managers = concurrentHashMapOf<Int, DiscoverVideoComponentCacheManager>()\n\n        fun getManager(scene: Int): DiscoverVideoComponentCacheManager {\n            return managers.getOrPut(scene) { DiscoverVideoComponentCacheManager(scene) }\n        }\n    }\n\n    private val TAG = \"DiscoverVideoComponentCacheManager_$scene\"\n    private var preCreateCount = 0\n    private val videoViewComponentCache = ConcurrentLinkedQueue<VideoViewComponentV2>()\n    private val handler = HandlerUtils.getMainHandler()\n    private val techReporter = DiscoverBeaconReport.getReporter(scene)\n\n    @Volatile\n    private var isPreCreating = false\n    private var isFirstGet = true;\n\n\n    fun initialize(context: Context) {\n        if (!isSwitchOn(scene)) {\n            XLog.i(TAG, \"initialize switch off return\")\n            return\n        }\n\n        XLog.i(TAG, \"initialize, videoViewComponentCache.size = ${videoViewComponentCache.size}\")\n        if (videoViewComponentCache.size > 0) {\n            XLog.i(TAG, \"initialize, videoViewComponentCache.size > 0, return\")\n            return\n        }\n        preCreateVideoViewComponent(context, initVideoPlayer = true, isFirst = true)\n    }\n\n    fun getVideoViewComponent(context: Context): VideoViewComponentV2 {\n        if (!isSwitchOn(scene)) {\n            XLog.i(TAG, \"getVideoViewComponent switch off, return default\")\n            return createVideoViewComponent(context, false)\n        }\n\n        var videoComponent = videoViewComponentCache.poll()\n        val hitCache = if (videoComponent == null) {\n            videoComponent = createVideoViewComponent(context)\n            XLog.i(TAG, \"getVideoViewComponent, videoComponent is null, create new\")\n            false\n        } else {\n            XLog.i(TAG, \"getVideoViewComponent, hit cache\")\n            true\n        }\n        if (isFirstGet) {\n            techReporter.addTag(\n                DiscoverBeaconReport.PreloadKey.VIDEO_COMPONENT_FIRST_GET,\n                DiscoverBeaconReport.ExtraMapKey.VIDEO_COMPONENT_FIRST_GET_HIT_CACHE to techReporter.getStatus\n                    (hitCache)\n            )\n            isFirstGet = false\n        }\n\n        if (preCreateCount++ < MAX_PRE_CREATE_COUNT) {\n            preCreateVideoViewComponent(context)\n        }\n        return videoComponent\n    }\n\n    private fun isSwitchOn(scene: Int) = when (scene) {\n        DiscoverRecommendReporter.Scene.RECOMMEND_SCENE -> {\n            DiscoverConfig.preCreateRecommendVideoComponent\n        }\n\n        DiscoverRecommendReporter.Scene.VIDEO_FEED_SCENE -> {\n            DiscoverConfig.preCreateVideoFeedVideoComponent\n        }\n\n        else -> false\n    }\n\n    private fun preCreateVideoViewComponent(\n        context: Context,\n        initVideoPlayer: Boolean = false,\n        isFirst: Boolean = false\n    ) {\n        if (isPreCreating) {\n            XLog.i(TAG, \"preCreateVideoViewComponent isPreCreating, return\")\n            return\n        }\n        isPreCreating = true\n\n        handler.postDelayed({\n            if (isFirst) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_COMPONENT_PRE_CREATE_START)\n            }\n            val videoViewComponent = createVideoViewComponent(context, initVideoPlayer)\n            if (isFirst) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_COMPONENT_PRE_CREATE_END)\n            }\n            videoViewComponentCache.add(videoViewComponent)\n            isPreCreating = false\n            XLog.i(TAG, \"preCreateVideoViewComponent, videoViewComponentCache.size = ${videoViewComponentCache.size}\")\n        }, if (isFirst) 100 else 500)\n    }\n\n    private fun createVideoViewComponent(context: Context, initVideoPlayer: Boolean = false):\n            VideoViewComponentV2 {\n        XLog.i(TAG, \"createVideoViewComponent initVideoPlayer = $initVideoPlayer\")\n        val time = System.currentTimeMillis()\n        val videoViewComponent = VideoViewManager.getInstance().createVideoViewV2(context).apply {\n            setLogSubTag(\"DiscoverRecommend_$scene\")\n            reportFirstFrameBySelf = DiscoverConfig.reportFirstFrameByUser\n        }\n        if (initVideoPlayer) {\n            try {\n                videoViewComponent.initVideoPlayer()\n            } catch (e: Exception) {\n                XLog.e(TAG, \"\")\n            }\n        }\n        XLog.i(TAG, \"createVideoViewComponent cost = ${System.currentTimeMillis() - time}\")\n\n        return videoViewComponent\n    }\n\n    fun onDestroy(context: Context) {\n        isPreCreating = false\n        isFirstGet = true\n\n        XLog.i(TAG, \"onDestroy, videoViewComponentCache.size = ${videoViewComponentCache.size}\")\n        try {\n            videoViewComponentCache.forEach { VideoViewComponentV2 ->\n                try {\n                    VideoViewComponentV2.onDestroy(context)\n                } catch (e: Exception) {\n                    XLog.e(TAG, \"onDestroy\", e)\n                }\n            }\n            videoViewComponentCache.clear()\n            preCreateCount = 0\n        } catch (e: Exception) {\n            XLog.e(TAG, \"onDestroy\", e);\n        }\n    }\n}\n\n"}
{"query": "DiscoverRecommendRepository.kt", "value": "package com.tencent.pangu.discover.recommend.model\n\nimport com.tencent.assistant.protocol.jce.DiscoveryPageLikeResponse\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendResponse\nimport com.tencent.assistant.request.RequestResult\nimport com.tencent.assistant.request.RequestType\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.toGsonString\nimport com.tencent.pangu.discover.base.model.DiscoverLikeEngine\nimport com.tencent.pangu.discover.base.model.DiscoverLikeEngine.Companion.RET_DUPLICATE\nimport com.tencent.pangu.discover.base.model.DiscoverLikeEngine.Companion.RET_SUCCESS\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendDataCacheManager\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.discover.videofeed.manager.VideoFeedDataCacheManager\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/2 14:04\n * Description: 发现推荐 Tab 数据仓库\n */\nclass DiscoverRecommendRepository(val scene: Int = 0) {\n\n    private val TAG = \"DiscoverRecommendRepository_$scene\"\n\n    private val recommendEngine by lazy {\n        DiscoverRecommendEngine(scene)\n    }\n\n    private val likeEngine by lazy {\n        DiscoverLikeEngine(scene)\n    }\n\n    /**\n     * 获取发现推荐 Tab 数据\n     * @param requestParam 请求参数\n     * @return 请求结果\n     */\n    suspend fun requestRecommendList(requestParam: DiscoverRecommendRequestParam): RequestResult<DiscoveryPageRecommendResponse> {\n        XLog.i(\n            TAG, \"requestRecommendList: requestParam = $requestParam, request= ${requestParam.request.toGsonString()}\"\n        )\n        if (requestParam.useCacheData) {\n            val cacheResult = getCacheResult(requestParam)\n            if (cacheResult is RequestResult.Success<DiscoveryPageRecommendResponse>) {\n                XLog.i(TAG, \"requestRecommendList: requestRecommend cacheResult = $cacheResult\")\n                recommendEngine.updateRequestContext(cacheResult)\n                return cacheResult\n            }\n        }\n\n        XLog.i(TAG, \"requestRecommendList: sendRequest, requestParam = $requestParam\")\n        val result = recommendEngine.sendRequest(requestParam)\n        cacheResult(requestParam, result)\n        return result\n    }\n\n    private suspend fun getCacheResult(requestParam: DiscoverRecommendRequestParam) =\n        if (isRecommendScene(requestParam.scene)) {\n            DiscoverRecommendDataCacheManager.getCacheResult()\n        } else {\n            VideoFeedDataCacheManager.getCacheResult(requestParam.request.topMid)\n        }\n\n    private fun isRecommendScene(scene: Int) = scene == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n\n\n    private fun cacheResult(\n        requestParam: DiscoverRecommendRequestParam,\n        result: RequestResult<DiscoveryPageRecommendResponse>\n    ) {\n        if (requestParam.requestType == RequestType.FULL_REQUEST\n            && requestParam.cacheData\n            && result is RequestResult.Success<DiscoveryPageRecommendResponse>\n            && (result.data?.items?.size ?: 0) > 0\n        ) {\n            DiscoverRecommendDataCacheManager.updateCache(result = result)\n        }\n    }\n\n    /**\n     * 点赞\n     * @param like 点赞状态: true 点赞, false 取消点赞\n     */\n    suspend fun toggleLike(like: Boolean, materialId: Long): RequestResult<DiscoveryPageLikeResponse> {\n        return likeEngine.sendRequest(like, materialId).also {\n            if (it is RequestResult.Success\n                && (it.data?.ret == RET_SUCCESS || it.data?.ret == RET_DUPLICATE)\n            ) {\n                likeEngine.dispatchLikeToggleEvent(materialId, like)\n            }\n        }\n    }\n\n    fun restoreRecommendRequestContext() {\n        recommendEngine.restoreRequestContext()\n    }\n}"}
{"query": "DiscoverGuideBarManager.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport android.content.Context\nimport android.text.TextUtils\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.assistant.Settings\nimport com.tencent.assistant.config.api.IConfigManagerService\nimport com.tencent.assistant.localres.ApkResourceManager\nimport com.tencent.assistant.protocol.jce.DiscoveryPageGuideBarType\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendGuideBarInfo\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.raft.TRAFT\nimport com.tencent.assistant.settings\nimport com.tencent.assistant.settings.api.setting\nimport com.tencent.assistant.utils.TimeUtil\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.isNotNullOrEmpty\nimport com.tencent.assistant.utils.toIntOr\nimport com.tencent.pangu.PNGSettings\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.link.IntentUtils\nimport com.tencent.pangu.middlepage.model.covertSimpleAppModelForMiddlePage\nimport java.util.Calendar\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/17 16:45\n * Description:\n *\n * <a herf=https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314119026019>【内容化】引导频控优化</a>\n */\n\nprivate var lastDayOfYear by settings(\n    \"discover_recommend_guide_day_of_year\",\n    0\n)\n\nobject DiscoverGuideBarManager {\n    private const val TAG = \"DiscoverGuideBarManager\"\n    private const val SPLIT = \",\"\n    private const val DEFAULT_CONFIG = \"0,2,-1,-1,-1\"\n    private const val DEFAULT_DELAY_SHOW_CONFIG = \"5000,0,0,0,0\"\n    private const val KEY_TOPIC_GUIDE_BAR = \"key_topic_guide_bar\"\n\n    private val defaultConfigMap = mapOf(\n        DiscoveryPageGuideBarType._DOWNLOAD to 0,\n        DiscoveryPageGuideBarType._TOPIC to 2,\n        DiscoveryPageGuideBarType._TOOL to Int.MAX_VALUE,\n        DiscoveryPageGuideBarType._MALL to Int.MAX_VALUE,\n        DiscoveryPageGuideBarType._HUYA to Int.MAX_VALUE,\n    )\n\n    private val defaultDelayConfigMap = mapOf(\n        DiscoveryPageGuideBarType._DOWNLOAD to 5000,\n        DiscoveryPageGuideBarType._TOPIC to 0,\n        DiscoveryPageGuideBarType._TOOL to 0,\n        DiscoveryPageGuideBarType._MALL to 0,\n        DiscoveryPageGuideBarType._HUYA to 0,\n    )\n\n\n    private val guideConfigStr = TRAFT.get(IConfigManagerService::class.java, IConfigManagerService.RDELIVERY)\n        .getConfig(PNGSettings.KEY_DISCOVER_RECOMMEND_GUIDE_CONFIG) ?: DEFAULT_CONFIG\n\n    private val showDelayConfigStr = TRAFT.get(IConfigManagerService::class.java, IConfigManagerService.RDELIVERY)\n        .getConfig(PNGSettings.KEY_DISCOVER_RECOMMEND_GUIDE_SHOW_DELAY_CONFIG) ?: DEFAULT_DELAY_SHOW_CONFIG\n\n    private val configMap by lazy {\n        val configMap = mutableMapOf<Int, Int>()\n        configMap.putAll(defaultConfigMap)\n        val configStr = guideConfigStr\n        XLog.d(TAG, \"configStr:$configStr\")\n        configStr.split(SPLIT).forEachIndexed { index, s ->\n            val type = index + 1\n            val maxCount = s.toIntOr(defaultConfigMap[type] ?: Int.MAX_VALUE)\n            configMap[type] = if (maxCount == -1) {\n                Int.MAX_VALUE\n            } else {\n                maxCount\n            }\n        }\n        XLog.d(TAG, \"configMap:$configMap\")\n        configMap\n    }\n\n\n    private val showDelayConfigMap by lazy {\n        val configMap = mutableMapOf<Int, Int>()\n        configMap.putAll(defaultDelayConfigMap)\n        val configStr = showDelayConfigStr\n        XLog.d(TAG, \"showDelayConfigStr:$showDelayConfigStr\")\n        configStr.split(SPLIT).forEachIndexed { index, s ->\n            val type = index + 1\n            val default = defaultConfigMap[type] ?: 0\n            val delayMills = s.toIntOr(default)\n            configMap[type] = 0.coerceAtLeast(delayMills)\n        }\n        XLog.d(TAG, \"showDelayConfigMap:$configMap\")\n        configMap\n    }\n\n\n    fun getShowDelayMillis(type: Int): Int {\n        return showDelayConfigMap.getOrElse(type) { 0 }\n    }\n\n\n    // 磁盘:展示过的id\n    private var showedIds by setting(\"discover_recommend_guide_showed_id\", \"\")\n\n    // 内存:展示过的id\n    private val showedIdMap by lazy {\n        val tempShowedTypeMap = mutableMapOf<String, Int>()\n        val showedTypeStr = showedIds\n        showedTypeStr.split(SPLIT).forEach { id ->\n            val count = tempShowedTypeMap[id] ?: 0\n            tempShowedTypeMap[id] = count + 1\n        }\n        XLog.d(TAG, \"showedIdStr:$showedTypeStr, tempShowedIdMap:$tempShowedTypeMap\")\n        tempShowedTypeMap\n    }\n\n\n    // 展示过的位置\n    private val showedGuideBarInfoMap = mutableMapOf<Int, DiscoveryPageRecommendGuideBarInfo>()\n\n    // 手动关闭过的位置\n    private val closedPositions = mutableSetOf<Int>()\n\n    @JvmStatic\n    fun getTopicGuideBarShownKey(id: String): String {\n        return \"${KEY_TOPIC_GUIDE_BAR}_$id\"\n    }\n\n\n    /**\n     * 根据位置获取已经展示过的引导\n     */\n    fun getShowedGuideBarInfo(position: Int): DiscoveryPageRecommendGuideBarInfo? {\n        val info = showedGuideBarInfoMap[position]\n        XLog.i(TAG, \"getShowedGuideBarInfo: ${getBarLogInfo(info, position)}\")\n        return info\n    }\n\n\n    /**\n     * 获取要展示的引导\n     */\n    fun getGuideBarInfo(position: Int, itemData: DiscoveryPageRecommendItem?): DiscoveryPageRecommendGuideBarInfo? {\n        checkTime()\n\n        if (closedPositions.contains(position)) {\n            XLog.i(TAG, \"position:$position has been closed\")\n            return null\n        }\n\n        var guideBarInfo = showedGuideBarInfoMap[position]\n        if (guideBarInfo != null) {\n            XLog.i(TAG, \"position:$position has been showed, return showed guide\")\n            return guideBarInfo\n        }\n\n        guideBarInfo = itemData?.guideBarInfo?.firstOrNull { info -> canShowGuideType(info) }\n        XLog.i(TAG, \"getGuideBarInfo: ${getBarLogInfo(guideBarInfo, position)}\")\n        return guideBarInfo\n    }\n\n    fun onJumpButtonClicked(\n        context: Context,\n        position: Int,\n        data: DiscoveryPageRecommendItem?,\n        info: DiscoveryPageRecommendGuideBarInfo?,\n        scene: Int\n    ) {\n        info?.let {\n            IntentUtils.innerForward(context, it.jumpUrl)\n            onGuideBarClick(position, it)\n            DiscoverRecommendReporter.getReporter(scene).reportGuideBtnClick(\n                position = position,\n                info = data,\n                guideBarInfo = it,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.GUIDE_ACTION\n            )\n        }\n\n    }\n\n    private fun canShowGuideType(info: DiscoveryPageRecommendGuideBarInfo): Boolean {\n        // TODO:marklima\n        if (BuildConfig.DEBUG && false) {\n            return true\n        }\n\n        val type = DiscoveryPageGuideBarType.convert(info.barType)\n        if (type == null) {\n            XLog.e(TAG, \"type is null, return false\")\n            return false\n        }\n\n        when (type) {\n            DiscoveryPageGuideBarType.DOWNLOAD -> {\n                val hasShowedCount = showedIdMap[info.barId] ?: 0\n                // 该类型最多展示3次\n                if (hasShowedCount >= getMaxCount(type.value())) {\n                    XLog.i(TAG, \"type: $type, id :${info.barId} has showed maxCount times, return false\")\n                    return false\n                }\n\n                // apk 已安装不展示\n                if (isAppInstalled(info)) {\n                    XLog.i(TAG, \"type: $type, id :${info.barId} has installed, return false\")\n                    return false\n                }\n            }\n            DiscoveryPageGuideBarType.VOTETOPIC,\n            DiscoveryPageGuideBarType.TOPIC -> {\n                if (info.barId.isNotNullOrEmpty()) {\n                    val currentDay = TimeUtil.getCurrentDay()\n                    val lastShowedDay = Settings().getString(getTopicGuideBarShownKey(info.barId), \"\")\n                    XLog.d(TAG, \"barId: ${info.barId} currentDay: $currentDay lastShownDay: $lastShowedDay ${info.title}\")\n                    if (currentDay != lastShowedDay) {\n                        return true\n                    }\n                }\n                return false\n            }\n\n            else -> {\n                val maxCount = getMaxCount(type.value())\n                if (maxCount >= Int.MAX_VALUE) {\n                    XLog.i(TAG, \"type: $type, id :${info.barId} maxCount >= Int.MAX_VALUE, return true\")\n                    return true\n                } else {\n                    val hasShowedCount = showedIdMap[info.barId] ?: 0\n                    // 该类型最多展示2次\n                    if (hasShowedCount >= maxCount) {\n                        XLog.i(TAG, \"type: $type, id :${info.barId} has showed $maxCount times, return false\")\n                        return false\n                    }\n                }\n\n                XLog.i(TAG, \"type: $type, id :${info.barId} return true\")\n                return true\n            }\n        }\n        XLog.i(TAG, \"type: $type, id :${info.barId} return true\")\n        return true\n    }\n\n    private fun getMaxCount(type: Int) =\n        configMap[type] ?: Int.MAX_VALUE\n\n    // 某个引导条仅允许一天展示一次\n    private fun needShowOncePerDay(type: Int): Boolean {\n        return type == DiscoveryPageGuideBarType._TOPIC || type == DiscoveryPageGuideBarType._VOTETOPIC\n    }\n\n    private fun isAppInstalled(info: DiscoveryPageRecommendGuideBarInfo): Boolean {\n        if (info.barType != DiscoveryPageGuideBarType._DOWNLOAD) return false\n        val model = info.appDetail?.covertSimpleAppModelForMiddlePage()\n        if (model == null) {\n            XLog.e(TAG, \"isAppInstalled, id:${info.barId}, type:${info.barType}, id:${info.title} appDetail null\")\n            return false\n        }\n\n        val installed = isAppInstalled(model.mPackageName, model.mVersionCode)\n        XLog.i(\n            TAG, \"isAppInstalled:$installed, mAppName:${model.mAppName}, package:${model.mPackageName}, \" +\n                    \"version:${model.mVersionCode}\"\n        )\n        return installed\n    }\n\n\n    /**\n     * 判断本地是否已安装给定包名与版本号的app\n     */\n    fun isAppInstalled(packageName: String?, versionCode: Int): Boolean {\n        if (!TextUtils.isEmpty(packageName)) {\n            val localInfo = ApkResourceManager.getInstance().getLocalApkInfo(packageName)\n            if (localInfo != null && localInfo.mVersionCode == versionCode) {\n                return true\n            }\n        }\n        return false\n    }\n\n\n    fun onGuideBarClosed(position: Int, info: DiscoveryPageRecommendGuideBarInfo) {\n        closedPositions.add(position)\n        showedGuideBarInfoMap.remove(position)\n        XLog.i(TAG, \"onGuideBarClosed: ${getBarLogInfo(info, position)}\")\n    }\n\n    fun onGuideBarClick(position: Int, info: DiscoveryPageRecommendGuideBarInfo) {\n        XLog.i(TAG, \"onGuideBarClick: ${getBarLogInfo(info, position)}\")\n    }\n\n    fun onGuideBarShowed(position: Int, info: DiscoveryPageRecommendGuideBarInfo) {\n        // 已经展示过的引导不重复记录\n        if (showedGuideBarInfoMap[position] == info) {\n            XLog.i(TAG, \"onGuideBarShowed already showed, return : ${getBarLogInfo(info, position)}\")\n            return\n        }\n\n        // 不限频次的引导且允许一天多次展示的不做记录\n        if (getMaxCount(info.barType) >= Int.MAX_VALUE && !needShowOncePerDay(info.barType)) {\n            XLog.i(TAG, \"onGuideBarShowed type maxCount >= Int.MAX_VALUE, return : ${getBarLogInfo(info, position)}\")\n            return\n        }\n\n\n        val count = (showedIdMap[info.barId] ?: 0) + 1\n        showedIds = if (showedIds.isNotNullOrEmpty()) {\n            \"$showedIds$SPLIT${info.barId}\"\n        } else {\n            info.barId.toString()\n        }\n        XLog.i(TAG, \"onGuideBarShowed: ${getBarLogInfo(info, position)}, ids :$showedIds , count :$count\")\n        showedIdMap[info.barId] = count\n        showedGuideBarInfoMap[position] = info\n    }\n\n    private fun checkTime() {\n        val day = Calendar.getInstance().get(Calendar.DAY_OF_YEAR)\n        XLog.i(TAG, \"checkTime , day :$day, lastDayOfYear :$lastDayOfYear\")\n        if (day != lastDayOfYear) {\n            lastDayOfYear = day\n            showedIds = \"\"\n            showedIdMap.clear()\n            closedPositions.clear()\n        }\n    }\n\n\n    fun clean() {\n        XLog.i(TAG, \"clean\")\n        showedGuideBarInfoMap.clear()\n        closedPositions.clear()\n    }\n\n\n    private fun getBarLogInfo(guideBarInfo: DiscoveryPageRecommendGuideBarInfo?, position: Int) =\n        \"${guideBarInfo?.getBarLogInfo()}, position: $position\"\n\n}\n\nfun DiscoveryPageRecommendGuideBarInfo.getBarLogInfo(): String {\n    return \"title: $title, type: $barType, id: $barId\"\n}\n\n\n\n"}
{"query": "DiscoverInteractionBar.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.content.res.TypedArray\nimport android.util.AttributeSet\nimport android.view.Gravity\nimport android.view.LayoutInflater\nimport android.view.View\nimport android.view.ViewGroup\nimport android.widget.FrameLayout\nimport android.widget.ImageView\nimport android.widget.TextView\nimport androidx.constraintlayout.widget.ConstraintLayout\nimport com.bumptech.glide.Glide\nimport com.bumptech.glide.load.resource.bitmap.CenterCrop\nimport com.bumptech.glide.load.resource.bitmap.RoundedCorners\nimport com.bumptech.glide.request.RequestOptions\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.AppConst.AppState\nimport com.tencent.assistant.component.booking.BookingState.BookState\nimport com.tencent.assistant.component.booking.CraftDownloadBookingButton\nimport com.tencent.assistant.component.booking.style.CustomStyle\nimport com.tencent.assistant.component.download.CraftDownloadButton\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.st.STConstAction\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.dp\nimport com.tencent.assistant.utils.setOnFilterClickListener\nimport com.tencent.assistant.utils.toFormattedString\nimport com.tencent.assistant.utils.toGsonString\nimport com.tencent.pangu.discover.recommend.action.DiscoverRecommendLikeBtnDecorator\nimport com.tencent.pangu.discover.recommend.action.DiscoverShareBtnDecorator\nimport com.tencent.pangu.discover.recommend.model.getLogString\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.link.IntentUtils\nimport com.tencent.pangu.middlepage.model.covertSimpleAppModelForMiddlePage\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/5 16:36\n * Description:\n */\nclass DiscoverInteractionBar @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : ConstraintLayout(context, attrs, defStyleAttr) {\n    private val TAG = \"DiscoverInteractionBar\"\n    var scene: Int = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    val reporter: DiscoverRecommendReporter\n        get() = DiscoverRecommendReporter.getReporter(scene)\n    var listener: InteractionBarListener? = null\n    private var position: Int = 0\n    private var itemData: DiscoveryPageRecommendItem? = null\n    private val radius = 4.dp\n    private val bigIconSize = 24.dp\n    private val bigIconMarginEnd = 6.dp\n    private val appLabel by lazy { findViewById<View>(R.id.app_label) }\n    private val appIcon by lazy { findViewById<ImageView>(R.id.app_icon) }\n    private val appName by lazy { findViewById<TextView>(R.id.app_name) }\n    private val likeBtn by lazy { findViewById<View>(R.id.like_btn) }\n    private val likeIcon by lazy { findViewById<ImageView>(R.id.like_icon) }\n    private val likeTV by lazy { findViewById<TextView>(R.id.like_tv) }\n    private val commentBtn by lazy { findViewById<View>(R.id.comment_btn) }\n    private val commentTV by lazy { findViewById<TextView>(R.id.comment_tv) }\n    private val shareBtn by lazy { findViewById<View>(R.id.share_btn) }\n    private val iconArrow by lazy { findViewById<ImageView>(R.id.icon_arrow) }\n    private val downLoadBtn by lazy {\n        findViewById<CraftDownloadButton>(R.id.download_button).apply {\n            setStyle(DiscoverDownloadBtnCraftStyle(10, 36, 20))\n        }\n    }\n\n    private val bookingBtnContainer by lazy {\n        findViewById<FrameLayout>(R.id.book_btn_container)\n    }\n\n    private val bookingBtn by lazy {\n        CraftDownloadBookingButton(context).apply {\n            setStyle(CustomStyle())\n            setNormalBgColor(PRIMARY_BLUE)\n            setNormalTextColor(PRIMARY_WHITE)\n            setBookingBgColor(PRIMARY_DARK_BLUE)\n            setBookingTextColor(PRIMARY_WHITE)\n            setReminderBgColor(PRIMARY_DARK_BLUE)\n            setReminderTextColor(PRIMARY_WHITE)\n            setBookedBgColor(PRIMARY_DARK_BLUE)\n            setBookedTextColor(PRIMARY_BLUE)\n            setCornerRadiusDp(16F)\n            resizeTextSize(10)\n            addStatusChangedListener { state, buttonText ->\n                XLog.i(TAG, \"onStatusChanged: $state, $buttonText\")\n                itemData?.interactiveInfo?.isBooked = state == BookState.BOOKED\n            }\n        }\n    }\n\n\n    private val likeBtnDecorator by lazy {\n        DiscoverRecommendLikeBtnDecorator(likeBtn, likeIcon, likeTV) { v, like ->\n            reporter.reportBtnClick(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.LIKE_BTN,\n                btnStatus = if (like) \"1\" else \"2\"\n            )\n        }\n    }\n    private val shareBtnDecorator by lazy {\n        DiscoverShareBtnDecorator(shareBtn, reporter) {\n            reporter.reportBtnClick(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.SHARE_BTN\n            )\n        }\n    }\n\n    private var style: Int = Style.SMALL\n\n    init {\n        LayoutInflater.from(context).inflate(R.layout.discover_recommend_interaction_bar, this, true)\n        initAttrs(context, attrs)\n        initView()\n    }\n\n    private fun initAttrs(context: Context, attrs: AttributeSet?) {\n        val ta: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.DiscoverInteractionBar)\n        style = ta.getInt(R.styleable.DiscoverInteractionBar_discoverInteractionBarStyle, Style.SMALL)\n        ta.recycle()\n    }\n\n    private fun initView() {\n        appLabel.setOnFilterClickListener {\n            itemData?.interactiveInfo?.detailTmast?.let {\n                IntentUtils.innerForward(context, it)\n            }\n            reporter.reportBtnClick(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.GAME_LABEL\n            )\n        }\n        commentBtn.setOnFilterClickListener {\n            listener?.onCommentClick()\n            reporter.reportBtnClick(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.COMMENT_BTN\n            )\n        }\n\n        if (style == Style.BIG) {\n            appLabel.background = null\n            appIcon.layoutParams.width = bigIconSize\n            appIcon.layoutParams.height = bigIconSize\n            (appIcon.layoutParams as? MarginLayoutParams)?.let {\n                it.marginEnd = bigIconMarginEnd\n            }\n            downLoadBtn.visibility = View.GONE\n        }\n    }\n\n    fun bindData(position: Int, itemData: DiscoveryPageRecommendItem) {\n        this.position = position\n        this.itemData = itemData\n\n        XLog.i(TAG, \"bindData, position=$position, data=${itemData.getLogString()}\")\n\n        if (itemData.interactiveInfo?.appIcon.isNullOrEmpty()\n            || itemData.interactiveInfo?.appName.isNullOrEmpty()\n        ) {\n            appLabel.visibility = View.GONE\n        } else {\n            appLabel.visibility = View.VISIBLE\n            Glide.with(context)\n                .load(itemData.interactiveInfo?.appIcon)\n                .apply(RequestOptions().transform(CenterCrop(), RoundedCorners(radius)))\n                .into(appIcon)\n            appName.text = itemData.interactiveInfo?.appName ?: \"\"\n            val hasTmast = itemData.interactiveInfo?.detailTmast.isNullOrEmpty()\n            if (hasTmast) {\n                iconArrow.visibility = View.GONE\n            } else {\n                iconArrow.visibility = View.VISIBLE\n            }\n\n            bindBtn(itemData, position)\n\n            reporter.reportBtnExposure(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.GAME_LABEL\n            )\n        }\n\n        commentTV.text = itemData.interactiveInfo?.commentCount?.toFormattedString() ?: \"\"\n        likeBtnDecorator.bindData(itemData)\n        shareBtnDecorator.bindData(position, itemData)\n        reportBtnExposure()\n    }\n\n    private fun bindBtn(itemData: DiscoveryPageRecommendItem, position: Int) {\n        if (style != Style.BIG) return\n\n        if (isBookGame(itemData)) {\n            downLoadBtn.visibility = GONE\n            bindBookButton(itemData, position)\n        } else {\n            bookingBtnContainer.visibility = GONE\n            bindDownloadBtn(itemData, position)\n        }\n    }\n\n    private fun isBookGame(itemData: DiscoveryPageRecommendItem): Boolean {\n        return itemData.interactiveInfo?.isBookGame == true\n    }\n\n    private fun bindDownloadBtn(\n        itemData: DiscoveryPageRecommendItem,\n        position: Int\n    ) {\n        XLog.i(TAG, \"bindDownloadBtn: appDetail: ${itemData.interactiveInfo.appDetail?.toGsonString()}\")\n        if (itemData.interactiveInfo?.appDetail == null\n            || itemData.interactiveInfo.appDetail.appInfo == null\n            || itemData.interactiveInfo?.appDetail?.apkList.isNullOrEmpty()\n        ) {\n            downLoadBtn.visibility = GONE\n            XLog.i(TAG, \"bindDownloadBtn: itemData.interactiveInfo.appDetail is null\")\n            return\n        }\n\n        val model = itemData.interactiveInfo.appDetail.covertSimpleAppModelForMiddlePage()\n        downLoadBtn.setDownloadModel(model)\n        if (isAppInstalled(downLoadBtn.appState) || isAppNeedUpdate(downLoadBtn.appState)) {\n            downLoadBtn.visibility = GONE\n        } else {\n            downLoadBtn.visibility = VISIBLE\n            iconArrow.visibility = GONE\n            reporter.reportInteractBarBtnEvent(\n                eventCode = STConstAction.ACTION_BUTTON_EXPOSURE,\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.DOWNLOAD_BTN\n            )\n        }\n\n        downLoadBtn.setIgnoreFileNotExist(true)\n        val stCommonInfo = reporter.getInteractBarDownloadBtnStCommonInfo(\n            position = position,\n            info = itemData,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.DOWNLOAD_BTN\n        )\n        downLoadBtn.setDefaultClickListener(stCommonInfo)\n        downLoadBtn.setBeforeDownloadButtonClick {\n            XLog.i(TAG, \"setBeforeDownloadButtonClick appState: ${downLoadBtn.appState.name}\")\n            reporter.reportAppClickEvent(\n                appState = downLoadBtn.appState,\n                position = position,\n                info = itemData\n            )\n        }\n    }\n\n    private fun bindBookButton(data: DiscoveryPageRecommendItem, position: Int) {\n        XLog.d(TAG, \"bindBookButton\")\n        iconArrow.visibility = GONE\n        bookingBtnContainer.visibility = VISIBLE\n        if (bookingBtnContainer.childCount <= 0) {\n            bookingBtnContainer.addView(\n                bookingBtn.getViewImpl(),\n                FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)\n            )\n        }\n        bookingBtn.gravity = Gravity.CENTER\n        bookingBtn.apply {\n            setAppId(data.interactiveInfo.appid)\n            setOrdered(data.interactiveInfo.isBooked)\n            report.scene = <EMAIL>\n        }\n        bookingBtn.report = reporter.getInteractBarBookBtnReportInfo(\n            position,\n            data,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.BOOKING_BTN\n        )\n        reporter.reportInteractBarBtnEvent(\n            eventCode = STConstAction.ACTION_BUTTON_EXPOSURE,\n            position = position,\n            info = data,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.BOOKING_BTN\n        )\n    }\n\n    private fun isAppNeedUpdate(appState: AppState?) = appState == AppState.UPDATE\n\n    private fun isAppInstalled(appState: AppState?) = appState == AppState.INSTALLED\n\n    fun isShareDialogShowing() = shareBtnDecorator.isShareDialogShowing()\n\n    fun updateCommentCount(count: Int) {\n        itemData?.interactiveInfo?.commentCount = count.toLong()\n        commentTV.text = count.toFormattedString()\n    }\n\n    private fun reportBtnExposure() {\n        reporter.reportBtnExposure(\n            position = position,\n            info = itemData,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.LIKE_BTN,\n            btnStatus = if (itemData?.interactiveInfo?.isLiked == true) {\n                \"1\"\n            } else {\n                \"2\"\n            }\n        )\n        reporter.reportBtnExposure(\n            position = position,\n            info = itemData,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.COMMENT_BTN,\n        )\n\n        if (shareBtn.visibility == View.VISIBLE) {\n            reporter.reportBtnExposure(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.SHARE_BTN,\n            )\n        }\n    }\n\n\n    interface InteractionBarListener {\n        fun onLikeClick()\n        fun onCommentClick()\n        fun onShareClick()\n    }\n\n    object Style {\n        const val SMALL = 0\n        const val BIG = 1\n    }\n\n}"}
{"query": "DiscoverVideoView.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.content.res.Configuration\nimport android.graphics.Color\nimport android.media.AudioManager\nimport android.media.MediaPlayer\nimport android.text.TextUtils\nimport android.util.AttributeSet\nimport android.view.Gravity\nimport android.view.LayoutInflater\nimport android.view.View\nimport android.view.ViewGroup\nimport android.widget.FrameLayout\nimport android.widget.ImageView\nimport android.widget.SeekBar\nimport android.widget.Toast\nimport androidx.constraintlayout.widget.ConstraintLayout\nimport androidx.constraintlayout.widget.ConstraintLayout.LayoutParams\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.activity.BaseActivity\nimport com.tencent.assistant.component.ToastUtils\nimport com.tencent.assistant.component.video.VideoViewManager\nimport com.tencent.assistant.component.video.listener.DefaultVideoPlayStateNotification\nimport com.tencent.assistant.component.video.listener.TVKPlayerInfoTransfer\nimport com.tencent.assistant.component.video.report.VideoPlayerLifeCycleMonitor\nimport com.tencent.assistant.component.video.view.AttachWindowListener\nimport com.tencent.assistant.component.video.view.VideoViewComponent\nimport com.tencent.assistant.component.video.view.VideoViewComponentV2\nimport com.tencent.assistant.foundation.video.VideoConstants\nimport com.tencent.assistant.net.NetworkUtil\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.st.STConst\nimport com.tencent.assistant.utils.SystemUtils\nimport com.tencent.assistant.utils.ViewUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.dp\nimport com.tencent.assistant.utils.fadeIn\nimport com.tencent.assistant.utils.fadeOut\nimport com.tencent.assistant.utils.isNotNullOrEmpty\nimport com.tencent.pangu.discover.base.manager.IVideoPreRenderVideoManager\nimport com.tencent.pangu.discover.base.utils.DiscoverUIUtil\nimport com.tencent.pangu.discover.recommend.DiscoverRecommendFragment\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendPreRenderVideo\nimport com.tencent.pangu.discover.recommend.manager.DiscoverVideoComponentCacheManager\nimport com.tencent.pangu.discover.recommend.model.getLogString\nimport com.tencent.pangu.discover.recommend.model.getSafeVideoRatio\nimport com.tencent.pangu.discover.recommend.model.isLandVideo\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter.ReportContextKey\nimport com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.TAG\nimport com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.landScapePlayBtnSize\nimport com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.portraitPlayBtnSize\nimport com.tencent.pangu.discover.videofeed.manager.VideoFeedPreRenderVideoManager\nimport com.tencent.pangu.playlet.detail.listener.PlayletProgressUpdateListener\nimport com.tencent.rapidview.deobfuscated.control.video_component.DefaultPlayerViewStateChangeListener\nimport com.tencent.rapidview.deobfuscated.control.video_component.IPlayerReportListener\nimport com.tencent.rapidview.deobfuscated.control.video_component.IPlayerSeekListener\nimport com.tencent.rapidview.deobfuscated.control.video_component.IPlayerStateChangeListener\nimport com.tencent.rapidview.deobfuscated.control.video_component.OnControlViewVisibilityListener\nimport kotlin.math.max\nimport kotlin.math.min\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/5 16:36\n * Description:\n */\nclass DiscoverVideoView @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : ConstraintLayout(context, attrs, defStyleAttr) {\n\n    companion object {\n        const val TAG = \"DiscoverVideoView\"\n        val portraitPlayBtnSize = 100.dp\n        val landScapePlayBtnSize = 36.dp\n    }\n\n    var seakBarlistener: SeekBar.OnSeekBarChangeListener? = null\n    var playletProgressListener: PlayletProgressUpdateListener? = null\n    var controViewlistener: OnControlViewVisibilityListener? = null\n    var firstFrameCallback: ((itemPosition: Int) -> Unit)? = null\n    var playCompleteCallback: ((itemPosition: Int, isFullScreen: Boolean) -> Unit)? = null\n    var isFragmentShow: (() -> Boolean)? = null\n    var isLoadingData: (() -> Boolean)? = null\n\n    private val seekBarBottomPadding =\n        resources.getDimensionPixelSize(R.dimen.discover_recommend_feed_seekbar_bottom_padding)\n    private val bottomBarHeight =\n        resources.getDimensionPixelSize(R.dimen.bottom_navigation_height) + seekBarBottomPadding\n\n    private val phoneScreenHeight = ViewUtils.getPhoneScreenHeight()\n    private val statusBarHeight = ViewUtils.getStatusBarHeight()\n    private val navigationBarHeight = ViewUtils.getNavigationBarHeight()\n\n    private var isVideoInit = false\n    private var isFirstFrameReported = false\n\n\n    private val videoPreRenderManager: IVideoPreRenderVideoManager by lazy {\n        val manager = if (scene == DiscoverRecommendReporter.Scene.VIDEO_FEED_SCENE) {\n            VideoFeedPreRenderVideoManager\n        } else {\n            DiscoverRecommendPreRenderVideo\n        }\n        XLog.i(TAG, \"getVideoPreRenderManager scene: $scene\")\n        manager\n    }\n\n    private fun getScreenHeight(): Int {\n        return getAppScreenHeight(ViewUtils.getScreenHeight(), phoneScreenHeight, navigationBarHeight)\n    }\n\n    private fun getAppScreenHeight(appScreenHeight: Int, phoneScreenHeight: Int, navigationBarHeight: Int): Int {\n\n        XLog.d(\n            TAG, \"getAppScreenHeight appScreenHeight: $appScreenHeight, phoneScreenHeight: $phoneScreenHeight, \" +\n                    \"navigationBarHeight: $navigationBarHeight\"\n        )\n\n        // 未开启底部导航, 且包含状态栏\n        if (appScreenHeight == phoneScreenHeight) {\n            XLog.d(TAG, \"getAppScreenHeight appScreenHeight == phoneScreenHeight: $appScreenHeight\")\n            return appScreenHeight\n        }\n\n        // 开启底部导航, 不包含状态栏\n        if ((appScreenHeight + navigationBarHeight + statusBarHeight) <= phoneScreenHeight) {\n            XLog.d(\n                TAG,\n                \"getAppScreenHeight $appScreenHeight + $navigationBarHeight + $statusBarHeight <= $phoneScreenHeight\"\n            )\n            return appScreenHeight + statusBarHeight\n        }\n\n        // 未开启底部导航, 不包含状态栏\n        if (appScreenHeight + statusBarHeight == phoneScreenHeight) {\n            XLog.d(TAG, \"getAppScreenHeight $appScreenHeight + $statusBarHeight == $phoneScreenHeight\")\n            return appScreenHeight + statusBarHeight\n        }\n\n        XLog.d(TAG, \"getAppScreenHeight $appScreenHeight + $statusBarHeight == $phoneScreenHeight\")\n        // 包含状态栏\n        return appScreenHeight\n\n    }\n\n    override fun onConfigurationChanged(newConfig: Configuration?) {\n        super.onConfigurationChanged(newConfig)\n        XLog.i(TAG, \"onConfigurationChanged\")\n    }\n\n    private var currentDialogHeight = 0\n    private var data: DiscoveryPageRecommendItem? = null\n    private var itemPosition = 0\n\n    var scene: Int = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    val techReporter: DiscoverBeaconReport\n        get() = DiscoverBeaconReport.getReporter(scene)\n    val reporter : DiscoverRecommendReporter\n        get() = DiscoverRecommendReporter.getReporter(scene)\n    private var video: VideoViewComponentV2? = null\n    private val muteBtn by lazy { findViewById<View>(R.id.mute_btn) }\n    private val fullScreenBtn by lazy { findViewById<View>(R.id.full_screen_btn) }\n    private val videoContainer by lazy { findViewById<FrameLayout>(R.id.video_container) }\n    private val seekBar by lazy { findViewById<DiscoverSeekBar>(R.id.seekbar) }\n    private val seekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {\n        override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {\n            seakBarlistener?.onProgressChanged(seekBar, progress, fromUser)\n        }\n\n        override fun onStartTrackingTouch(seekBar: SeekBar) {\n            XLog.d(TAG, \"onStartTrackingTouch\")\n            seakBarlistener?.onStartTrackingTouch(seekBar)\n            video?.onSeekStart(video?.currentPosition ?: 0)\n        }\n\n        override fun onStopTrackingTouch(seekBar: SeekBar) {\n            XLog.d(TAG, \"onStopTrackingTouch: ${seekBar.progress}\")\n            seakBarlistener?.onStopTrackingTouch(seekBar)\n            video?.seekTo(seekBar.progress)\n        }\n    }\n\n    private val playerStateChangeListener = object : IPlayerStateChangeListener {\n\n        override fun onPlayStart(videoView: VideoViewComponent?) {\n            if (itemPosition == 0) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PLAY_START)\n                techReporter.reportVideoOnStart()\n            }\n            playletProgressListener?.onPlayStart(itemPosition)\n            XLog.i(TAG, \"onPlayStart itemPosition = $itemPosition\")\n            setVideoMuteState(video, isFirstVideoMute())\n        }\n\n        override fun onPlayStop(videoView: VideoViewComponent?, currentPosition: Int) {\n            XLog.d(TAG, \"onPlayStop itemPosition = $itemPosition\")\n        }\n\n        override fun onPlayPause(videoView: VideoViewComponent?, currentPosition: Int, reason: Int) {\n            XLog.d(TAG, \"onPlayPause, itemPosition = $itemPosition\")\n            playletProgressListener?.onPlayPause(itemPosition)\n        }\n\n        override fun onPlayContinue(videoView: VideoViewComponent?, currentPosition: Int) {\n            XLog.i(TAG, \"onPlayContinue, itemPosition = $itemPosition, currentPosition =$currentPosition\")\n            if (itemPosition == 0 && currentPosition < 500) {\n                // 开启预渲染后不走 onPlayStart 会走 onPlayContinue\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PLAY_START)\n                techReporter.reportVideoOnStart()\n            }\n\n            setVideoMuteState(video, isFirstVideoMute())\n            playletProgressListener?.onPlayContinue(itemPosition)\n        }\n\n        override fun onPlayComplete(videoView: VideoViewComponent?) {\n            XLog.d(TAG, \"onPlayComplete, itemPosition = $itemPosition\")\n            video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_COMPLETE)\n            playletProgressListener?.onPlayComplete(itemPosition)\n\n        }\n\n        override fun onPrepared(videoView: VideoViewComponent?) {\n            if (itemPosition == 0) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PREPARED)\n                techReporter.reportVideoPrepared()\n            }\n            XLog.i(TAG, \"onPrepared , itemPosition = $itemPosition\")\n        }\n\n        override fun onError(videoView: VideoViewComponent?, errorCode: Int) {\n            XLog.e(TAG, \"onError $errorCode, itemPosition = $itemPosition, vid = ${videoView?.videoUri}\")\n            if (itemPosition == 0) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_ERROR)\n                techReporter.addTagExtra(\n                    DiscoverBeaconReport.ExtraMapKey.VIDEO_FIRST_ERROR_CODE,\n                    errorCode.toString()\n                )\n                techReporter.reportVideoError(errorCode)\n            }\n\n            video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_ERROR)\n        }\n\n        override fun onMute(videoView: VideoViewComponent?, mute: Boolean, changeByUser: Boolean) {\n            XLog.i(TAG, \"onMute =$mute; changeByUser=$changeByUser\")\n            if (!mute && itemPosition == 0) {\n                hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.PHONE_VOLUME_UP)\n            }\n        }\n\n        override fun onProgressUpdate(videoView: VideoViewComponent?, progress: Int) {\n            if (videoView == null) {\n                return\n            }\n            val hasPlayComplete = progress >= videoView.totalDuring - 1000 && videoView.isLoopPlay\n            if (hasPlayComplete) {\n                video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_COMPLETE)\n                XLog.i(TAG, \"onProgressUpdate hasPlayComplete, isSystemVideoPlayer = ${video?.isSystemVideoPlayer}\")\n                // 系统播放器在 loopPlay 时不会回调 TVKPlayerInfoTransfer.PLAYER_INFO_ONE_LOOP_COMPLETE, 此处根据进度判断是否播完\n                if (video?.isSystemVideoPlayer == true && DiscoverConfig.autoPlayNextVideo) {\n                    playCompleteCallback?.invoke(itemPosition, video?.isFullscreen == true)\n                }\n            }\n\n            val totalDuration = videoView.totalDuring\n\n            if (seekBar.max != totalDuration) {\n                seekBar.max = totalDuration\n            }\n            // 使用手势更新进度条时，屏蔽视频播放进度更新\n            if (!seekBar.isUseGestureSeeking()) {\n                seekBar.progress = progress\n            }\n\n\n            playletProgressListener?.onProgressUpdate(itemPosition, progress, totalDuration)\n        }\n\n        override fun onPlayButtonClick(videoView: VideoViewComponent?, isPlaying: Boolean) {\n            XLog.d(TAG, \"onPlayButtonClick, isPlaying $isPlaying\")\n        }\n\n        override fun onFirstFrameRending(videoView: VideoViewComponent?) {\n            firstFrameCallback?.invoke(itemPosition)\n            XLog.i(TAG, \"onFirstFrameRending, itemPosition = $itemPosition, vid =${videoView?.videoUri}, isFirstFrameReported = $isFirstFrameReported\")\n            if (itemPosition == 0) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_FRAME)\n                techReporter.reportFistFrame()\n            }\n\n            if (!(itemPosition == 0 && isFirstFrameReported)) {\n                isFirstFrameReported = true\n                VideoPlayerLifeCycleMonitor.getInstance().onFirstFrameRendingBySelf(videoView)\n            }\n            setVideoMuteState(video, isFirstVideoMute())\n        }\n\n        override fun onClickToJump(videoView: VideoViewComponent?) {\n        }\n\n        override fun onPureClick(videoView: VideoViewComponent?) {\n        }\n\n        override fun onRetryPlay(videoViewComponent: VideoViewComponent?) {\n            XLog.i(TAG, \"onRetryPlay itemPosition = $itemPosition\")\n        }\n    }\n\n    private val playerSeekListener = object : IPlayerSeekListener {\n        override fun onSeekStart(videoView: VideoViewComponent?, position: Int) {\n            XLog.d(TAG, \"onSeekStart $position\")\n        }\n\n        override fun onSeeking(videoView: VideoViewComponent?, position: Int) {\n            XLog.d(TAG, \"onSeeking $position\")\n        }\n\n        override fun onSeekEnd(videoView: VideoViewComponent?, position: Int) {\n            XLog.d(TAG, \"onSeekEnd $position\")\n        }\n\n        override fun onSeekComplete(videoView: VideoViewComponent?, position: Int) {\n            XLog.d(TAG, \"onSeekComplete $position\")\n            if (videoView == null) {\n                return\n            }\n\n            videoView.continuePlay(false)\n        }\n\n    }\n\n    private val playerReportListener = IPlayerReportListener { videoReportModel ->\n        data?.let { data ->\n            reporter.updateVideoReportModel(\n                videoReportModel,\n                data,\n                itemPosition\n            )\n        }\n    }\n\n    private val defaultVideoPlayStateNotification = object : DefaultVideoPlayStateNotification() {\n        override fun onInfo(mediaPlayer: MediaPlayer?, i: Int, i1: Int) {\n            super.onInfo(mediaPlayer, i, i1)\n            XLog.i(TAG, \"onInfo, i = $i, msg=${TVKPlayerInfoTransfer.getMsg(i)}\")\n            when (i) {\n                TVKPlayerInfoTransfer.PLAYER_INFO_ONE_LOOP_COMPLETE -> {\n                    if (video?.isLoopPlay == true) {\n                        XLog.i(TAG, \"onInfo  oneLoopComplete\")\n                        playletProgressListener?.onPlayComplete(itemPosition)\n                        if (DiscoverConfig.autoPlayNextVideo) {\n                            playCompleteCallback?.invoke(itemPosition, video?.isFullscreen == true)\n                        }\n                    }\n                }\n\n\n                TVKPlayerInfoTransfer.PLAYER_INFO_START_GET_VINFO -> {\n                    XLog.i(TAG, \"onInfo  getVInfo start$i\")\n                    if (itemPosition == 0 && data?.videoInfo?.vid?.isNotNullOrEmpty() == true) {\n                        techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_GET_V_INFO_START)\n                    }\n\n                }\n\n                TVKPlayerInfoTransfer.PLAYER_INFO_END_GET_VINFO -> {\n                    XLog.i(TAG, \"onInfo  getVInfo end$i\")\n                    if (itemPosition == 0 && data?.videoInfo?.vid?.isNotNullOrEmpty() == true) {\n                        techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_GET_V_INFO_END)\n                    }\n                }\n            }\n\n        }\n    }\n\n    init {\n        LayoutInflater.from(context).inflate(R.layout.discover_recommend_video_view, this, true)\n\n\n        muteBtn.setOnClickListener { onMuteBtnClick() }\n        seekBar.listener = seekBarChangeListener\n        fullScreenBtn.setOnClickListener {\n            if (isLoadingData?.invoke() == true) {\n                XLog.i(TAG, \"fullScreenBtn onClick return, isLoadingData = $isLoadingData\")\n                return@setOnClickListener\n            }\n            video?.gotoQuitFullscreen()\n        }\n\n        XLog.d(\n            TAG, \"heights = ${ViewUtils.getPhoneScreenHeight()}, ${ViewUtils.getScreenHeight()}, ${\n                ViewUtils\n                    .getScreenHeightReal()\n            }, ${ViewUtils.getNavigationBarHeight()}, ${ViewUtils.getStatusBarHeight()} ,\"\n        )\n\n    }\n\n    private fun initVideo(position: Int) {\n\n        if (isVideoInit) {\n            return\n        }\n        isVideoInit = true\n\n        if (position == 0) {\n            videoPreRenderManager.cancelPreRender()\n        }\n\n        val isPreRenderVideoReady = videoPreRenderManager.isVideoPlayerReady()\n\n        XLog.i(TAG,\"initVideo isPreRenderVideoReady = $isPreRenderVideoReady \")\n\n        video = if (position == 0 && isPreRenderVideoReady) {\n            val player = videoPreRenderManager.getVideoPlayer()!!\n            // getVideoPlayer() 后再获取 isVideoPlayHasReported\n            isFirstFrameReported = videoPreRenderManager.isVideoPlayHasReported\n            XLog.i(TAG, \"isFirstFrameReported = $isFirstFrameReported\")\n            player\n        } else {\n            DiscoverVideoComponentCacheManager.getManager(scene).getVideoViewComponent(context)\n        }\n\n        if (position == 0\n            && isPreRenderVideoReady\n            && video?.videoUri != data?.videoInfo?.vid\n            && video?.videoUri != data?.videoInfo?.videoUrl\n        ) {\n            XLog.i(\n                TAG,\n                \"initVideo video?.videoUri = ${video?.videoUri}, data.vid = ${data?.videoInfo?.vid} , data.videoUrl = ${data?.videoInfo?.videoUrl}\"\n            )\n\n            if (BuildConfig.DEBUG) {\n                ToastUtils.show(context, \"首个视频不一致\")\n            }\n        }\n\n        val layoutParams = FrameLayout.LayoutParams(\n            ViewGroup.LayoutParams.MATCH_PARENT,\n            ViewGroup.LayoutParams.MATCH_PARENT\n        ).apply {\n            this.gravity = Gravity.CENTER\n        }\n\n        if (position == 0 && isPreRenderVideoReady) {\n            addPreRenderVideoView(video!!,  layoutParams)\n        } else {\n            videoContainer.addView(video, layoutParams)\n        }\n\n        video?.setDiscoverMode()\n        video?.setControlViewVisibilityListener(object : OnControlViewVisibilityListener {\n            override fun onShow(onlyShowPlayButton: Boolean) {\n                controViewlistener?.onShow(onlyShowPlayButton)\n            }\n\n            override fun onHidden() {\n                controViewlistener?.onHidden()\n            }\n        })\n        video?.registerIPlayerSeekListener(playerSeekListener)\n        video?.registerIPlayerStateChangeListener(playerStateChangeListener)\n        video?.setPlayerViewStateChangeListener(object : DefaultPlayerViewStateChangeListener() {\n            override fun onStop(videoViewComponent: VideoViewComponent?): Boolean {\n                video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_LEAVE)\n                return true\n            }\n        })\n        video?.registerIPlayerReportListener(playerReportListener)\n        video?.updatePlayStateNotification(defaultVideoPlayStateNotification)\n        video?.setAttachWindowListener(object : AttachWindowListener {\n            override fun onAttachedToWindow() {\n                if (itemPosition == 0) {\n                    techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_ATTACH_TO_WINDOW)\n                }\n            }\n\n            override fun onDetachedFromWindow() {\n\n            }\n\n        })\n    }\n\n    /**\n     * 预渲染好的第一个视频attach到viewHolder上，texture复用达到续播效果\n     */\n    private fun addPreRenderVideoView(video: VideoViewComponentV2, layoutParams: FrameLayout.LayoutParams) {\n        XLog.i(TAG, \"preRenderVideoPlayer texture remove to view holder\")\n        val group = video.parent as ViewGroup\n        video.disableVideoViewCallback()\n        group.removeView(video)\n        videoContainer.addView(video, layoutParams)\n        video.enableVideoViewCallback()\n        // 当推荐页正在展示时播放, 否则在切到推荐页后再播放\n        if (isFragmentShow()) {\n            XLog.i(TAG, \"addPreRenderVideoView: video.videoUri = ${video.videoUri}\")\n            if (!TextUtils.isEmpty(video.videoUri)) {\n                video.tryContinueOrRestartPlay(true, true)\n                if (!isFirstFrameReported && video.rendFirstFrameFlag) {\n                    XLog.i(TAG, \"addPreRenderVideoView: video.rendFirstFrameFlag = ${video.rendFirstFrameFlag}\")\n                    video.notifyOnFirstFrameRending()\n                }\n            }\n        }\n    }\n\n    private fun isFragmentShow(): Boolean {\n        return isFragmentShow?.invoke() ?: true\n    }\n\n\n    fun bindData(itemData: DiscoveryPageRecommendItem, position: Int) {\n        XLog.i(TAG, \"bindData: position:$position, videoInfo: ${itemData.videoInfo?.getLogString()}\")\n        data = itemData\n        itemPosition = position\n        initVideo(position)\n        if (position == 0) {\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BIND_START)\n        }\n        muteBtn.visibility = View.GONE\n        bindVideo(itemData, position, video!!)\n        // 播放二级页的首个视频组件 context 是 MainActivity, 为了避免MainActivity影响此处不注册\n        if (context == video?.activityContext) {\n            VideoViewManager.getInstance().registerVideoViewComponent(video, true)\n        }\n        seekBar.progress = 0\n        if (position == 0) {\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BIND_END)\n            techReporter.reportVideoBind()\n        }\n    }\n\n    fun onSelected() {\n        video?.setActive(true)\n        XLog.i(TAG, \"onSelected: ${video?.isPlaying}, isAttachedToWindow: ${video?.isAttachedToWindow}, \" +\n                \"isAttachToWindow: ${video?.isAttachToWindow}, isDestroyed: ${video?.isDestroyed}\")\n        if (video?.isPlaying == false) {\n            XLog.i(TAG, \"onSelected video?.hasCode: ${video?.hashCode()} , video?.videoUri = ${video?.videoUri}\")\n            // TODO:marklima\n            video?.tryContinueOrRestartPlay(true, true)\n            seekBar.progress = 0\n        }\n\n\n        data?.let {\n            reporter.reportVideoExposure(it, itemPosition)\n        }\n    }\n\n    fun onResume() {\n        if (video?.isManualPaused == false) {\n            XLog.i(TAG, \"onResume: start tryContinueOrRestartPlay\")\n            video?.postDelayed({\n                XLog.i(TAG, \"onResume: delay run tryContinueOrRestartPlay\")\n                val currentContext = context\n                if (currentContext is BaseActivity) {\n                    if (currentContext.isPaused) {\n                        XLog.i(TAG, \"onResume: skip, delay run isPaused, tryContinueOrRestartPlay\")\n                        return@postDelayed\n                    }\n                }\n                XLog.i(\n                    TAG, \"onResume: realRun, delay run isPaused, tryContinueOrRestartPlay\"\n                            + \", video.videoUri = ${video?.videoUri}\"\n                )\n                video?.tryContinueOrRestartPlay(true, true)\n            }, 200)\n        }\n    }\n\n    private fun bindVideo(itemData: DiscoveryPageRecommendItem, position: Int, video: VideoViewComponentV2) {\n        if (position == 0) {\n            itemData.videoInfo?.vid?.let {\n                techReporter.addTagExtra(DiscoverBeaconReport.Key.VID, it)\n            }\n            itemData.videoInfo?.videoUrl?.let {\n                techReporter.addTagExtra(DiscoverBeaconReport.Key.VIDEO_URL, it)\n            }\n            techReporter.reportVideoBind()\n        }\n\n        itemData.videoInfo ?: return\n        XLog.i(TAG, \"bindVideo coverImageUrl is ${itemData.videoInfo.coverImg}, mid = ${itemData.videoInfo.materialId}\")\n\n        if (NetworkUtil.isNetworkActive()) {\n            video.setPlayButtonVisibility(GONE)\n        } else {\n            video.setPlayButtonVisibility(VISIBLE)\n        }\n\n        video.updateVideoLayout(videoContainer, itemData.getSafeVideoRatio())\n        fullScreenBtn.visibility = if (itemData.isLandVideo()) {\n            VISIBLE\n        } else {\n            GONE\n        }\n\n        video.coverImageUrl = itemData.videoInfo?.coverImg\n        val vid = itemData.videoInfo?.vid\n        if (vid.isNotNullOrEmpty()) {\n            video.setVid(vid)\n            XLog.i(TAG, \"bindVideo vid is ${itemData.videoInfo.vid}\")\n        } else if (itemData.videoInfo.videoUrl.isNotNullOrEmpty()) {\n            val url = itemData.videoInfo.videoUrl\n            video.setVideoUrl(url)\n            XLog.i(TAG, \"bindVideo url is $url\")\n        } else {\n            video.clearVideoUrl()\n            video.stop()\n            XLog.e(TAG, \"bindVideo vid is null\")\n            if (BuildConfig.DEBUG) {\n                ToastUtils.show(context, \"视频地址为空\")\n            }\n            return\n        }\n        reporter.let {\n            video.updateReportInfo(itemData, it, position)\n        }\n        video.setAutoPlay(true)\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BEFORE_START)\n        video.post {\n            if (position == 0) {\n                // 当推荐页正在展示时播放, 否则在切到推荐页后再播放\n                if (isFragmentShow()) {\n                    techReporter.addTag(DiscoverBeaconReport.PreloadKey.START_VIDEO)\n                    XLog.i(TAG, \"bindVideo: video.videoUri = ${video.videoUri}\")\n                    val success = video.tryContinueOrRestartPlay(true, true)\n                    techReporter.reportStartVideo(success)\n                }\n                updateMuteBtn(isFirstVideoMute())\n                setVideoMuteState(video, isFirstVideoMute())\n            }\n        }\n    }\n\n\n\n\n    fun onDialogHeightChange(height: Int, maxHeight: Int) {\n        data?.videoInfo?.let {\n            val landscapeVideo = isLandScapeVideo(data)\n            if (landscapeVideo) {\n                handleLandscapeVideoHeightChange(height, maxHeight)\n            } else {\n                handlePortraitVideoHeightChange(height, maxHeight)\n            }\n\n            if (height > 0 && currentDialogHeight == 0) {\n                fullScreenBtn.fadeOut()\n            } else if (height == 0 && currentDialogHeight > 0) {\n                fullScreenBtn.fadeIn()\n            }\n\n            currentDialogHeight = height\n        }\n    }\n\n    fun onSeekProgress(distanceX: Float) {\n        var newProgress: Int = calProgressValue(distanceX)\n        XLog.d(TAG, \"distanceX:$distanceX newProgress: $newProgress\")\n        seekBar.updateProgress(newProgress, true)\n    }\n\n    private fun calProgressValue(distanceX: Float): Int {\n        val progressChange = (distanceX * seekBar.max / seekBar.width).toInt()\n        var newProgress: Int = min(seekBar.progress + progressChange, seekBar.max)\n        newProgress = max(0, newProgress)\n        return newProgress\n    }\n\n    fun onSeekStart() {\n        seekBar.startTrackingTouch()\n    }\n\n    fun onSeekEnd() {\n        seekBar.stopTrackingTouch()\n    }\n\n    private fun isLandScapeVideo(data: DiscoveryPageRecommendItem?): Boolean {\n        return data?.isLandVideo() ?: true\n    }\n\n    private fun handlePortraitVideoHeightChange(height: Int, maxHeight: Int) {\n        val videoViewHeight = getScreenHeight() - height\n        val videoViewWidth = (videoViewHeight * data!!.getSafeVideoRatio()).toInt()\n        val marginBottom = if (height > bottomBarHeight) {\n            height - bottomBarHeight\n        } else {\n            0\n        }\n\n        videoContainer.layoutParams = (videoContainer.layoutParams as? ConstraintLayout.LayoutParams)?.apply {\n            this.width = if (DiscoverConfig.portraitVideoScaleOriginal) {\n                LayoutParams.MATCH_PARENT\n            } else {\n                videoViewWidth\n            }\n            this.height = videoViewHeight\n            this.bottomMargin = marginBottom\n        }\n        video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)\n        video?.post {\n            video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)\n        }\n\n\n        XLog.i(\n            TAG,\n            \"handlePortraitVideoHeightChange, marginBottom: ${marginBottom}, videoHeight: $videoViewHeight, videoWidth: $videoViewWidth\"\n        )\n    }\n\n    private fun handleLandscapeVideoHeightChange(height: Int, maxHeight: Int) {\n\n        val screenWidth = ViewUtils.getScreenWidth()\n        val availableHeight = getScreenHeight() - height\n        val videoHeight = (screenWidth / data!!.getSafeVideoRatio()).toInt()\n        val videoViewHeight = videoHeight.coerceAtMost(availableHeight)\n        val videoWith = (videoViewHeight * data!!.getSafeVideoRatio()).toInt()\n        val videoViewWidth = screenWidth.coerceAtMost(videoWith)\n\n        val extraHeight = getScreenHeight() - maxHeight - videoViewHeight\n\n        val videoContainerHeight = if (extraHeight > 0) {\n            (extraHeight / 0.4F).toInt() + videoViewHeight\n        } else {\n            videoViewHeight\n        }\n\n        val maxMarginBottom = getScreenHeight() - bottomBarHeight - videoContainerHeight\n        val marginBottom = if (height > bottomBarHeight) {\n            (height - bottomBarHeight).coerceAtMost(maxMarginBottom)\n        } else {\n            0\n        }\n\n        XLog.d(\n            TAG, \"handleLandscapeVideoHeightChange,  screenWith: $screenWidth, screenHeight: ${getScreenHeight()},\" +\n                    \"videoViewHeight: $videoViewHeight, videoViewWidth: $videoViewWidth, maxHeight: $maxHeight, \" +\n                    \"extraHeight: $extraHeight, videoContainerHeight: $videoContainerHeight, maxMarginBottom: \" +\n                    \"$maxMarginBottom,  marginBottom: $marginBottom, bottomBarHeight: $bottomBarHeight\"\n        )\n\n        videoContainer.layoutParams = (videoContainer.layoutParams as ConstraintLayout.LayoutParams).apply {\n            this.width = videoViewWidth\n            this.height = videoViewHeight\n            this.bottomMargin = marginBottom\n        }\n        video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)\n\n        XLog.i(\n            TAG,\n            \"handleLandscapeVideoHeightChange, marginBottom: ${marginBottom}, videoHeight: $videoViewHeight, videoWidth: $videoViewWidth\"\n        )\n\n    }\n\n    fun onViewAttachedToWindow() {\n        XLog.d(TAG, \"onViewAttachedToWindow, position:$itemPosition,\")\n        if (DiscoverConfig.discoverSlideVideoShowCoverImage){\n            video?.showCoverImage()\n        }\n    }\n\n    fun onViewDetachedFromWindow() {\n        XLog.d(TAG, \"onViewDetachedFromWindow, position:$itemPosition, ${data?.introductionInfo?.authorName}\")\n        if (DiscoverUIUtil.enablePreRender()) {\n            video?.pause(true, VideoConstants.PAUSE_REASON_SCROLL_OUTSIDE)\n        }\n\n    }\n\n    override fun onDetachedFromWindow() {\n        super.onDetachedFromWindow()\n        XLog.d(TAG, \"onDetachedFromWindow, position:$itemPosition, ${data?.introductionInfo?.authorName}\")\n        if (video?.isFullscreen == true) {\n            video?.gotoQuitFullscreen()\n        }\n    }\n\n    private fun setVideoMuteState(video: VideoViewComponentV2?, mute: Boolean, byUser: Boolean = false) {\n        XLog.i(TAG, \"setVideoMuteState, mute: $mute, byUser: $byUser\")\n        video?.setMute(mute, true, byUser)\n    }\n\n\n    private fun VideoViewComponent.updateReportInfo(\n        data: DiscoveryPageRecommendItem,\n        reporter: DiscoverRecommendReporter,\n        reportPosition: Int,\n    ) {\n        reporter.updateVideoReportModel(\n            videoReportModel,\n            data,\n            reportPosition\n        )\n    }\n\n    private fun updateMuteBtn(mute: Boolean) {\n        if (mute) {\n            muteBtn.visibility = View.VISIBLE\n            reporter.reportBtnExposure(\n                position = itemPosition,\n                info = data,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.MUTE_BTN\n            )\n        } else {\n            muteBtn.visibility = View.GONE\n        }\n    }\n\n    private fun onMuteBtnClick() {\n        val mute = video?.isMute ==  true\n        if (!mute) {\n            val volume = SystemUtils.getCurrentVolume(AudioManager.STREAM_MUSIC)\n            if (volume == 0) {\n                ToastUtils.show(muteBtn.context, \"请调大系统音量\", Toast.LENGTH_SHORT)\n            }\n        }\n\n        XLog.i(TAG, \"click mute state = $mute\")\n        hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.CLICK)\n        setVideoMuteState(video, mute, true)\n    }\n\n    private fun reportMuteBtnClick(type: String) {\n        reporter.reportBtnClick(\n            position = itemPosition,\n            info = data,\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.MUTE_BTN,\n            btnStatus = null,\n            reportContextKey = ReportContextKey.VIDEO_REPORT_CONTEXT,\n            DiscoverRecommendReporter.ExtendFiledKey.UNI_CANCEL_TYPE to type\n        )\n    }\n\n    private fun isFirstVideoMute(): Boolean {\n        return itemPosition == 0\n                && DiscoverRecommendFragment.firstVideoMute\n                && isRecommendScene()\n    }\n\n    private fun isRecommendScene() = scene == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n\n\n    fun onRecycled() {\n        XLog.i(TAG, \"onRecycled, position = $itemPosition, vid = ${video?.videoUri}\")\n        isFirstFrameReported = false\n        video?.setPlayButtonVisibility(GONE)\n        video?.onRecycled(DiscoverConfig.recycleForceStopVideo)\n        VideoViewManager.getInstance().unregisterVideoViewComponent(video)\n    }\n\n    fun preRender() {\n        XLog.i(TAG, \"preRender\")\n        if (DiscoverUIUtil.enablePreRender()) {\n            if (DiscoverConfig.recycleForceStopVideo && video?.isPreRendered == true) {\n                return\n            }\n            video?.preRender()\n        }\n    }\n\n    fun onUnSelected() {\n        if (DiscoverUIUtil.enablePreRender()) {\n            video?.pause(true, VideoConstants.PAUSE_REASON_SCROLL_OUTSIDE)\n        }\n        hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.SCROLL_DOWN)\n        video?.setActive(false)\n    }\n\n    private fun hideMuteBtn(reason: String) {\n        if (muteBtn.visibility == VISIBLE) {\n            updateMuteBtn(false)\n            reportMuteBtnClick(reason)\n            DiscoverRecommendFragment.firstVideoMute = false\n        }\n    }\n\n    fun toggleVideoFullScreen(fullScreen: Boolean) {\n        // 只横屏视频做旋转\n        if (!isLandScapeVideo(data)) {\n            return\n        }\n\n        if (fullScreen != video?.isFullscreen) {\n            video?.gotoQuitFullscreen()\n        }\n    }\n\n    fun isFullScreen(): Boolean {\n        return video?.isFullscreen == true\n    }\n\n    fun onPagePause() {\n        if (video?.isPaused == false) {\n            video?.onPause(video?.context)\n        }\n    }\n\n    fun onPageStop() {\n        if (video?.isStopped == false) {\n            video?.onStop(video?.context)\n        }\n    }\n\n    fun setBias(newBias: Float) {\n        val layoutParams = videoContainer.layoutParams as LayoutParams\n        val currentBias = layoutParams.verticalBias\n        if (currentBias == newBias) {\n            XLog.i(TAG, \"currentBias == newBias\")\n            return\n        }\n        layoutParams.verticalBias = newBias\n        videoContainer.layoutParams = layoutParams\n    }\n\n}\n\nfun VideoViewComponentV2.updateVideoLayout(\n    videoContainer: ViewGroup,\n    videoRatio: Float\n) {\n    val video = this\n    val landscapeVideo = isLandVideo(videoRatio)\n    video.setIsLandscapeVideo(landscapeVideo)\n    val screenWidth = ViewUtils.getScreenWidth()\n    val videoHeight = if (landscapeVideo) {\n        (screenWidth / videoRatio).toInt()\n    } else {\n        ViewGroup.LayoutParams.MATCH_PARENT\n    }\n\n    video.layoutParams = video.layoutParams?.apply {\n        this.height = videoHeight\n    }\n\n    video.post {\n        video.layoutParams = video.layoutParams?.apply {\n            this.height = videoHeight\n        }\n    }\n\n    videoContainer.layoutParams = videoContainer.layoutParams.apply {\n        this.height = if (landscapeVideo) {\n            LayoutParams.WRAP_CONTENT\n        } else {\n            LayoutParams.MATCH_PARENT\n        }\n    }\n\n    videoContainer.post {\n        videoContainer.layoutParams.apply {\n            this.height = if (landscapeVideo) {\n                LayoutParams.WRAP_CONTENT\n            } else {\n                LayoutParams.MATCH_PARENT\n            }\n        }\n    }\n\n    val coverHeight = (screenWidth / videoRatio).toInt()\n    video.setCoverImageViewSize(screenWidth, coverHeight)\n    video.post { video.setCoverImageViewSize(screenWidth, coverHeight) }\n    video.landscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    if (landscapeVideo || DiscoverConfig.portraitVideoScaleOriginal) {\n        video.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    } else {\n        video.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_FULLSCREEN\n    }\n    video.refreshScaleType()\n\n    XLog.d(TAG, \"ratio=$videoRatio, width = $screenWidth, height = ${video.layoutParams?.height}\")\n}\n\n\n\n\n\nfun VideoViewComponentV2.setDiscoverMode() {\n    this.isDiscoverMode = DiscoverUIUtil.enablePreRender()\n    this.setFixPauseBeforePrepared(DiscoverConfig.fixPauseBeforePrepare)\n    this.setBackgroundColor(Color.BLACK)\n    this.setNeedLoopPlay(true)\n    this.setCanPlayNotWifiFlag(true)\n    this.setIsShowMuteView(false, true)\n    this.isMute = true\n    this.setIsShowFullScreenView(false, true)\n    this.setIsShowTextProgressLayout(false, true, true)\n    this.setIsShowTextProgressLayout(true, false, true)\n    this.setIsShowProgressBar(false, true)\n    this.setIsShowProgressBar(true, false)\n    this.setEnableAdaptAutoScaleType(true)\n    this.setClickVideoToPause(false)\n    this.updateControlViewBottomMargin(16.dp)\n    this.adaptPortraitScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    this.adaptLandscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    this.landscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    this.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO\n    this.isReportLoopPlayStart = false\n    this.isReportLoopPlayEnd = false\n    this.setOnceLoopComplete(false)\n    this.setCloseGaussBlur(true)\n    this.setOnlyShowControlViewOnManual(true)\n    this.setNeedPreload(true)\n    this.setClickVideoToPause(true)\n    this.setPlaySpeedRatio(1.0F)\n    this.setStartPlayRationInsideListView(0F)\n    this.setCoverImageViewScaleType(ImageView.ScaleType.CENTER_CROP.ordinal)\n    this.hidePlayButton()\n    this.setPlayButtonSize(portraitPlayBtnSize, true)\n    this.setPlayButtonSize(landScapePlayBtnSize, false)\n    this.setCoverDismissAnimationDuring(100)\n    this.onClickNotControlArea = {\n        XLog.i(TAG, \"onClickNotControlArea\")\n        false\n    }\n}"}
{"query": "DiscoverRecommendReporter.kt", "value": "package com.tencent.pangu.discover.recommend.report\n\nimport com.tencent.assistant.AppConst.AppState\nimport com.tencent.assistant.component.video.report.VideoReportManager\nimport com.tencent.assistant.component.video.report.VideoReportModel\nimport com.tencent.assistant.protocol.jce.DiscoveryPageGuideBarType\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendGuideBarInfo\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.report.PageStateParams\nimport com.tencent.assistant.report.RequestType\nimport com.tencent.assistant.st.STConst\nimport com.tencent.assistant.st.STConstAction\nimport com.tencent.assistant.st.model.STCommonInfo\nimport com.tencent.assistant.st.page.STPageInfo\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.concurrentHashMapOf\nimport com.tencent.assistant.utils.isNotNullOrEmpty\nimport com.tencent.assistantv2.st.page.STInfoV2\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\nimport com.tencent.pangu.discover.recommend.model.isLandVideo\nimport com.tencent.pangu.utils.BasePageReporter\nimport com.tencent.rapidview.report.PhotonReportConst.PAGE_DURATION\nimport java.net.URLEncoder\nimport java.nio.charset.StandardCharsets\n\n/**\n * Copyright (c) 2023 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/11 19:26\n * Description: 发现推荐页数据上报\n *\n * @see <a href=\"https://doc.weixin.qq.com/sheet/e3_ALsAqwaaACcsJ61rvW6S5WKiJicaD?scode=AJEAIQdfAAolqBiwO2ALsAqwaaACc&tab=lwspg3&journal_source=chat&version=4.1.26.6024&platform=win\">埋点文档</a>\n */\nclass DiscoverRecommendReporter(override val scene: Int) : BasePageReporter() {\n\n    companion object {\n\n        private val reporters = concurrentHashMapOf<Int, DiscoverRecommendReporter>()\n\n        fun getReporter(scene: Int): DiscoverRecommendReporter {\n            return reporters.getOrPut(scene) { DiscoverRecommendReporter(scene) }\n        }\n    }\n\n    private val TAG = \"PlayletRecommendReporter_$scene\"\n\n    object ExtendFiledKey {\n        const val TOTAL_DURATION = \"total_duration\"\n        const val REPORT_CONTEXT = \"report_context\"\n        const val PAGE_DURATION = \"page_duration\"\n        const val UNI_TEXT_CONTENT = \"uni_text_content\"\n        const val UNI_BUTTON_TITLE = \"uni_button_title\"\n        const val SCREEN_STS = \"screen_sts\"\n        const val SCENE_APP_ID = \"scene_appid\"\n        const val BUTTON_STATUS = \"buttonstatus\"\n        const val UNI_CARD_TAG_ID = \"uni_card_tag_id\"\n        const val UNI_CARD_TAG_NAME = \"uni_card_tag_name\"\n        const val UNI_POP_TYPE = \"uni_pop_type\"\n        const val UNI_CANCEL_TYPE = \"uni_cancel_type\"\n        const val UNI_GAME_GUIDE_TYPE = \"uni_game_guide_type\"\n    }\n\n\n    /**\n     *\n     * https://git.woa.com/MobileAssistBusi/formal_jce/-/merge_requests/182\n     * button_report_context;（右上角分享等按钮）\n     * video_report_context;（推荐内容卡/视频+静音/标签/互动区按钮相关事件）\n     * pop_report_context; （分享面板相关事件）\n     * comment_report_context; （评论相关事件）\n     */\n    object ReportContextKey {\n        const val BUTTON_REPORT_CONTEXT = \"button_report_context\"\n        const val VIDEO_REPORT_CONTEXT = \"video_report_context\"\n        const val POP_REPORT_CONTEXT = \"pop_report_context\"\n        const val COMMENT_REPORT_CONTEXT = \"comment_report_context\"\n        const val APP_REPORT_CONTEXT = \"app_report_context\"\n    }\n\n    object ButtonTitle {\n        const val SEARCH = \"搜索\"\n        const val DOWNLOAD = \"下载\"\n        const val MORE = \"简介展开或收起\"\n        const val MUTE_BTN = \"视频静音按钮\"\n        const val GUIDE_ACTION = \"引导操作按钮\"\n        const val GUIDE_CLOSE = \"引导关闭按钮\"\n        const val BLUE_VIEW = \"蓝选项按钮\"\n        const val RED_VIEW = \"红选项按钮\"\n        const val NICK = \"作者昵称\"\n        const val TAG = \"简介标签\"\n        const val GAME_LABEL = \"内容互动游戏名\"\n        const val LIKE_BTN = \"内容互动点赞\"\n        const val COMMENT_BTN = \"内容互动评论\"\n        const val SHARE_BTN = \"内容互动分享\"\n\n        //\n        const val DOWNLOAD_BTN = \"引导下载\"\n        const val BOOKING_BTN = \"引导预约\"\n        const val OPEN_BTN = \"引导打开\"\n    }\n\n    object Element {\n        const val SEARCHBAR = \"searchbar\"\n        const val DOWNLOAD_CENTER_BTN = \"downloadlistentrance\"\n    }\n\n    object PageReportParams {\n        const val PAGE_NAME = \"发现页推荐流\"\n        const val CMD = \"2240\"\n    }\n\n\n    object ButtonStatus {\n        //点击按钮\n        const val CLICK = \"1\"\n\n        //下刷\n        const val SCROLL_DOWN = \"2\"\n\n        //按手机音量键\n        const val PHONE_VOLUME_UP = \"3\"\n    }\n\n    object Scene {\n        /**\n         * 推荐页\n         */\n        const val RECOMMEND_SCENE = 10843\n\n        /**\n         * 打榜页\n         */\n        const val TOPIC_SCENE = 10845\n\n        /**\n         * 游戏库\n         */\n        const val GAME_CENTER_SCENE = STConst.ST_MAIN_PAGE_GAME_CENTER\n\n        /**\n         * 视频流\n         */\n        const val VIDEO_FEED_SCENE = 10936\n    }\n\n    object DefaultConstants {\n        const val DEFAULT_SLOT = \"99_-1\"\n        const val DEFAULT_MODEL_TYPE = -1\n    }\n\n\n\n    override val pageStateParams = PageStateParams().apply {\n        pageName = PageReportParams.PAGE_NAME\n        cmd = PageReportParams.CMD\n    }\n\n\n    private var pageInfo: STPageInfo? = null\n    override var sourceScene = -1\n        get() = pageInfo?.prePageId ?: -1\n\n\n    override var sourceSlot = \"\"\n        get() = pageInfo?.sourceSlot ?: \"-1_-1_-1_-1\"\n\n    override var sourceModelType = -1\n        get() = pageInfo?.sourceModelType ?: -1\n\n\n    private var pageInTimeMs = 0L\n    private val modelType = DefaultConstants.DEFAULT_MODEL_TYPE\n\n    private var currentRecommendItemData: DiscoveryPageRecommendItem? = null\n    private var currentRecommendPosition: Int = 0\n\n\n    fun init(pageInfo: STPageInfo) {\n        this.pageInfo = pageInfo\n        XLog.i(\n            TAG, \"init, scene=$scene, sourceScene=$sourceScene, sourceSlot=$sourceSlot, \" +\n                    \"sourceModelType=$sourceModelType\"\n        )\n    }\n\n    fun pageIn() {\n        pageInTimeMs = System.currentTimeMillis()\n        reportPageEvent(STConstAction.ACTION_PAGE_IN)\n    }\n\n    fun pageOut() {\n        reportPageEvent(\n            STConstAction.ACTION_PAGE_OUT,\n            PAGE_DURATION to (System.currentTimeMillis() - pageInTimeMs)\n        )\n    }\n\n    fun pageExposure() {\n        reportPageEvent(STConstAction.ACTION_PAGE_EXPOSURE)\n    }\n\n    fun pageRenderFinish(cacheData: Int = 0, updateCacheData: Boolean = false) {\n        if (updateCacheData) {\n            editOriginalParams {\n                this.cacheData = cacheData\n            }\n            withParams {\n                this.cacheData = cacheData\n            }\n        }\n        if (pageStateParams.cacheData == 1 || pageStateParams.renderFinish == 0) {\n            reportRenderFinish()\n        }\n    }\n\n    fun pageRenderStart() {\n        if (pageStateParams.renderFinish == 0) {\n            reportRenderStart()\n        }\n    }\n\n    fun getRequestType(\n        requestType: com.tencent.assistant.request.RequestType,\n        refresh: Boolean,\n        retry: Boolean\n    ): RequestType {\n        if (refresh) {\n            return RequestType.Refresh\n        }\n        if (retry) {\n            return RequestType.Retry\n        }\n        if (requestType == com.tencent.assistant.request.RequestType.LOAD_MORE) {\n            return RequestType.LoadMore\n        }\n        return RequestType.Normal\n    }\n\n    private fun reportPageEvent(eventCode: Int, vararg extraData: Pair<String, Any?>) {\n        reportRecommendEvent(\n            eventCode = eventCode,\n            element = STConst.ELEMENT_PAGE,\n            slotId = \"-1_-1_-1_-1\",\n            info = null,\n            reportContextValue = null,\n            extraData = extraData\n        )\n    }\n\n    /**\n     * 发现页按钮点击\n     */\n    fun reportDiscoverBtnClick(appendCurrentItemInfo: Boolean, element: String) {\n        reportRecommendEvent(\n            scene = scene,\n            eventCode = STConstAction.ACTION_BUTTON_CLICK_TO_JUMP,\n            element = element,\n            slotId = if (appendCurrentItemInfo) {\n                getSlot(currentRecommendPosition)\n            } else {\n                DefaultConstants.DEFAULT_SLOT\n            },\n            recommendId = null,\n            info = if (appendCurrentItemInfo) {\n                currentRecommendItemData\n            } else {\n                null\n            },\n            reportContextValue = if (appendCurrentItemInfo) {\n                getCommonContextExtra(currentRecommendItemData, ReportContextKey.BUTTON_REPORT_CONTEXT)\n            } else {\n                null\n            }\n        )\n    }\n\n\n    fun updateDownloadEntranceBtnReportInfo(appendCurrentItemInfo: Boolean, stInfoV2: STInfoV2) {\n\n        stInfoV2.slotId = if (appendCurrentItemInfo) {\n            getSlot(currentRecommendPosition)\n        } else {\n            DefaultConstants.DEFAULT_SLOT\n        }\n\n        if (appendCurrentItemInfo && currentRecommendItemData != null) {\n            stInfoV2.appendExtendedField(ExtendFiledKey.SCENE_APP_ID, getAppId(currentRecommendItemData!!))\n        }\n\n        val reportContext = if (appendCurrentItemInfo) {\n            getCommonContextExtra(currentRecommendItemData, ReportContextKey.BUTTON_REPORT_CONTEXT)\n        } else {\n            null\n        }\n\n        stInfoV2.appendExtendedField(ExtendFiledKey.REPORT_CONTEXT, reportContext)\n    }\n\n\n    /**\n     * 卡曝光\n     */\n    fun reportCardExposure(info: DiscoveryPageRecommendItem, position: Int) {\n        currentRecommendPosition = position\n        currentRecommendItemData = info\n        reportRecommendEvent(\n            eventCode = STConstAction.ACTION_CARD_EXPOSURE,\n            element = STConst.ELEMENT_CARD,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = getCommonContextExtra(info, ReportContextKey.VIDEO_REPORT_CONTEXT)\n        )\n    }\n\n    /**\n     * 上报视频曝光\n     */\n    fun reportVideoExposure(info: DiscoveryPageRecommendItem, position: Int) {\n        reportRecommendEvent(\n            eventCode = STConstAction.ACTION_BROSWER,\n            element = STConst.ELEMENT_VIDEO,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = getCommonContextExtra(info, ReportContextKey.VIDEO_REPORT_CONTEXT),\n            extraData = getVideoExtra(info)\n        )\n    }\n\n\n    private fun getVideoExtra(info: DiscoveryPageRecommendItem): Array<Pair<String, Any?>> {\n        return arrayOf(\n            ExtendFiledKey.TOTAL_DURATION to getVideoDuration(info),\n            ExtendFiledKey.SCREEN_STS to getVideoType(info),\n            // 此处为和视频其他上报保持一致 VideoReportManager.VIDEO_ID 为 \"videoid\"\n            VideoReportManager.VIDEO_ID to getVid(info)\n        )\n    }\n\n    private fun getVideoType(info: DiscoveryPageRecommendItem) =\n        if (info.isLandVideo()) {\n            \"横屏视频\"\n        } else {\n            \"竖屏视频\"\n        }\n\n\n    fun updateVideoReportModel(\n        videoReportModel: VideoReportModel,\n        info: DiscoveryPageRecommendItem,\n        reportPosition: Int,\n    ) {\n        videoReportModel.apply {\n            isReportCloneModel = DiscoverConfig.cloneReportModel\n            scene = <EMAIL>\n            sourceScene = <EMAIL>\n            sourceSceneSlot = <EMAIL>\n            setSourceModelType(<EMAIL>)\n            modelType = <EMAIL>\n            isAutoPlay = true\n            recommendId = getRecommendId(info)\n            slot = <EMAIL>(reportPosition)\n            subPosition = DEFAULT_SUB_POSITION.toString()\n            videoId = <EMAIL>(info)\n            stInfoV2.appendExtendedField(\n                ExtendFiledKey.REPORT_CONTEXT, <EMAIL>(\n                    info,\n                    ReportContextKey.VIDEO_REPORT_CONTEXT\n                )\n            )\n            totalDuration = <EMAIL>(info)\n            stInfoV2.appendExtendedField(\n                ExtendFiledKey.SCREEN_STS, <EMAIL>\n                    (info)\n            )\n            stInfoV2.appendExtendedField(ExtendFiledKey.SCENE_APP_ID, <EMAIL>(info))\n            stInfoV2.appendExtendedField(\n                ExtendFiledKey.TOTAL_DURATION, this@DiscoverRecommendReporter\n                    .getVideoDuration(info)\n            )\n        }\n    }\n\n    private fun getRecommendId(info: DiscoveryPageRecommendItem) = info.videoInfo?.rid\n\n    private fun getSlot(reportPosition: Int) = \"99_${reportPosition + 1}\"\n\n\n    fun reportBtnExposure(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        btnTitle: String,\n        btnStatus: String? = null,\n        reportContextKey: String = ReportContextKey.VIDEO_REPORT_CONTEXT,\n        vararg extraData: Pair<String, Any?> = emptyArray()\n    ) {\n        info ?: return\n\n        reportBtnEvent(\n            position,\n            info,\n            btnTitle,\n            btnStatus,\n            STConstAction.ACTION_BUTTON_EXPOSURE,\n            reportContextKey,\n            *extraData\n        )\n    }\n\n\n    fun reportBtnClick(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        btnTitle: String,\n        btnStatus: String? = null,\n        reportContextKey: String = ReportContextKey.VIDEO_REPORT_CONTEXT,\n        vararg extraData: Pair<String, Any?> = emptyArray()\n    ) {\n        info ?: return\n        reportBtnEvent(\n            position,\n            info,\n            btnTitle,\n            btnStatus,\n            STConstAction.ACTION_BUTTON_CLICK_TO_JUMP,\n            reportContextKey,\n            *extraData\n        )\n    }\n\n    private fun reportBtnEvent(\n        position: Int,\n        info: DiscoveryPageRecommendItem,\n        btnTitle: String,\n        btnStatus: String?,\n        eventCode: Int,\n        reportContextKey: String,\n        vararg extraData: Pair<String, Any?> = emptyArray()\n    ) {\n        reportRecommendEvent(\n            eventCode = eventCode,\n            element = STConst.ELEMENT_BUTTON,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = getCommonContextExtra(info, reportContextKey),\n            ExtendFiledKey.BUTTON_STATUS to btnStatus,\n            ExtendFiledKey.UNI_BUTTON_TITLE to btnTitle,\n            *extraData\n        )\n    }\n\n    private fun getVideoDuration(itemData: DiscoveryPageRecommendItem): Long {\n        return (itemData.videoInfo?.duration ?: 0) * 1000\n    }\n\n\n    fun reportGuideBtnExposure(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        guideBarInfo: DiscoveryPageRecommendGuideBarInfo,\n        isUpdate: Boolean = false,\n        btnTitle: String,\n    ) {\n        info ?: return\n        val guideTypeStr = getGuideTypeStr(guideBarInfo, isUpdate)\n        reportRecommendEvent(\n            eventCode = STConstAction.ACTION_BUTTON_EXPOSURE,\n            element = STConst.ELEMENT_BUTTON,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = guideBarInfo.reportContext?.get(ReportContextKey.BUTTON_REPORT_CONTEXT),\n            ExtendFiledKey.UNI_GAME_GUIDE_TYPE to guideTypeStr,\n            ExtendFiledKey.UNI_BUTTON_TITLE to btnTitle\n        )\n    }\n\n\n    fun reportGuideBtnClick(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        guideBarInfo: DiscoveryPageRecommendGuideBarInfo,\n        isUpdate: Boolean = false,\n        btnTitle: String,\n    ) {\n        info ?: return\n        val guideTypeStr = getGuideTypeStr(guideBarInfo, isUpdate)\n        reportRecommendEvent(\n            eventCode = STConstAction.ACTION_BUTTON_CLICK_TO_JUMP,\n            element = STConst.ELEMENT_BUTTON,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = guideBarInfo.reportContext?.get(ReportContextKey.BUTTON_REPORT_CONTEXT),\n            ExtendFiledKey.UNI_GAME_GUIDE_TYPE to guideTypeStr,\n            ExtendFiledKey.UNI_BUTTON_TITLE to btnTitle\n        )\n    }\n\n\n    fun getGuideDownloadBtnStCommonInfo(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        guideBarInfo: DiscoveryPageRecommendGuideBarInfo,\n        isUpdate: Boolean = false,\n        btnTitle: String\n    ):\n            STCommonInfo {\n        val reportContextValue: String? = guideBarInfo.reportContext?.get(ReportContextKey.BUTTON_REPORT_CONTEXT)\n        return STCommonInfo().apply {\n            this.scene = <EMAIL>\n            this.sourceScene = <EMAIL>\n            this.sourceModleType = <EMAIL>\n            this.sourceSceneSlotId = <EMAIL>\n            this.modleType = <EMAIL>\n            this.slotId = getSlot(position)\n            val guideTypeStr = getGuideTypeStr(guideBarInfo, isUpdate)\n            setReportElement(STConst.ELEMENT_APP)\n            appendExtendedField(ExtendFiledKey.REPORT_CONTEXT, reportContextValue)\n            appendExtendedField(ExtendFiledKey.UNI_GAME_GUIDE_TYPE, guideTypeStr)\n            appendExtendedField(ExtendFiledKey.UNI_BUTTON_TITLE, btnTitle)\n            info?.let {\n                appendExtendedField(ExtendFiledKey.SCENE_APP_ID, getAppId(it))\n            }\n        }\n    }\n\n    fun reportInteractBarBtnEvent(\n        eventCode: Int,\n        position: Int,\n        info: DiscoveryPageRecommendItem,\n        btnTitle: String,\n    ) {\n        reportAppEvent(\n            eventCode = eventCode,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = info.reportContext?.get(ReportContextKey.APP_REPORT_CONTEXT),\n            STConst.UNI_APP_STATE to btnTitle\n        )\n    }\n\n\n    fun getInteractBarDownloadBtnStCommonInfo(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        btnTitle: String\n    ): STCommonInfo {\n        val reportContextValue: String? = info?.reportContext?.get(ReportContextKey.BUTTON_REPORT_CONTEXT)\n        return STCommonInfo().apply {\n            this.scene = <EMAIL>\n            this.sourceScene = <EMAIL>\n            this.sourceModleType = <EMAIL>\n            this.sourceSceneSlotId = <EMAIL>\n            this.modleType = <EMAIL>\n            this.slotId = getSlot(position)\n            setReportElement(STConst.ELEMENT_APP)\n            appendExtendedField(ExtendFiledKey.REPORT_CONTEXT, reportContextValue)\n            appendExtendedField(ExtendFiledKey.UNI_BUTTON_TITLE, btnTitle)\n            info?.let {\n                appendExtendedField(ExtendFiledKey.SCENE_APP_ID, getAppId(it))\n            }\n        }\n    }\n\n\n    fun getInteractBarBookBtnReportInfo(\n        position: Int,\n        info: DiscoveryPageRecommendItem,\n        btnTitle: String\n    ): STInfoV2 {\n        val reportContextValue = info.reportContext?.get(ReportContextKey.APP_REPORT_CONTEXT)\n        val slot = getSlot(position)\n        val stInfoV2 = STInfoV2(scene, slot, sourceScene, sourceSlot, 0)\n        stInfoV2.setReportElement(STConst.ELEMENT_APP)\n        stInfoV2.sourceScene = sourceScene\n        stInfoV2.sourceModleType = sourceModelType\n        stInfoV2.sourceSceneSlotId = sourceSlot\n        stInfoV2.slotId = slot\n        stInfoV2.appendExtendedField(STConst.UNI_RELATED_APPID, getAppId(info))\n        stInfoV2.modleType = modelType\n        stInfoV2.appendExtendedField(STConst.UNI_APP_STATE, btnTitle)\n        stInfoV2.appendExtendedField(ExtendFiledKey.REPORT_CONTEXT, reportContextValue)\n        stInfoV2.appendExtendedField(ExtendFiledKey.SCENE_APP_ID, getAppId(info))\n        return stInfoV2\n    }\n\n\n    private fun getGuideTypeStr(guideBarInfo: DiscoveryPageRecommendGuideBarInfo, isUpdate: Boolean): String {\n        val type = DiscoveryPageGuideBarType.convert(guideBarInfo.barType)\n        val guideTypeStr = when (type) {\n            DiscoveryPageGuideBarType.DOWNLOAD -> {\n                if (isUpdate) {\n                    \"2\"\n                } else {\n                    \"1\"\n                }\n            }\n\n            DiscoveryPageGuideBarType.TOPIC -> {\n                \"3\"\n            }\n\n            DiscoveryPageGuideBarType.TOOL -> {\n                \"4\"\n            }\n\n            DiscoveryPageGuideBarType.MALL -> {\n                \"5\"\n            }\n\n            DiscoveryPageGuideBarType.HUYA -> {\n                \"6\"\n            }\n\n            DiscoveryPageGuideBarType.VOTETOPIC -> {\n                \"7\"\n            }\n\n            else -> {\n                \"0\"\n            }\n        }\n        return guideTypeStr\n    }\n\n\n    /**\n     * Toast  曝光\n     */\n    fun reportPopEvent(\n        position: Int,\n        info: DiscoveryPageRecommendItem?,\n        eventCode: Int,\n        vararg extraData: Pair<String, Any?>\n    ) {\n        info ?: return\n        reportRecommendEvent(\n            eventCode = eventCode,\n            element = STConst.ELEMENT_POP,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = getCommonContextExtra(info, ReportContextKey.POP_REPORT_CONTEXT),\n            extraData = extraData\n        )\n    }\n\n    private fun reportRecommendEvent(\n        eventCode: Int,\n        element: String,\n        slotId: String = DefaultConstants.DEFAULT_SLOT,\n        info: DiscoveryPageRecommendItem? = null,\n        reportContextValue: String?,\n        vararg extraData: Pair<String, Any?>\n    ) {\n        reportRecommendEvent(\n            scene = scene,\n            eventCode = eventCode,\n            element = element,\n            slotId = slotId,\n            recommendId = info?.videoInfo?.rid,\n            info = info,\n            reportContextValue = reportContextValue,\n            *extraData\n        )\n    }\n\n\n    private fun reportRecommendEvent(\n        scene: Int,\n        eventCode: Int,\n        element: String,\n        slotId: String = DefaultConstants.DEFAULT_SLOT,\n        recommendId: ByteArray?,\n        info: DiscoveryPageRecommendItem? = null,\n        reportContextValue: String?,\n        vararg extraData: Pair<String, Any?>\n    ) {\n        reportEvent(\n            scene = scene,\n            eventCode = eventCode,\n            slotId = slotId,\n            reportElement = element,\n            subPosition = DEFAULT_SUB_POSITION,\n            appId = 0,\n            recommendId = recommendId,\n            modelType = modelType,\n            ExtendFiledKey.REPORT_CONTEXT to reportContextValue,\n            *getCommonExtra(info),\n            *extraData\n        )\n    }\n\n\n    fun reportAppClickEvent(\n        appState: AppState,\n        position: Int,\n        info: DiscoveryPageRecommendItem,\n    ) {\n\n        val eventCode = when (appState) {\n            AppState.INSTALLED -> {\n                STConstAction.ACTION_HIT_OPEN\n            }\n\n            AppState.PAUSED -> {\n                STConstAction.ACTION_HIT_APP_CONTINUE\n            }\n\n            AppState.DOWNLOADING -> {\n                STConstAction.ACTION_HIT_APP_PAUSE\n            }\n\n            else -> {\n                STConstAction.ACTION_HIT_DOWNLOAD\n            }\n        }\n\n        reportAppEvent(\n            eventCode = eventCode,\n            slotId = getSlot(position),\n            info = info,\n            reportContextValue = info.reportContext?.get(ReportContextKey.APP_REPORT_CONTEXT)\n        )\n    }\n\n    private fun reportAppEvent(\n        eventCode: Int,\n        slotId: String = DefaultConstants.DEFAULT_SLOT,\n        info: DiscoveryPageRecommendItem,\n        reportContextValue: String?,\n        vararg extraData: Pair<String, Any?>\n    ) {\n        reportEvent(\n            scene = scene,\n            eventCode = eventCode,\n            slotId = slotId,\n            reportElement = STConst.ELEMENT_APP,\n            subPosition = DEFAULT_SUB_POSITION,\n            appId = getAppId(info) ?: 0L,\n            recommendId = getRecommendId(info),\n            modelType = modelType,\n            ExtendFiledKey.REPORT_CONTEXT to reportContextValue,\n            *getCommonExtra(info),\n            *extraData\n        )\n    }\n\n\n    private fun getCommonExtra(info: DiscoveryPageRecommendItem?): Array<Pair<String, Any?>> {\n        info ?: return emptyArray()\n        return arrayOf(\n            ExtendFiledKey.SCENE_APP_ID to getAppId(info),\n        )\n    }\n\n    private fun getAppId(info: DiscoveryPageRecommendItem) = info.interactiveInfo?.appid\n\n\n    /**\n     *\n     * https://git.woa.com/MobileAssistBusi/formal_jce/-/merge_requests/182\n     * button_report_context;（右上角分享等按钮）\n     * video_report_context;（推荐内容卡/视频+静音/标签/互动区按钮相关事件）\n     * pop_report_context; （分享面板相关事件）\n     * comment_report_context; （评论相关事件）\n     */\n    fun getCommonContextExtra(info: DiscoveryPageRecommendItem?, contextKey: String): String? {\n        return info?.reportContext?.get(contextKey)\n    }\n\n    private fun getVid(info: DiscoveryPageRecommendItem): String? {\n        val vid = info.videoInfo?.vid\n        if (vid.isNotNullOrEmpty()) {\n            return vid\n        }\n\n        val videoUrl = info.videoInfo?.videoUrl\n        if (videoUrl.isNotNullOrEmpty()) {\n            return if (DiscoverConfig.videoReportUrlEncode) {\n                encodeUrl(videoUrl)\n            } else {\n                videoUrl\n            }\n        }\n\n        return \"\"\n    }\n\n    private fun encodeUrl(url: String): String? = try {\n        URLEncoder.encode(url, StandardCharsets.UTF_8.name())\n    } catch (e: Exception) {\n        XLog.e(TAG, \"scene=$scene, getVid exception 1\", e)\n        try {\n            URLEncoder.encode(url, \"UTF-8\")\n        } catch (e: Exception) {\n            XLog.e(TAG, \"scene=$scene, getVid exception 2\", e)\n            \"\"\n        }\n    }\n\n}"}
{"query": "DiscoverRecommendLikeBtnDecorator.kt", "value": "package com.tencent.pangu.discover.recommend.action\n\nimport android.view.View\nimport android.widget.ImageView\nimport android.widget.TextView\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.component.ToastUtils\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.utils.toFormattedString\nimport com.tencent.pangu.discover.base.model.action.LikeAction\nimport com.tencent.pangu.discover.base.utils.DiscoverUIUtil\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/11 20:59\n * Description:\n */\nclass DiscoverRecommendLikeBtnDecorator(\n    private val btn: View,\n    private val btnIcon: ImageView,\n    private val btnText: TextView,\n    private val onClick: ((view: View, like: Boolean) -> Unit?)? = null\n) :\n    LikeAction() {\n\n    private var itemInfo: DiscoveryPageRecommendItem? = null\n\n    init {\n        btn.setOnClickListener {\n            itemInfo?.let { info ->\n                onClick?.invoke(btn, itemInfo?.interactiveInfo?.isLiked ?: false)\n                toggleLikeAction(info.toLikeInfo())\n            }\n        }\n    }\n\n    override fun onActionStart() {\n        btn.isClickable = false\n        // 先改状态,不等后台结果\n        toggleLikeState()\n    }\n\n    private fun toggleLikeState() {\n        itemInfo?.let { info ->\n            val like = !(info.interactiveInfo?.isLiked ?: false)\n            info.interactiveInfo?.isLiked = like\n            info.interactiveInfo?.likeCount = if (like) {\n                info.interactiveInfo?.likeCount?.plus(1)\n            } else {\n                info.interactiveInfo?.likeCount?.minus(1)\n            }\n            if (like) {\n                DiscoverUIUtil.clickVibrate()\n            }\n            updateView(info)\n        }\n    }\n\n    override fun onActionFailed(errorCode: Int) {\n        ToastUtils.show(btnIcon.context, \"点赞失败\")\n        // 失败后恢复\n        toggleLikeState()\n        btn.isClickable = true\n    }\n\n    override fun onActionSuccess(likeInfo: LikeInfo) {\n        btn.isClickable = true\n    }\n\n    fun bindData(info: DiscoveryPageRecommendItem) {\n        this.itemInfo = info\n        updateView(info)\n    }\n\n    private fun updateView(info: DiscoveryPageRecommendItem) {\n        btnText.text = info.interactiveInfo?.likeCount?.toFormattedString()\n        if (info.interactiveInfo?.isLiked == true) {\n            btnIcon.setImageResource(R.drawable.ic_discover_btn_liked)\n        } else {\n            btnIcon.setImageResource(R.drawable.ic_discover_btn_like)\n        }\n    }\n\n    private fun DiscoveryPageRecommendItem.toLikeInfo(): LikeInfo {\n        return LikeInfo(\n            <EMAIL>,\n            <EMAIL>,\n            <EMAIL>\n        )\n    }\n\n}"}
{"query": "DiscoverSeekBar.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.util.AttributeSet\nimport android.view.LayoutInflater\nimport android.view.ViewGroup\nimport android.widget.FrameLayout\nimport android.widget.SeekBar\nimport android.widget.TextView\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.component.video.view.VideoComponentUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.dp\nimport com.tencent.assistant.utils.fadeIn\nimport com.tencent.assistant.utils.fadeOut\nimport com.tencent.pangu.playlet.detail.widget.PlayletSeekBar\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/9 16:17\n * Description:\n */\nclass DiscoverSeekBar @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : FrameLayout(context, attrs, defStyleAttr) {\n\n    var listener: SeekBar.OnSeekBarChangeListener? = null\n\n    private val seekBarSelectTopPadding = 7.dp\n    private val seekBarTopPadding = 8.dp\n    private val seekBar by lazy { findViewById<PlayletSeekBar>(R.id.progress_bar) }\n    private val progressTextLayout by lazy { findViewById<ViewGroup>(R.id.progress_text_layout) }\n    private val progressTV by lazy { findViewById<TextView>(R.id.video_progress_text) }\n    private val totalProgressTV by lazy { findViewById<TextView>(R.id.video_total_length) }\n\n    var max: Int\n        get() = seekBar.max\n        set(value) {\n            seekBar.max = value\n        }\n\n    var progress: Int\n        get() = seekBar.progress\n        set(value) {\n            seekBar.progress = value\n        }\n\n\n    init {\n        LayoutInflater.from(context).inflate(R.layout.discover_recommend_seekbar, this, true)\n        initView()\n    }\n\n    fun updateProgress(progress: Int, fromUser: Boolean) {\n        seekBar.progress = progress\n        seekBar.updateProgress(progress, fromUser)\n    }\n\n    fun startTrackingTouch() {\n        seekBar.startTrackingTouch()\n    }\n\n    fun stopTrackingTouch() {\n        seekBar.stopTrackingTouch()\n    }\n\n    fun isUseGestureSeeking(): Boolean {\n        return seekBar.isUseGestureSeeking\n    }\n\n    private fun initView() {\n        seekBar.enableTouchSeek = false\n        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {\n            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {\n                val current = VideoComponentUtils.getMinutesSecondTime(progress)\n                val total = VideoComponentUtils.getMinutesSecondTime((seekBar.max))\n                progressTV.text = current\n                totalProgressTV.text = total\n                listener?.onProgressChanged(seekBar, progress, fromUser)\n            }\n\n            override fun onStartTrackingTouch(seekBar: SeekBar) {\n                progressTextLayout.visibility = VISIBLE\n                seekBar.setPadding(0, seekBarSelectTopPadding, 0, seekBar.paddingBottom)\n                listener?.onStartTrackingTouch(seekBar)\n            }\n\n            override fun onStopTrackingTouch(seekBar: SeekBar) {\n                progressTextLayout.post { progressTextLayout.fadeOut() }\n                seekBar.setPadding(0, seekBarTopPadding, 0, seekBar.paddingBottom)\n                listener?.onStopTrackingTouch(seekBar)\n            }\n        })\n    }\n\n\n}\n"}
{"query": "DiscoverRecommendAdapter.kt", "value": "package com.tencent.pangu.discover.recommend\n\n\nimport android.view.ViewGroup\nimport androidx.recyclerview.widget.RecyclerView\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.discover.base.view.BaseVideoViewHolder\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\n\nopen class DiscoverRecommendAdapter(val scene: Int) : RecyclerView.Adapter<BaseVideoViewHolder>() {\n\n    private val TAG = \"DiscoverRecommendAdapter_$scene\"\n\n    private val itemDataList = mutableListOf<DiscoveryPageRecommendItem>()\n\n    var currentBindVH: BaseVideoViewHolder? = null\n        private set\n\n    var currentBindPosition = 0\n        private set\n\n    var currentSelectVH: BaseVideoViewHolder? = null\n        private set\n\n    var currentSelectPosition = 0\n        private set\n\n    var recommendPageListener: AdapterListener? = null\n\n    private var firstItemExposed = false\n\n    val reporter: DiscoverRecommendReporter\n        get() = DiscoverRecommendReporter.getReporter(scene)\n    val techReporter: DiscoverBeaconReport\n        get() = DiscoverBeaconReport.getReporter(scene)\n\n    fun setData(refresh: Boolean, data: ArrayList<DiscoveryPageRecommendItem>): Boolean {\n        XLog.d(TAG, \"setData, refresh:$refresh, data.size:${data.size}\")\n\n\n        if (data.isEmpty()) {\n            XLog.i(TAG, \"data.isEmpty()\")\n            return false\n        }\n\n        if (data == itemDataList) {\n            XLog.i(TAG, \"data == itemDataList\")\n            return false\n        }\n\n        if (itemDataList.containsAll(data)){\n            XLog.i(TAG, \"setData containsAll\")\n            return false\n        }\n\n        if (refresh) {\n            firstItemExposed = false\n            itemDataList.clear()\n            itemDataList.addAll(data)\n            notifyDataSetChanged()\n        } else {\n            val index = itemDataList.size\n            itemDataList.addAll(data)\n            notifyItemRangeInserted(index, data.size)\n        }\n        return true\n    }\n\n    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVideoViewHolder {\n        XLog.d(TAG, \"onCreateViewHolder, viewType:$viewType\")\n        return BaseVideoViewHolder(parent, R.layout.layout_discover_recommend_feed_item, scene)\n    }\n\n    override fun onBindViewHolder(holder: BaseVideoViewHolder, position: Int) {\n        val itemData = getItemData(position) ?: return\n        XLog.d(TAG, \"onBindViewHolder position:$position\")\n        if (!firstItemExposed) {\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_ITEM_BIND_START)\n        }\n        holder.recommendPageListener = recommendPageListener\n        holder.bind(itemData, position)\n        currentBindVH = holder\n        currentBindPosition = position\n        if (!firstItemExposed) {\n            recommendPageListener?.onFirstItemExposed(position, holder)\n            firstItemExposed = true\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_ITEM_BIND_END)\n        }\n    }\n\n    fun getItemData(position: Int) = itemDataList.getOrNull(position)\n\n    override fun onViewRecycled(holder: BaseVideoViewHolder) {\n        super.onViewRecycled(holder)\n        holder.onRecycled()\n    }\n\n    override fun onViewAttachedToWindow(holder: BaseVideoViewHolder) {\n        super.onViewAttachedToWindow(holder)\n        holder.onViewAttachedToWindow()\n    }\n\n    override fun onViewDetachedFromWindow(holder: BaseVideoViewHolder) {\n        super.onViewDetachedFromWindow(holder)\n        holder.onViewDetachedFromWindow()\n    }\n\n    override fun getItemCount(): Int {\n        return itemDataList.size\n    }\n\n    fun getCurrentItem(): DiscoveryPageRecommendItem? {\n        return getItemData(currentSelectPosition)\n    }\n\n    fun onItemSelected(vh: BaseVideoViewHolder, position: Int) {\n        if (currentSelectVH != vh) {\n            currentSelectVH?.onItemUnSelected()\n        }\n        currentSelectVH = vh\n        currentSelectPosition = position\n        vh.onItemSelected()\n    }\n\n    inline fun DiscoverRecommendAdapter.addListener(\n        crossinline onFirstItemExposed: (itemPosition: Int, vh: BaseVideoViewHolder) -> Unit = { _, _ -> },\n        crossinline onVideoFirstFrame: (itemPosition: Int, vh: BaseVideoViewHolder) -> Unit = { _, _ -> },\n        crossinline onMuteBtnClick: (mute: Boolean) -> Unit = {},\n        crossinline onVideoClick: () -> Unit = {},\n        crossinline onShareClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },\n        crossinline onLikeClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },\n        crossinline onCommentClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },\n        crossinline onPlayComplete: (itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean) -> Unit = { _, _, _ -> },\n        crossinline isFragmentShow: () -> Boolean = { true },\n        crossinline isLoadingData: () -> Boolean = { false }\n\n    ): AdapterListener {\n        val listener = object : AdapterListener {\n            override fun onFirstItemExposed(itemPosition: Int, vh: BaseVideoViewHolder) = onFirstItemExposed(itemPosition, vh)\n            override fun onVideoFirstFrame(itemPosition: Int, vh: BaseVideoViewHolder) = onVideoFirstFrame(itemPosition, vh)\n            override fun onMuteBtnClick(mute: Boolean) = onMuteBtnClick(mute)\n            override fun onVideoClick() = onVideoClick()\n            override fun onShareClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =\n                onShareClick(itemInfo, itemPosition)\n\n            override fun onLikeClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =\n                onLikeClick(itemInfo, itemPosition)\n\n            override fun onCommentClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =\n                onCommentClick(itemInfo, itemPosition)\n\n            override fun onPlayComplete(itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean) {\n                onPlayComplete(itemPosition, vh, isFullScreen)\n            }\n            override fun isFragmentShow() = isFragmentShow()\n\n            override fun isLoadingData() = isLoadingData()\n\n        }\n        this.recommendPageListener = listener\n        return listener\n    }\n\n    fun toggleVideoFullScreen(fullScreen: Boolean) {\n        currentSelectVH?.toggleVideoFullScreen(fullScreen)\n    }\n\n\n    interface AdapterListener {\n        fun onFirstItemExposed(itemPosition: Int, vh: BaseVideoViewHolder)\n\n        fun onVideoFirstFrame(itemPosition: Int, vh: BaseVideoViewHolder)\n\n        fun onMuteBtnClick(mute: Boolean)\n\n        fun onVideoClick()\n\n        fun onShareClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)\n\n        fun onLikeClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)\n\n        fun onCommentClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)\n\n        fun onPlayComplete(itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean)\n\n        fun isFragmentShow(): Boolean\n\n        fun isLoadingData(): Boolean\n    }\n\n}"}
{"query": "DiscoverGuideBar.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.util.AttributeSet\nimport android.view.LayoutInflater\nimport android.view.View\nimport android.widget.FrameLayout\nimport androidx.constraintlayout.widget.ConstraintLayout\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.AppConst\nimport com.tencent.assistant.module.AppRelatedDataProcesser\nimport com.tencent.assistant.protocol.jce.DiscoveryPageGuideBarType\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendGuideBarInfo\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.utils.HandlerUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.fadeIn\nimport com.tencent.assistant.utils.fadeOut\nimport com.tencent.assistant.utils.setOnFilterClickListener\nimport com.tencent.assistant.utils.toGsonString\nimport com.tencent.pangu.discover.recommend.manager.DiscoverGuideBarManager\nimport com.tencent.pangu.discover.recommend.manager.getBarLogInfo\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.discover.recommend.wdiget.guidebar.IGuideBar\nimport com.tencent.pangu.discover.recommend.wdiget.guidebar.NormalGuideBar\nimport com.tencent.pangu.discover.recommend.wdiget.guidebar.TopicGuideBar\nimport com.tencent.pangu.discover.recommend.wdiget.guidebar.VoteGuideBar\nimport com.tencent.pangu.middlepage.model.covertSimpleAppModelForMiddlePage\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/5 16:36\n * Description:\n */\nclass DiscoverGuideBar @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : ConstraintLayout(context, attrs, defStyleAttr) {\n\n    var scene: Int = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    val reporter: DiscoverRecommendReporter\n        get() = DiscoverRecommendReporter.getReporter(scene)\n    var listener: GuideBarListener? = null\n    var itemData: DiscoveryPageRecommendItem? = null\n    private var guideBarInfo: DiscoveryPageRecommendGuideBarInfo? = null\n    private var position: Int = 0\n\n    private val closeBtn by lazy { findViewById<View>(R.id.close_btn) }\n    private val container by lazy { findViewById<FrameLayout>(R.id.guide_bar_container) }\n    private var guideBar: IGuideBar ?= null\n\n    private val TAG = \"DiscoverGuideBar\"\n\n    init {\n        LayoutInflater.from(context).inflate(R.layout.discover_recommend_guide_bar, this, true)\n        initView()\n    }\n\n    private fun initView() {\n        closeBtn.setOnFilterClickListener {\n            <EMAIL>()\n            guideBarInfo?.let {\n                DiscoverGuideBarManager.onGuideBarClosed(position, it)\n                DiscoverRecommendReporter.getReporter(scene).reportGuideBtnClick(\n                    position = position,\n                    info = itemData,\n                    guideBarInfo = it,\n                    isUpdate = isAppUpdate(it),\n                    btnTitle = DiscoverRecommendReporter.ButtonTitle.GUIDE_CLOSE\n                )\n            }\n            guideBarInfo = null\n        }\n    }\n\n    private fun isAppUpdate(info: DiscoveryPageRecommendGuideBarInfo): Boolean {\n        if (info.barType != DiscoveryPageGuideBarType._DOWNLOAD) {\n            return false\n        }\n        val appModel = info.appDetail.covertSimpleAppModelForMiddlePage()\n        return AppRelatedDataProcesser.getAppState(appModel) == AppConst.AppState.UPDATE\n    }\n\n    private val runnable = Runnable {\n        XLog.i(TAG, \"runnable: ${getBarLogInfo(guideBarInfo, position)}\")\n        if (guideBarInfo == null || listener?.canShowGuideNow() == false) {\n            XLog.i(TAG, \"runnable: ${getBarLogInfo(guideBarInfo, position)}, ${listener?.canShowGuideNow()}\")\n            return@Runnable\n        }\n\n        show(position, guideBarInfo!!)\n    }\n\n    private fun getBarLogInfo(guideBarInfo: DiscoveryPageRecommendGuideBarInfo?, position: Int) =\n        \"${guideBarInfo?.getBarLogInfo()}, position: $position\"\n\n    fun bindData(position: Int, itemData: DiscoveryPageRecommendItem) {\n        this.position = position\n        this.itemData = itemData\n        this.guideBar = getGuideBar(itemData)?.also {\n            it.setData(position, itemData, scene)\n            it.initView()\n            container.removeAllViews()\n            container.addView(it.getView())\n        }\n        XLog.d(TAG, \"bindData, position: $position, guideBarInfo: ${itemData.guideBarInfo?.toGsonString()}\")\n        val barStr = itemData.guideBarInfo?.mapIndexed { index, info ->\n            \"index: $index, ${getBarLogInfo(info, position)}; \"\n        }?.toGsonString()\n        XLog.i(TAG, \"bindData, barStr: $barStr\")\n    }\n\n    fun showGuideIfCan() {\n        guideBarInfo?.let {\n            show(position, it)\n        }\n    }\n\n    private fun getGuideBar(itemData: DiscoveryPageRecommendItem): IGuideBar? {\n        guideBarInfo = DiscoverGuideBarManager.getGuideBarInfo(position, itemData)\n        if (guideBarInfo == null) {\n            XLog.i(TAG, \"getGuideBar is null.\")\n            return null\n        }\n        return when(guideBarInfo!!.barType) {\n            DiscoveryPageGuideBarType._DOWNLOAD,\n            DiscoveryPageGuideBarType._MALL,\n            DiscoveryPageGuideBarType._TOOL,\n            DiscoveryPageGuideBarType._HUYA -> NormalGuideBar(context)\n            DiscoveryPageGuideBarType._TOPIC -> TopicGuideBar(context)\n            DiscoveryPageGuideBarType._VOTETOPIC -> VoteGuideBar(context)\n            else -> null\n        }\n    }\n\n    private fun tryShowGuideBar() {\n        removeRunnable()\n        guideBarInfo = DiscoverGuideBarManager.getShowedGuideBarInfo(position)\n        if (guideBarInfo != null) {\n            XLog.i(TAG, \"tryShowGuideBar getShowedGuideBarInfo not null show now: ${guideBarInfo?.toGsonString()}\")\n            show(position, guideBarInfo!!, anim = false)\n            return\n        }\n\n        guideBarInfo = DiscoverGuideBarManager.getGuideBarInfo(position, itemData)\n        if (guideBarInfo == null) {\n            XLog.i(TAG, \"tryShowGuideBar getGuideBarInfo null, return\")\n            visibility = GONE\n            return\n        }\n\n        val delayMillis = DiscoverGuideBarManager.getShowDelayMillis(guideBarInfo!!.barType)\n        if (delayMillis > 0) {\n            visibility = GONE\n            XLog.i(TAG, \"tryShowGuideBar getGuideBarInfo not null show delay: ${guideBarInfo?.toGsonString()}\")\n            HandlerUtils.getMainHandler().postDelayed(runnable, delayMillis.toLong())\n        } else {\n            XLog.i(TAG, \"tryShowGuideBar getGuideBarInfo not null show now: ${guideBarInfo?.toGsonString()}\")\n            show(position, guideBarInfo!!, anim = false)\n        }\n    }\n\n\n    private fun show(position: Int, guideBarInfo: DiscoveryPageRecommendGuideBarInfo, anim: Boolean = true) {\n        XLog.i(TAG, \"show guide bar: ${getBarLogInfo(guideBarInfo, position)}\")\n        if (visibility == VISIBLE) {\n            XLog.i(TAG, \"show guide, already show\")\n            return\n        }\n\n        if (anim) {\n            fadeIn()\n        } else {\n            visibility = VISIBLE\n        }\n\n        guideBar?.show()\n        DiscoverGuideBarManager.onGuideBarShowed(position, guideBarInfo)\n        reporter.reportGuideBtnExposure(\n            position = position,\n            info = itemData,\n            guideBarInfo = guideBarInfo,\n            isUpdate = isAppUpdate(guideBarInfo),\n            btnTitle = DiscoverRecommendReporter.ButtonTitle.GUIDE_CLOSE\n        )\n        XLog.i(TAG, \"show guide bar 222: ${getBarLogInfo(guideBarInfo, position)}\")\n    }\n\n    override fun onDetachedFromWindow() {\n        super.onDetachedFromWindow()\n        XLog.i(TAG, \"onDetachedFromWindow, position: $position\")\n        removeRunnable()\n    }\n\n    fun onRecycled() {\n        XLog.i(TAG, \"onRecycled,  position: $position\")\n        removeRunnable()\n        visibility = GONE\n    }\n\n    override fun setVisibility(visibility: Int) {\n        XLog.i(TAG, \"setVisibility: $visibility, position: $position\")\n        super.setVisibility(visibility)\n    }\n\n    fun onSelected() {\n        XLog.i(TAG, \"onSelected, position: $position, visibility: ${visibility == VISIBLE}\")\n        if (visibility == VISIBLE) {\n            guideBarInfo?.let {\n                DiscoverGuideBarManager.onGuideBarShowed(position, it)\n            }\n        } else {\n            tryShowGuideBar()\n        }\n    }\n\n    fun onUnSelect() {\n        removeRunnable()\n    }\n\n    private fun removeRunnable() {\n        HandlerUtils.getMainHandler().removeCallbacks(runnable)\n    }\n\n    interface GuideBarListener {\n        fun canShowGuideNow(): Boolean\n        fun onGuideClosed()\n    }\n}"}
{"query": "DiscoverModelExt.kt", "value": "package com.tencent.pangu.discover.recommend.model\n\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendInteractiveInfo\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendIntroductionInfo\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendVideoInfo\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.discover.DiscoverConstant\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/17 10:37\n * Description:\n */\n\nprivate const val TAG = \"DiscoverModelExt\"\n\n\nfun DiscoveryPageRecommendItem.getSafeVideoRatio(): Float {\n    var ratio = videoInfo?.aspectRatio ?: 0F\n    if (ratio <= 0F || ratio > 3F) {\n        XLog.e(\n            TAG,\n            \"getSafeVideoRatio: vid=${videoInfo?.vid}, name=${introductionInfo.authorName},aspectRatio illegal ratio=$ratio\"\n        )\n        ratio = DiscoverConstant.VIDEO_DEFAULT_RATIO\n    }\n    return ratio\n}\n\n\nfun DiscoveryPageRecommendItem.isLandVideo(): Boolean {\n    return isLandVideo(getSafeVideoRatio())\n}\n\nfun isLandVideo(ratio: Float): Boolean {\n    return ratio >= DiscoverConfig.landScapeVideoRatio\n}\n\nfun DiscoveryPageRecommendItem.getLogString(): String {\n    return \"videoInfo:[${videoInfo?.getLogString()}], introductionInfo:[${introductionInfo?.getLogString()}],\" +\n            \"interactiveInfo:[${interactiveInfo?.getLogString()}]\"\n}\n\nfun DiscoveryPageRecommendVideoInfo.getLogString(): String {\n    return \"vid:$vid, videoUrl:$videoUrl, coverImg:$coverImg, aspectRatio:$aspectRatio, duration:$duration\"\n}\n\nfun DiscoveryPageRecommendIntroductionInfo.getLogString(): String {\n    return \"authorName:$authorName, title:$title, tagInfo:${tagInfo}\"\n}\n\nfun DiscoveryPageRecommendInteractiveInfo.getLogString(): String {\n    return \"appName:$appName, appIcon:$appIcon, appid:$appid, isLiked:$isLiked, likeCount:$likeCount, \" +\n            \"commentCount:$commentCount, isBookGame:$isBookGame, isBooked:$isBooked\"\n}\n\n\n\n\n"}
{"query": "DiscoverRecommendViewModel.kt", "value": "package com.tencent.pangu.discover.recommend.vm\n\nimport androidx.lifecycle.MutableLiveData\nimport androidx.lifecycle.ViewModel\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendResponse\nimport com.tencent.assistant.report.ResponseState\nimport com.tencent.assistant.request.RequestResult\nimport com.tencent.assistant.request.RequestType\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.default\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendDataCacheManager\nimport com.tencent.pangu.discover.recommend.model.DiscoverRecommendRepository\nimport com.tencent.pangu.discover.recommend.model.DiscoverRecommendRequestParam\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport kotlinx.coroutines.CoroutineScope\nimport kotlinx.coroutines.Dispatchers\nimport kotlinx.coroutines.Job\nimport kotlinx.coroutines.launch\nimport kotlinx.coroutines.withContext\n\nclass DiscoverRecommendViewModel : ViewModel() {\n    private val TAG by lazy { \"DiscoverRecommendAdapter_$scene\" }\n    private val scope = CoroutineScope(Dispatchers.IO + Job())\n    private val repository by lazy { DiscoverRecommendRepository(scene) }\n\n    var isVideoFullScreen: (() -> Boolean)? = null\n    var reporter: DiscoverRecommendReporter? = null\n    var techReporter: DiscoverBeaconReport? = null\n    var scene: Int = 0\n\n    val firstLoadingLiveData = MutableLiveData<Boolean>().default(true)\n    val errorPageLiveData = MutableLiveData<Boolean>().default(false)\n    val recommendLiveData = MutableLiveData<RequestResult<DiscoveryPageRecommendResponse>>()\n    val loadMoreLiveData = MutableLiveData<LoadMoreStatus>().default(LoadMoreStatus())\n    val refreshFinishLiveData = MutableLiveData<Boolean>().default(false)\n    val loadingDataFinishCallback = MutableLiveData<Pair<Boolean, Boolean>>().default(Pair(false, false))\n\n    fun requestRecommend(\n        requestParam: DiscoverRecommendRequestParam\n    ) {\n        val requestType = requestParam.requestType\n        val refresh = requestParam.isRefresh\n        XLog.i(TAG, \"requestRecommend requestParam = $requestParam\")\n        if (requestType == RequestType.FULL_REQUEST) {\n            errorPageLiveData.postValue(false)\n            if (!refresh) {\n                firstLoadingLiveData.postValue(true)\n            }\n        }\n\n\n        scope.launch {\n            reportRequest(requestParam)\n            XLog.i(TAG, \"requestRecommend launch requestParam=$requestParam\")\n            val result = repository.requestRecommendList(requestParam)\n            if (requestParam.isFirstRequest) {\n                techReporter?.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_LOAD_DATA_END)\n            }\n            XLog.i(TAG, \"requestRecommend result = $result\")\n            if (requestType == RequestType.FULL_REQUEST) {\n                firstLoadingLiveData.postValue(false)\n                if (refresh) {\n                    refreshFinishLiveData.postValue(true)\n                }\n            }\n\n            when (result) {\n                is RequestResult.Failed<DiscoveryPageRecommendResponse> -> {\n                    handleRequestFailed(requestType, requestParam, result)\n                }\n\n                is RequestResult.Success<DiscoveryPageRecommendResponse> -> {\n                    handleRequestSuccess(requestType, result, requestParam)\n                }\n            }\n        }\n    }\n\n    private suspend fun handleRequestSuccess(\n        requestType: RequestType,\n        result: RequestResult<DiscoveryPageRecommendResponse>,\n        requestParam: DiscoverRecommendRequestParam\n    ) {\n        XLog.i(\n            TAG, \"handleRequestSuccess: ${(result.data as DiscoveryPageRecommendResponse).items?.size}\"\n        )\n        withContext(Dispatchers.Main) {\n            if (requestType == RequestType.FULL_REQUEST) {\n                errorPageLiveData.postValue(false)\n                firstLoadingLiveData.postValue(false)\n            }\n            loadMoreLiveData.postValue(\n                LoadMoreStatus(\n                    hasMoreData = result.hasMoreData, isLoadMore = requestParam.isLoadMore, loadSuccess = true\n                )\n            )\n\n            //视频横屏时不做数据更新,  解决 crash: https://tapd.woa.com/tapd_fe/20422314/bug/detail/1020422314133457665\n            if (isVideoFullScreen?.invoke() == true && DiscoverConfig.ignoreDataUpdateWhenVideoFullScreen) {\n                XLog.e(TAG, \"requestRecommend success return, isVideoFullScreen\")\n                repository.restoreRecommendRequestContext()\n                loadingDataFinishCallback.postValue(Pair(true, true))\n                return@withContext\n            }\n\n            reportResponse(ResponseState.Success, 0, requestType == RequestType.LOAD_MORE)\n\n            recommendLiveData.postValue(result)\n            loadingDataFinishCallback.postValue(Pair(true, true))\n        }\n    }\n\n    private suspend fun handleRequestFailed(\n        requestType: RequestType,\n        requestParam: DiscoverRecommendRequestParam,\n        result: RequestResult<DiscoveryPageRecommendResponse>\n    ) {\n        val needLoadCacheFromDisk =\n            requestType == RequestType.FULL_REQUEST && DiscoverConfig.useDiskCache && requestParam.scene == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n        var response: DiscoveryPageRecommendResponse? = null\n        if (needLoadCacheFromDisk) {\n            response = DiscoverRecommendDataCacheManager.getDiskCacheResponse()\n        }\n        withContext(Dispatchers.Main) {\n            val isLoadMore = requestType == RequestType.LOAD_MORE\n            loadMoreLiveData.postValue(\n                LoadMoreStatus(hasMoreData = isLoadMore, isLoadMore = isLoadMore, loadSuccess = false)\n            )\n            if (isVideoFullScreen?.invoke() == true && DiscoverConfig.ignoreDataUpdateWhenVideoFullScreen) {\n                XLog.e(TAG, \"requestRecommend fail return, isVideoFullScreen\")\n                repository.restoreRecommendRequestContext()\n                loadingDataFinishCallback.postValue(Pair(true, false))\n                return@withContext\n            }\n\n            reportResponse(ResponseState.Failed, result.errCode, requestType == RequestType.LOAD_MORE)\n\n            if (needLoadCacheFromDisk) {\n                //load data from cache\n                loadDataFromDiskCache(response, result)\n            }\n            loadingDataFinishCallback.postValue(Pair(true, false))\n        }\n    }\n\n    /**\n     * 从磁盘缓存中加载数据\n     * @param result 预加载结果\n     */\n    private fun loadDataFromDiskCache(\n        response: DiscoveryPageRecommendResponse?, result: RequestResult<DiscoveryPageRecommendResponse>\n    ) {\n        if (response == null) {\n            errorPageLiveData.postValue(true)\n            recommendLiveData.postValue(result)\n        } else {\n            errorPageLiveData.postValue(false)\n            recommendLiveData.postValue(\n                RequestResult.Success(\n                    response, RequestType.FULL_REQUEST, true\n                )\n            )\n            loadMoreLiveData.postValue(\n                LoadMoreStatus(hasMoreData = true, isLoadMore = false, loadSuccess = false)\n            )\n        }\n    }\n\n    private fun reportResponse(success: ResponseState, errorCode: Int, isLoadMore: Boolean) {\n        reporter?.reportResponse(success, errorCode, isLoadMore)\n    }\n\n    private fun reportRequest(requestParam: DiscoverRecommendRequestParam) {\n        val reportRequestType = reporter?.getRequestType(\n            requestParam.requestType, requestParam.isRefresh, requestParam.isRetry\n        ) ?: return\n        reporter?.reportRequest(reportRequestType, false, requestParam.isLoadMore)\n    }\n\n\n    data class LoadMoreStatus(\n        val hasMoreData: Boolean = true, val isLoadMore: Boolean = false, val loadSuccess: Boolean = true\n    )\n}"}
{"query": "DiscoverRecommendFragment.kt", "value": "package com.tencent.pangu.discover.recommend\n\nimport android.animation.AnimatorSet\nimport android.animation.ValueAnimator\nimport android.content.Context\nimport android.content.res.Configuration\nimport android.os.Bundle\nimport android.provider.Settings\nimport android.view.LayoutInflater\nimport android.view.OrientationEventListener\nimport android.view.OrientationEventListener.ORIENTATION_UNKNOWN\nimport android.view.View\nimport android.view.ViewGroup\nimport android.view.animation.DecelerateInterpolator\nimport androidx.lifecycle.LifecycleOwner\nimport androidx.lifecycle.ViewModelProvider\nimport androidx.recyclerview.widget.LinearLayoutManager\nimport androidx.recyclerview.widget.PagerSnapHelper\nimport androidx.recyclerview.widget.RecyclerView\nimport com.qq.AppService.ApplicationProxy\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.Global\nimport com.tencent.assistant.activity.BaseActivity\nimport com.tencent.assistant.activity.BaseFragment\nimport com.tencent.assistant.component.LoadingView\nimport com.tencent.assistant.component.txscrollview.ScrollIdleEventInfo\nimport com.tencent.assistant.component.txscrollview.ScrolledDirection\nimport com.tencent.assistant.component.video.VideoViewManager\nimport com.tencent.assistant.event.EventDispatcherEnum\nimport com.tencent.assistant.net.NetworkUtil\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendRequest\nimport com.tencent.assistant.request.RequestType\nimport com.tencent.assistant.settings.api.settingBoolean\nimport com.tencent.assistant.st.STConst\nimport com.tencent.assistant.utils.HandlerUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.dp\nimport com.tencent.assistant.utils.postToUiThreadDelayed\nimport com.tencent.pangu.activity.ShareBaseActivity\nimport com.tencent.pangu.discover.base.IDiscoverChildFragment\nimport com.tencent.pangu.discover.base.manager.DiscoverPageJumpManager\nimport com.tencent.pangu.discover.base.utils.DiscoverUIUtil\nimport com.tencent.pangu.discover.base.view.BaseVideoViewHolder\nimport com.tencent.pangu.discover.comment.common.ICommentEventCallback\nimport com.tencent.pangu.discover.comment.fragment.CommentDialogFragment\nimport com.tencent.pangu.discover.comment.model.CommentFragmentConfig\nimport com.tencent.pangu.discover.comment.model.CommentReportParam\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\nimport com.tencent.pangu.discover.recommend.manager.DiscoverGuideBarManager\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendPreRenderVideo\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendVideoPreloadManager\nimport com.tencent.pangu.discover.recommend.manager.DiscoverVideoComponentCacheManager\nimport com.tencent.pangu.discover.recommend.model.DiscoverRecommendRequestParam\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.discover.recommend.view.PreRenderLinearLayoutManager\nimport com.tencent.pangu.discover.recommend.vm.DiscoverRecommendViewModel\nimport com.tencent.pangu.discover.recommend.wdiget.DiscoverRecommendViewHolder\nimport com.tencent.pangu.middlepage.view.FixedRecyclerView\nimport com.tencent.pangu.middlepage.view.MiddlePageErrorPage\nimport com.tencent.ptrlayout.SmartRefreshLayout\nimport com.tencent.ptrlayout.footer.YYBFooter\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/6/28 16:02\n * Description:\n */\nopen class DiscoverRecommendFragment : BaseFragment(), IDiscoverChildFragment {\n\n    private var root: View? = null\n    private val TAG = \"DiscoverRecommendFragment_${getScene()}\"\n\n    companion object {\n        var firstVideoMute by settingBoolean(\"mute_first_video\", true)\n        const val IS_DISCOVER_FIRST_SHOW_FRAGMENT = \"is_discover_first_show_fragment\"\n        const val IS_DISCOVER_USER_SCROLLED = \"is_discover_user_scrolled\"\n        const val SCROLL_DURATION = 300L\n        const val DELAY_DURATION = 1000L\n    }\n\n    private var userScrolled by settingBoolean(IS_DISCOVER_USER_SCROLLED, false)\n    private val reporter by lazy {\n        DiscoverRecommendReporter.getReporter(getScene()).apply {\n            (activity as? BaseActivity)?.stPageInfo?.let {\n                init(it)\n            }\n        }\n    }\n\n    private val techReporter by lazy {\n        DiscoverBeaconReport.getReporter(getScene())\n    }\n\n    open fun getScene(): Int {\n        return DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    }\n\n    open val enableRefresh = true\n\n    var onCommentDialogHeightChangeListener: ((Int, Int) -> Unit?)? = null\n    var firstFrameCallback: (() -> Unit)? = null\n\n    private lateinit var refreshLayout: SmartRefreshLayout\n    private lateinit var recyclerView: FixedRecyclerView\n    private lateinit var loadingView: LoadingView\n    private lateinit var errorView: MiddlePageErrorPage\n    private lateinit var topShadow: View\n    private var viewCreated: Boolean = false\n    private val exposedItemPosition = mutableMapOf<Int, Boolean>()\n    private val scrolledDirection = ScrolledDirection()\n    private var currentItem: BaseVideoViewHolder? = null\n    private val viewModel by lazy {\n        ViewModelProvider(this)[DiscoverRecommendViewModel::class.java].apply {\n            reporter = <EMAIL>\n            techReporter = <EMAIL>\n            scene = getScene()\n            this.isVideoFullScreen = {\n                adapter.currentSelectVH?.isVideoFullScreen() ?: false\n            }\n        }\n    }\n\n    private val pagerSnapHelper by lazy {\n        object : PagerSnapHelper() {\n            override fun findTargetSnapPosition(\n                layoutManager: RecyclerView.LayoutManager,\n                velocityX: Int,\n                velocityY: Int\n            ): Int {\n                val newPosition = super.findTargetSnapPosition(layoutManager, velocityX, velocityY)\n                calculateItemChange(newPosition, layoutManager)\n                return newPosition\n            }\n        }\n    }\n\n    private var isFirstVideoFirstFrameCalled = false\n\n    private var isDiscoverFirstShowFragment = true\n\n    private val optimizePreRender = DiscoverConfig.optimizePreRender\n\n    private var isLoadingData = false\n\n    override fun setArguments(bundle: Bundle?) {\n        super.setArguments(bundle)\n        isDiscoverFirstShowFragment = bundle?.getBoolean(\n            IS_DISCOVER_FIRST_SHOW_FRAGMENT,\n            isDiscoverFirstShowFragment\n        ) ?: isDiscoverFirstShowFragment\n\n    }\n\n\n    protected val adapter by lazy {\n        object : DiscoverRecommendAdapter(getScene()) {\n            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVideoViewHolder {\n                return <EMAIL>(parent, viewType, getScene())\n            }\n        }.apply {\n            addListener(\n                onFirstItemExposed = { position, vh ->\n                    XLog.d(\n                        TAG,\n                        \"firstItemExposedCallback position=$position, time:${System.currentTimeMillis() - time}\"\n                    )\n                    currentItem = vh\n                    <EMAIL>(vh, position)\n                    reporter.pageRenderFinish(0, true)\n                    isFirstVideoFirstFrameCalled = false\n                    firstFrameCallback?.invoke()\n                },\n                onVideoFirstFrame = { position, vh ->\n                    XLog.i(\n                        TAG,\n                        \"onVideoFirstFrame, position=$position\"\n                    )\n                    if (position == 0) {\n                        tryPreBindNextItem()\n                    }\n                },\n                onMuteBtnClick = { mute ->\n                },\n                onVideoClick = {\n                    XLog.i(TAG, \"onVideoClick\")\n                },\n                onLikeClick = { data, itemPosition ->\n                },\n                onCommentClick = { data, itemPosition ->\n                    val reportContext = reporter.getCommonContextExtra(\n                        data,\n                        DiscoverRecommendReporter.ReportContextKey.COMMENT_REPORT_CONTEXT\n                    ) ?: \"\"\n                    val config = CommentFragmentConfig(\n                        contentId = data.videoInfo.materialId.toString(),\n                        contentType = data.commentContentType,\n                        reportParam = CommentReportParam(\n                            scene = reporter.scene,\n                            sourceScene = reporter.sourceScene,\n                            sourceSlotId = reporter.sourceSlot,\n                            slotId = \"99_${itemPosition + 1}\",\n                            appId = data.interactiveInfo.appid,\n                            recommendId = Global.encodeRecommendIdToString(data.videoInfo.rid),\n                            extraMap = mutableMapOf(\n                                STConst.UNI_REPORT_CONTEXT to reportContext,\n                                STConst.SCENE_APPID to data.interactiveInfo.appid.toString(),\n                            )\n\n                        )\n                    )\n                    showCommentDialog(config)\n                },\n                onShareClick = { data, itemPosition ->\n                },\n                onPlayComplete = { position, vh, isFullScreen ->\n                    XLog.i(TAG, \"onPlayComplete, isFullScreen=$isFullScreen\")\n                    if (isFullScreen) {\n                        return@addListener\n                    }\n\n                    if (commentDialogFragment != null) {\n                        XLog.i(TAG, \"onPlayComplete, commentDialogFragment isShowing\")\n                        return@addListener\n                    }\n\n                    if (vh.isShareDialogShowing()) {\n                        XLog.i(TAG, \"onPlayComplete, shareDialog isShowing\")\n                        return@addListener\n                    }\n\n                    if (position >= ((recyclerView.adapter?.itemCount ?: 0) - 1)) {\n                        XLog.i(TAG, \"onPlayComplete, position >= ((recyclerView.adapter?.itemCount ?: 0) - 1)\")\n                        return@addListener\n                    }\n\n                    if (scrollState != RecyclerView.SCROLL_STATE_IDLE) {\n                        XLog.i(TAG, \"onPlayComplete, scrollState!= RecyclerView.SCROLL_STATE_IDLE\")\n                        return@addListener\n                    }\n\n                    recyclerView.smoothScrollToPosition(position + 1)\n\n                },\n                isFragmentShow = {\n                    val resumed = <EMAIL>\n                    XLog.i(TAG, \"isFragmentShowing, resumed=$resumed\")\n                    resumed\n                },\n                isLoadingData = {\n                    XLog.i(TAG, \"DiscoverRecommendAdapter: isLoadingData = $isLoadingData\")\n                    isLoadingData\n                }\n            )\n        }\n    }\n\n    private fun tryPreBindNextItem() {\n        XLog.i(TAG, \"tryPreBindNextItem, isFirstVideoFirstFrameCalled=$isFirstVideoFirstFrameCalled\")\n        if (isFirstVideoFirstFrameCalled) {\n            return\n        }\n        isFirstVideoFirstFrameCalled = true\n        preloadVideo(position)\n        // 首个视频首帧渲染完成后, 往下移动 1px 预渲染下一个视频, 当用户上下刷时, RecyclerView 的 PrefetchItem 会预渲染下一个\n        if (optimizePreRender) {\n            recyclerView.scrollBy(0, 1)\n            recyclerView.post { recyclerView.scrollBy(0, -1) }\n        }\n    }\n\n    open fun onCreateViewHolder(parent: ViewGroup, viewType: Int, scene: Int): BaseVideoViewHolder {\n        return DiscoverRecommendViewHolder(parent, scene)\n    }\n\n    private var commentDialogFragment: CommentDialogFragment? = null\n\n\n    private var lastOrientation: Int = ORIENTATION_UNKNOWN\n    private var position: Int = 0\n\n    private val orientationEventListener by lazy {\n        object : OrientationEventListener(context) {\n            override fun onOrientationChanged(orientation: Int) {\n                // 请求数据时刷新前不旋转屏幕\n                if (isLoadingData) {\n                    XLog.e(TAG, \"onOrientationChanged: isLoadingData skip,  orientation = $orientation\")\n                    return\n                }\n\n                val isLandscape = orientation in 60..120 || orientation in 240..300\n                val isPortrait = orientation in 0..30 || orientation in 330..360\n                if (isLandscape && lastOrientation != Configuration.ORIENTATION_LANDSCAPE) {\n                    XLog.i(TAG, \"Landscape mode\")\n                    lastOrientation = Configuration.ORIENTATION_LANDSCAPE\n                    adapter.toggleVideoFullScreen(true)\n                } else if (isPortrait && lastOrientation != Configuration.ORIENTATION_PORTRAIT) {\n                    XLog.i(TAG, \"Portrait mode\")\n                    lastOrientation = Configuration.ORIENTATION_PORTRAIT\n                    adapter.toggleVideoFullScreen(false)\n                }\n            }\n        }\n    }\n\n    private var time = System.currentTimeMillis()\n    private var exposed = false\n    private var fistDataLoaded = false\n    private var scrollState = RecyclerView.SCROLL_STATE_IDLE\n    private var doFirstItemSelectedOnResume = false\n    private val scrollY = 102.dp\n    private var showGuideRunnable: Runnable? = null\n    private var isFirstShowDiscover = true\n\n\n    override fun onAttach(context: Context) {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_ATTACH)\n        super.onAttach(context)\n        XLog.d(TAG, \"onAttach, time:${System.currentTimeMillis() - time}\")\n        time = System.currentTimeMillis()\n    }\n\n    override fun onCreate(savedInstanceState: Bundle?) {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_CREATE)\n        super.onCreate(savedInstanceState)\n        (activity as? BaseActivity)?.stPageInfo?.let {\n            reporter.init(it)\n        }\n        reporter.reportPageCreate()\n        XLog.d(TAG, \"onCreate, time:${System.currentTimeMillis() - time}\")\n    }\n\n\n    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_CREATE_VIEW)\n        if (root == null) {\n            root = inflater.inflate(R.layout.discover_recommend_fragment, container, false)\n        } else {\n            // 首页fragment创建后下次进入会使用缓存页面，这里如果发现使用了缓存页面并且已经渲染完成，则手动上报一次页面渲染完成\n            reporter.pageRenderFinish(1, true)\n        }\n        return root\n    }\n\n    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_VIEW_CREATED)\n        super.onViewCreated(view, savedInstanceState)\n        XLog.i(TAG, \"onViewCreated, viewCreated: $viewCreated, time:${System.currentTimeMillis() - time}\")\n        if (!viewCreated) {\n            initView(view)\n            firstLoadData()\n        }\n        initObserver(viewLifecycleOwner)\n        viewCreated = true\n    }\n\n    private fun showUserGuide() {\n        XLog.d(TAG, \"userScrolled = $userScrolled\")\n        if (userScrolled) {\n            return\n        }\n\n        showGuideRunnable = Runnable { setUpUserGuideAnim() }\n        HandlerUtils.getMainHandler().postDelayed({\n            showGuideRunnable?.run()\n        }, 1500)\n    }\n\n    private fun setUpUserGuideAnim() {\n        // 下滑动画\n        val translationRecyclerAnimator = ValueAnimator.ofInt(0, scrollY).apply {\n            duration = SCROLL_DURATION\n            var scrolledY = 0\n            addUpdateListener {\n                val value = it.animatedValue as Int\n                val scrollByY = value - scrolledY\n                scrolledY = value\n                XLog.d(TAG, \"[show] scrollByY: $scrollByY, scrolledY: $scrolledY\")\n                recyclerView.scrollBy(0, scrollByY)\n            }\n            interpolator = DecelerateInterpolator()\n        }\n\n        // 顶部停留动画\n        val delayAnimator = ValueAnimator.ofInt(0, 0).apply {\n            duration = DELAY_DURATION\n        }\n\n        // 复位动画\n        val resetRecyclerAnimator = ValueAnimator.ofInt(scrollY, 0).apply {\n            duration = SCROLL_DURATION\n            var scrolledY = scrollY\n            addUpdateListener {\n                val value = it.animatedValue as Int\n                val scrollByY = value - scrolledY\n                scrolledY = value\n                XLog.d(TAG, \"[reset] scrollByY: $scrollByY, scrolledY: $scrolledY\")\n                recyclerView.scrollBy(0, scrollByY)\n            }\n            interpolator = DecelerateInterpolator()\n        }\n\n        val animatorSet = AnimatorSet()\n        animatorSet.playSequentially(translationRecyclerAnimator, delayAnimator, resetRecyclerAnimator)\n        animatorSet.start()\n    }\n\n    override fun onResume() {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_RESUME)\n        super.onResume()\n        XLog.i(TAG, \"onResume, time:${System.currentTimeMillis() - time}, userVisibleHint: \" + userVisibleHint)\n        if (doFirstItemSelectedOnResume) {\n            XLog.i(TAG, \"onResume doFirstItemSelectedOnResume\")\n            currentItem?.let {\n                onItemSelected(it, 0)\n            }\n            doFirstItemSelectedOnResume = false\n        }\n\n        if (!DiscoverUIUtil.enablePreRender()) {\n            postToUiThreadDelayed(100) {\n                VideoViewManager.getInstance().onResume(activity)\n            }\n        }\n        if (!exposed) {\n            reporter.pageExposure()\n            techReporter.reportPageResume(position)\n            exposed = true\n        }\n        reporter.pageIn()\n        reporter.reportPageIn()\n\n        registerRotate()\n        (activity as? ShareBaseActivity)?.getShareEngine(reporter.scene)?.onResume()\n        if (DiscoverUIUtil.enablePreRender() && userVisibleHint) {\n            currentItem?.onPageResume()\n        }\n    }\n\n    private fun registerRotate() {\n        if (DiscoverUIUtil.isWideScreen()) {\n            XLog.i(TAG, \"isWideScreen, return\")\n            return\n        }\n\n        context ?: return\n        if (!isAutoRotateEnabled(requireContext())) {\n            return\n        }\n        orientationEventListener.enable()\n    }\n\n    override fun onPause() {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_PAUSE)\n        super.onPause()\n        XLog.i(TAG, \"onPause, time:${System.currentTimeMillis() - time}\")\n        if (requireActivity().isFinishing) {\n            VideoViewManager.getInstance().onStop(activity)\n            adapter.currentSelectVH?.onPageStop()\n            activity?.let {\n                DiscoverVideoComponentCacheManager.getManager(getScene()).onDestroy(it)\n                if (isRecommendScene()) {\n                    DiscoverRecommendPreRenderVideo.onDestroy(it)\n                }\n            }\n        } else {\n            VideoViewManager.getInstance().onPause(activity)\n            adapter.currentSelectVH?.onPagePause()\n        }\n        reporter.pageOut()\n        reporter.reportPageOut()\n        unregisterRotate()\n\n        if (showGuideRunnable != null) {\n            HandlerUtils.getMainHandler().removeCallbacks(showGuideRunnable!!)\n            showGuideRunnable = null\n        }\n    }\n\n\n    override fun onDestroy() {\n        super.onDestroy()\n        reporter.reportPageDestroy()\n    }\n\n    private fun unregisterRotate() {\n        activity ?: return\n        orientationEventListener.disable()\n    }\n\n    private fun initView(view: View) {\n        view.setBackgroundResource(R.color.discover_bg_color)\n        loadingView = view.findViewById(R.id.loading_view)\n        errorView = view.findViewById(R.id.error_page)\n        errorView.callback = object : MiddlePageErrorPage.ErrorPageCallBack {\n            override fun onClickRetry() {\n                XLog.d(TAG, \"onClickRefresh\")\n                retryLoadData()\n            }\n        }\n        refreshLayout = view.findViewById<SmartRefreshLayout>(R.id.refresh_layout).apply {\n            setEnableRefresh(enableRefresh)\n            setEnableLoadMore(true)\n            setEnableScrollContentWhenLoaded(false)\n            setRefreshFooter(YYBFooter(context), ViewGroup.LayoutParams.MATCH_PARENT, 63.dp)\n            setOnLoadMoreListener {\n                XLog.d(TAG, \"OnLoadMore\")\n                loadData(true)\n            }\n            setOnRefreshListener {\n                XLog.d(TAG, \"OnRefresh\")\n                loadData(isLoadMore = false, refresh = true)\n            }\n        }\n        recyclerView = view.findViewById<FixedRecyclerView>(R.id.recycler_view).apply {\n            setHasFixedSize(true)\n            layoutManager = if (optimizePreRender) {\n                LinearLayoutManager(context)\n            } else {\n                PreRenderLinearLayoutManager(context)\n            }\n            itemAnimator = null\n            adapter = <EMAIL>\n            addOnScrollListener(object : RecyclerView.OnScrollListener() {\n                var totalDy = 0\n                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {\n                    super.onScrollStateChanged(recyclerView, newState)\n                    <EMAIL> = newState\n                    XLog.i(TAG, \"onScrollStateChanged newState: $newState\")\n                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {\n                        sendScrollIdleEvent()\n                        recyclerView.layoutManager?.let {\n                            val newPosition =\n                                pagerSnapHelper.findTargetSnapPosition(it, recyclerView.scrollX, recyclerView.scrollY)\n                            calculateItemChange(newPosition, it)\n                        }\n                        totalDy = 0\n                        adjustCurVideoPosition()\n                    }\n                    if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {\n                        XLog.d(TAG, \"user dragging\")\n                        if (!userScrolled && !BuildConfig.DEBUG) {\n                            userScrolled = true\n                        }\n                    }\n                }\n\n                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {\n                    super.onScrolled(recyclerView, dx, dy)\n                    scrolledDirection.onScrolled(dx, dy)\n                    if (DiscoverConfig.discoverSlideVideoShowCoverImage) {\n                        totalDy += dy\n                        if (totalDy > 0) {\n                            // 向下滑动\n                            adjustNextVideoPosition()\n                        }\n                    }\n                }\n            })\n        }\n\n        pagerSnapHelper.attachToRecyclerView(recyclerView)\n        topShadow = view.findViewById(R.id.top_shadow)\n    }\n\n\n    private fun calculateItemChange(newPosition: Int, layoutManager: RecyclerView.LayoutManager) {\n\n        XLog.i(TAG, \"calculateItemChange, newPageIndex: $newPosition, current: $position\")\n        if (newPosition == position) {\n            return\n        }\n\n        if (newPosition > position) {\n            preloadVideo(newPosition)\n        }\n\n        position = newPosition\n        val itemView = layoutManager.findViewByPosition(newPosition) ?: return\n        val vh = recyclerView.getChildViewHolder(itemView) as BaseVideoViewHolder\n        onItemSelected(vh, newPosition)\n    }\n\n    private fun preloadVideo(newPageIndex: Int) {\n        val position = newPageIndex + 2\n        XLog.i(TAG, \"preloadVideo, position=$position, count=${adapter.itemCount}\")\n        val preloadData = adapter.getItemData(position) ?: return\n        DiscoverRecommendVideoPreloadManager.preloadVideo(preloadData)\n    }\n\n    private fun onItemSelected(vh: BaseVideoViewHolder, position: Int) {\n        XLog.d(TAG, \"onItemSelected : position=$position\")\n        currentItem = vh\n        // 如果当前页面没有 resume 即发现tab 第一个展示的不是推荐页,这里不做处理, 在切换到推荐tab(resume)时处理\n        if (!isResumed && position == 0) {\n            XLog.i(TAG, \"onItemSelected: isResumed = $isResumed, return\")\n            doFirstItemSelectedOnResume = true\n            return\n        }\n        val itemInfo = adapter.getItemData(position) ?: return\n        adapter.onItemSelected(vh, position)\n        reporter.reportCardExposure(itemInfo, position)\n        if (position != 0 && isRecommendScene()) {\n            firstVideoMute = false\n        }\n        tryAutoLoadMore(position)\n        exposedItemPosition[position] = true\n    }\n\n    private fun tryAutoLoadMore(position: Int) {\n        if (position == adapter.itemCount - 3 && !exposedItemPosition.contains(position)) {\n            XLog.i(TAG, \"autoLoadMore\")\n            loadData(true)\n        }\n    }\n\n    /**\n     * 设置当前视频的bias为0.4\n     * 修复部分机型 SCROLL_STATE_IDLE 时，视频处于顶部\n     */\n    private fun adjustCurVideoPosition() {\n        XLog.i(TAG, \"adjustCurVideoPosition\")\n        val layoutManager = recyclerView.layoutManager as LinearLayoutManager\n        val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()\n        val currentVisibleView = layoutManager.findViewByPosition(firstVisiblePosition) ?: return\n        val currentVisibleViewHolder = recyclerView.getChildViewHolder(currentVisibleView) as? BaseVideoViewHolder ?: return\n        // 设置 bias 为 0.4f\n        currentVisibleViewHolder.adjustVideoPosition(1f,0)\n    }\n\n    /**\n     * 向下滑动时，动态设置下一个视频的bias\n     */\n    private fun adjustNextVideoPosition() {\n        val layoutManager = recyclerView.layoutManager as LinearLayoutManager\n        val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()\n        val firstVisibleView = layoutManager.findViewByPosition(firstVisiblePosition) ?: return\n\n        val nextVisiblePosition = firstVisiblePosition + 1\n        val nextVisibleView = layoutManager.findViewByPosition(nextVisiblePosition) ?: return\n        val nextVisibleViewHolder = recyclerView.getChildViewHolder(nextVisibleView) as? BaseVideoViewHolder ?: return\n\n        val totalHeight = firstVisibleView.height\n        val visibleHeight = totalHeight - firstVisibleView.bottom\n        val scrollFraction = visibleHeight.toFloat() / totalHeight\n        XLog.i(TAG, \"totalHeight: $totalHeight, firstVisibleView.bottom: ${firstVisibleView.bottom} visibleHeight: $visibleHeight, scrollFraction: $scrollFraction\")\n        nextVisibleViewHolder.adjustVideoPosition(scrollFraction, visibleHeight)\n    }\n\n    private fun initObserver(lifecycleOwner: LifecycleOwner) {\n        viewModel.recommendLiveData.observe(lifecycleOwner) { result ->\n            reporter.pageRenderStart()\n            val data = result.data?.items ?: return@observe\n            val refresh = result.requestType == RequestType.FULL_REQUEST\n            XLog.i(TAG, \"onDataLoaded, refresh:$refresh, time:${System.currentTimeMillis() - time}\")\n            if (refresh && !fistDataLoaded) {\n                techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_DATA_BIND_START)\n            }\n            val success = adapter.setData(refresh, data)\n            if (refresh && success) {\n                recyclerView.scrollToPosition(0)\n                exposedItemPosition.clear()\n                DiscoverGuideBarManager.clean()\n                if (!fistDataLoaded) {\n                    fistDataLoaded = true\n                    techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_DATA_BIND_END)\n                    techReporter.reportFirstDataLoad()\n                } else {\n                    techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_REFRESH)\n                    techReporter.reportRefresh()\n                }\n            }\n            DiscoverRecommendVideoPreloadManager.preloadVideo(data)\n            XLog.i(TAG, \"initObserver: recommendLiveData off isLoadingData.\")\n            isLoadingData = false\n\n            // 数据加载完成后显示动画\n            if (isFirstShowDiscover) {\n                showUserGuide()\n                if (DiscoverConfig.discoverSlideVideoShowCoverImage){\n                    adjustNextVideoPosition()\n                }\n                isFirstShowDiscover = false\n            }\n        }\n\n        viewModel.errorPageLiveData.observe(lifecycleOwner) { showErrorPage ->\n            togglePageError(showErrorPage)\n        }\n\n        viewModel.firstLoadingLiveData.observe(lifecycleOwner) { loading ->\n            togglePageLoading(loading)\n        }\n\n        viewModel.loadMoreLiveData.observe(lifecycleOwner) { loadMoreStatus ->\n            updateLoadMoreView(loadMoreStatus)\n        }\n\n        viewModel.refreshFinishLiveData.observe(lifecycleOwner) { refreshSuccess ->\n            refreshLayout.finishRefresh(refreshSuccess)\n        }\n\n        viewModel.loadingDataFinishCallback.observe(lifecycleOwner) { result ->\n            XLog.i(TAG, \"initObserver: loadingDataFinishCallback off isLoadingData, loadingDataCallback = $result\")\n            isLoadingData = false\n        }\n    }\n\n    private fun firstLoadData() {\n        // 是否有外部跳转的参数，有的话则不能在首次请求时使用缓存数据，需要将refresh置为 true\n        val hasValidJumpParams = DiscoverPageJumpManager.hasValidJumpParams()\n        XLog.d(\n            TAG,\n            \"firstLoadData, time:${System.currentTimeMillis() - time} , hasValidJumpParams = $hasValidJumpParams\"\n        )\n        loadData(isLoadMore = false, refresh = hasValidJumpParams)\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_LOAD_DATA_START)\n        techReporter.reportStartFirstLoadData()\n    }\n\n    fun loadData(isLoadMore: Boolean, refresh: Boolean = false, retry: Boolean = false) {\n        XLog.i(TAG, \"loadData, loadMore: $isLoadMore, fresh: $refresh\")\n        viewModel.requestRecommend(getDiscoverRecommendRequestParam(isLoadMore, refresh, retry))\n        isLoadingData = true\n    }\n\n    open fun getDiscoverRecommendRequestParam(\n        isLoadMore: Boolean,\n        refresh: Boolean,\n        retry: Boolean\n    ) = DiscoverRecommendRequestParam(\n        request = DiscoveryPageRecommendRequest().apply { DiscoverPageJumpManager.appendTmastRequestParams(this) },\n        isLoadMore = isLoadMore,\n        isRefresh = refresh,\n        isRetry = retry,\n        useCacheData = !isLoadMore && !refresh && !retry,\n        cacheData = (isRecommendScene() && !DiscoverPageJumpManager.hasValidJumpParams()),\n        scene = getScene()\n    )\n\n    private fun isRecommendScene() = getScene() == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n\n    private fun togglePageLoading(show: Boolean) {\n        if (show) {\n            loadingView.visibility = View.VISIBLE\n        } else {\n            loadingView.visibility = View.GONE\n        }\n    }\n\n    private fun togglePageError(show: Boolean) {\n        if (show) {\n            errorView.visibility = View.VISIBLE\n            reporter.pageRenderFinish()\n        } else {\n            errorView.visibility = View.GONE\n        }\n    }\n\n    private fun updateLoadMoreView(loadMoreStatus: DiscoverRecommendViewModel.LoadMoreStatus) {\n        XLog.d(TAG, \"updateLoadMoreView: $loadMoreStatus\")\n        if (loadMoreStatus.isLoadMore) {\n            if (loadMoreStatus.hasMoreData) {\n                refreshLayout.finishLoadMore(loadMoreStatus.loadSuccess)\n            } else {\n                refreshLayout.finishLoadMoreWithNoMoreData()\n            }\n\n        } else {\n            refreshLayout.resetNoMoreData()\n            refreshLayout.setNoMoreData(!loadMoreStatus.hasMoreData)\n        }\n    }\n\n    private fun sendScrollIdleEvent() {\n        scrolledDirection.onScrollIdle()\n        if (scrolledDirection.isNoDirection){\n            XLog.i(TAG, \"sendScrollIdleEvent return, isNoDirection\")\n            return\n        }\n        val msg = ApplicationProxy.getEventDispatcher().obtainMessage()\n        msg.what = EventDispatcherEnum.UI_EVENT_VIEW_SCROLL_IDLE\n        msg.obj = ScrollIdleEventInfo(context, recyclerView)\n        ApplicationProxy.getEventDispatcher().sendMessage(msg)\n    }\n\n\n    private fun showCommentDialog(config: CommentFragmentConfig) {\n        activity?.let {\n            commentDialogFragment = CommentDialogFragment.newInstance(\n                config = config,\n                listener = object : CommentDialogFragment.CommentDialogListener {\n                    override fun onDismiss() {\n                        handleCommentDialogDismissed()\n                    }\n\n                    override fun onHeightChange(currentHeight: Int, totalHeight: Int) {\n                        XLog.d(TAG, \"onHeightChange $currentHeight, totalHeight: $totalHeight\")\n\n                        onCommentDialogHeightChange(currentHeight, totalHeight)\n                        onCommentDialogHeightChangeListener?.invoke(currentHeight, totalHeight)\n                    }\n\n                },\n                callback = object : ICommentEventCallback {\n                    override fun onCommentCountChanged(newCount: Int) {\n                        adapter.currentSelectVH?.updateCommentCount(newCount)\n                    }\n\n                })\n            commentDialogFragment?.show(childFragmentManager, \"commentDialogFragment\")\n        }\n    }\n\n    private fun onCommentDialogHeightChange(dialogHeight: Int, totalHeight: Int) {\n        changeTopShadowAlpha(1F - dialogHeight / totalHeight.toFloat())\n        adapter.currentSelectVH?.onDialogHeightChange(dialogHeight, totalHeight)\n    }\n\n    private fun changeTopShadowAlpha(alpha: Float) {\n        val safeAlpha = if (alpha < 0) {\n            0F\n        } else if (alpha > 1) {\n            1F\n        } else {\n            alpha\n        }\n        topShadow.alpha = safeAlpha\n    }\n\n\n    private fun handleCommentDialogDismissed() {\n        commentDialogFragment = null\n        onCommentDialogHeightChange(0, 1)\n    }\n\n    override fun onBackPressed(): Boolean {\n        return false\n    }\n\n    override fun onPageSelect() {\n        root ?: return\n        refresh(true)\n    }\n\n    override fun refresh(retry: Boolean) {\n        root ?: return\n        if (retry) {\n            if (errorView.visibility == View.VISIBLE && NetworkUtil.isNetworkActive()) {\n                retryLoadData()\n            }\n        } else {\n            refreshLayout.autoRefresh()\n        }\n    }\n\n    private fun retryLoadData() {\n        loadData(isLoadMore = false, refresh = true, retry = true)\n    }\n\n    override fun onLoginStateChanged() {\n        loadData(isLoadMore = false, refresh = true)\n    }\n\n    private fun isAutoRotateEnabled(context: Context): Boolean {\n        return try {\n            Settings.System.getInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION) == 1\n        } catch (e: Settings.SettingNotFoundException) {\n            e.printStackTrace()\n            false\n        }\n    }\n\n}"}
{"query": "DiscoverRecommendPreloadManager.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport android.content.Context\nimport com.qq.AppService.AstApp\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.assistant.protocol.jce.GetDynamicFrameEntranceResponse\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistantv2.activity.MainActivity\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.managerv7.EntranceManager\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/23 16:41\n * Description:\n */\nobject DiscoverRecommendPreloadManager {\n\n    private const val TAG = \"DiscoverRecommendPreloadManager\"\n\n    @Volatile\n    private var needRetryAfterEntranceUpdate = false\n\n    @Volatile\n    private var hasDiscoverEntranceFromResponse = false\n\n    private val scene = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n\n    private val techReporter = DiscoverBeaconReport.getReporter(scene)\n\n    fun preload(context: Context) {\n        XLog.i(TAG, \"preload\")\n        if (!DiscoverConfig.preloadRecommend) {\n            XLog.i(TAG, \"preload switch off return\")\n            return\n        }\n\n        if (!hasDiscoverEntrance()) {\n            XLog.e(TAG, \"preload no discover entrance return\")\n            needRetryAfterEntranceUpdate = true\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_NO_ENTRANCE)\n            return\n        }\n        needRetryAfterEntranceUpdate = false\n        preloadDirectly(context)\n    }\n\n    private fun hasDiscoverEntrance() = EntranceManager.getInstance().hasDiscoverEntrance()\n            || hasDiscoverEntranceFromResponse\n            || DiscoverConfig.forceEntrance\n\n    private fun preloadDirectly(context: Context) {\n        if (!DiscoverConfig.preloadRecommend) {\n            XLog.i(TAG, \"preload switch off return\")\n            return\n        }\n        XLog.i(TAG, \"discover preloadDirectly\")\n\n        try {\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_START)\n            XLog.i(TAG, \"preload start\")\n            DiscoverRecommendVideoPreloadManager.initPreloadManager()\n            DiscoverVideoComponentCacheManager.getManager(scene).initialize(context)\n            DiscoverRecommendDataCacheManager.preloadData(false)\n            XLog.i(TAG, \"preload end\")\n        } catch (e: Exception) {\n            if (BuildConfig.DEBUG) {\n                throw e\n            }\n            XLog.e(TAG, \"DiscoverPreloadManager preloadDirectly\", e)\n        }\n    }\n\n\n    fun preloadDiscoverIfHasEntrance(response: GetDynamicFrameEntranceResponse) {\n        hasDiscoverEntranceFromResponse = false\n        if (!DiscoverConfig.tryPreloadAfterEntranceUpdate) {\n            XLog.i(TAG, \"preloadDiscoverIfHasEntrance switch off return\")\n            return\n        }\n\n        if (response.groups.isNullOrEmpty()) {\n            XLog.i(TAG, \"preloadDiscoverIfHasEntrance response.groups is null\")\n            return\n        }\n\n\n        for (group in response.groups) {\n            if (group?.firstEntrance == null) {\n                XLog.i(TAG, \"preloadDiscoverIfHasEntrance group.firstEntrance is null\")\n                continue\n            }\n\n            XLog.d(TAG, \"preloadDiscoverIfHasEntrance tabType ${group.firstEntrance.tabType}\")\n            if (group.firstEntrance.tabType == EntranceManager.ENTRANCE_TYPE_DISCOVER) {\n                hasDiscoverEntranceFromResponse = true\n                val activity = AstApp.getAllCurActivity()\n                if (activity !is MainActivity) {\n                    XLog.i(TAG, \"preloadDiscoverIfHasEntrance not main activity\")\n                    return\n                }\n                XLog.i(TAG, \"preloadDiscoverIfHasEntrance start\")\n                if (!needRetryAfterEntranceUpdate) {\n                    XLog.i(TAG, \"preloadDiscoverIfHasEntrance needRetryAfterEntranceUpdate is false\")\n                    return\n                }\n\n                needRetryAfterEntranceUpdate = false\n                preloadDirectly(activity)\n                XLog.i(TAG, \"preloadDiscoverIfHasEntrance end\")\n                return\n            }\n        }\n    }\n\n    fun onDestroy(activity: MainActivity) {\n        DiscoverVideoComponentCacheManager.getManager(scene).onDestroy(activity)\n        DiscoverRecommendVideoPreloadManager.onDestroy()\n        DiscoverRecommendDataCacheManager.onDestroy()\n        needRetryAfterEntranceUpdate = false\n        hasDiscoverEntranceFromResponse = false\n    }\n\n    fun onMainCreate() {\n        if (!DiscoverConfig.preloadDataOnMainCreate) {\n            XLog.i(TAG, \"onMainCreate return\")\n            return\n        }\n\n        try {\n            XLog.i(TAG, \"onMainCreate-preLoadFirstRecommendPage\")\n            DiscoverRecommendDataCacheManager.preloadData(true)\n        } catch (e: Exception) {\n            if (BuildConfig.DEBUG) {\n                throw e\n            }\n            XLog.e(TAG, \"onMainCreate exception\", e)\n        }\n    }\n\n\n    fun onMainResume() {\n        try {\n            XLog.i(TAG, \"onMainResume\")\n            if (!hasDiscoverEntrance()) {\n                XLog.i(TAG, \"onMainResume return, hasDiscoverEntrance = ${hasDiscoverEntrance()}\")\n                return\n            }\n\n            if (!DiscoverConfig.checkVideoExpiredOnMainResume) {\n                XLog.i(TAG, \"onMainResume return, checkVideoExpiredOnMainResume = false\")\n                return\n            }\n\n            DiscoverRecommendVideoPreloadManager.onMainResume()\n        } catch (e: Exception) {\n            if (BuildConfig.DEBUG) {\n                throw e\n            }\n            XLog.e(TAG, \"onMainResume exception\", e)\n        }\n    }\n\n}"}
{"query": "PreRenderLinearLayoutManager.kt", "value": "package com.tencent.pangu.discover.recommend.view\n\nimport android.content.Context\nimport android.util.AttributeSet\nimport androidx.recyclerview.widget.LinearLayoutManager\nimport androidx.recyclerview.widget.RecyclerView\nimport com.tencent.assistant.utils.XLog\n\nclass PreRenderLinearLayoutManager : LinearLayoutManager {\n    private var mIsDisableScrolling = false\n    private var forceCloseRenderNext = false\n\n    constructor(context: Context?) : super(context)\n\n    constructor(\n        context: Context?, orientation: Int,\n        reverseLayout: Boolean\n    ) : super(context, orientation, reverseLayout)\n\n    constructor(\n        context: Context?, attrs: AttributeSet?,\n        defStyleAttr: Int, defStyleRes: Int\n    ) : super(context, attrs, defStyleAttr, defStyleRes)\n\n    override fun canScrollVertically(): Boolean {\n        return !mIsDisableScrolling && super.canScrollVertically()\n    }\n\n    fun disableScrolling(isDisableScrolling: Boolean) {\n        this.mIsDisableScrolling = isDisableScrolling\n    }\n\n\n    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {\n        try {\n            //try catch一下\n            super.onLayoutChildren(recycler, state)\n        } catch (e: IndexOutOfBoundsException) {\n            XLog.i(TAG, \"onLayoutChildren\", e)\n        }\n    }\n\n    /**\n     * 预渲染下一个视频\n     */\n    override fun getExtraLayoutSpace(state: RecyclerView.State?): Int {\n        return if (canRenderNext()) DISPLAY_NEXT_ITEM_PIXEL else super.getExtraLayoutSpace(state)\n    }\n\n    fun setForceCloseRenderNext(forceCloseRenderNext: Boolean) {\n        this.forceCloseRenderNext = forceCloseRenderNext\n    }\n\n    private fun canRenderNext(): Boolean {\n        if (forceCloseRenderNext) {\n            return false\n        }\n        return true\n    }\n\n    companion object {\n        private const val TAG = \"DisableScrollingLinearLayoutManager\"\n        private const val DISPLAY_NEXT_ITEM_PIXEL = 10\n    }\n}"}
{"query": "DiscoverSeekConstraintLayout.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.util.AttributeSet\nimport android.view.MotionEvent\nimport androidx.constraintlayout.widget.ConstraintLayout\nimport com.tencent.assistant.utils.ViewUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.discover.base.IDiscoverAreaMoveListener\nimport kotlin.math.abs\nimport kotlin.math.atan2\nimport kotlin.math.max\n\nclass DiscoverSeekConstraintLayout @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : ConstraintLayout(context, attrs, defStyleAttr){\n\n    companion object {\n        private const val TAG = \"DiscoverFixedConstraintLayout\"\n        // 滑动阈值\n        private const val MOVE_THRESHOLD = 6\n        // 可滑动区域高度\n        private const val ENABLE_SEEK_HEIGHT = 96\n        // 限制角度, 只有滑动时在限制角度范围才会拦截\n        private const val LIMIT_LESS_DEGREES = 30\n        private const val LIMIT_MORE_DEGREE = 150\n    }\n\n    private var currentX = 0f\n    private var currentY = 0f\n    var listener: IDiscoverAreaMoveListener ?= null\n    private var minY = 0\n    private var isDragging = false\n    private var isMoving = false\n    private var isChecked = false\n\n    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {\n        XLog.d(TAG, \"onInterceptTouchEvent.${ev.action} x: ${ev.x} y: ${ev.y} minY: $minY isDragging:${isDragging}\")\n        return when(ev.action) {\n            MotionEvent.ACTION_UP,\n            MotionEvent.ACTION_CANCEL -> {\n                isChecked = false\n                isDragging\n            }\n            MotionEvent.ACTION_MOVE -> {\n\n                if (check(ev)) {\n                    isDragging = true\n                    isChecked = true\n                }\n                isDragging\n            }\n            MotionEvent.ACTION_DOWN -> {\n                currentX = ev.x\n                currentY = ev.y\n                isDragging\n            }\n            else -> {\n                super.onInterceptTouchEvent(ev)\n            }\n        }\n    }\n\n    override fun onTouchEvent(event: MotionEvent?): Boolean {\n        when(event?.action) {\n            MotionEvent.ACTION_UP,\n            MotionEvent.ACTION_CANCEL -> {\n                if (isMoving) {\n                    isChecked = false\n                    isDragging = false\n                    listener?.onMoveStopped()\n                    parent.requestDisallowInterceptTouchEvent(false)\n                    isMoving = false\n                }\n            }\n            MotionEvent.ACTION_MOVE -> {\n                if (!isChecked) {\n                    isChecked = check(event)\n                }\n                if (isChecked) {\n                    if (!isMoving) {\n                        parent.requestDisallowInterceptTouchEvent(true)\n                        isMoving = true\n                        listener?.onMoveStarted(event.x)\n                    } else {\n                        listener?.onMoving(event.x, currentX)\n                    }\n                    currentX = event.x\n                    currentY = event.y\n                }\n            }\n            MotionEvent.ACTION_DOWN -> {\n                currentX = event.x\n                currentY = event.y\n            }\n        }\n        return super.onTouchEvent(event)\n    }\n\n    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {\n        super.onLayout(changed, left, top, right, bottom)\n        XLog.d(TAG, \"onLayout: width: $width height: $height\")\n        minY = max(0, height - ViewUtils.dip2px(ENABLE_SEEK_HEIGHT))\n    }\n\n    private fun check(ev: MotionEvent): Boolean {\n        if (ev.y < minY) {\n            return false\n        }\n        val dx = (ev.x - currentX).toDouble()\n        if (abs(dx) <= MOVE_THRESHOLD) {\n            return false\n        }\n        val dy = (ev.y - currentY).toDouble()\n        val degree = Math.toDegrees(atan2(dy, dx))\n        return (abs(degree) > 0 && abs(degree) <= LIMIT_LESS_DEGREES) || abs(degree) >= LIMIT_MORE_DEGREE\n    }\n\n\n\n}"}
{"query": "DiscoverConfig.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.assistant.config.api.IConfigManagerService\nimport com.tencent.assistant.raft.TRAFT\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.PNGSettings\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/19 20:15\n * Description:\n */\nobject DiscoverConfig {\n    private const val TAG = \"DiscoverConfig\"\n\n    /**\n     * 修复当有外部参数传入，期望刷新列表时，提前回收预加载的视频，避免播放视频错误\n     */\n    private const val KEY_FIX_DISCOVER_PRERENDER_PLAYER_ERROR =\n        \"key_fix_discover_prerender_player_error\"\n\n    private val config = TRAFT.get(IConfigManagerService::class.java, IConfigManagerService.RDELIVERY)\n\n    val preloadRecommend: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_RECOMMEND_PRELOAD, true)\n\n\n    /**\n     * 是否预加载视频组件\n     */\n    val preCreateRecommendVideoComponent: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_RECOMMEND_PRELOAD_VIDEO_COMPONENT, true)\n\n    /**\n     * 是否预加载视频源\n     */\n    val preloadVideoSource: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_RECOMMEND_PRELOAD_VIDEO_SOURCE, true)\n\n    /**\n     * 是否预加载推荐页数据\n     */\n    val preloadRecommendPage: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_RECOMMEND_PRELOAD_PAGE_DATA, true) && preloadRecommend\n\n\n    /**\n     * 是否懒加载打榜页\n     */\n    val lazyLoadTopicPage: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_LAZY_LOAD_TOPIC_PAGE, true)\n\n\n    /**\n     * 是否自主上报首帧\n     */\n    val reportFirstFrameByUser: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_RECOMMEND_REPORT_FIRST_FRAME_BY_USER, true)\n\n\n    /**\n     * 是否强制显示发现入口\n     */\n    val forceEntrance: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_ENTRANCE_FORCE_OPEN, false)\n\n\n    /**\n     * 发现页自主上报 pageIn pageOut\n     */\n    val discoverReportPageBySelf: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_REPORT_PAGE_BY_SELF, true)\n\n\n    /**\n     * 发现页在入口更新后 preload\n     */\n    val tryPreloadAfterEntranceUpdate: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_TRY_PRELOAD_AFTER_ENTRANCE_UPDATE, true)\n\n\n    /**\n     * 发现页预加载视频数量\n     */\n    val preloadVideoCount: Int\n        get() = config.getConfigInt(PNGSettings.KEY_DISCOVER_PRELOAD_VIDEO_COUNT, 1)\n\n\n    /**\n     * 自动播放下一个视频\n     */\n    val autoPlayNextVideo: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_AUTO_PLAY_NEXT, true)\n\n    /**\n     * 发现页预渲染第一个视频\n     */\n    val preRenderDiscoverFirstVideo: Boolean\n        get() = config.getConfigBoolean(PNGSettings.KEY_DISCOVER_PRERENDER_FIRST_VIDEO, true)\n\n\n\n    private const val HOUR = 60 * 60 * 1000\n\n    /**\n     * 视频缓存有效期两个小时,  25% 用户3H 后才进入发现页, 25% 用户在 3H ~ 13Min 之间进入发现页, 25% 用户在 13Min~ 45s 之间进入发现页, 25% 用户在 45s 之内进入发现页\n     */\n    val videoCacheExpTime: Int\n        get() {\n            val configHour = config.getConfigInt(PNGSettings.KEY_DISCOVER_VIDEO_CACHE_EXP_TIME, 2)\n            return configHour * HOUR\n        }\n\n\n    // 数据缓存有效期\n    val dataCacheExpTime: Int\n        get() {\n            val configHour = config.getConfigInt(PNGSettings.KEY_DISCOVER_DATA_CACHE_EXP_TIME, 72)\n            return configHour * HOUR\n        }\n\n\n    // 预渲染优化\n    val optimizePreRender: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_OPTIMIZE_PRERENDER, true)\n        }\n\n\n    // MainCreate时预下载数据\n    val preloadDataOnMainCreate: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_PRELOAD_DATA_ON_MAIN_CREATE, false)\n        }\n\n    // 上传 0vv 日志\n    val upload0VVLog: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_UPLOAD_0VV_LOG, false)\n        }\n\n    // 检查视频是否过期\n    val checkVideoExpiredOnMainResume: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_CHECK_VIDEO_EXPIRED_ON_MAIN_RESUME, true)\n        }\n\n    // 视频组件回收时是否强制停止视频\n    val recycleForceStopVideo: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_FORCE_STOP_WHEN_RECYCLE, true)\n        }\n\n    // 重置预渲染状态\n    val resetPreRender: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_RESET_PRERENDER, true)\n        }\n\n\n    // 上报 视频 url 是否 encode\n    val videoReportUrlEncode: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_REPORT_ENCODE_URL, false)\n        }\n\n    // 发现页默认tab\n    val discoverDefaultTab: Int\n        get() {\n            return config.getConfigInt(PNGSettings.KEY_DISCOVER_DEFAULT_TAB_POSITION, if (BuildConfig.DEBUG) 0 else 0)\n        }\n\n    // 发现页气泡引导\n    val discoverTabTip: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_SHOW_TAB_TIP, true)\n        }\n\n\n    //是否使用磁盘缓存\n    val useDiskCache: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_USE_DISK_CACHE, true)\n        }\n\n    // 重置视频上报信息\n    val resetReportModel: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_RESET_REPORT_MODEL, true)\n        }\n\n    // 复制视频上报信息\n    val cloneReportModel: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_VIDEO_CLONE_REPORT_MODEL, true)\n        }\n\n    //视频横屏时不做数据更新\n    val ignoreDataUpdateWhenVideoFullScreen: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_IGNORE_DATA_UPDATE_WHEN_VIDEO_FULL_SCREEN, true)\n        }\n\n    //视频横屏时不做数据更新\n    val fixDiscoverPreRenderPlayerError: Boolean\n        get() {\n            val enable = config.getConfigBoolean(KEY_FIX_DISCOVER_PRERENDER_PLAYER_ERROR, true)\n            XLog.i(TAG, \"fixDiscoverPreRenderPlayerError: enable = $enable\")\n            return enable\n        }\n\n    /**\n     * 发现页微下滑时可看见下一个视频内容\n     */\n    val discoverSlideVideoShowCoverImage: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_SLIDE_VIDEO_SHOW_COVER, false)\n        }\n\n\n    private const val KEY_DISCOVER_VIDEO_FEED_PRELOAD = \"key_discover_video_feed_preload\"\n    /**\n     * 二级页预加载开关\n     */\n    val preloadVideoFeed: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_VIDEO_FEED_PRELOAD, true)\n\n    private const val KEY_DISCOVER_VIDEO_FEED_PRELOAD_DATA = \"key_discover_video_feed_preload_data\"\n\n    /**\n     * 视频二级页数据预加载\n     */\n    val preloadVideoFeedData: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_VIDEO_FEED_PRELOAD_DATA, true) && preloadVideoFeed\n\n    private const val KEY_DISCOVER_VIDEO_FEED_PRERENDER_FIRST_VIDEO = \"key_discover_video_feed_prerender_first_video\"\n    /**\n     * 视频二级页预渲染第一个视频\n     */\n    val preRenderVideoFeedFirstVideo: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_VIDEO_FEED_PRERENDER_FIRST_VIDEO, true)\n\n\n    private const val KEY_DISCOVER_VIDEO_FEED_PRELOAD_VIDEO_COMPONENT =\n        \"key_discover_video_feed_preload_video_component\"\n    /**\n     * 二级页预加载视频组件\n     */\n    val preCreateVideoFeedVideoComponent: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_VIDEO_FEED_PRELOAD_VIDEO_COMPONENT, true)\n\n    private const val KEY_DISCOVER_VIDEO_FEED_PRELOAD_VIDEO_SOURCE =\n        \"key_discover_video_feed_preload_video_source\"\n\n    /**\n     * 二级页预加载视频源\n     */\n    val preloadVideoFeedVideoSource: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_VIDEO_FEED_PRELOAD_VIDEO_SOURCE, true)\n\n    private const val KEY_DISCOVER_ONLY_REQUEST_URL_VIDEO =\n        \"key_discover_only_request_url_video\"\n\n    /**\n     * 是否在插件未安装时只请求url的视频\n     */\n    val onlyRequestUrlVideo: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_ONLY_REQUEST_URL_VIDEO, true)\n\n    private const val KEY_DISCOVER_PRELOAD_TOPIC_PAGE =\n        \"key_discover_preload_topic_page\"\n\n    /**\n     * 是否预加载打榜页\n     */\n    val preloadTopicPage: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_PRELOAD_TOPIC_PAGE, true)\n\n\n    private const val KEY_DISCOVER_FIX_PAUSE_BEFORE_PREPARE =\n        \"key_discover_fix_pause_before_prepare\"\n\n    /**\n     * 是否修复prepare前暂停未生效问题\n     */\n    val fixPauseBeforePrepare: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_FIX_PAUSE_BEFORE_PREPARE, true)\n\n\n    private const val KEY_DISCOVER_LANDSCAPE_VIDEO_RATIO =\n        \"key_discover_landscape_video_ratio\"\n\n    private const val LAND_VIDEO_RATIO = 1F / 1.2F\n\n    /**\n     * 判断是否是横屏视频的比例\n     */\n    val landScapeVideoRatio: Float\n        get() {\n            return config.getConfig(KEY_DISCOVER_LANDSCAPE_VIDEO_RATIO)?.toFloatOrNull() ?: LAND_VIDEO_RATIO\n        }\n\n    private const val KEY_DISCOVER_PORTRAIT_VIDEO_SCALE_ORIGINAL = \"key_discover_portrait_video_scale_original\"\n\n    /**\n     * 判断是否是横屏视频的比例\n     */\n    val portraitVideoScaleOriginal: Boolean\n        get() = config.getConfigBoolean(KEY_DISCOVER_PORTRAIT_VIDEO_SCALE_ORIGINAL, true)\n\n\n\n    /**\n     * 发现页引导气泡 是否 要在 10s 后隐藏\n     */\n    val discoverTipTenSecHide: Boolean\n        get() {\n            return config.getConfigBoolean(PNGSettings.KEY_DISCOVER_TIP_TEN_SEC_HIDE, true)\n        }\n}"}
{"query": "DiscoverInfoView.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.content.Context\nimport android.graphics.Color\nimport android.graphics.Typeface\nimport android.text.SpannableStringBuilder\nimport android.text.method.LinkMovementMethod\nimport android.util.AttributeSet\nimport android.view.LayoutInflater\nimport android.view.View\nimport android.widget.TextView\nimport androidx.constraintlayout.widget.ConstraintLayout\nimport com.tencent.android.qqdownloader.BuildConfig\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.component.ToastUtils\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.utils.NoLineClickSpan\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.appendSpan\nimport com.tencent.assistant.utils.isNotNullOrEmpty\nimport com.tencent.assistant.utils.setOnFilterClickListener\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.playlet.widget.ExpandLayoutTextView\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/9 16:17\n * Description:\n */\nclass DiscoverInfoView @JvmOverloads constructor(\n    context: Context,\n    attrs: AttributeSet? = null,\n    defStyleAttr: Int = 0\n) : ConstraintLayout(context, attrs, defStyleAttr) {\n\n    private val TAG = \"DiscoverInfoView\"\n    var scene: Int = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    val reporter: DiscoverRecommendReporter\n        get() = DiscoverRecommendReporter.getReporter(scene)\n    var listener: ExpandLayoutTextView.OnExpandStateChangeListener? = null\n    private val color = Color.WHITE\n    private val userNameTV by lazy { findViewById<TextView>(R.id.user_name_tv) }\n    private val descTV by lazy { findViewById<ExpandLayoutTextView>(R.id.desc_tv) }\n\n    init {\n        LayoutInflater.from(context).inflate(R.layout.discover_recommend_info_view, this, true)\n        initView()\n    }\n\n    private fun initView() {\n        descTV.setTypeface(null, Typeface.BOLD)\n        descTV.ignoreLandScreen = true\n    }\n\n    fun bindData(position: Int, itemData: DiscoveryPageRecommendItem) {\n        val authorName = itemData.introductionInfo?.authorName\n        if (authorName.isNullOrBlank()) {\n            userNameTV.visibility = View.GONE\n        } else {\n            userNameTV.visibility = View.VISIBLE\n            userNameTV.text = \"@$authorName\"\n            if (BuildConfig.DEBUG && false) {\n                userNameTV.text = \"@$authorName - ${itemData.videoInfo.vid}\"\n            }\n\n        }\n        userNameTV.setOnFilterClickListener {\n            reporter.reportBtnClick(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.NICK\n            )\n        }\n\n        descTV.setIsExpand(false)\n        val desc = itemData.introductionInfo?.title\n        val content = desc ?: \"\"\n        XLog.d(\"DiscoverInfoView\", \"bindData, content $content\")\n\n        val spannableString = SpannableStringBuilder(content)\n        itemData.introductionInfo?.tagInfo?.filter { it.tagName.isNotNullOrEmpty() && it.tagId > 0 }\n            ?.forEachIndexed { index, tagInfo ->\n                val text = if (index == 0) {\n                    \"  #${tagInfo.tagName}\"\n                } else {\n                    \" #${tagInfo.tagName}\"\n                }\n\n                spannableString.appendSpan(text, object : NoLineClickSpan(color) {\n                    override fun onClick(widget: View) {\n                        // TODO:marklima\n                        if (BuildConfig.DEBUG) {\n                            ToastUtils.show(context, \"ClickTag ${tagInfo.tagName}, tagId = ${tagInfo.tagId}\")\n                        }\n\n                        reporter.reportBtnClick(\n                            position = position,\n                            info = itemData,\n                            btnTitle = DiscoverRecommendReporter.ButtonTitle.TAG,\n                            btnStatus = null,\n                            reportContextKey = DiscoverRecommendReporter.ReportContextKey.VIDEO_REPORT_CONTEXT,\n                            DiscoverRecommendReporter.ExtendFiledKey.UNI_CARD_TAG_NAME to tagInfo.tagName,\n                            DiscoverRecommendReporter.ExtendFiledKey.UNI_CARD_TAG_ID to tagInfo.tagId.toString(),\n                        )\n                    }\n                })\n            }\n\n        descTV.setContent(spannableString, object : ExpandLayoutTextView.OnExpandStateChangeListener {\n            override fun onExpand() {\n                XLog.i(TAG, \"onExpand\")\n                listener?.onExpand()\n                reporter.reportBtnClick(\n                    position,\n                    itemData,\n                    DiscoverRecommendReporter.ButtonTitle.MORE,\n                    \"2\"\n                )\n            }\n\n            override fun onCollapse() {\n                XLog.i(TAG, \"onCollapse\")\n                listener?.onCollapse()\n                reporter.reportBtnClick(\n                    position,\n                    itemData,\n                    DiscoverRecommendReporter.ButtonTitle.MORE,\n                    \"1\"\n                )\n            }\n\n            override fun onExpandView(show: Boolean) {\n                XLog.i(TAG, \"onExpandView, show = $show\")\n                listener?.onExpandView(show)\n            }\n        })\n\n        descTV.setMovementMethod(LinkMovementMethod.getInstance())\n\n        if (descTV.isExpandViewShow) {\n            reporter.reportBtnExposure(\n                position = position,\n                info = itemData,\n                btnTitle = DiscoverRecommendReporter.ButtonTitle.MORE\n            )\n        }\n\n    }\n\n    fun isExpand(): Boolean {\n        return descTV.isExpand\n    }\n}\n"}
{"query": "DiscoverRecommendPreRenderVideo.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport android.content.Context\nimport android.widget.FrameLayout\nimport com.tencent.assistant.component.video.report.VideoPlayerLifeCycleMonitor\nimport com.tencent.assistant.component.video.view.VideoViewComponent\nimport com.tencent.assistant.component.video.view.VideoViewComponentV2\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.discover.base.manager.DiscoverBaseVideoPreRenderManager\nimport com.tencent.pangu.discover.base.manager.IVideoPreRenderVideoManager\nimport com.tencent.pangu.discover.recommend.model.getSafeVideoRatio\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\n\n\n/**\n * ********************************************************\n *    author : sarahuang\n *    date   : 2024/10/7 16:17\n *    desc   : 发现页预渲染视频组件\n *    version: 1.0\n * ********************************************************\n */\nobject DiscoverRecommendPreRenderVideo: IVideoPreRenderVideoManager {\n\n    private const val TAG = \"DiscoverPrerenderVideo\"\n    private const val SCENE = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE\n    @Volatile\n    override var isVideoPlayHasReported = false\n    private var videoUrlTime = 0L\n    private const val EXPIRED_TIME = 2 * 58 * 60 * 1000\n    private var firstVideItem: DiscoveryPageRecommendItem? = null\n\n    private var reporter = DiscoverRecommendReporter.getReporter(SCENE)\n    private var techReporter = DiscoverBeaconReport.getReporter(SCENE)\n    private val manager = DiscoverBaseVideoPreRenderManager(SCENE)\n\n    /**\n     * 取消首个视频预渲染\n     */\n    override fun cancelPreRender() {\n        manager.cancelPreRender()\n    }\n\n    /**\n     * 预渲染的播放器是否准备就绪\n     */\n    override fun isVideoPlayerReady(): Boolean {\n        return manager.isVideoPlayerReady()\n    }\n\n\n    /**\n     * 获取预渲染的播放器\n     */\n    override fun getVideoPlayer(): VideoViewComponentV2? {\n        XLog.i(TAG, \"getVideoPlayer\")\n        val videoPlayer = manager.getVideoPlayer()\n        return videoPlayer\n    }\n    /**\n     * 首个视频已经预渲染完成，调起播放时进行启播上报\n     */\n    private fun reportVideoStartPlay(videoPlayer: VideoViewComponentV2?) {\n        if (!manager.isOnFirstFrameRendering || isVideoPlayHasReported || videoPlayer == null) {\n            return\n        }\n        isVideoPlayHasReported = true\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_FRAME)\n        techReporter.reportDuration(\"discover_recommend_first_frame\", -1)\n        VideoPlayerLifeCycleMonitor.getInstance().onFirstFrameRendingBySelf(videoPlayer)\n        XLog.i(TAG, \"reportVideoStartPlay onFirstFrameRendingBySelf Report\")\n    }\n\n\n    /***\n     *  创建预渲染播放器\n     *  @param videoItem 发现页预渲染的视频信息\n     */\n    @Synchronized\n    fun createVideoPlayer(videoItem: DiscoveryPageRecommendItem) {\n        XLog.i(TAG, \"createVideoPlayer begin\")\n        val videoPlayer = manager.createVideoPlayer(\n            videoItem.videoInfo?.vid,\n            videoItem.videoInfo?.videoUrl,\n            videoItem.getSafeVideoRatio()\n        )\n        if (videoPlayer == null) {\n            return\n        }\n        firstVideItem = videoItem\n        videoUrlTime = System.currentTimeMillis()\n        videoPlayer.coverImageUrl = videoItem.videoInfo?.coverImg\n        videoPlayer.updateReportInfo(videoItem, reporter, 0)\n        XLog.i(TAG, \"createVideoPlayer end\")\n    }\n\n    /**\n     * 刷新预渲染播放器链接，播放器链接2小时有效\n     */\n    fun refreshPreVideoUrlIfNeed() {\n        XLog.i(TAG, \"refreshPreVideoUrlIfNeed()\")\n        firstVideItem?.let {\n            refreshPreVideoUrlIfNeed(it)\n        }\n    }\n\n    /**\n     * 刷新预渲染播放器链接，播放器链接2小时有效\n     */\n    private fun refreshPreVideoUrlIfNeed(videoItem: DiscoveryPageRecommendItem) {\n        XLog.i(TAG, \"topViewPlayer refreshPreVideoUrlIfNeed\")\n        val cachedTime = System.currentTimeMillis() - videoUrlTime\n        if (videoUrlTime == 0L || cachedTime < EXPIRED_TIME) {\n            XLog.i(TAG, \"not need refreshPreVideoUrl, videoUrl cached time = $cachedTime\")\n            return\n        }\n        XLog.i(TAG, \"refreshPreVideoUrl real !!\")\n        manager.updateVideoInfo(\n            videoItem.videoInfo?.vid,\n            videoItem.videoInfo?.videoUrl,\n            videoItem.getSafeVideoRatio()\n        )\n    }\n\n    /**\n     * 刷新播放器上报参数\n     */\n    private fun VideoViewComponent.updateReportInfo(\n        data: DiscoveryPageRecommendItem,\n        reporter: DiscoverRecommendReporter,\n        reportPosition: Int,\n    ) {\n        reporter.updateVideoReportModel(videoReportModel, data, reportPosition)\n    }\n\n    /***\n     *  将播放器添加到容器中\n     */\n    override fun addPreRenderVideo(videoContainer: FrameLayout): Boolean {\n        val success = manager.addPreRenderVideo(videoContainer)\n        if (success) {\n            reportVideoStartPlay(manager.getVideoPlayer())\n        }\n        return success\n    }\n\n    override fun onDestroy(context: Context) {\n        manager.reset(context)\n        isVideoPlayHasReported = false\n    }\n\n}"}
{"query": "DiscoverShareBtnDecorator.kt", "value": "package com.tencent.pangu.discover.recommend.action\n\nimport android.app.Activity\nimport android.view.View\nimport com.qq.AppService.ApplicationProxy\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.assistant.event.EventDispatcherEnum\nimport com.tencent.assistant.event.listener.UIEventListener\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.st.STConst\nimport com.tencent.assistant.st.STConstAction\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.setOnFilterClickListener\nimport com.tencent.pangu.activity.ShareBaseActivity\nimport com.tencent.pangu.component.appdetail.AppdetailFloatingDialog\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter.ExtendFiledKey.UNI_CANCEL_TYPE\nimport com.tencent.pangu.model.ShareAppModel\nimport com.tencent.pangu.share.ShareEngine\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/15 15:16\n * Description:\n */\nclass DiscoverShareBtnDecorator(\n    val btn: View,\n    val reporter: DiscoverRecommendReporter? = null,\n    val onClick: ((view: View) -> Unit?)? = null\n) {\n\n    private var position: Int = 0\n    private val TAG = \"DiscoverShareBtnDecorator\"\n    private val POP_TYPE = \"443\"\n    private var shareDialog: AppdetailFloatingDialog? = null\n    private var itemInfo: DiscoveryPageRecommendItem? = null\n    private val shareEngine: ShareEngine? =\n        (btn.context as? ShareBaseActivity)?.getShareEngine(reporter?.scene?:0)\n\n    private val uiEventListener = UIEventListener { msg ->\n        XLog.i(TAG, \"UIEventListener msg = $msg\")\n        val messageWhat = msg.what\n        XLog.i(TAG, \" share success  messageWhat = $messageWhat\")\n        when (messageWhat) {\n            EventDispatcherEnum.UI_EVENT_SHARE_SUCCESS -> {\n                XLog.i(TAG, \" share success\")\n                shareDialog?.dismiss()\n                shareEngine?.dismissLoading()\n                unregisterEvent()\n            }\n\n            EventDispatcherEnum.UI_EVENT_SHARE_FAIL -> {\n                XLog.i(TAG, \" share fail\")\n                shareDialog?.dismiss()\n                shareEngine?.dismissLoading()\n                unregisterEvent()\n            }\n\n            else -> {}\n        }\n    }\n\n    init {\n        btn.setOnFilterClickListener {\n            itemInfo?.let {\n                share(it)\n                onClick?.invoke(btn)\n            }\n        }\n    }\n\n    fun bindData(position: Int, itemInfo: DiscoveryPageRecommendItem) {\n        this.position = position\n        this.itemInfo = itemInfo\n    }\n\n    private fun share(discoveryPageRecommendItem: DiscoveryPageRecommendItem) {\n        if ((btn.context as? Activity)?.isDestroyed != false) {\n            XLog.i(TAG, \"share error! leak params \")\n            return\n        }\n        reportPopExposure()\n        buttonShareExposure(btn.context.getString(R.string.share_dialog_qq))\n        buttonShareExposure(btn.context.getString(R.string.share_dialog_qz))\n        buttonShareExposure(btn.context.getString(R.string.share_dialog_wx))\n        buttonShareExposure(btn.context.getString(R.string.share_dialog_time_line))\n        shareDialog = AppdetailFloatingDialog.newInstance(btn.context).apply {\n            setClickCancelListener { v ->\n                XLog.i(TAG, \"setClickCancelListener \")\n                val tag = v.getTag(R.id.cancel_tag) as String\n                reportPopCancel(tag)\n            }\n\n            listener = getOnFloatViewListener(discoveryPageRecommendItem)\n        }\n        shareDialog?.show()\n    }\n\n    fun isShareDialogShowing() = shareDialog?.isShowing ?: false\n\n\n    private fun getOnFloatViewListener(itemInfo: DiscoveryPageRecommendItem) =\n        object : AppdetailFloatingDialog.IOnFloatViewListener {\n            override fun shareToQQ() {\n                reportPopClickToJump(btn.context.getString(R.string.share_dialog_qq))\n                XLog.i(TAG, \"shareToQQ itemInfo = $itemInfo\")\n                shareEngine?.shareToQQ(btn.context as Activity, getShareAppModel(itemInfo))\n                registerEvent()\n            }\n\n            override fun shareToQZ() {\n                reportPopClickToJump(btn.context.getString(R.string.share_dialog_qz))\n                XLog.i(TAG, \"shareToQZ itemInfo = $itemInfo\")\n                shareEngine?.shareToQzBase(btn.context as Activity, getShareAppModel(itemInfo))\n                registerEvent()\n            }\n\n            override fun shareToWX() {\n                reportPopClickToJump(btn.context.getString(R.string.share_dialog_wx))\n                XLog.i(TAG, \"shareToWX itemInfo = $itemInfo\")\n                shareEngine?.shareToWx(btn.context as Activity, getShareAppModel(itemInfo), false)\n                registerEvent()\n            }\n\n            override fun shareToTimeLine() {\n                reportPopClickToJump(btn.context.getString(R.string.share_dialog_time_line))\n                XLog.i(TAG, \"shareToTimeLine itemInfo = $itemInfo\")\n                shareEngine?.shareToWx(btn.context as Activity, getShareAppModel(itemInfo), true)\n                registerEvent()\n            }\n        }\n\n\n    private fun buttonShareExposure(buttonTitle: String) {\n        XLog.i(TAG, \"buttonShareExposure\")\n        reporter?.reportBtnExposure(\n            position = position,\n            info = itemInfo,\n            btnTitle = buttonTitle,\n            btnStatus = null,\n            reportContextKey = DiscoverRecommendReporter.ReportContextKey.POP_REPORT_CONTEXT,\n            DiscoverRecommendReporter.ExtendFiledKey.UNI_POP_TYPE to POP_TYPE\n        )\n\n    }\n\n    private fun reportPopClickToJump(buttonTitle: String) {\n        XLog.i(TAG, \"buttonClick\")\n        reporter?.reportPopEvent(\n            position = position,\n            info = itemInfo,\n            eventCode = STConstAction.ACTION_POP_CLICK_JUMP,\n            DiscoverRecommendReporter.ExtendFiledKey.UNI_POP_TYPE to POP_TYPE,\n            STConst.UNI_BUTTON_TITLE to buttonTitle\n        )\n    }\n\n    private fun reportPopExposure() {\n        XLog.i(TAG, \"reportPopExposure\")\n        reporter?.reportPopEvent(\n            position = position,\n            info = itemInfo,\n            eventCode = STConstAction.ACTION_POP_EXPOSURE,\n            DiscoverRecommendReporter.ExtendFiledKey.UNI_POP_TYPE to POP_TYPE,\n        )\n\n    }\n\n    private fun reportPopCancel(dismissType: String) {\n        XLog.i(TAG, \"reportPopExposure dismissType = $dismissType\")\n        reporter?.reportPopEvent(\n            position = position,\n            info = itemInfo,\n            eventCode = STConstAction.ACTION_POP_CANCEL,\n            DiscoverRecommendReporter.ExtendFiledKey.UNI_POP_TYPE to POP_TYPE,\n            UNI_CANCEL_TYPE to dismissType\n        )\n    }\n\n    fun getShareAppModel(itemInfo: DiscoveryPageRecommendItem): ShareAppModel {\n        val shareBaseModel = ShareAppModel()\n        val shareData = itemInfo.interactiveInfo.shareData\n        shareBaseModel.mCustomTitle = shareData.title\n        shareBaseModel.mCustomSummary = shareData.summary\n        shareBaseModel.actionFlag = ShareAppModel.ACTION_FLAG_CUSTOM.toInt()\n        shareBaseModel.mIconUrl = shareData.iconUrl\n        shareBaseModel.mTargetUrl = shareData.jumpUrl\n        return shareBaseModel\n    }\n\n\n    private fun registerEvent() {\n        ApplicationProxy.getEventController().addUIEventListener(\n            EventDispatcherEnum.UI_EVENT_SHARE_SUCCESS, uiEventListener\n        )\n        ApplicationProxy.getEventController().addUIEventListener(\n            EventDispatcherEnum.UI_EVENT_SHARE_FAIL, uiEventListener\n        )\n    }\n\n    private fun unregisterEvent() {\n        ApplicationProxy.getEventController().removeUIEventListener(\n            EventDispatcherEnum.UI_EVENT_SHARE_SUCCESS, uiEventListener\n        )\n        ApplicationProxy.getEventController().removeUIEventListener(\n            EventDispatcherEnum.UI_EVENT_SHARE_FAIL, uiEventListener\n        )\n    }\n\n}"}
{"query": "DiscoverRecommendEngine.kt", "value": "package com.tencent.pangu.discover.recommend.model\n\nimport com.qq.taf.jce.JceStruct\nimport com.tencent.assistant.component.video.view.VideoViewComponent\nimport com.tencent.assistant.plugin.mgr.PluginInstalledManager\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendResponse\nimport com.tencent.assistant.request.AbstractRequestEngine\nimport com.tencent.assistant.request.ErrorCode\nimport com.tencent.assistant.request.RequestResult\nimport com.tencent.assistant.request.RequestType\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\n\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/2 14:04\n * @Description:发现推荐网络请求事宜\n */\nclass DiscoverRecommendEngine(val scene: Int) : AbstractRequestEngine<DiscoveryPageRecommendResponse>() {\n    override var TAG: String = \"DiscoverRecommendEngine_$scene\"\n    private val requestContext = com.tencent.assistant.request.RequestContext()\n    private val requestContextLastCache: MutableMap<String, String> = mutableMapOf()\n\n    private var time = 0L\n\n    suspend fun sendRequest(\n        requestParam: DiscoverRecommendRequestParam,\n    ): RequestResult<DiscoveryPageRecommendResponse> {\n        try {\n            // 发起请求前，暂存reportContext，用于请求到的数据没能正常刷新到列表时来恢复。\n            requestContextLastCache.clear()\n            requestContextLastCache.putAll(requestContext.getParamClone())\n            XLog.d(TAG, \"sendRequest: requestContextLastCache = $requestContextLastCache\")\n        } catch (e: Throwable) {\n            XLog.e(TAG, \"sendRequest: save cache requestContext error.\", e)\n        }\n\n        val requestType = requestParam.requestType\n        if (requestType == RequestType.FULL_REQUEST) {\n            requestContext.reset()\n        }\n\n        cancelLastRequest()\n        val request = requestParam.request\n        request.mapReqParam = (request.mapReqParam ?: HashMap<String, String>()).apply {\n            putAll(requestContext.mapReqParam)\n            putAll(requestParam.mapReqParam)\n        }\n\n        request.isOnlyRequestUrlVideo = if (DiscoverConfig.onlyRequestUrlVideo) {\n            val pluginInfo = PluginInstalledManager.get().getPlugin(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n            val isOnlyRequestUrl = pluginInfo == null\n            XLog.i(TAG, \"sendRequest: isOnlyRequestUrlVideo = $isOnlyRequestUrl\")\n            isOnlyRequestUrl\n        } else {\n            false\n        }\n\n        time = System.currentTimeMillis()\n        val result = request(request, requestType)\n        XLog.i(TAG, \"sendRequest, request=$request result = $result ,time = ${System.currentTimeMillis() - time}\")\n        if (result is RequestResult.Success) {\n            if ((result.data?.items?.size ?: 0) == 0) {\n                XLog.e(TAG, \"sendRequest success illegal data, data=${result.data}, size=${result.data?.items?.size}\")\n                return RequestResult.Failed(ErrorCode.DATA_EMPTY, data = null, result.requestType)\n            } else {\n                XLog.i(TAG, \"sendRequest success data.size = ${result.data?.items?.size}\")\n                updateRequestContext(result)\n                return RequestResult.Success(result.data, result.requestType, result.hasMoreData)\n            }\n        } else {\n            return RequestResult.Failed(result.errCode, data = null, result.requestType)\n        }\n    }\n\n    fun updateRequestContext(result: RequestResult<DiscoveryPageRecommendResponse>) {\n        result.data?.mapRspParam?.let {\n            requestContext.update(it)\n        }\n    }\n\n    override fun convertResponse(response: JceStruct?): DiscoveryPageRecommendResponse? {\n        return response as? DiscoveryPageRecommendResponse\n    }\n\n    override fun hasMoreData(rsp: DiscoveryPageRecommendResponse?): Boolean {\n        XLog.i(TAG, \"hasMoreData:${rsp?.hasNext}\")\n        return rsp?.hasNext ?: true\n    }\n\n    fun restoreRequestContext() {\n        if (requestContextLastCache.isEmpty()) {\n            XLog.i(TAG, \"restoreRequestContext: reset\")\n            requestContext.reset()\n            return\n        }\n        XLog.i(TAG, \"restoreRequestContext: update = $requestContextLastCache\")\n        requestContext.update(requestContextLastCache)\n    }\n}"}
{"query": "DiscoverPreRenderVideo.kt", "value": ""}
{"query": "DiscoverRecommendRequestParam.kt", "value": "package com.tencent.pangu.discover.recommend.model\n\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendRequest\nimport com.tencent.assistant.request.RequestType\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: mark\n * Email: <EMAIL>\n * Date: 2024/7/2 15:47\n * Description: 发现推荐请求参数\n */\ndata class DiscoverRecommendRequestParam(\n    /**\n     * 请求参数\n     */\n    val request: DiscoveryPageRecommendRequest = DiscoveryPageRecommendRequest(),\n\n    /**\n     * 是否加载更多\n     */\n    val isLoadMore: Boolean = false,\n\n    /**\n     * 请求类型\n     * @see RequestType\n     */\n    val requestType: RequestType = if (isLoadMore) {\n        RequestType.LOAD_MORE\n    } else {\n        RequestType.FULL_REQUEST\n    },\n\n    /**\n     * 是否刷新\n     */\n    val isRefresh: Boolean = false,\n\n    /**\n     * 是否重试\n     */\n    val isRetry: Boolean = false,\n\n    /**\n     * 是否使用缓存\n     */\n    val useCacheData: Boolean = false,\n\n    /**\n     * 是否缓存\n     */\n    val cacheData: Boolean = false,\n\n    /**\n     * 场景\n     */\n    val scene: Int = 0,\n\n    /**\n     * 请求扩展参数: key=>value(可扩展)。比如：sourceScene，vid，material_id, pageIndex等等\n     */\n    val mapReqParam: MutableMap<String, String> = mutableMapOf(),\n\n    /**\n     * 是否是第一次请求\n     */\n    val isFirstRequest: Boolean = !isRefresh && !isLoadMore && !isRetry && useCacheData\n)\n"}
{"query": "DiscoverDownloadBtnCraftStyle.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.graphics.Color\nimport com.tencent.assistant.component.DownloadButton\nimport com.tencent.assistant.component.download.ICraftDownloadButton\nimport com.tencent.assistant.component.download.style.BaseCraft\nimport com.tencent.assistant.utils.XLog\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/7/5 16:36\n * Description:\n */\nval PRIMARY_DARK_BLUE = Color.parseColor(\"#400080FF\")\nval PRIMARY_BLUE = Color.parseColor(\"#0080FF\")\nval PRIMARY_WHITE = Color.parseColor(\"#ffffff\")\n\nclass DiscoverDownloadBtnCraftStyle(val textSize: Int = 12, val width: Int = 60, val height: Int = 24) : BaseCraft() {\n\n    private val TAG = \"DiscoverDownloadBtnCraftStyle\"\n\n    override fun overrideWaitWifiText(button: ICraftDownloadButton): String {\n        XLog.d(TAG, \"overrideWaitWifiText\")\n        return \"等WiFi\"\n    }\n\n\n    override fun attach(button: ICraftDownloadButton) {\n\n        button.setTextSize(textSize)\n        // 正常状态下设置，深蓝色背景白色文字\n        button.setNormalBgColor(PRIMARY_BLUE)\n        button.setNormalStrokeColor(Color.TRANSPARENT)\n        button.setNormalTextColor(PRIMARY_WHITE)\n\n        // 进度内颜色：蓝色\n        button.setBarInProgressColor(PRIMARY_BLUE)\n        // 进度外颜色：深蓝色\n        button.setBarOutProgressColor(PRIMARY_DARK_BLUE)\n        // 进度内文字颜色：白色\n        button.setTvInProgressColor(PRIMARY_WHITE)\n        // 进度外文字颜色：白色\n        button.setTvOutProgressColor(PRIMARY_WHITE)\n\n        // 下载完的蓝色背景白色文字\n        button.setDownloadedBgColor(PRIMARY_BLUE)\n        button.setDownloadedTextColor(PRIMARY_WHITE)\n\n        // 已安装蓝色背景白色文字\n        button.setInstalledBgColor(PRIMARY_BLUE)\n        button.setInstalledTextColor(PRIMARY_WHITE)\n        button.setCornerRadiusDp(16F)\n    }\n\n    override fun resetSize(button: ICraftDownloadButton) {\n        val downloadButton = (button as? DownloadButton) ?: return\n        val length = downloadButton.mChangeText?.currentSetTxt?.length ?: 0\n        XLog.i(TAG, \"resetSize, text: ${downloadButton.mChangeText?.currentSetTxt}\")\n        when (length) {\n            4 -> {\n                button.setCraftSize(height, 64.coerceAtLeast(width))\n            }\n\n            3 -> {\n                button.setCraftSize(height, 48.coerceAtLeast(width))\n            }\n\n            else -> {\n                button.setCraftSize(height, width)\n            }\n        }\n    }\n}"}
{"query": "DiscoverRecommendViewHolder.kt", "value": "package com.tencent.pangu.discover.recommend.wdiget\n\nimport android.view.ViewGroup\nimport com.tencent.android.qqdownloader.R\nimport com.tencent.pangu.discover.base.view.BaseVideoViewHolder\n\n/**\n * 发现推荐页 VH\n */\nclass DiscoverRecommendViewHolder(parent: ViewGroup, scene: Int) :\n    BaseVideoViewHolder(parent, R.layout.layout_discover_recommend_feed_item, scene) {\n}"}
{"query": "DiscoverBeaconReport.kt", "value": "package com.tencent.pangu.discover.recommend.report\n\nimport com.tencent.assistant.beacon.api.IBeaconReportService\nimport com.tencent.assistant.component.video.view.VideoViewComponent\nimport com.tencent.assistant.logger.ILogUploadListener\nimport com.tencent.assistant.net.NetworkUtil\nimport com.tencent.assistant.plugin.PluginHelper\nimport com.tencent.assistant.plugin.mgr.PluginInstalledManager\nimport com.tencent.assistant.raft.TRAFT\nimport com.tencent.assistant.utils.HandlerUtils\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.concurrentHashMapOf\nimport com.tencent.pangu.discover.recommend.manager.DiscoverConfig\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendDataCacheManager\nimport com.tencent.pangu.discover.recommend.manager.DiscoverRecommendVideoPreloadManager\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_ERROR\nimport com.tencent.pangu.discover.videofeed.manager.VideoFeedDataCacheManager\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/26 16:41\n * Description:\n */\nclass DiscoverBeaconReport(val scene: Int) {\n\n\n    var mid: Long  = 0L\n\n    companion object {\n\n        const val MINUTE = 60 * 1000\n        const val HOUR = 60 * MINUTE\n        const val DAY = 24 * HOUR\n\n        private val reporters = concurrentHashMapOf<Int, DiscoverBeaconReport>()\n\n        /**\n         * 获取上报器\n         * scene\n         * @see DiscoverRecommendReporter.Scene\n         */\n        fun getReporter(scene: Int): DiscoverBeaconReport {\n            return reporters.getOrPut(scene) { DiscoverBeaconReport(scene) }\n        }\n    }\n\n    private  val TAG = \"DiscoverRecommendBeaconReport_$scene\"\n\n    private val tagMap = LinkedHashMap<String, Long>()\n    private val extraMap = concurrentHashMapOf<String, String>().apply {\n        put(Key.SCENE, scene.toString())\n    }\n\n\n    object Event {\n        const val DISCOVER_RECOMMEND_PRELOAD_EVENT = \"discover_recommend_start_event\"\n        const val DISCOVER_FIRST_VIDEO_BIND = \"discover_recommend_first_video_on_bind\"\n        const val DISCOVER_FIRST_VIDEO_START = \"discover_recommend_first_video_start\"\n        const val DISCOVER_FIRST_VIDEO_ON_START = \"discover_recommend_first_video_on_play_start\"\n        const val DISCOVER_FIRST_VIDEO_PREPARED = \"discover_recommend_first_video_on_prepared\"\n        const val DISCOVER_FIRST_FRAME = \"discover_recommend_first_frame\"\n        const val DISCOVER_RESUME = \"discover_recommend_resume\"\n\n        const val DISCOVER_RECOMMEND_FIRST_PAGE_DATA_LOAD = \"discover_recommend_first_page_data_load\"\n        const val DISCOVER_REFRESH = \"discover_recommend_refresh\"\n    }\n\n\n    object PreloadKey {\n        // 预加载开始\n        const val PRELOAD_START = \"DiscoverPreloadManager_preloadStart\"\n\n        // 没有发现入口\n        const val PRELOAD_NO_ENTRANCE = \"DiscoverPreloadManager_preloadNoEntrance\"\n\n        // 检测视频预加载器开始\n        const val PRELOAD_CHECK_MANAGER_START = \"DiscoverVideoPreloadManager_checkPreloadManagerStart\"\n\n        // 检测视频预加载器失败\n        const val PRELOAD_CHECK_MANAGER_SUCCESS = \"DiscoverVideoPreloadManager_checkPreloadManagerSuccess\"\n\n        // 检测视频预加载器成功\n        const val PRELOAD_CHECK_MANAGER_ERROR = \"DiscoverVideoPreloadManager_checkPreloadManagerError\"\n\n\n        // 检测视频预加载器结束\n        const val PRELOAD_CHECK_MANAGER_END = \"DiscoverVideoPreloadManager_checkPreloadManagerEnd\"\n\n        // 预下载数据 开始\n        const val PRELOAD_DATA_START = \"PreloadData_start\"\n\n        // 预下载数据 成功\n        const val PRELOAD_DATA_SUCCESS = \"PreloadData_success\"\n\n        // 预下载数据 失败\n        const val PRELOAD_DATA_ERROR = \"PreloadData_error\"\n\n        // 预下载数据 返回为空\n        const val PRELOAD_DATA_EMPTY = \"PreloadData_empty\"\n\n        // 预下载数据 结束\n        const val PRELOAD_DATA_END = \"PreloadData_end\"\n\n\n        // 预加载首个视频开始\n        const val PRELOAD_VIDEO_START = \"DiscoverVideoPreloadManager_preloadVideoStart\"\n\n        // 预加载首个视频开始成功\n        const val PRELOAD_VIDEO_START_SUCCESS = \"DiscoverVideoPreloadManager_preloadVideoStartSuccess\"\n\n        // 预加载首个视频开始失败\n        const val PRELOAD_VIDEO_START_ERROR = \"DiscoverVideoPreloadManager_preloadVideoStartError\"\n\n        // 预加载首个视频成功\n        const val PRELOAD_VIDEO_SUCCEED = \"DiscoverVideoPreloadManager_preloadVideoSucceed\"\n\n        // 预加载首个视频失败\n        const val PRELOAD_VIDEO_FAILED = \"DiscoverVideoPreloadManager_preloadVideoFailed\"\n\n        // 预加载首个视频停止\n        const val PRELOAD_VIDEO_STOP = \"DiscoverVideoPreloadManager_preloadVideoStop\"\n\n        // 预加载首个视频结束\n        const val PRELOAD_VIDEO_END = \"DiscoverVideoPreloadManager_preloadVideoEnd\"\n\n\n        // 发现页 attach\n        const val DISCOVER_PAGE_ATTACH = \"DiscoverTabFragment_onAttach\"\n\n        // 发现页 create\n        const val DISCOVER_PAGE_CREATE = \"DiscoverTabFragment_onCreate\"\n\n        // 发现页 createView\n        const val DISCOVER_PAGE_CREATE_VIEW = \"DiscoverTabFragment_onCreateView\"\n\n        // 发现页 viewCreated\n        const val DISCOVER_PAGE_VIEW_CREATED = \"DiscoverTabFragment_onViewCreated\"\n\n        // 发现页 resume\n        const val DISCOVER_PAGE_RESUME = \"DiscoverTabFragment_onResume\"\n\n        const val DISCOVER_PAGE_PAUSE = \"DiscoverTabFragment_onPause\"\n\n\n        // 推荐页 resume\n        const val RECOMMEND_PAGE_ATTACH = \"DiscoverRecommendFragment_onAttach\"\n\n        // 推荐页 create\n        const val RECOMMEND_PAGE_CREATE = \"DiscoverRecommendFragment_onCreate\"\n\n        // 推荐页 createView\n        const val RECOMMEND_PAGE_CREATE_VIEW = \"DiscoverRecommendFragment_onCreateView\"\n\n        // 推荐页 viewCreated\n        const val RECOMMEND_PAGE_VIEW_CREATED = \"DiscoverRecommendFragment_onViewCreated\"\n\n        // 推荐页 resume\n        const val RECOMMEND_PAGE_RESUME = \"DiscoverRecommendFragment_onResume\"\n\n        // 推荐页 开始拉取数据\n        const val RECOMMEND_PAGE_FIRST_LOAD_DATA_START = \"DiscoverRecommendFragment_loadDataStart\"\n\n        // 推荐页 数据回包\n        const val RECOMMEND_PAGE_FIRST_LOAD_DATA_END = \"DiscoverRecommendFragment_loadDataEnd\"\n\n        // 推荐页 数据绑定开始\n        const val RECOMMEND_PAGE_FIRST_DATA_BIND_START = \"DiscoverRecommendFragment_dataBindStart\"\n\n        // 推荐页 数据绑定结束\n        const val RECOMMEND_PAGE_FIRST_DATA_BIND_END = \"DiscoverRecommendFragment_dataBindEnd\"\n\n        // 推荐页 刷新数据\n        const val RECOMMEND_PAGE_REFRESH = \"DiscoverRecommendFragment_refresh\"\n\n        // 推荐页 首个itemBind开始\n        const val RECOMMEND_PAGE_FIRST_ITEM_BIND_START = \"DiscoverRecommendFragment_onFirstItemBindStart\"\n\n        // 推荐页 首个itemBind结束\n        const val RECOMMEND_PAGE_FIRST_ITEM_BIND_END = \"DiscoverRecommendFragment_onFirstItemBindEnd\"\n\n        // 推荐页  pause\n        const val RECOMMEND_PAGE_PAUSE = \"DiscoverRecommendFragment_onPause\"\n\n        // 首个视频 bind 开始\n        const val VIDEO_BIND_START = \"DiscoverRecommendFragment_bindVideoStart\"\n\n        // 首个视频 bind 结束\n        const val VIDEO_BIND_END = \"DiscoverRecommendFragment_bindVideoEnd\"\n\n        // 首个视频 start 前\n        const val VIDEO_BEFORE_START = \"DiscoverRecommendFragment_beforeStartVideo\"\n\n        // 首个视频 attachToWindow\n        const val VIDEO_ATTACH_TO_WINDOW = \"DiscoverRecommendFragment_videoAttachToWindow\"\n\n        // 首个视频 startPlay\n        const val START_VIDEO = \"DiscoverRecommendFragment_startVideo\"\n\n        // 首个视频 playStart\n        const val VIDEO_PLAY_START = \"DiscoverRecommendFragment_onPlayStart\"\n\n        // 首个视频 getVInfo 开始, 仅 vid 视频上报\n        const val VIDEO_GET_V_INFO_START = \"DiscoverRecommendFragment_getVInfoStart\"\n\n        // 首个视频 getVInfo 结束, 仅 vid 视频上报\n        const val VIDEO_GET_V_INFO_END = \"DiscoverRecommendFragment_getVInfoEnd\"\n\n        // 首个视频 prepared\n        const val VIDEO_PREPARED = \"DiscoverRecommendFragment_onPrepared\"\n\n        // 首个视频 首帧\n        const val VIDEO_FIRST_FRAME = \"DiscoverRecommendFragment_onFirstFrameRending\"\n\n        // 首个视频 失败\n        const val VIDEO_FIRST_ERROR = \"DiscoverRecommendFragment_onFirstVideoError\"\n\n\n        // 视频组件预创建开始\n        const val VIDEO_COMPONENT_PRE_CREATE_START =\n            \"VideoComponentCacheManager_videoVideoPreCreateStart\"\n\n        // 视频组件预创建结束\n        const val VIDEO_COMPONENT_PRE_CREATE_END =\n            \"VideoComponentCacheManager_videoViewPreCreateEnd\"\n\n        // 视频组件首次获取\n        const val VIDEO_COMPONENT_FIRST_GET = \"VideoComponentCacheManager_videoViewFirstGet\"\n\n\n        // 视频缓存过期\n        const val VIDEO_CACHE_EXPIRED =\n            \"DiscoverVideoPreloadManager_videoCacheExpired\"\n    }\n\n    object ExtraMapKey {\n        const val PRELOAD_DATA_ERROR_CODE = \"PreloadData_errorCode\"\n        const val PRELOAD_VIDEO_START_RESULT_CODE = \"PreloadVideoStart_resultCode\"\n        const val VIDEO_FIRST_ERROR_CODE = \"OnFirstVideoError_errorCode\"\n        const val VIDEO_COMPONENT_FIRST_GET_HIT_CACHE = \"VideoViewFirstGet_hitCache\"\n        const val VIDEO_PRELOAD_END_TO_DISCOVER_FRAGMENT_ATTACH = \"PreloadVideoEnd_to_DiscoverFragmentAttach\"\n        const val DISCOVER_FRAGMENT_ATTACH_TO_PAUSE = \"DiscoverFragment_attach_to_pause\"\n    }\n\n\n    object PluginKey {\n        const val PLUGIN_STATE = \"plugin_state\"\n        const val PLUGIN_VERSION = \"plugin_version\"\n        const val PLUGIN_INSTALLED = \"plugin_installed\"\n    }\n\n    object Key {\n        const val SUCCESS = \"success\"\n        const val ERROR_CODE = \"error_code\"\n        const val VID = \"vid\"\n        const val VIDEO_URL = \"video_url\"\n        const val FIRST_RESUME = \"first_resume\"\n        const val HAS_CACHE = \"has_cache\"\n        const val NET_ACTIVE = \"net_active\"\n        const val SCENE = \"scene\"\n\n    }\n\n    private var isReported = false\n\n\n    fun addTag(tag: String, vararg extraData: Pair<String, String>) {\n        if (isReported) {\n            return\n        }\n\n        val time = System.currentTimeMillis()\n        if (tagMap.contains(tag)) {\n            XLog.i(TAG, \"addTag:$tag already exist\")\n            return\n        }\n\n        XLog.i(TAG, \"addTag:$tag\")\n\n        when (tag) {\n            // 检查预下载管理器时, 上报插件状态\n            PreloadKey.PRELOAD_CHECK_MANAGER_START -> {\n                addPluginState(tag)\n            }\n\n            // 预下载数据时, 上报网络状态\n            PreloadKey.PRELOAD_DATA_START -> {\n                addNetState(tag)\n            }\n\n            // 预下载失败时, 上报网络状态\n            PreloadKey.PRELOAD_DATA_ERROR -> {\n                addNetState(tag)\n            }\n\n            // 预下载视频时, 上报插件状态, 网络状态, 首个视频状态\n            PreloadKey.PRELOAD_VIDEO_START -> {\n                addNetState(tag)\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n            }\n\n            // 预下载失败时, 上报插件状态, 网络状态\n            PreloadKey.PRELOAD_VIDEO_FAILED -> {\n                addNetState(tag)\n                addPluginState(tag)\n            }\n\n            // 预创建视频组件时, 上报插件状态\n            PreloadKey.VIDEO_COMPONENT_PRE_CREATE_START -> {\n                addPluginState(tag)\n            }\n\n            // 推荐页可见\n            PreloadKey.RECOMMEND_PAGE_RESUME -> {\n                addTagExtra(Key.FIRST_RESUME, !tagMap.contains(PreloadKey.RECOMMEND_PAGE_RESUME))\n            }\n\n            // 首次开始拉取数据时, 上报是否有缓存, 网络状态\n            PreloadKey.RECOMMEND_PAGE_FIRST_LOAD_DATA_START -> {\n                addTagExtra(\"${tag}_${Key.HAS_CACHE}\", hasCacheResult())\n                addNetState(tag)\n            }\n\n            // 首次数据回包时, 上报插件状态, 网络状态\n            PreloadKey.RECOMMEND_PAGE_FIRST_LOAD_DATA_END -> {\n                addPluginState(tag)\n                addNetState(tag)\n            }\n\n            // 首次视频开始绑定时, 上报插件状态, 视频状态\n            PreloadKey.VIDEO_BIND_START -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n            }\n\n            // 视频调用播放前, 上报插件状态, 视频状态\n            PreloadKey.VIDEO_BEFORE_START -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n            }\n\n            // 视频调用播放时, 上报插件状态, 视频状态\n            PreloadKey.START_VIDEO -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n            }\n\n            // 视频真正调用播放时, 上报插件状态, 视频状态, 网络状态\n            PreloadKey.VIDEO_PLAY_START -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n                addNetState(tag)\n            }\n\n            // 视频 prepared 时, 上报插件状态, 视频状态, 网络状态\n            PreloadKey.VIDEO_PREPARED -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n                addNetState(tag)\n            }\n\n            PreloadKey.VIDEO_FIRST_FRAME -> {\n                addFirstVideoStatus(tag)\n                addPluginState(tag)\n                addNetState(tag)\n            }\n\n        }\n        tagMap[tag] = time\n        extraMap.putAll(extraData)\n    }\n\n    fun removeTag(tag: String) {\n        tagMap.remove(tag)\n    }\n\n    private fun addFirstVideoStatus(tag: String) {\n        addTagExtra(\"${tag}_videoPreloadStatus\", DiscoverRecommendVideoPreloadManager.firstVideoPreloadStatus.toString())\n        addTagExtra(\"${tag}_videoPreloadSize\", DiscoverRecommendVideoPreloadManager.firstVideoDownloadSize.toString())\n        addTagExtra(\"${tag}_firstVideoStatus\", DiscoverRecommendVideoPreloadManager.firstVideoStatus().toString())\n    }\n\n\n    private fun addPluginState(tag: String) {\n        val time = System.currentTimeMillis()\n        val map = HashMap<String, String>()\n        val state = PluginHelper.requireInstall(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n        map[\"${tag}_${PluginKey.PLUGIN_STATE}\"] = state.toString()\n        val pluginInfo = PluginInstalledManager.get().getPlugin(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n        if (pluginInfo != null) {\n            map[\"${tag}_${PluginKey.PLUGIN_INSTALLED}\"] = getStatus(true)\n            map[\"${tag}_${PluginKey.PLUGIN_VERSION}\"] = pluginInfo.version.toString()\n        } else {\n            map[\"${tag}_$PluginKey.PLUGIN_INSTALLED\"] = getStatus(false)\n            map[\"${tag}_${PluginKey.PLUGIN_VERSION}\"] = \"-1\"\n        }\n        extraMap.putAll(map)\n        XLog.d(TAG, \"addPluginState, state:$state ,time:${System.currentTimeMillis() - time}\")\n    }\n\n    private fun addNetState(tag: String) {\n        extraMap[\"${tag}_${Key.NET_ACTIVE}\"] = getStatus(NetworkUtil.isNetworkActive())\n    }\n\n\n    fun addTagExtra(key: String, value: String) {\n        extraMap[key] = value\n    }\n\n    fun addTagExtra(key: String, value: Boolean) {\n        addTagExtra(key, getStatus(value))\n    }\n\n    fun getStatus(success: Boolean) = if (success) \"1\" else \"0\"\n\n\n    fun reportTag() {\n        if (isReported) {\n            return\n        }\n        isReported = true\n\n        val startTime = tagMap[PreloadKey.DISCOVER_PAGE_ATTACH] ?: 0\n        if (startTime == 0L) {\n            XLog.e(TAG, \"reportTag error resume time = 0\")\n            return\n        }\n\n        val pauseTime = System.currentTimeMillis()\n        tagMap[PreloadKey.DISCOVER_PAGE_PAUSE] = pauseTime\n\n        val reportMap = tagMap.map { (k, v) -> k to (v - startTime).toString() }.toMap().toMutableMap().apply {\n            putAll(extraMap)\n        }\n\n\n        // 视频下载完成到进入发现页时间\n        val discoverFragmentAttach = tagMap[PreloadKey.DISCOVER_PAGE_ATTACH] ?: 0L\n        val preloadVideoEnd = tagMap[PreloadKey.PRELOAD_VIDEO_END] ?: 0\n        if (discoverFragmentAttach > 0L && preloadVideoEnd > 0L) {\n            reportMap[ExtraMapKey.VIDEO_PRELOAD_END_TO_DISCOVER_FRAGMENT_ATTACH] =\n                (discoverFragmentAttach - preloadVideoEnd).toString()\n        }\n\n        // 发现页 attach - pause 时间\n        reportMap[ExtraMapKey.DISCOVER_FRAGMENT_ATTACH_TO_PAUSE] = (pauseTime - startTime).toString()\n        TRAFT.get(IBeaconReportService::class.java)\n            .onUserAction(Event.DISCOVER_RECOMMEND_PRELOAD_EVENT, reportMap, true)\n        printTag()\n\n\n        safeUpload0VVLog(pauseTime, startTime)\n\n    }\n\n    private fun safeUpload0VVLog(pauseTime: Long, startTime: Long) {\n        HandlerUtils.getDefaultHandler().postDelayed({\n            tryUpload0VVLog(pauseTime, startTime)\n        }, 10 * 1000)\n    }\n\n\n    private fun tryUpload0VVLog(pauseTime: Long, startTime: Long) {\n        if (!DiscoverConfig.upload0VVLog) {\n            XLog.i(TAG, \"uploadLog return upload0VVLog switch off\")\n            return\n        }\n\n        if (pauseTime - startTime < 1000) {\n            XLog.i(TAG, \"uploadLog return time too short\")\n            return\n        }\n\n        if (tagMap.containsKey(PreloadKey.VIDEO_FIRST_FRAME)) {\n            XLog.i(TAG, \"uploadLog return videoFirstFrame exist\")\n            return\n        }\n\n        if (!tagMap.containsKey(PreloadKey.PRELOAD_START)) {\n            XLog.i(TAG, \"uploadLog return preloadStart not exist\")\n            return\n        }\n\n\n        val currentTime = System.currentTimeMillis()\n        val preloadStartTime = tagMap[PreloadKey.PRELOAD_START] ?: 0L\n\n        if (currentTime - preloadStartTime > DAY) {\n            XLog.i(TAG, \"uploadLog return time too long\")\n            return\n        }\n\n        val pluginInfo = PluginInstalledManager.get().getPlugin(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n        if (pluginInfo == null) {\n            XLog.i(TAG, \"uploadLog return pluginInfo null\")\n            return\n        }\n\n        if (pluginInfo.version < 112) {\n            XLog.i(TAG, \"uploadLog return pluginInfo version too low: ${pluginInfo.version}\")\n            return\n        }\n\n\n        XLog.i(TAG, \"uploadLog start\")\n        val startTimestamp = preloadStartTime - HOUR\n        val endTimestamp = currentTime + MINUTE\n        XLog.uploadLog(\"OVV自动上报\", startTimestamp, endTimestamp, object : ILogUploadListener {\n            override fun onStart() {\n                XLog.i(TAG, \"uploadLog onStart\")\n            }\n\n            override fun onProgress(percent: Int) {\n                XLog.i(TAG, \"uploadLog onProgress $percent\")\n            }\n\n            override fun onSuccess() {\n                XLog.i(TAG, \"uploadLog onSuccess\")\n            }\n\n            override fun onFailure(reason: Int) {\n                XLog.i(TAG, \"uploadLog onFailure: $reason\")\n            }\n        })\n\n    }\n\n\n    private fun printTag() {\n        val builder = StringBuilder(\"DiscoverPreloadManager--------------------\")\n        val resumeTime = tagMap[PreloadKey.DISCOVER_PAGE_ATTACH] ?: 0\n        var lastTime = 0L\n        tagMap.forEach {\n            builder.append(\"\\n\")\n            builder.append(\"[\")\n            builder.append(it.key)\n            builder.append(\"]\")\n            builder.append(\", totalTime=[\")\n            builder.append(it.value - resumeTime)\n            builder.append(\"], time=[\")\n            builder.append((it.value - lastTime).toInt())\n            builder.append(\"]\")\n            lastTime = it.value\n        }\n        extraMap.forEach {\n            builder.append(\"\\n\")\n            builder.append(\"[\")\n            builder.append(it.key)\n            builder.append(\"]\")\n            builder.append(\", value=[\")\n            builder.append(it.value)\n            builder.append(\"]\")\n        }\n        builder.append(\"\\n\")\n\n\n        builder.append(\"DiscoverPreloadManager++++++++++++++++++++\")\n        XLog.i(TAG, builder.toString())\n    }\n\n\n    /***************  分点上报分割线  ***************/\n\n    var time = -1L\n\n    fun reportRefresh() {\n        time = System.currentTimeMillis()\n        reportDuration(Event.DISCOVER_REFRESH, 0)\n    }\n\n\n    fun reportStartFirstLoadData() {\n        if (time < 0) {\n            XLog.i(TAG, \"reportFirstDataLoad, time < 0 ,return\")\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(\n            Event.DISCOVER_RECOMMEND_FIRST_PAGE_DATA_LOAD, duration, Key.HAS_CACHE to getStatus\n                (hasCacheResult())\n        )\n    }\n\n    private fun hasCacheResult() = if (scene == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE) {\n        DiscoverRecommendDataCacheManager.hasCachedResult()\n    } else {\n        VideoFeedDataCacheManager.hasCachedResult(mid)\n    }\n\n\n    fun reportFirstDataLoad() {\n        if (time < 0) {\n            XLog.i(TAG, \"reportFirstDataLoad, time < 0 ,return\")\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(Event.DISCOVER_RECOMMEND_FIRST_PAGE_DATA_LOAD, duration)\n    }\n\n\n    fun reportPageResume(position: Int) {\n        if (position > 0) {\n            time = -1L\n            return\n        }\n        time = System.currentTimeMillis()\n        refreshPluginInfo()\n        reportDuration(\n            Event.DISCOVER_RESUME,\n            0\n        )\n    }\n\n\n    fun reportVideoBind() {\n        if (time < 0) {\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(Event.DISCOVER_FIRST_VIDEO_BIND, duration)\n    }\n\n\n    fun reportStartVideo(success: Boolean) {\n        if (time < 0) {\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(\n            Event.DISCOVER_FIRST_VIDEO_START,\n            duration, Key.SUCCESS to getStatus(success)\n        )\n    }\n\n    fun reportVideoOnStart() {\n        if (time < 0) {\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(Event.DISCOVER_FIRST_VIDEO_ON_START, duration)\n    }\n\n    fun reportVideoPrepared() {\n        if (time < 0) {\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(Event.DISCOVER_FIRST_VIDEO_PREPARED, duration)\n    }\n\n    fun reportFistFrame() {\n        if (time < 0) {\n            XLog.i(TAG, \"reportFistFrame, time < 0, return\")\n            return\n        }\n        val duration = System.currentTimeMillis() - time\n        reportDuration(Event.DISCOVER_FIRST_FRAME, duration)\n        time = -1L\n    }\n\n\n    fun reportVideoError(errorCode: Int) {\n        if (time < 0) {\n            return\n        }\n\n        val duration = System.currentTimeMillis() - time\n        reportDuration(VIDEO_FIRST_ERROR, duration, Key.ERROR_CODE to errorCode.toString())\n    }\n\n\n    fun reportDuration(event: String, duration: Long, vararg extraData: Pair<String, String>) {\n        val reportMap = getCommonExtra().apply {\n            put(\"duration\", duration.toString())\n            putAll(extraData)\n        }\n        report(event, reportMap)\n    }\n\n    private fun getCommonExtra(): HashMap<String, String> {\n        val reportMap = HashMap<String, String>().apply {\n            put(Key.NET_ACTIVE, getStatus(NetworkUtil.isNetworkActive()))\n            put(\"videoPreloadStatus\", DiscoverRecommendVideoPreloadManager.firstVideoPreloadStatus.toString())\n            put(\"videoPreloadSize\", DiscoverRecommendVideoPreloadManager.firstVideoDownloadSize.toString())\n            put(\"firstVideoStatus\", DiscoverRecommendVideoPreloadManager.firstVideoStatus().toString())\n            putAll(getPluginInfo())\n            put(Key.SCENE, scene.toString())\n        }\n        return reportMap\n    }\n\n    fun report(event: String, reportMap: HashMap<String, String>) {\n        XLog.i(TAG, \"report event=$event, reportMap=$reportMap\")\n        TRAFT.get(IBeaconReportService::class.java)\n            .onUserAction(event, reportMap, true)\n    }\n\n    private val pluginInfoMap = mutableMapOf<String, String>()\n\n    private fun getPluginInfo(refresh: Boolean = false): Map<String, String> {\n        if (refresh || pluginInfoMap.isEmpty()) {\n            refreshPluginInfo()\n        }\n        return pluginInfoMap\n    }\n\n    private fun refreshPluginInfo() {\n        val map = HashMap<String, String>()\n        val state = PluginHelper.requireInstall(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n        map[PluginKey.PLUGIN_STATE] = state.toString()\n        val pluginInfo = PluginInstalledManager.get().getPlugin(VideoViewComponent.PLUGIN_VIDEO_PACKAGE_NAME)\n        if (pluginInfo != null) {\n            map[PluginKey.PLUGIN_INSTALLED] = getStatus(true)\n            map[PluginKey.PLUGIN_VERSION] = pluginInfo.version.toString()\n        } else {\n            map[PluginKey.PLUGIN_INSTALLED] = getStatus(false)\n            map[PluginKey.PLUGIN_VERSION] = \"-1\"\n        }\n        pluginInfoMap.clear()\n        pluginInfoMap.putAll(map)\n    }\n\n    fun reset() {\n        tagMap.clear()\n        extraMap.clear()\n        extraMap[Key.SCENE] = scene.toString()\n        mid = 0\n        isReported = false\n    }\n\n\n}"}
{"query": "DiscoverRecommendVideoPreloadManager.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport com.tencent.assistant.component.video.VideoPreLoader\nimport com.tencent.assistant.component.video.VideoPreLoader.PreloadStrategy\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendVideoInfo\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.isNotNullOrEmpty\nimport com.tencent.assistant.utils.runOnUiThread\nimport com.tencent.pangu.discover.base.manager.DiscoverVideoPreloadManager\nimport com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport kotlinx.coroutines.CoroutineScope\nimport kotlinx.coroutines.Dispatchers\nimport kotlinx.coroutines.launch\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/15 16:46\n * Description:\n */\nobject DiscoverRecommendVideoPreloadManager {\n\n    private val TAG = \"DiscoverRecommendVideoPreloadManager\"\n\n    object VideoStatus {\n        const val IS_NULL = 0\n        const val IS_VID = 1\n        const val IS_URL = 2\n        const val BOTH_NULL = 3\n    }\n\n    var firstVideoProgress = 0\n    var firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_UNKNOWN\n    // kb\n    var firstVideoDownloadSize = 0L\n        get() = field / 1024\n\n\n    object PreloadStatus {\n        const val PRELOAD_STATUS_UNKNOWN = -1\n        const val PRELOAD_STATUS_SUCCESS = 1\n        const val PRELOAD_STATUS_FAILED = 2\n        const val PRELOAD_STATUS_STOP = 3\n        const val PRELOAD_STATUS_PRELOADING = 4\n    }\n\n\n    @Volatile\n    private var firstVideoInfo: DiscoveryPageRecommendVideoInfo? = null\n\n    @Volatile\n    private var firstVideoItem: DiscoveryPageRecommendItem? = null\n\n    @Volatile\n    private var firstVideoPreloadTime = 0L\n\n    @Volatile\n    private var isFirstVideoPreloadStarted = false\n\n    private val techReporter = DiscoverBeaconReport.getReporter(DiscoverRecommendReporter.Scene.RECOMMEND_SCENE)\n\n    private val preloadManager = DiscoverVideoPreloadManager(DiscoverRecommendReporter.Scene.RECOMMEND_SCENE)\n\n\n    // 尝试预下载第一个视频\n    private fun tryPreloadFirstVideo() {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"tryPreloadFirstVideo switch off return\")\n            return\n        }\n        XLog.i(TAG, \"tryPreloadFirstVideo firstVideItem = $firstVideoItem\")\n\n        // TODO:marklima 没判断插件状态\n        firstVideoItem?.let {\n            runOnUiThread {\n                DiscoverRecommendPreRenderVideo.createVideoPlayer(it)\n            }\n        }\n\n        if (firstVideoInfo == null) {\n            XLog.i(TAG, \"tryPreloadFirstVideo firstVideo is not ready\")\n            return\n        }\n\n        val vid = firstVideoInfo?.vid\n        XLog.i(TAG, \"tryPreloadFirstVideo vid:$vid\")\n        //  首个视频不是 vid 时上报 PreloadVideo_start 和  PreloadVideo_startError 和 PreloadVideo_end\n        if (vid.isNullOrEmpty()) {\n            addVideoPreloadIgnoreTag()\n            return\n        }\n\n        removeExpiredVideoCache(vid)\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START)\n\n        preloadVideo(vid, object : DiscoverVideoPreloadManager.PreloadListener {\n            override fun onStartSuccess(resultCode: Int) {\n                XLog.i(TAG, \"preloadVideo onStartSuccess vid:$vid, startResultCode:$resultCode\")\n                recordStartPreloadResult(true, resultCode)\n            }\n\n            override fun onStartFail(errorCode: Int) {\n                recordStartPreloadResult(false, errorCode)\n                XLog.i(TAG, \"preloadVideo onStartFail vid:$vid, errorCode:$errorCode\")\n            }\n\n            override fun onSucceed(preloadStrategy: PreloadStrategy?) {\n                XLog.i(TAG, \"preload onSucceed, vid=${preloadStrategy?.vid}\")\n                firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_SUCCESS\n                addReportTag(\n                    DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_SUCCEED\n                )\n            }\n\n            override fun onFailed(preloadStrategy: PreloadStrategy?) {\n                XLog.i(TAG, \"preload onFailed, vid=${preloadStrategy?.vid}\")\n                firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_FAILED\n                addReportTag(\n                    DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_FAILED\n                )\n            }\n\n            override fun onStop(preloadStrategy: PreloadStrategy?) {\n                XLog.i(TAG, \"preload onStop, vid=${preloadStrategy?.vid}\")\n                firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_STOP\n                addReportTag(\n                    DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_STOP\n                )\n            }\n\n            override fun onPrepareDownloadProgressUpdate(\n                vid: String?,\n                playableDurationMS: Int,\n                downloadSpeedKBs: Int,\n                currentDownloadSizeByte: Long,\n                totalFileSizeByte: Long\n            ) {\n                XLog.i(\n                    TAG,\n                    \"preload onPrepareDownloadProgressUpdate, vid=${vid}, \" +\n                            \"playableDurationMS=$playableDurationMS, \" +\n                            \"downloadSpeedKBs=$downloadSpeedKBs, \" +\n                            \"currentDownloadSizeByte=$currentDownloadSizeByte, \" +\n                            \"totalFileSizeByte=$totalFileSizeByte\"\n                )\n\n                firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_PRELOADING\n                firstVideoProgress = playableDurationMS * 100 / PreloadStrategy.DEFAULT_PRELOAD_LENGTH\n                firstVideoDownloadSize = currentDownloadSizeByte\n            }\n        })\n    }\n\n    private fun addVideoPreloadIgnoreTag() {\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START)\n        techReporter.addTag(\n            DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START_ERROR,\n            DiscoverBeaconReport.ExtraMapKey.PRELOAD_VIDEO_START_RESULT_CODE to DiscoverVideoPreloadManager.VideoPreloadCode.PRELOAD_VIDEO_IS_NOT_VID.toString()\n        )\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_END)\n    }\n\n\n    fun firstVideoStatus(): Int {\n        if (firstVideoInfo == null) {\n            return VideoStatus.IS_NULL\n        }\n        if (firstVideoInfo?.vid?.isNotNullOrEmpty() == true) {\n            return VideoStatus.IS_VID\n        }\n\n        if (firstVideoInfo?.videoUrl?.isNotNullOrEmpty() == true) {\n            return VideoStatus.IS_URL\n        }\n        return VideoStatus.BOTH_NULL\n    }\n\n\n    private fun addReportTag(tag: String) {\n        techReporter.addTag(tag)\n        techReporter.addTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_END)\n    }\n\n    fun initPreloadManager() {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"checkPreloadManager switch off return\")\n            return\n        }\n\n        CoroutineScope(Dispatchers.IO).launch {\n            val isPreloadManagerInit = preloadManager.checkPreloadManager()\n            if (isPreloadManagerInit) {\n                if (!isFirstVideoPreloadStarted) {\n                    // PreloadManager 初始化成功后尝试预下载第一个视频\n                    tryPreloadFirstVideo()\n                }\n            }\n        }\n    }\n\n    fun preloadFirstVideo(firstRecommendItem: DiscoveryPageRecommendItem) {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"preloadVideo switch off return\")\n            return\n        }\n\n        updateFirstVideoInfo(firstRecommendItem)\n        tryPreloadFirstVideo()\n    }\n\n    fun preloadVideo(dataList: ArrayList<DiscoveryPageRecommendItem>) {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"preloadVideo switch off return\")\n            return\n        }\n\n        XLog.i(TAG, \"preloadVideo list\")\n        dataList.take(DiscoverConfig.preloadVideoCount).forEach { info ->\n            val vid = info.videoInfo?.vid\n            if (isFirstVideo(info.videoInfo)) {\n                // 如果是首个视频不重复预加载\n            } else if (vid.isNotNullOrEmpty()) {\n                preloadVideo(vid)\n            }\n            XLog.i(TAG, \"preloadVideo vid =${info.videoInfo?.vid}\")\n        }\n    }\n\n    private fun isFirstVideo(videoInfo: DiscoveryPageRecommendVideoInfo?): Boolean {\n        return videoInfo != null && videoInfo == firstVideoInfo\n    }\n\n\n    @Synchronized\n    private fun updateFirstVideoInfo(firstVideoItem: DiscoveryPageRecommendItem) {\n        if (this.firstVideoItem == null) {\n            this.firstVideoItem = firstVideoItem\n        }\n\n        val videoInfo = firstVideoItem.videoInfo\n        if (firstVideoInfo == null) {\n            firstVideoInfo = videoInfo\n            firstVideoPreloadTime = System.currentTimeMillis()\n            addFirstVideoInfoTag(videoInfo)\n            return\n        }\n\n        XLog.i(TAG, \"updateFirstVideoInfo vid: ${videoInfo?.vid}\")\n\n        // 数据缓存过期后会重新拉取, 此时要重新预加载视频\n        val vid = videoInfo?.vid\n        if (vid.isNotNullOrEmpty() && vid == firstVideoInfo?.vid) {\n            // 如果视频缓存超过 2H , 需要重新预加载\n            if (System.currentTimeMillis() - firstVideoPreloadTime > DiscoverConfig.videoCacheExpTime) {\n                VideoPreLoader.getInstance().removeTask(vid)\n            }\n        }\n        addFirstVideoInfoTag(videoInfo)\n        firstVideoInfo = videoInfo\n        firstVideoPreloadTime = System.currentTimeMillis()\n    }\n\n    private fun addFirstVideoInfoTag(videoInfo: DiscoveryPageRecommendVideoInfo?) {\n        val vid = videoInfo?.vid\n        val videoUrl = videoInfo?.videoUrl\n        XLog.i(TAG, \"addFirstVideoInfoTag, vid=$vid, videoUrl=$videoUrl\")\n\n        if (vid.isNotNullOrEmpty()) {\n            techReporter.addTagExtra(DiscoverBeaconReport.Key.VID, vid)\n        } else if (videoUrl.isNotNullOrEmpty()) {\n            techReporter.addTagExtra(DiscoverBeaconReport.Key.VIDEO_URL, videoUrl)\n        }\n    }\n\n    fun preloadVideo(data: DiscoveryPageRecommendItem?) {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"preloadVideo switch off return\")\n            return\n        }\n        val vid = data?.videoInfo?.vid\n        XLog.i(TAG, \"preloadVideo 222: $vid\")\n        preloadVideo(vid)\n    }\n\n    private fun preloadVideo(vid: String?, preloadCallBack: DiscoverVideoPreloadManager.PreloadListener? = null) {\n        if (!DiscoverConfig.preloadVideoSource) {\n            XLog.i(TAG, \"preloadVideo, vid:$vid, return, switch off\")\n            return\n        }\n\n        if (vid.isNullOrEmpty()) {\n            XLog.i(TAG, \"preloadVideo return vid:$vid\")\n            return\n        }\n\n        preloadManager.preloadVideo(vid, preloadCallBack)\n    }\n\n    private fun recordStartPreloadResult(success: Boolean, resultCode: Int) {\n        val tag = if (success) {\n            isFirstVideoPreloadStarted = true\n            firstVideoPreloadTime = System.currentTimeMillis()\n            DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START_SUCCESS\n        } else {\n            DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START_ERROR\n        }\n        techReporter.addTag(\n            tag,\n            DiscoverBeaconReport.ExtraMapKey.PRELOAD_VIDEO_START_RESULT_CODE to resultCode.toString()\n        )\n\n    }\n\n    private fun removeExpiredVideoCache(vid: String) {\n        // 如果视频缓存超过 2H , 需要清掉之前的预加载任务重新预加载\n        if (isFirstVideoCacheExpired()) {\n            XLog.i(TAG, \"updateFirstVideoInfo video cache expired, remove preload task\")\n            removeTask(vid)\n            resetFirstVideoStatus()\n            techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_CACHE_EXPIRED)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START_SUCCESS)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_START_ERROR)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_SUCCEED)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_FAILED)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_STOP)\n            techReporter.removeTag(DiscoverBeaconReport.PreloadKey.PRELOAD_VIDEO_END)\n        }\n    }\n\n    private fun isFirstVideoCacheExpired(): Boolean {\n        if (firstVideoPreloadTime <= 0) {\n            return false\n        }\n        return System.currentTimeMillis() - firstVideoPreloadTime >= DiscoverConfig.videoCacheExpTime\n    }\n\n\n    private fun removeTask(vid: String) {\n        preloadManager.removeTask(vid)\n    }\n\n\n    fun onDestroy() {\n        val vid = firstVideoInfo?.vid\n        XLog.i(TAG, \"onDestroy start: vid = $vid\")\n        if (vid.isNotNullOrEmpty()) {\n            removeTask(vid)\n        }\n\n        firstVideoInfo = null\n        resetFirstVideoStatus()\n        XLog.i(TAG, \"onDestroy end: vid = $vid\")\n    }\n\n    private fun resetFirstVideoStatus() {\n        firstVideoProgress = 0\n        firstVideoPreloadStatus = PreloadStatus.PRELOAD_STATUS_UNKNOWN\n        firstVideoDownloadSize = 0L\n        isFirstVideoPreloadStarted = false\n    }\n\n    fun onMainResume() {\n\n        if (firstVideoInfo == null) {\n            return\n        }\n\n        if (firstVideoPreloadTime == 0L) {\n            return\n        }\n\n\n        if (!isFirstVideoCacheExpired()) {\n            return\n        }\n\n        XLog.i(TAG, \"onMainResume tryPreloadFirstVideo\")\n        tryPreloadFirstVideo()\n    }\n\n}"}
{"query": "DiscoverRecommendDataCacheManager.kt", "value": "package com.tencent.pangu.discover.recommend.manager\n\nimport com.tencent.assistant.manager.JceCacheManager\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendRequest\nimport com.tencent.assistant.protocol.jce.DiscoveryPageRecommendResponse\nimport com.tencent.assistant.request.RequestResult\nimport com.tencent.assistant.request.RequestType\nimport com.tencent.assistant.utils.XLog\nimport com.tencent.assistant.utils.runOnUiThread\nimport com.tencent.pangu.discover.base.manager.DiscoverBaseDataCacheManager\nimport com.tencent.pangu.discover.base.manager.IDiscoverDataCacheManager\nimport com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter\nimport com.tencent.pangu.managerv7.EntranceManager\n\n/**\n * Copyright (c) 2024 Tencent. All rights reserved.\n * Author: marklima\n * Email: <EMAIL>\n * Date: 2024/8/16 19:34\n * Description:\n */\nobject DiscoverRecommendDataCacheManager : IDiscoverDataCacheManager {\n\n    private const val TAG = \"DiscoverRecommendDataCacheManager\"\n\n    private var cacheTime: Long = 0\n\n    private val videoPreloader = DiscoverRecommendVideoPreloadManager\n\n    private val cacheManager = DiscoverBaseDataCacheManager(DiscoverRecommendReporter.Scene.RECOMMEND_SCENE)\n\n\n    override fun hasCachedResult(mid: Long): Boolean {\n        val hasCache = cacheManager.hasCachedResult(mid)\n        val cacheValid = (System.currentTimeMillis() - cacheTime) < DiscoverConfig.dataCacheExpTime\n        XLog.i(TAG, \"cacheValid:\" + DiscoverConfig.dataCacheExpTime)\n        val hasCachedResult = hasCache && cacheValid\n        XLog.i(TAG, \"hasCachedResult: $hasCache, hasCache: $hasCache, cacheValid: $cacheValid\")\n        return hasCachedResult\n    }\n\n\n    override suspend fun getCacheResult(mid: Long): RequestResult<DiscoveryPageRecommendResponse>? {\n        XLog.i(TAG, \"getCacheResult\")\n        return cacheManager.getCacheResult(mid)\n    }\n\n    override fun updateCache(mid: Long, result: RequestResult<DiscoveryPageRecommendResponse>) {\n        cacheManager.updateCache(mid, result)\n    }\n\n    override fun preloadData(\n        request: DiscoveryPageRecommendRequest,\n        resultInterceptor: ((result: RequestResult<DiscoveryPageRecommendResponse>) -> RequestResult<DiscoveryPageRecommendResponse>)?\n    ) {\n        cacheManager.preloadData(request, resultInterceptor)\n    }\n\n    fun preloadData(checkDiscoverEntrance: Boolean = true) {\n        XLog.i(TAG, \"preLoadFirstRecommendPage\")\n        if (!DiscoverConfig.preloadRecommendPage) {\n            XLog.i(TAG, \"preLoadFirstRecommendPage, switcher is off return\")\n            return\n        }\n\n\n        if (checkDiscoverEntrance && !EntranceManager.getInstance().hasDiscoverEntrance()) {\n            XLog.i(TAG, \"preLoadFirstRecommendPage has no discover entrance, return\")\n            return\n        }\n\n        if (hasCachedResult()) {\n            getRecommendResult()?.data?.items?.firstOrNull()?.let {\n                preloadFirstVideo(it)\n            }\n\n            XLog.i(TAG, \"preLoadFirstRecommendPage cachedResult !=null, return\")\n            DiscoverRecommendPreRenderVideo.refreshPreVideoUrlIfNeed()\n            return\n        }\n\n        cacheManager.preloadData(DiscoveryPageRecommendRequest()) { result ->\n            var finalResult = result\n\n            when (result) {\n                is RequestResult.Success<DiscoveryPageRecommendResponse> -> {\n                    val items = result.data?.items\n                    if (items?.isNotEmpty() == true) {\n                        items.firstOrNull()?.let {\n                            preloadFirstVideo(it)\n                        }\n                        runOnUiThread { DiscoverRecommendPreRenderVideo.createVideoPlayer(items[0]) }\n                        cacheTime = System.currentTimeMillis()\n                    } else {\n                        XLog.i(TAG, \"preLoadFirstRecommendPage preLoad data empty \")\n                    }\n                    result.data?.let { saveCacheResponse(it) }\n                }\n\n                else -> {\n                    XLog.e(TAG, \"preLoadFirstRecommendPage error\")\n                    finalResult = getDiskCacheDataResult() ?: result\n                }\n            }\n\n            XLog.i(TAG, \"preLoadFirstRecommendPage result = $result\")\n            finalResult\n        }\n    }\n\n    private fun preloadFirstVideo(it: DiscoveryPageRecommendItem) {\n        videoPreloader.preloadFirstVideo(it)\n    }\n\n    private fun getDiskCacheDataResult(): RequestResult<DiscoveryPageRecommendResponse>? {\n        if (!DiscoverConfig.useDiskCache) {\n            return null\n        }\n        if (getRecommendResult() != null) {\n            return null\n        }\n        getDiskCacheResponse()?.let {\n            val items = it.items\n            if (items?.isNotEmpty() == true) {\n                runOnUiThread {\n                    DiscoverRecommendPreRenderVideo.createVideoPlayer(items[0])\n                }\n                return RequestResult.Success(it, RequestType.FULL_REQUEST, true)\n            }\n        }\n\n        return null\n    }\n\n    private fun getRecommendResult() = cacheManager.cacheResults[0]\n\n    /**\n     * 持久化response 数据\n     */\n    private fun saveCacheResponse(response: DiscoveryPageRecommendResponse) {\n        if (!DiscoverConfig.useDiskCache) {\n            return\n        }\n        JceCacheManager.getInstance().saveDiscoverRecommendPageResponse(response)\n    }\n\n    fun getDiskCacheResponse(): DiscoveryPageRecommendResponse? {\n        return JceCacheManager.getInstance().discoverRecommendPageResponse\n    }\n\n    override fun onDestroy() {\n        cacheManager.onDestroy()\n        cacheTime = 0\n        XLog.i(TAG, \"onDestroy end\")\n    }\n}"}