{"query":"推荐tab视频页相关代码", "id":"20250304000000","value":"package com.tencent.pangu.discover.recommend  import android.animation.AnimatorSet import android.animation.ValueAnimator import android.content.Context import android.content.res.Configuration import android.os.Bundle import android.provider.Settings import android.view.LayoutInflater import android.view.OrientationEventListener import android.view.OrientationEventListener.ORIENTATION_UNKNOWN import android.view.View import android.view.ViewGroup import android.view.animation.DecelerateInterpolator import androidx.lifecycle.LifecycleOwner import androidx.lifecycle.ViewModelProvider import androidx.recyclerview.widget.LinearLayoutManager import androidx.recyclerview.widget.PagerSnapHelper import androidx.recyclerview.widget.RecyclerView import com.qq.AppService.ApplicationProxy import com.tencent.android.qqdownloader.BuildConfig import com.tencent.android.qqdownloader.R import com.tencent.assistant.Global import com.tencent.assistant.activity.BaseActivity import com.tencent.assistant.activity.BaseFragment import com.tencent.assistant.component.LoadingView import com.tencent.assistant.component.txscrollview.ScrollIdleEventInfo import com.tencent.assistant.component.txscrollview.ScrolledDirection import com.tencent.assistant.component.video.VideoViewManager import com.tencent.assistant.event.EventDispatcherEnum import com.tencent.assistant.net.NetworkUtil import com.tencent.assistant.protocol.jce.DiscoveryPageRecommendRequest import com.tencent.assistant.request.RequestType import com.tencent.assistant.settings.api.settingBoolean import com.tencent.assistant.st.STConst import com.tencent.assistant.utils.HandlerUtils import com.tencent.assistant.utils.XLog import com.tencent.assistant.utils.dp import com.tencent.assistant.utils.postToUiThreadDelayed import com.tencent.pangu.activity.ShareBaseActivity import com.tencent.pangu.discover.base.IDiscoverChildFragment import com.tencent.pangu.discover.base.manager.DiscoverPageJumpManager import com.tencent.pangu.discover.base.utils.DiscoverUIUtil import com.tencent.pangu.discover.base.view.BaseVideoViewHolder import com.tencent.pangu.discover.comment.common.ICommentEventCallback import com.tencent.pangu.discover.comment.fragment.CommentDialogFragment import com.tencent.pangu.discover.comment.model.CommentFragmentConfig import com.tencent.pangu.discover.comment.model.CommentReportParam import com.tencent.pangu.discover.recommend.manager.DiscoverConfig import com.tencent.pangu.discover.recommend.manager.DiscoverGuideBarManager import com.tencent.pangu.discover.recommend.manager.DiscoverRecommendPreRenderVideo import com.tencent.pangu.discover.recommend.manager.DiscoverRecommendVideoPreloadManager import com.tencent.pangu.discover.recommend.manager.DiscoverVideoComponentCacheManager import com.tencent.pangu.discover.recommend.model.DiscoverRecommendRequestParam import com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport import com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter import com.tencent.pangu.discover.recommend.view.PreRenderLinearLayoutManager import com.tencent.pangu.discover.recommend.vm.DiscoverRecommendViewModel import com.tencent.pangu.discover.recommend.wdiget.DiscoverRecommendViewHolder import com.tencent.pangu.middlepage.view.FixedRecyclerView import com.tencent.pangu.middlepage.view.MiddlePageErrorPage import com.tencent.ptrlayout.SmartRefreshLayout import com.tencent.ptrlayout.footer.YYBFooter  /**  * Copyright (c) 2024 Tencent. All rights reserved.  * Author: marklima  * Email: <EMAIL>  * Date: 2024/6/28 16:02  * Description:  */ open class DiscoverRecommendFragment : BaseFragment(), IDiscoverChildFragment {      private var root: View? = null     private val TAG = \"DiscoverRecommendFragment_${getScene()}\"      companion object {         var firstVideoMute by settingBoolean(\"mute_first_video\", true)         const val IS_DISCOVER_FIRST_SHOW_FRAGMENT = \"is_discover_first_show_fragment\"         const val IS_DISCOVER_USER_SCROLLED = \"is_discover_user_scrolled\"         const val SCROLL_DURATION = 300L         const val DELAY_DURATION = 1000L     }      private var userScrolled by settingBoolean(IS_DISCOVER_USER_SCROLLED, false)     private val reporter by lazy {         DiscoverRecommendReporter.getReporter(getScene()).apply {             (activity as? BaseActivity)?.stPageInfo?.let {                 init(it)             }         }     }      private val techReporter by lazy {         DiscoverBeaconReport.getReporter(getScene())     }      open fun getScene(): Int {         return DiscoverRecommendReporter.Scene.RECOMMEND_SCENE     }      open val enableRefresh = true      var onCommentDialogHeightChangeListener: ((Int, Int) -> Unit?)? = null     var firstFrameCallback: (() -> Unit)? = null      private lateinit var refreshLayout: SmartRefreshLayout     private lateinit var recyclerView: FixedRecyclerView     private lateinit var loadingView: LoadingView     private lateinit var errorView: MiddlePageErrorPage     private lateinit var topShadow: View     private var viewCreated: Boolean = false     private val exposedItemPosition = mutableMapOf<Int, Boolean>()     private val scrolledDirection = ScrolledDirection()     private var currentItem: BaseVideoViewHolder? = null     private val viewModel by lazy {         ViewModelProvider(this)[DiscoverRecommendViewModel::class.java].apply {             reporter = <EMAIL>             techReporter = <EMAIL>             scene = getScene()             this.isVideoFullScreen = {                 adapter.currentSelectVH?.isVideoFullScreen() ?: false             }         }     }      private val pagerSnapHelper by lazy {         object : PagerSnapHelper() {             override fun findTargetSnapPosition(                 layoutManager: RecyclerView.LayoutManager,                 velocityX: Int,                 velocityY: Int             ): Int {                 val newPosition = super.findTargetSnapPosition(layoutManager, velocityX, velocityY)                 calculateItemChange(newPosition, layoutManager)                 return newPosition             }         }     }      private var isFirstVideoFirstFrameCalled = false      private var isDiscoverFirstShowFragment = true      private val optimizePreRender = DiscoverConfig.optimizePreRender      private var isLoadingData = false      override fun setArguments(bundle: Bundle?) {         super.setArguments(bundle)         isDiscoverFirstShowFragment = bundle?.getBoolean(             IS_DISCOVER_FIRST_SHOW_FRAGMENT,             isDiscoverFirstShowFragment         ) ?: isDiscoverFirstShowFragment      }       protected val adapter by lazy {         object : DiscoverRecommendAdapter(getScene()) {             override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVideoViewHolder {                 return <EMAIL>(parent, viewType, getScene())             }         }.apply {             addListener(                 onFirstItemExposed = { position, vh ->                     XLog.d(                         TAG,                         \"firstItemExposedCallbackposition=$position,time:${System.currentTimeMillis()-time}\"                     )                     currentItem = vh                     <EMAIL>(vh, position)                     reporter.pageRenderFinish(0, true)                     isFirstVideoFirstFrameCalled = false                     firstFrameCallback?.invoke()                 },                 onVideoFirstFrame = { position, vh ->                     XLog.i(                         TAG,                         \"onVideoFirstFrame,position=$position\"                     )                     if (position == 0) {                         tryPreBindNextItem()                     }                 },                 onMuteBtnClick = { mute ->                 },                 onVideoClick = {                     XLog.i(TAG, \"onVideoClick\")                 },                 onLikeClick = { data, itemPosition ->                 },                 onCommentClick = { data, itemPosition ->                     val reportContext = reporter.getCommonContextExtra(                         data,                         DiscoverRecommendReporter.ReportContextKey.COMMENT_REPORT_CONTEXT                     ) ?: \"\"                     val config = CommentFragmentConfig(                         contentId = data.videoInfo.materialId.toString(),                         contentType = data.commentContentType,                         reportParam = CommentReportParam(                             scene = reporter.scene,                             sourceScene = reporter.sourceScene,                             sourceSlotId = reporter.sourceSlot,                             slotId = \"99_${itemPosition+1}\",                             appId = data.interactiveInfo.appid,                             recommendId = Global.encodeRecommendIdToString(data.videoInfo.rid),                             extraMap = mutableMapOf(                                 STConst.UNI_REPORT_CONTEXT to reportContext,                                 STConst.SCENE_APPID to data.interactiveInfo.appid.toString(),                             )                          )                     )                     showCommentDialog(config)                 },                 onShareClick = { data, itemPosition ->                 },                 onPlayComplete = { position, vh, isFullScreen ->                     XLog.i(TAG, \"onPlayComplete,isFullScreen=$isFullScreen\")                     if (isFullScreen) {                         return@addListener                     }                      if (commentDialogFragment != null) {                         XLog.i(TAG, \"onPlayComplete,commentDialogFragmentisShowing\")                         return@addListener                     }                      if (vh.isShareDialogShowing()) {                         XLog.i(TAG, \"onPlayComplete,shareDialogisShowing\")                         return@addListener                     }                      if (position >= ((recyclerView.adapter?.itemCount ?: 0) - 1)) {                         XLog.i(TAG, \"onPlayComplete,position>=((recyclerView.adapter?.itemCount?:0)-1)\")                         return@addListener                     }                      if (scrollState != RecyclerView.SCROLL_STATE_IDLE) {                         XLog.i(TAG, \"onPlayComplete,scrollState!=RecyclerView.SCROLL_STATE_IDLE\")                         return@addListener                     }                      recyclerView.smoothScrollToPosition(position + 1)                  },                 isFragmentShow = {                     val resumed = <EMAIL>                     XLog.i(TAG, \"isFragmentShowing,resumed=$resumed\")                     resumed                 },                 isLoadingData = {                     XLog.i(TAG, \"DiscoverRecommendAdapter:isLoadingData=$isLoadingData\")                     isLoadingData                 }             )         }     }      private fun tryPreBindNextItem() {         XLog.i(TAG, \"tryPreBindNextItem,isFirstVideoFirstFrameCalled=$isFirstVideoFirstFrameCalled\")         if (isFirstVideoFirstFrameCalled) {             return         }         isFirstVideoFirstFrameCalled = true         preloadVideo(position)         // 首个视频首帧渲染完成后, 往下移动 1px 预渲染下一个视频, 当用户上下刷时, RecyclerView 的 PrefetchItem 会预渲染下一个         if (optimizePreRender) {             recyclerView.scrollBy(0, 1)             recyclerView.post { recyclerView.scrollBy(0, -1) }         }     }      open fun onCreateViewHolder(parent: ViewGroup, viewType: Int, scene: Int): BaseVideoViewHolder {         return DiscoverRecommendViewHolder(parent, scene)     }      private var commentDialogFragment: CommentDialogFragment? = null       private var lastOrientation: Int = ORIENTATION_UNKNOWN     private var position: Int = 0      private val orientationEventListener by lazy {         object : OrientationEventListener(context) {             override fun onOrientationChanged(orientation: Int) {                 // 请求数据时刷新前不旋转屏幕                 if (isLoadingData) {                     XLog.e(TAG, \"onOrientationChanged:isLoadingDataskip,orientation=$orientation\")                     return                 }                  val isLandscape = orientation in 60..120 || orientation in 240..300                 val isPortrait = orientation in 0..30 || orientation in 330..360                 if (isLandscape && lastOrientation != Configuration.ORIENTATION_LANDSCAPE) {                     XLog.i(TAG, \"Landscapemode\")                     lastOrientation = Configuration.ORIENTATION_LANDSCAPE                     adapter.toggleVideoFullScreen(true)                 } else if (isPortrait && lastOrientation != Configuration.ORIENTATION_PORTRAIT) {                     XLog.i(TAG, \"Portraitmode\")                     lastOrientation = Configuration.ORIENTATION_PORTRAIT                     adapter.toggleVideoFullScreen(false)                 }             }         }     }      private var time = System.currentTimeMillis()     private var exposed = false     private var fistDataLoaded = false     private var scrollState = RecyclerView.SCROLL_STATE_IDLE     private var doFirstItemSelectedOnResume = false     private val scrollY = 102.dp     private var showGuideRunnable: Runnable? = null     private var isFirstShowDiscover = true       override fun onAttach(context: Context) {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_ATTACH)         super.onAttach(context)         XLog.d(TAG, \"onAttach,time:${System.currentTimeMillis()-time}\")         time = System.currentTimeMillis()     }      override fun onCreate(savedInstanceState: Bundle?) {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_CREATE)         super.onCreate(savedInstanceState)         (activity as? BaseActivity)?.stPageInfo?.let {             reporter.init(it)         }         reporter.reportPageCreate()         XLog.d(TAG, \"onCreate,time:${System.currentTimeMillis()-time}\")     }       override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_CREATE_VIEW)         if (root == null) {             root = inflater.inflate(R.layout.discover_recommend_fragment, container, false)         } else {             // 首页fragment创建后下次进入会使用缓存页面，这里如果发现使用了缓存页面并且已经渲染完成，则手动上报一次页面渲染完成             reporter.pageRenderFinish(1, true)         }         return root     }      override fun onViewCreated(view: View, savedInstanceState: Bundle?) {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_VIEW_CREATED)         super.onViewCreated(view, savedInstanceState)         XLog.i(TAG, \"onViewCreated,viewCreated:$viewCreated,time:${System.currentTimeMillis()-time}\")         if (!viewCreated) {             initView(view)             firstLoadData()         }         initObserver(viewLifecycleOwner)         viewCreated = true     }      private fun showUserGuide() {         XLog.d(TAG, \"userScrolled=$userScrolled\")         if (userScrolled) {             return         }          showGuideRunnable = Runnable { setUpUserGuideAnim() }         HandlerUtils.getMainHandler().postDelayed({             showGuideRunnable?.run()         }, 1500)     }      private fun setUpUserGuideAnim() {         // 下滑动画         val translationRecyclerAnimator = ValueAnimator.ofInt(0, scrollY).apply {             duration = SCROLL_DURATION             var scrolledY = 0             addUpdateListener {                 val value = it.animatedValue as Int                 val scrollByY = value - scrolledY                 scrolledY = value                 XLog.d(TAG, \"[show]scrollByY:$scrollByY,scrolledY:$scrolledY\")                 recyclerView.scrollBy(0, scrollByY)             }             interpolator = DecelerateInterpolator()         }          // 顶部停留动画         val delayAnimator = ValueAnimator.ofInt(0, 0).apply {             duration = DELAY_DURATION         }          // 复位动画         val resetRecyclerAnimator = ValueAnimator.ofInt(scrollY, 0).apply {             duration = SCROLL_DURATION             var scrolledY = scrollY             addUpdateListener {                 val value = it.animatedValue as Int                 val scrollByY = value - scrolledY                 scrolledY = value                 XLog.d(TAG, \"[reset]scrollByY:$scrollByY,scrolledY:$scrolledY\")                 recyclerView.scrollBy(0, scrollByY)             }             interpolator = DecelerateInterpolator()         }          val animatorSet = AnimatorSet()         animatorSet.playSequentially(translationRecyclerAnimator, delayAnimator, resetRecyclerAnimator)         animatorSet.start()     }      override fun onResume() {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_RESUME)         super.onResume()         XLog.i(TAG, \"onResume,time:${System.currentTimeMillis()-time},userVisibleHint:\" + userVisibleHint)         if (doFirstItemSelectedOnResume) {             XLog.i(TAG, \"onResumedoFirstItemSelectedOnResume\")             currentItem?.let {                 onItemSelected(it, 0)             }             doFirstItemSelectedOnResume = false         }          if (!DiscoverUIUtil.enablePreRender()) {             postToUiThreadDelayed(100) {                 VideoViewManager.getInstance().onResume(activity)             }         }         if (!exposed) {             reporter.pageExposure()             techReporter.reportPageResume(position)             exposed = true         }         reporter.pageIn()         reporter.reportPageIn()          registerRotate()         (activity as? ShareBaseActivity)?.getShareEngine(reporter.scene)?.onResume()         if (DiscoverUIUtil.enablePreRender() && userVisibleHint) {             currentItem?.onPageResume()         }     }      private fun registerRotate() {         if (DiscoverUIUtil.isWideScreen()) {             XLog.i(TAG, \"isWideScreen,return\")             return         }          context ?: return         if (!isAutoRotateEnabled(requireContext())) {             return         }         orientationEventListener.enable()     }      override fun onPause() {         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_PAUSE)         super.onPause()         XLog.i(TAG, \"onPause,time:${System.currentTimeMillis()-time}\")         if (requireActivity().isFinishing) {             VideoViewManager.getInstance().onStop(activity)             adapter.currentSelectVH?.onPageStop()             activity?.let {                 DiscoverVideoComponentCacheManager.getManager(getScene()).onDestroy(it)                 if (isRecommendScene()) {                     DiscoverRecommendPreRenderVideo.onDestroy(it)                 }             }         } else {             VideoViewManager.getInstance().onPause(activity)             adapter.currentSelectVH?.onPagePause()         }         reporter.pageOut()         reporter.reportPageOut()         unregisterRotate()          if (showGuideRunnable != null) {             HandlerUtils.getMainHandler().removeCallbacks(showGuideRunnable!!)             showGuideRunnable = null         }     }       override fun onDestroy() {         super.onDestroy()         reporter.reportPageDestroy()     }      private fun unregisterRotate() {         activity ?: return         orientationEventListener.disable()     }      private fun initView(view: View) {         view.setBackgroundResource(R.color.discover_bg_color)         loadingView = view.findViewById(R.id.loading_view)         errorView = view.findViewById(R.id.error_page)         errorView.callback = object : MiddlePageErrorPage.ErrorPageCallBack {             override fun onClickRetry() {                 XLog.d(TAG, \"onClickRefresh\")                 retryLoadData()             }         }         refreshLayout = view.findViewById<SmartRefreshLayout>(R.id.refresh_layout).apply {             setEnableRefresh(enableRefresh)             setEnableLoadMore(true)             setEnableScrollContentWhenLoaded(false)             setRefreshFooter(YYBFooter(context), ViewGroup.LayoutParams.MATCH_PARENT, 63.dp)             setOnLoadMoreListener {                 XLog.d(TAG, \"OnLoadMore\")                 loadData(true)             }             setOnRefreshListener {                 XLog.d(TAG, \"OnRefresh\")                 loadData(isLoadMore = false, refresh = true)             }         }         recyclerView = view.findViewById<FixedRecyclerView>(R.id.recycler_view).apply {             setHasFixedSize(true)             layoutManager = if (optimizePreRender) {                 LinearLayoutManager(context)             } else {                 PreRenderLinearLayoutManager(context)             }             itemAnimator = null             adapter = <EMAIL>             addOnScrollListener(object : RecyclerView.OnScrollListener() {                 var totalDy = 0                 override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {                     super.onScrollStateChanged(recyclerView, newState)                     <EMAIL> = newState                     XLog.i(TAG, \"onScrollStateChangednewState:$newState\")                     if (newState == RecyclerView.SCROLL_STATE_IDLE) {                         sendScrollIdleEvent()                         recyclerView.layoutManager?.let {                             val newPosition =                                 pagerSnapHelper.findTargetSnapPosition(it, recyclerView.scrollX, recyclerView.scrollY)                             calculateItemChange(newPosition, it)                         }                         totalDy = 0                         adjustCurVideoPosition()                     }                     if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {                         XLog.d(TAG, \"userdragging\")                         if (!userScrolled && !BuildConfig.DEBUG) {                             userScrolled = true                         }                     }                 }                  override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {                     super.onScrolled(recyclerView, dx, dy)                     scrolledDirection.onScrolled(dx, dy)                     if (DiscoverConfig.discoverSlideVideoShowCoverImage) {                         totalDy += dy                         if (totalDy > 0) {                             // 向下滑动                             adjustNextVideoPosition()                         }                     }                 }             })         }          pagerSnapHelper.attachToRecyclerView(recyclerView)         topShadow = view.findViewById(R.id.top_shadow)     }       private fun calculateItemChange(newPosition: Int, layoutManager: RecyclerView.LayoutManager) {          XLog.i(TAG, \"calculateItemChange,newPageIndex:$newPosition,current:$position\")         if (newPosition == position) {             return         }          if (newPosition > position) {             preloadVideo(newPosition)         }          position = newPosition         val itemView = layoutManager.findViewByPosition(newPosition) ?: return         val vh = recyclerView.getChildViewHolder(itemView) as BaseVideoViewHolder         onItemSelected(vh, newPosition)     }      private fun preloadVideo(newPageIndex: Int) {         val position = newPageIndex + 2         XLog.i(TAG, \"preloadVideo,position=$position,count=${adapter.itemCount}\")         val preloadData = adapter.getItemData(position) ?: return         DiscoverRecommendVideoPreloadManager.preloadVideo(preloadData)     }      private fun onItemSelected(vh: BaseVideoViewHolder, position: Int) {         XLog.d(TAG, \"onItemSelected:position=$position\")         currentItem = vh         // 如果当前页面没有 resume 即发现tab 第一个展示的不是推荐页,这里不做处理, 在切换到推荐tab(resume)时处理         if (!isResumed && position == 0) {             XLog.i(TAG, \"onItemSelected:isResumed=$isResumed,return\")             doFirstItemSelectedOnResume = true             return         }         val itemInfo = adapter.getItemData(position) ?: return         adapter.onItemSelected(vh, position)         reporter.reportCardExposure(itemInfo, position)         if (position != 0 && isRecommendScene()) {             firstVideoMute = false         }         tryAutoLoadMore(position)         exposedItemPosition[position] = true     }      private fun tryAutoLoadMore(position: Int) {         if (position == adapter.itemCount - 3 && !exposedItemPosition.contains(position)) {             XLog.i(TAG, \"autoLoadMore\")             loadData(true)         }     }      /**      * 设置当前视频的bias为0.4      * 修复部分机型 SCROLL_STATE_IDLE 时，视频处于顶部      */     private fun adjustCurVideoPosition() {         XLog.i(TAG, \"adjustCurVideoPosition\")         val layoutManager = recyclerView.layoutManager as LinearLayoutManager         val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()         val currentVisibleView = layoutManager.findViewByPosition(firstVisiblePosition) ?: return         val currentVisibleViewHolder = recyclerView.getChildViewHolder(currentVisibleView) as? BaseVideoViewHolder ?: return         // 设置 bias 为 0.4f         currentVisibleViewHolder.adjustVideoPosition(1f,0)     }      /**      * 向下滑动时，动态设置下一个视频的bias      */     private fun adjustNextVideoPosition() {         val layoutManager = recyclerView.layoutManager as LinearLayoutManager         val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()         val firstVisibleView = layoutManager.findViewByPosition(firstVisiblePosition) ?: return          val nextVisiblePosition = firstVisiblePosition + 1         val nextVisibleView = layoutManager.findViewByPosition(nextVisiblePosition) ?: return         val nextVisibleViewHolder = recyclerView.getChildViewHolder(nextVisibleView) as? BaseVideoViewHolder ?: return          val totalHeight = firstVisibleView.height         val visibleHeight = totalHeight - firstVisibleView.bottom         val scrollFraction = visibleHeight.toFloat() / totalHeight         XLog.i(TAG, \"totalHeight:$totalHeight,firstVisibleView.bottom:${firstVisibleView.bottom}visibleHeight:$visibleHeight,scrollFraction:$scrollFraction\")         nextVisibleViewHolder.adjustVideoPosition(scrollFraction, visibleHeight)     }      private fun initObserver(lifecycleOwner: LifecycleOwner) {         viewModel.recommendLiveData.observe(lifecycleOwner) { result ->             reporter.pageRenderStart()             val data = result.data?.items ?: return@observe             val refresh = result.requestType == RequestType.FULL_REQUEST             XLog.i(TAG, \"onDataLoaded,refresh:$refresh,time:${System.currentTimeMillis()-time}\")             if (refresh && !fistDataLoaded) {                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_DATA_BIND_START)             }             val success = adapter.setData(refresh, data)             if (refresh && success) {                 recyclerView.scrollToPosition(0)                 exposedItemPosition.clear()                 DiscoverGuideBarManager.clean()                 if (!fistDataLoaded) {                     fistDataLoaded = true                     techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_DATA_BIND_END)                     techReporter.reportFirstDataLoad()                 } else {                     techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_REFRESH)                     techReporter.reportRefresh()                 }             }             DiscoverRecommendVideoPreloadManager.preloadVideo(data)             XLog.i(TAG, \"initObserver:recommendLiveDataoffisLoadingData.\")             isLoadingData = false              // 数据加载完成后显示动画             if (isFirstShowDiscover) {                 showUserGuide()                 if (DiscoverConfig.discoverSlideVideoShowCoverImage){                     adjustNextVideoPosition()                 }                 isFirstShowDiscover = false             }         }          viewModel.errorPageLiveData.observe(lifecycleOwner) { showErrorPage ->             togglePageError(showErrorPage)         }          viewModel.firstLoadingLiveData.observe(lifecycleOwner) { loading ->             togglePageLoading(loading)         }          viewModel.loadMoreLiveData.observe(lifecycleOwner) { loadMoreStatus ->             updateLoadMoreView(loadMoreStatus)         }          viewModel.refreshFinishLiveData.observe(lifecycleOwner) { refreshSuccess ->             refreshLayout.finishRefresh(refreshSuccess)         }          viewModel.loadingDataFinishCallback.observe(lifecycleOwner) { result ->             XLog.i(TAG, \"initObserver:loadingDataFinishCallbackoffisLoadingData,loadingDataCallback=$result\")             isLoadingData = false         }     }      private fun firstLoadData() {         // 是否有外部跳转的参数，有的话则不能在首次请求时使用缓存数据，需要将refresh置为 true         val hasValidJumpParams = DiscoverPageJumpManager.hasValidJumpParams()         XLog.d(             TAG,             \"firstLoadData,time:${System.currentTimeMillis()-time},hasValidJumpParams=$hasValidJumpParams\"         )         loadData(isLoadMore = false, refresh = hasValidJumpParams)         techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_LOAD_DATA_START)         techReporter.reportStartFirstLoadData()     }      fun loadData(isLoadMore: Boolean, refresh: Boolean = false, retry: Boolean = false) {         XLog.i(TAG, \"loadData,loadMore:$isLoadMore,fresh:$refresh\")         viewModel.requestRecommend(getDiscoverRecommendRequestParam(isLoadMore, refresh, retry))         isLoadingData = true     }      open fun getDiscoverRecommendRequestParam(         isLoadMore: Boolean,         refresh: Boolean,         retry: Boolean     ) = DiscoverRecommendRequestParam(         request = DiscoveryPageRecommendRequest().apply { DiscoverPageJumpManager.appendTmastRequestParams(this) },         isLoadMore = isLoadMore,         isRefresh = refresh,         isRetry = retry,         useCacheData = !isLoadMore && !refresh && !retry,         cacheData = (isRecommendScene() && !DiscoverPageJumpManager.hasValidJumpParams()),         scene = getScene()     )      private fun isRecommendScene() = getScene() == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE      private fun togglePageLoading(show: Boolean) {         if (show) {             loadingView.visibility = View.VISIBLE         } else {             loadingView.visibility = View.GONE         }     }      private fun togglePageError(show: Boolean) {         if (show) {             errorView.visibility = View.VISIBLE             reporter.pageRenderFinish()         } else {             errorView.visibility = View.GONE         }     }      private fun updateLoadMoreView(loadMoreStatus: DiscoverRecommendViewModel.LoadMoreStatus) {         XLog.d(TAG, \"updateLoadMoreView:$loadMoreStatus\")         if (loadMoreStatus.isLoadMore) {             if (loadMoreStatus.hasMoreData) {                 refreshLayout.finishLoadMore(loadMoreStatus.loadSuccess)             } else {                 refreshLayout.finishLoadMoreWithNoMoreData()             }          } else {             refreshLayout.resetNoMoreData()             refreshLayout.setNoMoreData(!loadMoreStatus.hasMoreData)         }     }      private fun sendScrollIdleEvent() {         scrolledDirection.onScrollIdle()         if (scrolledDirection.isNoDirection){             XLog.i(TAG, \"sendScrollIdleEventreturn,isNoDirection\")             return         }         val msg = ApplicationProxy.getEventDispatcher().obtainMessage()         msg.what = EventDispatcherEnum.UI_EVENT_VIEW_SCROLL_IDLE         msg.obj = ScrollIdleEventInfo(context, recyclerView)         ApplicationProxy.getEventDispatcher().sendMessage(msg)     }       private fun showCommentDialog(config: CommentFragmentConfig) {         activity?.let {             commentDialogFragment = CommentDialogFragment.newInstance(                 config = config,                 listener = object : CommentDialogFragment.CommentDialogListener {                     override fun onDismiss() {                         handleCommentDialogDismissed()                     }                      override fun onHeightChange(currentHeight: Int, totalHeight: Int) {                         XLog.d(TAG, \"onHeightChange$currentHeight,totalHeight:$totalHeight\")                          onCommentDialogHeightChange(currentHeight, totalHeight)                         onCommentDialogHeightChangeListener?.invoke(currentHeight, totalHeight)                     }                  },                 callback = object : ICommentEventCallback {                     override fun onCommentCountChanged(newCount: Int) {                         adapter.currentSelectVH?.updateCommentCount(newCount)                     }                  })             commentDialogFragment?.show(childFragmentManager, \"commentDialogFragment\")         }     }      private fun onCommentDialogHeightChange(dialogHeight: Int, totalHeight: Int) {         changeTopShadowAlpha(1F - dialogHeight / totalHeight.toFloat())         adapter.currentSelectVH?.onDialogHeightChange(dialogHeight, totalHeight)     }      private fun changeTopShadowAlpha(alpha: Float) {         val safeAlpha = if (alpha < 0) {             0F         } else if (alpha > 1) {             1F         } else {             alpha         }         topShadow.alpha = safeAlpha     }       private fun handleCommentDialogDismissed() {         commentDialogFragment = null         onCommentDialogHeightChange(0, 1)     }      override fun onBackPressed(): Boolean {         return false     }      override fun onPageSelect() {         root ?: return         refresh(true)     }      override fun refresh(retry: Boolean) {         root ?: return         if (retry) {             if (errorView.visibility == View.VISIBLE && NetworkUtil.isNetworkActive()) {                 retryLoadData()             }         } else {             refreshLayout.autoRefresh()         }     }      private fun retryLoadData() {         loadData(isLoadMore = false, refresh = true, retry = true)     }      override fun onLoginStateChanged() {         loadData(isLoadMore = false, refresh = true)     }      private fun isAutoRotateEnabled(context: Context): Boolean {         return try {             Settings.System.getInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION) == 1         } catch (e: Settings.SettingNotFoundException) {             e.printStackTrace()             false         }     }  }"}
{"query":"推荐tab视频页相关代码", "id":"20250304000001","value":"package com.tencent.pangu.discover.recommend   import android.view.ViewGroup import androidx.recyclerview.widget.RecyclerView import com.tencent.android.qqdownloader.R import com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem import com.tencent.assistant.utils.XLog import com.tencent.pangu.discover.base.view.BaseVideoViewHolder import com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport import com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter  open class DiscoverRecommendAdapter(val scene: Int) : RecyclerView.Adapter<BaseVideoViewHolder>() {      private val TAG = \"DiscoverRecommendAdapter_$scene\"      private val itemDataList = mutableListOf<DiscoveryPageRecommendItem>()      var currentBindVH: BaseVideoViewHolder? = null         private set      var currentBindPosition = 0         private set      var currentSelectVH: BaseVideoViewHolder? = null         private set      var currentSelectPosition = 0         private set      var recommendPageListener: AdapterListener? = null      private var firstItemExposed = false      val reporter: DiscoverRecommendReporter         get() = DiscoverRecommendReporter.getReporter(scene)     val techReporter: DiscoverBeaconReport         get() = DiscoverBeaconReport.getReporter(scene)      fun setData(refresh: Boolean, data: ArrayList<DiscoveryPageRecommendItem>): Boolean {         XLog.d(TAG, \"setData,refresh:$refresh,data.size:${data.size}\")           if (data.isEmpty()) {             XLog.i(TAG, \"data.isEmpty()\")             return false         }          if (data == itemDataList) {             XLog.i(TAG, \"data==itemDataList\")             return false         }          if (itemDataList.containsAll(data)){             XLog.i(TAG, \"setDatacontainsAll\")             return false         }          if (refresh) {             firstItemExposed = false             itemDataList.clear()             itemDataList.addAll(data)             notifyDataSetChanged()         } else {             val index = itemDataList.size             itemDataList.addAll(data)             notifyItemRangeInserted(index, data.size)         }         return true     }      override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVideoViewHolder {         XLog.d(TAG, \"onCreateViewHolder,viewType:$viewType\")         return BaseVideoViewHolder(parent, R.layout.layout_discover_recommend_feed_item, scene)     }      override fun onBindViewHolder(holder: BaseVideoViewHolder, position: Int) {         val itemData = getItemData(position) ?: return         XLog.d(TAG, \"onBindViewHolderposition:$position\")         if (!firstItemExposed) {             techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_ITEM_BIND_START)         }         holder.recommendPageListener = recommendPageListener         holder.bind(itemData, position)         currentBindVH = holder         currentBindPosition = position         if (!firstItemExposed) {             recommendPageListener?.onFirstItemExposed(position, holder)             firstItemExposed = true             techReporter.addTag(DiscoverBeaconReport.PreloadKey.RECOMMEND_PAGE_FIRST_ITEM_BIND_END)         }     }      fun getItemData(position: Int) = itemDataList.getOrNull(position)      override fun onViewRecycled(holder: BaseVideoViewHolder) {         super.onViewRecycled(holder)         holder.onRecycled()     }      override fun onViewAttachedToWindow(holder: BaseVideoViewHolder) {         super.onViewAttachedToWindow(holder)         holder.onViewAttachedToWindow()     }      override fun onViewDetachedFromWindow(holder: BaseVideoViewHolder) {         super.onViewDetachedFromWindow(holder)         holder.onViewDetachedFromWindow()     }      override fun getItemCount(): Int {         return itemDataList.size     }      fun getCurrentItem(): DiscoveryPageRecommendItem? {         return getItemData(currentSelectPosition)     }      fun onItemSelected(vh: BaseVideoViewHolder, position: Int) {         if (currentSelectVH != vh) {             currentSelectVH?.onItemUnSelected()         }         currentSelectVH = vh         currentSelectPosition = position         vh.onItemSelected()     }      inline fun DiscoverRecommendAdapter.addListener(         crossinline onFirstItemExposed: (itemPosition: Int, vh: BaseVideoViewHolder) -> Unit = { _, _ -> },         crossinline onVideoFirstFrame: (itemPosition: Int, vh: BaseVideoViewHolder) -> Unit = { _, _ -> },         crossinline onMuteBtnClick: (mute: Boolean) -> Unit = {},         crossinline onVideoClick: () -> Unit = {},         crossinline onShareClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },         crossinline onLikeClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },         crossinline onCommentClick: (itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) -> Unit = { _, _ -> },         crossinline onPlayComplete: (itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean) -> Unit = { _, _, _ -> },         crossinline isFragmentShow: () -> Boolean = { true },         crossinline isLoadingData: () -> Boolean = { false }      ): AdapterListener {         val listener = object : AdapterListener {             override fun onFirstItemExposed(itemPosition: Int, vh: BaseVideoViewHolder) = onFirstItemExposed(itemPosition, vh)             override fun onVideoFirstFrame(itemPosition: Int, vh: BaseVideoViewHolder) = onVideoFirstFrame(itemPosition, vh)             override fun onMuteBtnClick(mute: Boolean) = onMuteBtnClick(mute)             override fun onVideoClick() = onVideoClick()             override fun onShareClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =                 onShareClick(itemInfo, itemPosition)              override fun onLikeClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =                 onLikeClick(itemInfo, itemPosition)              override fun onCommentClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int) =                 onCommentClick(itemInfo, itemPosition)              override fun onPlayComplete(itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean) {                 onPlayComplete(itemPosition, vh, isFullScreen)             }             override fun isFragmentShow() = isFragmentShow()              override fun isLoadingData() = isLoadingData()          }         this.recommendPageListener = listener         return listener     }      fun toggleVideoFullScreen(fullScreen: Boolean) {         currentSelectVH?.toggleVideoFullScreen(fullScreen)     }       interface AdapterListener {         fun onFirstItemExposed(itemPosition: Int, vh: BaseVideoViewHolder)          fun onVideoFirstFrame(itemPosition: Int, vh: BaseVideoViewHolder)          fun onMuteBtnClick(mute: Boolean)          fun onVideoClick()          fun onShareClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)          fun onLikeClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)          fun onCommentClick(itemInfo: DiscoveryPageRecommendItem, itemPosition: Int)          fun onPlayComplete(itemPosition: Int, vh: BaseVideoViewHolder, isFullScreen: Boolean)          fun isFragmentShow(): Boolean          fun isLoadingData(): Boolean     }  }"}
{"query":"推荐tab视频页相关代码", "id":"20250304000002","value":"package com.tencent.pangu.discover.base.view  import android.view.LayoutInflater import android.view.View import android.view.ViewGroup import android.widget.SeekBar import androidx.annotation.LayoutRes import androidx.constraintlayout.widget.ConstraintLayout import androidx.recyclerview.widget.RecyclerView import com.tencent.android.qqdownloader.BuildConfig import com.tencent.android.qqdownloader.R import com.tencent.assistant.plugin.PluginProxyUtils import com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem import com.tencent.assistant.utils.JceUtils import com.tencent.assistant.utils.XLog import com.tencent.assistant.utils.fadeIn import com.tencent.assistant.utils.fadeOut import com.tencent.assistant.utils.toGsonString import com.tencent.pangu.discover.base.IDiscoverAreaMoveListener import com.tencent.pangu.discover.recommend.DiscoverRecommendAdapter import com.tencent.pangu.discover.recommend.manager.DiscoverConfig import com.tencent.pangu.discover.recommend.model.getLogString import com.tencent.pangu.discover.recommend.wdiget.DiscoverGuideBar import com.tencent.pangu.discover.recommend.wdiget.DiscoverInfoView import com.tencent.pangu.discover.recommend.wdiget.DiscoverInteractionBar import com.tencent.pangu.discover.recommend.wdiget.DiscoverSeekConstraintLayout import com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView import com.tencent.pangu.playlet.widget.ExpandLayoutTextView   open class BaseVideoViewHolder(     parent: ViewGroup,     @LayoutRes resId: Int,     val scene: Int ) : RecyclerView.ViewHolder(     LayoutInflater.from(parent.context).inflate(resId, parent, false) ) {      companion object{         /** 视频 处于顶部时的 的阈值 */         const val TOP_FRACTION_VALUE = 0.13f         /** 视频 处于底部时的 的bias */         const val BOTTOM_BIAS = 0.005f         /** 视频 处于静止状态 的bias */         const val VIDEO_BIAS = 0.4f         /** 根据 TOP_FRACTION_VALUE 算出的 来确保慢慢滑动到 VIDEO_BIAS */         const val VIDEO_BIAS_ADJUST = 0.119125f         const val VIDEO_BIAS_ADJUST_2 = 0.87f     }      var recommendPageListener: DiscoverRecommendAdapter.AdapterListener? = null      private val TAG = \"DiscoverBaseViewHolder\"     val video = itemView.findViewById<DiscoverVideoView>(R.id.video_view)     private val infoContainer = itemView.findViewById<ConstraintLayout>(R.id.info_container)     private val seekArea = itemView.findViewById<DiscoverSeekConstraintLayout>(R.id.seek_area)     private val maskView = itemView.findViewById<View>(R.id.mask)     private val guideBar = itemView.findViewById<DiscoverGuideBar>(R.id.discover_guide_bar)     private val infoView = itemView.findViewById<DiscoverInfoView>(R.id.discover_info_view)     private val interactionBar = itemView.findViewById<DiscoverInteractionBar>(R.id.discover_interaction_bar)      private var data: DiscoveryPageRecommendItem? = null      private var performShowGuide = false      private val expandListener = object : ExpandLayoutTextView.OnExpandStateChangeListener {         override fun onExpand() {             maskView.fadeIn()             if (guideBar.visibility == View.VISIBLE) {                 guideBar.visibility = View.GONE                 performShowGuide = true             } else {                 performShowGuide = false             }         }          override fun onCollapse() {             maskView.fadeOut()             if (performShowGuide) {                 guideBar.showGuideIfCan()             }         }          override fun onExpandView(show: Boolean) {         }     }      private val seekAreaListener = object : IDiscoverAreaMoveListener {          override fun onMoving(newX: Float, oldX: Float) {             video.onSeekProgress(newX - oldX)         }          override fun onMoveStarted(currentDistanceX: Float) {             video.onSeekStart()         }          override fun onMoveStopped() {             video.onSeekEnd()         }     }      init {         video.scene = scene         video.seakBarlistener = object : SeekBar.OnSeekBarChangeListener {             override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {             }              override fun onStartTrackingTouch(seekBar: SeekBar) {                 XLog.d(TAG, \"onStartTrackingTouch\")                 infoContainer.visibility = View.GONE             }              override fun onStopTrackingTouch(seekBar: SeekBar) {                 XLog.d(TAG, \"onStopTrackingTouch\")                 infoContainer.post { infoContainer.fadeIn() }             }         }          guideBar.scene = scene         guideBar.listener = object : DiscoverGuideBar.GuideBarListener {             override fun canShowGuideNow(): Boolean {                 performShowGuide = true                 return !infoView.isExpand()             }              override fun onGuideClosed() {                 performShowGuide = false             }         }          interactionBar.scene = scene         interactionBar.listener = object : DiscoverInteractionBar.InteractionBarListener {             override fun onLikeClick() {                 recommendPageListener?.onLikeClick(data!!, bindingAdapterPosition)             }              override fun onCommentClick() {                 recommendPageListener?.onCommentClick(data!!, bindingAdapterPosition)             }              override fun onShareClick() {                 recommendPageListener?.onShareClick(data!!, bindingAdapterPosition)             }         }           infoView.scene = scene         infoView.listener = expandListener         seekArea.listener = seekAreaListener         video.firstFrameCallback = {             XLog.i(TAG, \"firstFrameCallback\")             recommendPageListener?.onVideoFirstFrame(bindingAdapterPosition, this)         }          video.playCompleteCallback = { position, isFullScreen ->             recommendPageListener?.onPlayComplete(position, this, isFullScreen)             XLog.i(TAG, \"playCompleteCallbackposition:$position,isFullScreen:$isFullScreen\")         }          video.isFragmentShow = {             recommendPageListener?.isFragmentShow() ?: true         }          video.isLoadingData = {             recommendPageListener?.isLoadingData()?: false         }      }      fun bind(itemData: DiscoveryPageRecommendItem, position: Int) {         if (BuildConfig.DEBUG) {             val copyItemData = JceUtils.copyJceObject(itemData, DiscoveryPageRecommendItem::class.java)             copyItemData.videoInfo.rid = null             copyItemData.guideBarInfo = null             XLog.d(TAG, \"bindposition:$position,data:${copyItemData.toGsonString()}\")         } else {             XLog.i(TAG, \"bindposition:$position,data:${itemData.getLogString()}\")         }         data = itemData         video.bindData(itemData, position)         infoView.bindData(position, itemData)         interactionBar.bindData(position, itemData)         guideBar.bindData(position, itemData)         maskView.visibility = View.GONE         XLog.i(TAG, \"bindItemposition:$position\")         if (position > 0 || DiscoverConfig.recycleForceStopVideo) {             preRenderVideo()         }     }      private fun preRenderVideo() {         XLog.i(TAG, \"preRenderVideo\")         val plugin = PluginProxyUtils.getPlugin(\"com.tencent.assistant.plugin.video\")         if (plugin == null || plugin.version < 111) {             return         }         XLog.i(TAG, \"preRenderVideodoPeRenderplugin:\" + plugin.version)         video.preRender()     }      fun onRecycled() {         video.onRecycled()         guideBar.onRecycled()         performShowGuide = false     }      fun onItemSelected() {         XLog.i(TAG, \"onItemSelected,position:$bindingAdapterPosition\")         video.onSelected()         guideBar.onSelected()     }      fun onPageResume() {         video.onResume()     }      fun onItemUnSelected() {         guideBar.onUnSelect()         video.onUnSelected()     }      fun onDialogHeightChange(height: Int, maxHeight: Int) {         video.onDialogHeightChange(height, maxHeight)     }      fun onViewAttachedToWindow() {         video.onViewAttachedToWindow()     }      fun onViewDetachedFromWindow() {         video.onViewDetachedFromWindow()     }      fun updateCommentCount(newCount: Int) {         interactionBar.updateCommentCount(newCount)     }      fun toggleVideoFullScreen(landscape: Boolean) {         video.toggleVideoFullScreen(landscape)     }      fun isShareDialogShowing() = interactionBar?.isShareDialogShowing() ?: false      fun isVideoFullScreen() = video.isFullScreen()      fun onPagePause() {         video.onPagePause()     }      fun onPageStop() {         video.onPageStop()     }      fun adjustVideoPosition(scrollFraction: Float, visibleHeight: Int) {         val computeBias = (scrollFraction - VIDEO_BIAS_ADJUST) / VIDEO_BIAS_ADJUST_2 * VIDEO_BIAS         val newBias = if (visibleHeight == 0) {             XLog.i(TAG, \"adjustVideoPosition,visibleHeight==0,scrollFraction:$scrollFraction\")             VIDEO_BIAS         } else if (scrollFraction < TOP_FRACTION_VALUE) {             BOTTOM_BIAS         } else if(computeBias < VIDEO_BIAS){             computeBias         } else {             VIDEO_BIAS         }         video.setBias(newBias)     } }"}
{"query":"推荐tab视频页相关代码", "id":"20250304000003","value":"package com.tencent.pangu.discover.recommend.wdiget  import android.content.Context import android.content.res.Configuration import android.graphics.Color import android.media.AudioManager import android.media.MediaPlayer import android.text.TextUtils import android.util.AttributeSet import android.view.Gravity import android.view.LayoutInflater import android.view.View import android.view.ViewGroup import android.widget.FrameLayout import android.widget.ImageView import android.widget.SeekBar import android.widget.Toast import androidx.constraintlayout.widget.ConstraintLayout import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams import com.tencent.android.qqdownloader.BuildConfig import com.tencent.android.qqdownloader.R import com.tencent.assistant.activity.BaseActivity import com.tencent.assistant.component.ToastUtils import com.tencent.assistant.component.video.VideoViewManager import com.tencent.assistant.component.video.listener.DefaultVideoPlayStateNotification import com.tencent.assistant.component.video.listener.TVKPlayerInfoTransfer import com.tencent.assistant.component.video.report.VideoPlayerLifeCycleMonitor import com.tencent.assistant.component.video.view.AttachWindowListener import com.tencent.assistant.component.video.view.VideoViewComponent import com.tencent.assistant.component.video.view.VideoViewComponentV2 import com.tencent.assistant.foundation.video.VideoConstants import com.tencent.assistant.net.NetworkUtil import com.tencent.assistant.protocol.jce.DiscoveryPageRecommendItem import com.tencent.assistant.st.STConst import com.tencent.assistant.utils.SystemUtils import com.tencent.assistant.utils.ViewUtils import com.tencent.assistant.utils.XLog import com.tencent.assistant.utils.dp import com.tencent.assistant.utils.fadeIn import com.tencent.assistant.utils.fadeOut import com.tencent.assistant.utils.isNotNullOrEmpty import com.tencent.pangu.discover.base.manager.IVideoPreRenderVideoManager import com.tencent.pangu.discover.base.utils.DiscoverUIUtil import com.tencent.pangu.discover.recommend.DiscoverRecommendFragment import com.tencent.pangu.discover.recommend.manager.DiscoverConfig import com.tencent.pangu.discover.recommend.manager.DiscoverRecommendPreRenderVideo import com.tencent.pangu.discover.recommend.manager.DiscoverVideoComponentCacheManager import com.tencent.pangu.discover.recommend.model.getLogString import com.tencent.pangu.discover.recommend.model.getSafeVideoRatio import com.tencent.pangu.discover.recommend.model.isLandVideo import com.tencent.pangu.discover.recommend.report.DiscoverBeaconReport import com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter import com.tencent.pangu.discover.recommend.report.DiscoverRecommendReporter.ReportContextKey import com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.TAG import com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.landScapePlayBtnSize import com.tencent.pangu.discover.recommend.wdiget.DiscoverVideoView.Companion.portraitPlayBtnSize import com.tencent.pangu.discover.videofeed.manager.VideoFeedPreRenderVideoManager import com.tencent.pangu.playlet.detail.listener.PlayletProgressUpdateListener import com.tencent.rapidview.deobfuscated.control.video_component.DefaultPlayerViewStateChangeListener import com.tencent.rapidview.deobfuscated.control.video_component.IPlayerReportListener import com.tencent.rapidview.deobfuscated.control.video_component.IPlayerSeekListener import com.tencent.rapidview.deobfuscated.control.video_component.IPlayerStateChangeListener import com.tencent.rapidview.deobfuscated.control.video_component.OnControlViewVisibilityListener import kotlin.math.max import kotlin.math.min  /**  * Copyright (c) 2024 Tencent. All rights reserved.  * Author: marklima  * Email: <EMAIL>  * Date: 2024/7/5 16:36  * Description:  */ class DiscoverVideoView @JvmOverloads constructor(     context: Context,     attrs: AttributeSet? = null,     defStyleAttr: Int = 0 ) : ConstraintLayout(context, attrs, defStyleAttr) {      companion object {         const val TAG = \"DiscoverVideoView\"         val portraitPlayBtnSize = 100.dp         val landScapePlayBtnSize = 36.dp     }      var seakBarlistener: SeekBar.OnSeekBarChangeListener? = null     var playletProgressListener: PlayletProgressUpdateListener? = null     var controViewlistener: OnControlViewVisibilityListener? = null     var firstFrameCallback: ((itemPosition: Int) -> Unit)? = null     var playCompleteCallback: ((itemPosition: Int, isFullScreen: Boolean) -> Unit)? = null     var isFragmentShow: (() -> Boolean)? = null     var isLoadingData: (() -> Boolean)? = null      private val seekBarBottomPadding =         resources.getDimensionPixelSize(R.dimen.discover_recommend_feed_seekbar_bottom_padding)     private val bottomBarHeight =         resources.getDimensionPixelSize(R.dimen.bottom_navigation_height) + seekBarBottomPadding      private val phoneScreenHeight = ViewUtils.getPhoneScreenHeight()     private val statusBarHeight = ViewUtils.getStatusBarHeight()     private val navigationBarHeight = ViewUtils.getNavigationBarHeight()      private var isVideoInit = false     private var isFirstFrameReported = false       private val videoPreRenderManager: IVideoPreRenderVideoManager by lazy {         val manager = if (scene == DiscoverRecommendReporter.Scene.VIDEO_FEED_SCENE) {             VideoFeedPreRenderVideoManager         } else {             DiscoverRecommendPreRenderVideo         }         XLog.i(TAG, \"getVideoPreRenderManagerscene:$scene\")         manager     }      private fun getScreenHeight(): Int {         return getAppScreenHeight(ViewUtils.getScreenHeight(), phoneScreenHeight, navigationBarHeight)     }      private fun getAppScreenHeight(appScreenHeight: Int, phoneScreenHeight: Int, navigationBarHeight: Int): Int {          XLog.d(             TAG, \"getAppScreenHeightappScreenHeight:$appScreenHeight,phoneScreenHeight:$phoneScreenHeight,\" +                     \"navigationBarHeight:$navigationBarHeight\"         )          // 未开启底部导航, 且包含状态栏         if (appScreenHeight == phoneScreenHeight) {             XLog.d(TAG, \"getAppScreenHeightappScreenHeight==phoneScreenHeight:$appScreenHeight\")             return appScreenHeight         }          // 开启底部导航, 不包含状态栏         if ((appScreenHeight + navigationBarHeight + statusBarHeight) <= phoneScreenHeight) {             XLog.d(                 TAG,                 \"getAppScreenHeight$appScreenHeight+$navigationBarHeight+$statusBarHeight<=$phoneScreenHeight\"             )             return appScreenHeight + statusBarHeight         }          // 未开启底部导航, 不包含状态栏         if (appScreenHeight + statusBarHeight == phoneScreenHeight) {             XLog.d(TAG, \"getAppScreenHeight$appScreenHeight+$statusBarHeight==$phoneScreenHeight\")             return appScreenHeight + statusBarHeight         }          XLog.d(TAG, \"getAppScreenHeight$appScreenHeight+$statusBarHeight==$phoneScreenHeight\")         // 包含状态栏         return appScreenHeight      }      override fun onConfigurationChanged(newConfig: Configuration?) {         super.onConfigurationChanged(newConfig)         XLog.i(TAG, \"onConfigurationChanged\")     }      private var currentDialogHeight = 0     private var data: DiscoveryPageRecommendItem? = null     private var itemPosition = 0      var scene: Int = DiscoverRecommendReporter.Scene.RECOMMEND_SCENE     val techReporter: DiscoverBeaconReport         get() = DiscoverBeaconReport.getReporter(scene)     val reporter : DiscoverRecommendReporter         get() = DiscoverRecommendReporter.getReporter(scene)     private var video: VideoViewComponentV2? = null     private val muteBtn by lazy { findViewById<View>(R.id.mute_btn) }     private val fullScreenBtn by lazy { findViewById<View>(R.id.full_screen_btn) }     private val videoContainer by lazy { findViewById<FrameLayout>(R.id.video_container) }     private val seekBar by lazy { findViewById<DiscoverSeekBar>(R.id.seekbar) }     private val seekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {         override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {             seakBarlistener?.onProgressChanged(seekBar, progress, fromUser)         }          override fun onStartTrackingTouch(seekBar: SeekBar) {             XLog.d(TAG, \"onStartTrackingTouch\")             seakBarlistener?.onStartTrackingTouch(seekBar)             video?.onSeekStart(video?.currentPosition ?: 0)         }          override fun onStopTrackingTouch(seekBar: SeekBar) {             XLog.d(TAG, \"onStopTrackingTouch:${seekBar.progress}\")             seakBarlistener?.onStopTrackingTouch(seekBar)             video?.seekTo(seekBar.progress)         }     }      private val playerStateChangeListener = object : IPlayerStateChangeListener {          override fun onPlayStart(videoView: VideoViewComponent?) {             if (itemPosition == 0) {                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PLAY_START)                 techReporter.reportVideoOnStart()             }             playletProgressListener?.onPlayStart(itemPosition)             XLog.i(TAG, \"onPlayStartitemPosition=$itemPosition\")             setVideoMuteState(video, isFirstVideoMute())         }          override fun onPlayStop(videoView: VideoViewComponent?, currentPosition: Int) {             XLog.d(TAG, \"onPlayStopitemPosition=$itemPosition\")         }          override fun onPlayPause(videoView: VideoViewComponent?, currentPosition: Int, reason: Int) {             XLog.d(TAG, \"onPlayPause,itemPosition=$itemPosition\")             playletProgressListener?.onPlayPause(itemPosition)         }          override fun onPlayContinue(videoView: VideoViewComponent?, currentPosition: Int) {             XLog.i(TAG, \"onPlayContinue,itemPosition=$itemPosition,currentPosition=$currentPosition\")             if (itemPosition == 0 && currentPosition < 500) {                 // 开启预渲染后不走 onPlayStart 会走 onPlayContinue                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PLAY_START)                 techReporter.reportVideoOnStart()             }              setVideoMuteState(video, isFirstVideoMute())             playletProgressListener?.onPlayContinue(itemPosition)         }          override fun onPlayComplete(videoView: VideoViewComponent?) {             XLog.d(TAG, \"onPlayComplete,itemPosition=$itemPosition\")             video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_COMPLETE)             playletProgressListener?.onPlayComplete(itemPosition)          }          override fun onPrepared(videoView: VideoViewComponent?) {             if (itemPosition == 0) {                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_PREPARED)                 techReporter.reportVideoPrepared()             }             XLog.i(TAG, \"onPrepared,itemPosition=$itemPosition\")         }          override fun onError(videoView: VideoViewComponent?, errorCode: Int) {             XLog.e(TAG, \"onError$errorCode,itemPosition=$itemPosition,vid=${videoView?.videoUri}\")             if (itemPosition == 0) {                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_ERROR)                 techReporter.addTagExtra(                     DiscoverBeaconReport.ExtraMapKey.VIDEO_FIRST_ERROR_CODE,                     errorCode.toString()                 )                 techReporter.reportVideoError(errorCode)             }              video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_ERROR)         }          override fun onMute(videoView: VideoViewComponent?, mute: Boolean, changeByUser: Boolean) {             XLog.i(TAG, \"onMute=$mute;changeByUser=$changeByUser\")             if (!mute && itemPosition == 0) {                 hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.PHONE_VOLUME_UP)             }         }          override fun onProgressUpdate(videoView: VideoViewComponent?, progress: Int) {             if (videoView == null) {                 return             }             val hasPlayComplete = progress >= videoView.totalDuring - 1000 && videoView.isLoopPlay             if (hasPlayComplete) {                 video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_COMPLETE)                 XLog.i(TAG, \"onProgressUpdatehasPlayComplete,isSystemVideoPlayer=${video?.isSystemVideoPlayer}\")                 // 系统播放器在 loopPlay 时不会回调 TVKPlayerInfoTransfer.PLAYER_INFO_ONE_LOOP_COMPLETE, 此处根据进度判断是否播完                 if (video?.isSystemVideoPlayer == true && DiscoverConfig.autoPlayNextVideo) {                     playCompleteCallback?.invoke(itemPosition, video?.isFullscreen == true)                 }             }              val totalDuration = videoView.totalDuring              if (seekBar.max != totalDuration) {                 seekBar.max = totalDuration             }             // 使用手势更新进度条时，屏蔽视频播放进度更新             if (!seekBar.isUseGestureSeeking()) {                 seekBar.progress = progress             }               playletProgressListener?.onProgressUpdate(itemPosition, progress, totalDuration)         }          override fun onPlayButtonClick(videoView: VideoViewComponent?, isPlaying: Boolean) {             XLog.d(TAG, \"onPlayButtonClick,isPlaying$isPlaying\")         }          override fun onFirstFrameRending(videoView: VideoViewComponent?) {             firstFrameCallback?.invoke(itemPosition)             XLog.i(TAG, \"onFirstFrameRending,itemPosition=$itemPosition,vid=${videoView?.videoUri},isFirstFrameReported=$isFirstFrameReported\")             if (itemPosition == 0) {                 techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_FIRST_FRAME)                 techReporter.reportFistFrame()             }              if (!(itemPosition == 0 && isFirstFrameReported)) {                 isFirstFrameReported = true                 VideoPlayerLifeCycleMonitor.getInstance().onFirstFrameRendingBySelf(videoView)             }             setVideoMuteState(video, isFirstVideoMute())         }          override fun onClickToJump(videoView: VideoViewComponent?) {         }          override fun onPureClick(videoView: VideoViewComponent?) {         }          override fun onRetryPlay(videoViewComponent: VideoViewComponent?) {             XLog.i(TAG, \"onRetryPlayitemPosition=$itemPosition\")         }     }      private val playerSeekListener = object : IPlayerSeekListener {         override fun onSeekStart(videoView: VideoViewComponent?, position: Int) {             XLog.d(TAG, \"onSeekStart$position\")         }          override fun onSeeking(videoView: VideoViewComponent?, position: Int) {             XLog.d(TAG, \"onSeeking$position\")         }          override fun onSeekEnd(videoView: VideoViewComponent?, position: Int) {             XLog.d(TAG, \"onSeekEnd$position\")         }          override fun onSeekComplete(videoView: VideoViewComponent?, position: Int) {             XLog.d(TAG, \"onSeekComplete$position\")             if (videoView == null) {                 return             }              videoView.continuePlay(false)         }      }      private val playerReportListener = IPlayerReportListener { videoReportModel ->         data?.let { data ->             reporter.updateVideoReportModel(                 videoReportModel,                 data,                 itemPosition             )         }     }      private val defaultVideoPlayStateNotification = object : DefaultVideoPlayStateNotification() {         override fun onInfo(mediaPlayer: MediaPlayer?, i: Int, i1: Int) {             super.onInfo(mediaPlayer, i, i1)             XLog.i(TAG, \"onInfo,i=$i,msg=${TVKPlayerInfoTransfer.getMsg(i)}\")             when (i) {                 TVKPlayerInfoTransfer.PLAYER_INFO_ONE_LOOP_COMPLETE -> {                     if (video?.isLoopPlay == true) {                         XLog.i(TAG, \"onInfooneLoopComplete\")                         playletProgressListener?.onPlayComplete(itemPosition)                         if (DiscoverConfig.autoPlayNextVideo) {                             playCompleteCallback?.invoke(itemPosition, video?.isFullscreen == true)                         }                     }                 }                   TVKPlayerInfoTransfer.PLAYER_INFO_START_GET_VINFO -> {                     XLog.i(TAG, \"onInfogetVInfostart$i\")                     if (itemPosition == 0 && data?.videoInfo?.vid?.isNotNullOrEmpty() == true) {                         techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_GET_V_INFO_START)                     }                  }                  TVKPlayerInfoTransfer.PLAYER_INFO_END_GET_VINFO -> {                     XLog.i(TAG, \"onInfogetVInfoend$i\")                     if (itemPosition == 0 && data?.videoInfo?.vid?.isNotNullOrEmpty() == true) {                         techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_GET_V_INFO_END)                     }                 }             }          }     }      init {         LayoutInflater.from(context).inflate(R.layout.discover_recommend_video_view, this, true)           muteBtn.setOnClickListener { onMuteBtnClick() }         seekBar.listener = seekBarChangeListener         fullScreenBtn.setOnClickListener {             if (isLoadingData?.invoke() == true) {                 XLog.i(TAG, \"fullScreenBtnonClickreturn,isLoadingData=$isLoadingData\")                 return@setOnClickListener             }             video?.gotoQuitFullscreen()         }          XLog.d(             TAG, \"heights=${ViewUtils.getPhoneScreenHeight()},${ViewUtils.getScreenHeight()},${ViewUtils.getScreenHeightReal()},${ViewUtils.getNavigationBarHeight()},${ViewUtils.getStatusBarHeight()},\"         )      }      private fun initVideo(position: Int) {          if (isVideoInit) {             return         }         isVideoInit = true          if (position == 0) {             videoPreRenderManager.cancelPreRender()         }          val isPreRenderVideoReady = videoPreRenderManager.isVideoPlayerReady()          XLog.i(TAG,\"initVideoisPreRenderVideoReady=$isPreRenderVideoReady\")          video = if (position == 0 && isPreRenderVideoReady) {             val player = videoPreRenderManager.getVideoPlayer()!!             // getVideoPlayer() 后再获取 isVideoPlayHasReported             isFirstFrameReported = videoPreRenderManager.isVideoPlayHasReported             XLog.i(TAG, \"isFirstFrameReported=$isFirstFrameReported\")             player         } else {             DiscoverVideoComponentCacheManager.getManager(scene).getVideoViewComponent(context)         }          if (position == 0             && isPreRenderVideoReady             && video?.videoUri != data?.videoInfo?.vid             && video?.videoUri != data?.videoInfo?.videoUrl         ) {             XLog.i(                 TAG,                 \"initVideovideo?.videoUri=${video?.videoUri},data.vid=${data?.videoInfo?.vid},data.videoUrl=${data?.videoInfo?.videoUrl}\"             )              if (BuildConfig.DEBUG) {                 ToastUtils.show(context, \"首个视频不一致\")             }         }          val layoutParams = FrameLayout.LayoutParams(             ViewGroup.LayoutParams.MATCH_PARENT,             ViewGroup.LayoutParams.MATCH_PARENT         ).apply {             this.gravity = Gravity.CENTER         }          if (position == 0 && isPreRenderVideoReady) {             addPreRenderVideoView(video!!,  layoutParams)         } else {             videoContainer.addView(video, layoutParams)         }          video?.setDiscoverMode()         video?.setControlViewVisibilityListener(object : OnControlViewVisibilityListener {             override fun onShow(onlyShowPlayButton: Boolean) {                 controViewlistener?.onShow(onlyShowPlayButton)             }              override fun onHidden() {                 controViewlistener?.onHidden()             }         })         video?.registerIPlayerSeekListener(playerSeekListener)         video?.registerIPlayerStateChangeListener(playerStateChangeListener)         video?.setPlayerViewStateChangeListener(object : DefaultPlayerViewStateChangeListener() {             override fun onStop(videoViewComponent: VideoViewComponent?): Boolean {                 video?.videoReportModel?.addExtraReport(STConst.END_TYPE, STConst.VIDEO_END_DUE_TO_LEAVE)                 return true             }         })         video?.registerIPlayerReportListener(playerReportListener)         video?.updatePlayStateNotification(defaultVideoPlayStateNotification)         video?.setAttachWindowListener(object : AttachWindowListener {             override fun onAttachedToWindow() {                 if (itemPosition == 0) {                     techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_ATTACH_TO_WINDOW)                 }             }              override fun onDetachedFromWindow() {              }          })     }      /**      * 预渲染好的第一个视频attach到viewHolder上，texture复用达到续播效果      */     private fun addPreRenderVideoView(video: VideoViewComponentV2, layoutParams: FrameLayout.LayoutParams) {         XLog.i(TAG, \"preRenderVideoPlayertextureremovetoviewholder\")         val group = video.parent as ViewGroup         video.disableVideoViewCallback()         group.removeView(video)         videoContainer.addView(video, layoutParams)         video.enableVideoViewCallback()         // 当推荐页正在展示时播放, 否则在切到推荐页后再播放         if (isFragmentShow()) {             XLog.i(TAG, \"addPreRenderVideoView:video.videoUri=${video.videoUri}\")             if (!TextUtils.isEmpty(video.videoUri)) {                 video.tryContinueOrRestartPlay(true, true)                 if (!isFirstFrameReported && video.rendFirstFrameFlag) {                     XLog.i(TAG, \"addPreRenderVideoView:video.rendFirstFrameFlag=${video.rendFirstFrameFlag}\")                     video.notifyOnFirstFrameRending()                 }             }         }     }      private fun isFragmentShow(): Boolean {         return isFragmentShow?.invoke() ?: true     }       fun bindData(itemData: DiscoveryPageRecommendItem, position: Int) {         XLog.i(TAG, \"bindData:position:$position,videoInfo:${itemData.videoInfo?.getLogString()}\")         data = itemData         itemPosition = position         initVideo(position)         if (position == 0) {             techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BIND_START)         }         muteBtn.visibility = View.GONE         bindVideo(itemData, position, video!!)         // 播放二级页的首个视频组件 context 是 MainActivity, 为了避免MainActivity影响此处不注册         if (context == video?.activityContext) {             VideoViewManager.getInstance().registerVideoViewComponent(video, true)         }         seekBar.progress = 0         if (position == 0) {             techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BIND_END)             techReporter.reportVideoBind()         }     }      fun onSelected() {         video?.setActive(true)         XLog.i(TAG, \"onSelected:${video?.isPlaying},isAttachedToWindow:${video?.isAttachedToWindow},\" +                 \"isAttachToWindow:${video?.isAttachToWindow},isDestroyed:${video?.isDestroyed}\")         if (video?.isPlaying == false) {             XLog.i(TAG, \"onSelectedvideo?.hasCode:${video?.hashCode()},video?.videoUri=${video?.videoUri}\")             // TODO:marklima             video?.tryContinueOrRestartPlay(true, true)             seekBar.progress = 0         }           data?.let {             reporter.reportVideoExposure(it, itemPosition)         }     }      fun onResume() {         if (video?.isManualPaused == false) {             XLog.i(TAG, \"onResume:starttryContinueOrRestartPlay\")             video?.postDelayed({                 XLog.i(TAG, \"onResume:delayruntryContinueOrRestartPlay\")                 val currentContext = context                 if (currentContext is BaseActivity) {                     if (currentContext.isPaused) {                         XLog.i(TAG, \"onResume:skip,delayrunisPaused,tryContinueOrRestartPlay\")                         return@postDelayed                     }                 }                 XLog.i(                     TAG, \"onResume:realRun,delayrunisPaused,tryContinueOrRestartPlay\"                             + \",video.videoUri=${video?.videoUri}\"                 )                 video?.tryContinueOrRestartPlay(true, true)             }, 200)         }     }      private fun bindVideo(itemData: DiscoveryPageRecommendItem, position: Int, video: VideoViewComponentV2) {         if (position == 0) {             itemData.videoInfo?.vid?.let {                 techReporter.addTagExtra(DiscoverBeaconReport.Key.VID, it)             }             itemData.videoInfo?.videoUrl?.let {                 techReporter.addTagExtra(DiscoverBeaconReport.Key.VIDEO_URL, it)             }             techReporter.reportVideoBind()         }          itemData.videoInfo ?: return         XLog.i(TAG, \"bindVideocoverImageUrlis${itemData.videoInfo.coverImg},mid=${itemData.videoInfo.materialId}\")          if (NetworkUtil.isNetworkActive()) {             video.setPlayButtonVisibility(GONE)         } else {             video.setPlayButtonVisibility(VISIBLE)         }          video.updateVideoLayout(videoContainer, itemData.getSafeVideoRatio())         fullScreenBtn.visibility = if (itemData.isLandVideo()) {             VISIBLE         } else {             GONE         }          video.coverImageUrl = itemData.videoInfo?.coverImg         val vid = itemData.videoInfo?.vid         if (vid.isNotNullOrEmpty()) {             video.setVid(vid)             XLog.i(TAG, \"bindVideovidis${itemData.videoInfo.vid}\")         } else if (itemData.videoInfo.videoUrl.isNotNullOrEmpty()) {             val url = itemData.videoInfo.videoUrl             video.setVideoUrl(url)             XLog.i(TAG, \"bindVideourlis$url\")         } else {             video.clearVideoUrl()             video.stop()             XLog.e(TAG, \"bindVideovidisnull\")             if (BuildConfig.DEBUG) {                 ToastUtils.show(context, \"视频地址为空\")             }             return         }         reporter.let {             video.updateReportInfo(itemData, it, position)         }         video.setAutoPlay(true)         techReporter.addTag(DiscoverBeaconReport.PreloadKey.VIDEO_BEFORE_START)         video.post {             if (position == 0) {                 // 当推荐页正在展示时播放, 否则在切到推荐页后再播放                 if (isFragmentShow()) {                     techReporter.addTag(DiscoverBeaconReport.PreloadKey.START_VIDEO)                     XLog.i(TAG, \"bindVideo:video.videoUri=${video.videoUri}\")                     val success = video.tryContinueOrRestartPlay(true, true)                     techReporter.reportStartVideo(success)                 }                 updateMuteBtn(isFirstVideoMute())                 setVideoMuteState(video, isFirstVideoMute())             }         }     }         fun onDialogHeightChange(height: Int, maxHeight: Int) {         data?.videoInfo?.let {             val landscapeVideo = isLandScapeVideo(data)             if (landscapeVideo) {                 handleLandscapeVideoHeightChange(height, maxHeight)             } else {                 handlePortraitVideoHeightChange(height, maxHeight)             }              if (height > 0 && currentDialogHeight == 0) {                 fullScreenBtn.fadeOut()             } else if (height == 0 && currentDialogHeight > 0) {                 fullScreenBtn.fadeIn()             }              currentDialogHeight = height         }     }      fun onSeekProgress(distanceX: Float) {         var newProgress: Int = calProgressValue(distanceX)         XLog.d(TAG, \"distanceX:$distanceXnewProgress:$newProgress\")         seekBar.updateProgress(newProgress, true)     }      private fun calProgressValue(distanceX: Float): Int {         val progressChange = (distanceX * seekBar.max / seekBar.width).toInt()         var newProgress: Int = min(seekBar.progress + progressChange, seekBar.max)         newProgress = max(0, newProgress)         return newProgress     }      fun onSeekStart() {         seekBar.startTrackingTouch()     }      fun onSeekEnd() {         seekBar.stopTrackingTouch()     }      private fun isLandScapeVideo(data: DiscoveryPageRecommendItem?): Boolean {         return data?.isLandVideo() ?: true     }      private fun handlePortraitVideoHeightChange(height: Int, maxHeight: Int) {         val videoViewHeight = getScreenHeight() - height         val videoViewWidth = (videoViewHeight * data!!.getSafeVideoRatio()).toInt()         val marginBottom = if (height > bottomBarHeight) {             height - bottomBarHeight         } else {             0         }          videoContainer.layoutParams = (videoContainer.layoutParams as? ConstraintLayout.LayoutParams)?.apply {             this.width = if (DiscoverConfig.portraitVideoScaleOriginal) {                 LayoutParams.MATCH_PARENT             } else {                 videoViewWidth             }             this.height = videoViewHeight             this.bottomMargin = marginBottom         }         video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)         video?.post {             video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)         }           XLog.i(             TAG,             \"handlePortraitVideoHeightChange,marginBottom:${marginBottom},videoHeight:$videoViewHeight,videoWidth:$videoViewWidth\"         )     }      private fun handleLandscapeVideoHeightChange(height: Int, maxHeight: Int) {          val screenWidth = ViewUtils.getScreenWidth()         val availableHeight = getScreenHeight() - height         val videoHeight = (screenWidth / data!!.getSafeVideoRatio()).toInt()         val videoViewHeight = videoHeight.coerceAtMost(availableHeight)         val videoWith = (videoViewHeight * data!!.getSafeVideoRatio()).toInt()         val videoViewWidth = screenWidth.coerceAtMost(videoWith)          val extraHeight = getScreenHeight() - maxHeight - videoViewHeight          val videoContainerHeight = if (extraHeight > 0) {             (extraHeight / 0.4F).toInt() + videoViewHeight         } else {             videoViewHeight         }          val maxMarginBottom = getScreenHeight() - bottomBarHeight - videoContainerHeight         val marginBottom = if (height > bottomBarHeight) {             (height - bottomBarHeight).coerceAtMost(maxMarginBottom)         } else {             0         }          XLog.d(             TAG, \"handleLandscapeVideoHeightChange,screenWith:$screenWidth,screenHeight:${getScreenHeight()},\" +                     \"videoViewHeight:$videoViewHeight,videoViewWidth:$videoViewWidth,maxHeight:$maxHeight,\" +                     \"extraHeight:$extraHeight,videoContainerHeight:$videoContainerHeight,maxMarginBottom:\" +                     \"$maxMarginBottom,marginBottom:$marginBottom,bottomBarHeight:$bottomBarHeight\"         )          videoContainer.layoutParams = (videoContainer.layoutParams as ConstraintLayout.LayoutParams).apply {             this.width = videoViewWidth             this.height = videoViewHeight             this.bottomMargin = marginBottom         }         video?.setCoverImageViewSize(videoViewWidth, videoViewHeight)          XLog.i(             TAG,             \"handleLandscapeVideoHeightChange,marginBottom:${marginBottom},videoHeight:$videoViewHeight,videoWidth:$videoViewWidth\"         )      }      fun onViewAttachedToWindow() {         XLog.d(TAG, \"onViewAttachedToWindow,position:$itemPosition,\")         if (DiscoverConfig.discoverSlideVideoShowCoverImage){             video?.showCoverImage()         }     }      fun onViewDetachedFromWindow() {         XLog.d(TAG, \"onViewDetachedFromWindow,position:$itemPosition,${data?.introductionInfo?.authorName}\")         if (DiscoverUIUtil.enablePreRender()) {             video?.pause(true, VideoConstants.PAUSE_REASON_SCROLL_OUTSIDE)         }      }      override fun onDetachedFromWindow() {         super.onDetachedFromWindow()         XLog.d(TAG, \"onDetachedFromWindow,position:$itemPosition,${data?.introductionInfo?.authorName}\")         if (video?.isFullscreen == true) {             video?.gotoQuitFullscreen()         }     }      private fun setVideoMuteState(video: VideoViewComponentV2?, mute: Boolean, byUser: Boolean = false) {         XLog.i(TAG, \"setVideoMuteState,mute:$mute,byUser:$byUser\")         video?.setMute(mute, true, byUser)     }       private fun VideoViewComponent.updateReportInfo(         data: DiscoveryPageRecommendItem,         reporter: DiscoverRecommendReporter,         reportPosition: Int,     ) {         reporter.updateVideoReportModel(             videoReportModel,             data,             reportPosition         )     }      private fun updateMuteBtn(mute: Boolean) {         if (mute) {             muteBtn.visibility = View.VISIBLE             reporter.reportBtnExposure(                 position = itemPosition,                 info = data,                 btnTitle = DiscoverRecommendReporter.ButtonTitle.MUTE_BTN             )         } else {             muteBtn.visibility = View.GONE         }     }      private fun onMuteBtnClick() {         val mute = video?.isMute ==  true         if (!mute) {             val volume = SystemUtils.getCurrentVolume(AudioManager.STREAM_MUSIC)             if (volume == 0) {                 ToastUtils.show(muteBtn.context, \"请调大系统音量\", Toast.LENGTH_SHORT)             }         }          XLog.i(TAG, \"clickmutestate=$mute\")         hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.CLICK)         setVideoMuteState(video, mute, true)     }      private fun reportMuteBtnClick(type: String) {         reporter.reportBtnClick(             position = itemPosition,             info = data,             btnTitle = DiscoverRecommendReporter.ButtonTitle.MUTE_BTN,             btnStatus = null,             reportContextKey = ReportContextKey.VIDEO_REPORT_CONTEXT,             DiscoverRecommendReporter.ExtendFiledKey.UNI_CANCEL_TYPE to type         )     }      private fun isFirstVideoMute(): Boolean {         return itemPosition == 0                 && DiscoverRecommendFragment.firstVideoMute                 && isRecommendScene()     }      private fun isRecommendScene() = scene == DiscoverRecommendReporter.Scene.RECOMMEND_SCENE       fun onRecycled() {         XLog.i(TAG, \"onRecycled,position=$itemPosition,vid=${video?.videoUri}\")         isFirstFrameReported = false         video?.setPlayButtonVisibility(GONE)         video?.onRecycled(DiscoverConfig.recycleForceStopVideo)         VideoViewManager.getInstance().unregisterVideoViewComponent(video)     }      fun preRender() {         XLog.i(TAG, \"preRender\")         if (DiscoverUIUtil.enablePreRender()) {             if (DiscoverConfig.recycleForceStopVideo && video?.isPreRendered == true) {                 return             }             video?.preRender()         }     }      fun onUnSelected() {         if (DiscoverUIUtil.enablePreRender()) {             video?.pause(true, VideoConstants.PAUSE_REASON_SCROLL_OUTSIDE)         }         hideMuteBtn(DiscoverRecommendReporter.ButtonStatus.SCROLL_DOWN)         video?.setActive(false)     }      private fun hideMuteBtn(reason: String) {         if (muteBtn.visibility == VISIBLE) {             updateMuteBtn(false)             reportMuteBtnClick(reason)             DiscoverRecommendFragment.firstVideoMute = false         }     }      fun toggleVideoFullScreen(fullScreen: Boolean) {         // 只横屏视频做旋转         if (!isLandScapeVideo(data)) {             return         }          if (fullScreen != video?.isFullscreen) {             video?.gotoQuitFullscreen()         }     }      fun isFullScreen(): Boolean {         return video?.isFullscreen == true     }      fun onPagePause() {         if (video?.isPaused == false) {             video?.onPause(video?.context)         }     }      fun onPageStop() {         if (video?.isStopped == false) {             video?.onStop(video?.context)         }     }      fun setBias(newBias: Float) {         val layoutParams = videoContainer.layoutParams as LayoutParams         val currentBias = layoutParams.verticalBias         if (currentBias == newBias) {             XLog.i(TAG, \"currentBias==newBias\")             return         }         layoutParams.verticalBias = newBias         videoContainer.layoutParams = layoutParams     }  }  fun VideoViewComponentV2.updateVideoLayout(     videoContainer: ViewGroup,     videoRatio: Float ) {     val video = this     val landscapeVideo = isLandVideo(videoRatio)     video.setIsLandscapeVideo(landscapeVideo)     val screenWidth = ViewUtils.getScreenWidth()     val videoHeight = if (landscapeVideo) {         (screenWidth / videoRatio).toInt()     } else {         ViewGroup.LayoutParams.MATCH_PARENT     }      video.layoutParams = video.layoutParams?.apply {         this.height = videoHeight     }      video.post {         video.layoutParams = video.layoutParams?.apply {             this.height = videoHeight         }     }      videoContainer.layoutParams = videoContainer.layoutParams.apply {         this.height = if (landscapeVideo) {             LayoutParams.WRAP_CONTENT         } else {             LayoutParams.MATCH_PARENT         }     }      videoContainer.post {         videoContainer.layoutParams.apply {             this.height = if (landscapeVideo) {                 LayoutParams.WRAP_CONTENT             } else {                 LayoutParams.MATCH_PARENT             }         }     }      val coverHeight = (screenWidth / videoRatio).toInt()     video.setCoverImageViewSize(screenWidth, coverHeight)     video.post { video.setCoverImageViewSize(screenWidth, coverHeight) }     video.landscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     if (landscapeVideo || DiscoverConfig.portraitVideoScaleOriginal) {         video.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     } else {         video.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_FULLSCREEN     }     video.refreshScaleType()      XLog.d(TAG, \"ratio=$videoRatio,width=$screenWidth,height=${video.layoutParams?.height}\") }      fun VideoViewComponentV2.setDiscoverMode() {     this.isDiscoverMode = DiscoverUIUtil.enablePreRender()     this.setFixPauseBeforePrepared(DiscoverConfig.fixPauseBeforePrepare)     this.setBackgroundColor(Color.BLACK)     this.setNeedLoopPlay(true)     this.setCanPlayNotWifiFlag(true)     this.setIsShowMuteView(false, true)     this.isMute = true     this.setIsShowFullScreenView(false, true)     this.setIsShowTextProgressLayout(false, true, true)     this.setIsShowTextProgressLayout(true, false, true)     this.setIsShowProgressBar(false, true)     this.setIsShowProgressBar(true, false)     this.setEnableAdaptAutoScaleType(true)     this.setClickVideoToPause(false)     this.updateControlViewBottomMargin(16.dp)     this.adaptPortraitScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     this.adaptLandscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     this.landscapeScaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     this.scaleType = VideoConstants.PLAYER_SCALE_ORIGINAL_RATIO     this.isReportLoopPlayStart = false     this.isReportLoopPlayEnd = false     this.setOnceLoopComplete(false)     this.setCloseGaussBlur(true)     this.setOnlyShowControlViewOnManual(true)     this.setNeedPreload(true)     this.setClickVideoToPause(true)     this.setPlaySpeedRatio(1.0F)     this.setStartPlayRationInsideListView(0F)     this.setCoverImageViewScaleType(ImageView.ScaleType.CENTER_CROP.ordinal)     this.hidePlayButton()     this.setPlayButtonSize(portraitPlayBtnSize, true)     this.setPlayButtonSize(landScapePlayBtnSize, false)     this.setCoverDismissAnimationDuring(100)     this.onClickNotControlArea = {         XLog.i(TAG, \"onClickNotControlArea\")         false     } }"}
{"query":"推荐tab视频页相关代码", "id":"20250304000004","value":"package com.tencent.pangu.discover.recommend.wdiget  import android.content.Context import android.util.AttributeSet import android.view.LayoutInflater import android.view.ViewGroup import android.widget.FrameLayout import android.widget.SeekBar import android.widget.TextView import com.tencent.android.qqdownloader.R import com.tencent.assistant.component.video.view.VideoComponentUtils import com.tencent.assistant.utils.XLog import com.tencent.assistant.utils.dp import com.tencent.assistant.utils.fadeIn import com.tencent.assistant.utils.fadeOut import com.tencent.pangu.playlet.detail.widget.PlayletSeekBar  /**  * Copyright (c) 2024 Tencent. All rights reserved.  * Author: marklima  * Email: <EMAIL>  * Date: 2024/7/9 16:17  * Description:  */ class DiscoverSeekBar @JvmOverloads constructor(     context: Context,     attrs: AttributeSet? = null,     defStyleAttr: Int = 0 ) : FrameLayout(context, attrs, defStyleAttr) {      var listener: SeekBar.OnSeekBarChangeListener? = null      private val seekBarSelectTopPadding = 7.dp     private val seekBarTopPadding = 8.dp     private val seekBar by lazy { findViewById<PlayletSeekBar>(R.id.progress_bar) }     private val progressTextLayout by lazy { findViewById<ViewGroup>(R.id.progress_text_layout) }     private val progressTV by lazy { findViewById<TextView>(R.id.video_progress_text) }     private val totalProgressTV by lazy { findViewById<TextView>(R.id.video_total_length) }      var max: Int         get() = seekBar.max         set(value) {             seekBar.max = value         }      var progress: Int         get() = seekBar.progress         set(value) {             seekBar.progress = value         }       init {         LayoutInflater.from(context).inflate(R.layout.discover_recommend_seekbar, this, true)         initView()     }      fun updateProgress(progress: Int, fromUser: Boolean) {         seekBar.progress = progress         seekBar.updateProgress(progress, fromUser)     }      fun startTrackingTouch() {         seekBar.startTrackingTouch()     }      fun stopTrackingTouch() {         seekBar.stopTrackingTouch()     }      fun isUseGestureSeeking(): Boolean {         return seekBar.isUseGestureSeeking     }      private fun initView() {         seekBar.enableTouchSeek = false         seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {             override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {                 val current = VideoComponentUtils.getMinutesSecondTime(progress)                 val total = VideoComponentUtils.getMinutesSecondTime((seekBar.max))                 progressTV.text = current                 totalProgressTV.text = total                 listener?.onProgressChanged(seekBar, progress, fromUser)             }              override fun onStartTrackingTouch(seekBar: SeekBar) {                 progressTextLayout.visibility = VISIBLE                 seekBar.setPadding(0, seekBarSelectTopPadding, 0, seekBar.paddingBottom)                 listener?.onStartTrackingTouch(seekBar)             }              override fun onStopTrackingTouch(seekBar: SeekBar) {                 progressTextLayout.post { progressTextLayout.fadeOut() }                 seekBar.setPadding(0, seekBarTopPadding, 0, seekBar.paddingBottom)                 listener?.onStopTrackingTouch(seekBar)             }         })     }   } "}