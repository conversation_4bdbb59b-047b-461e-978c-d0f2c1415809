
【技术知识问答agent】
通过构建代码库索引和编排知识问答agent，实现大模型对代码库的理解与问答。以下是针对推荐页模块，完成的几项工作：
1. 构建业务场景索引。建立[业务场景_类名]-[类代码]对照表（例：推荐页_DiscoverRecommendFragment → 对应类）），帮助大模型定位核心业务代码。
2. 构建代码注释索引。建立[代码片段总结]-[代码片段]对照表，把代码片段翻译成人类自然语言（大白话）说明，增大用户query命中索引的概率，提高大模型 定位代码细节 的能力。
3. 初步搭建 推荐页-技术知识问答agent，识别用户query中的业务场景（推荐页）和技术要素（如播放时隐藏进度条），同步调用类级索引和代码片段索引进行交叉验证。初步实现业务场景的关键代码定位，并支持技术问题的推理回答。
   当用户提问时，agent流程如下
    - 理解问题：自动识别问题中的业务场景（如推荐页）和技术关键词（如播放时隐藏进度条）
    - 双重检索：同时查找对应的类文件和相关代码片段
    - 智能验证：交叉比对两类索引结果，确保答案准确性
    - 生成回答：用自然语言解释技术实现，支持追问和多步推理


# 背景
通过构建代码库索引和编排知识问答agent，实现大模型对代码库的理解与问答。

整体设计


通过构建代码库索引和编排知识问答agent，实现大模型对代码库的理解与问答。以下是针对推荐页模块，完成的几项工作：
1. 构建业务场景索引。建立[业务场景_类名]-[类代码]对照表（例：推荐页_DiscoverRecommendFragment → 对应类）），帮助大模型定位核心业务代码。
2. 构建代码注释索引。建立[代码片段总结]-[代码片段]对照表，把代码片段翻译成人类自然语言（大白话）说明，增大用户query命中索引的概率，提高大模型 定位代码细节 的能力。
3. 初步搭建 推荐页-技术知识问答agent，借助上述构建的索引，识别用户query中的业务场景（推荐页）和技术要素（如播放时隐藏进度条）。初步实现业务场景的关键代码定位，并支持技术问题的推理回答。