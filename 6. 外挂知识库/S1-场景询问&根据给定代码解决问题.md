处理场景 -- 定位场景代码（详情页代码在哪？**能否关联tapd单子和技术文档**）

终极目标：能找到对应的类就行，把代码给模型，模型会处理。


思考：怎么让查找路径更准确？
- 调用链路，根据调用链路找到方法
- 如果需要一直在该类上进行问答，修改错误。如何一直让模型看到该类的全文
- 模型看到了方法，但没有看到调用方法的实现，怎么从知识库中找到对应方法的上下文？
  - 代码实现（自己写个插件）
    - 如何知道模型缺少方法？
      - 格式化，模型的输出格式 {想要查找的类}{缺少的方法}
      - 根据 方法名、类名，返回类
  - 逻辑分流
    - 识别意图
      - 查找场景
      - 是否需要查找新的类（判断方法）
        - 如果[类]、[总结片段]、[代码库]信息不足，不能了解到足够信息，请列出需要了解的代码块的类名或者函数名。格式为{needMore:""} -- 是
        - 否则 输出格式为{"enoughInfo":"true"}   -- 否


代码定位（如“app详情页的代码在哪里”）
- **检索优化**：  
  - 关键词检索匹配文件路径（如 `DetailActivity.java`）。  
  - 元数据筛选（如 `type: class` 且包含 `详情页` 注释）。  
- **生成模板**：  
  ```plaintext
  App详情页的代码位于：  
  文件路径：{文件路径}  
  包含类：{类名}  
  主要方法：{方法列表}
  ```


- 领域扩展 --- 如 打tag的时候，可以配合不同的英文名。更好的命中索引。（q：进度条。索引操作：查找tag，定位到：进度条、seekBar。）

关键词查找（类-全文）、注释查找（找到注释的chunk）、tag查找  -> 定位类              -> 查看主类输出答案       
（定位代码块：）       （直接从索引 输出主类）  

进一步询问，推荐页的进度条组件在哪？
关键词查找、tag查找（进度条组件）  -> 定位类              -> 查看主类输出答案


app推荐页的代码在哪？  - 专门的业务术语 映射表。只能从这几个里面问。 -- 用户输入的时候，可以检索映射表，给出提示列表
-> 搜索推荐页的 tag 


- 元数据增强 --- 为每个代码块添加业务标签（如 `#支付`、`#推荐`），通过聚类或规则生成。
为每个代码块附加：
```json
{
  "file_path": "com/example/app/ui/recommend/RecommendRequest.kt",
  "class": "RecommendRequest",
  "method": "parseResponse",
  "tag":"推荐",
  "last_modified": "2024-03-15",
  "git_blame": "author: Alice"
}
```

```json
{"query":"场景tag", "value":"场景class"}
```

