# todo
- 可以在格式化日志的时候，就进行时间的筛选
- pageIn 2006
- pageOut 2005
- 优化函数：filterDumplicatedLine 
- 是否可以 check 模型的回答？意图check？LangGraph Retrieval Agent？


# 优化方案

就是基于日志的特征值，抽取多条路线。比如我们是可以从日志中知道用户进入前台和退出到后台的关键节点的，其中用户打开了哪些页面，行为日志也是有的。然后也就是基于这些特征，多路开花。比如先分析当前用户的行为（操作路径），然后分析操作路径上是否产生了异常，如果产生了异常，那是什么异常？反正就是这样一层层的拆。

- 按时间过滤
- 过滤用户操作路径。在answer里清晰的打出用户访问了哪些页面点击了什么按钮页面前后台切换进程启动时间等。然后每一步操作判断是否有异常。 然后异常等操作分析原因。


获取切换前后台的日志
获取行为日志：曝光、点击 -> 根据日志信息获取用户的操作路径。操作是否有异常？？
- 怎么判断操作是否有异常
  - 截取 用户行为1 到 用户行为2 之间 的 日志
    - 如果有异步日志怎么办？
  - 请求数据是否正常
  - 有没有出现error

# 用户行为日志tag
- BaseActivity 
  - 页面 create（曝光） [ActivityLifeCycle] onCreate
  - 页面 Start [ActivityLifeCycle] onStart 
  - 页面 onPause [ActivityLifeCycle] onPause 
  - 页面 onResume [ActivityLifeCycle] onResume
  - 页面 onDestroy [ActivityLifeCycle] onDestroy
  - 页面 onStop [ActivityLifeCycle] onStop
- [BaseFragment] onCreate this = HomeSecondFloorMultiTabFragment
- 用户点击: tag - UserActionLog === UserActionLog -- scene: 10113 actionId: 200 sourceScene: 2000 report_context: {"uni_cardid":"PNG-TabFeedsCardAdapter-10-_590360","uni_material_ids":"590360","uni_content_loadid":"174194351037989","uni_content_load_seq_no":"1","uni_card_title_name":"热门应用","uni_card_tag_list":"_590360","uni_related_appid":"6633","uni_dloadfree_type":"0","uni_is_orderfree":"0"} report_element: app

## 用户行为日志过长怎么办？
- 页面之间进行分片？
- 有很多操作是重复的

## bug分析，是怎么操作的？
- bug：点击 新游日历入口 出现闪退
- 定位时间(过滤前后五分钟？)
- 用户路径 - 



# 人为日志定位分析
1. 下载问题，根据下载的tag来过滤日志
2. 查看过滤的日志，获取关键字段的信息：scene（定位页面）、retCode（返回码查询失败原因）
3. 根据日志解析

# 模型定位日志
1. 用户问题：时间、业务tag
2. 通过tag过滤关键日志（前后五分钟）、（从知识库）检索出 scene 和 retCode
3. 关键日志 + scene 和 retCode + 问题描述 丢给模型处理。


- 关键日志是否需要再解释后丢给模型？


清洗日志 - 日志特征

用户输入问题 -> 知识库(业务场景-tag) -> 找到了对应tag（可以进一步用模型校验对不对），生成标题并归类 -> 建立日志规则知识库
     (找出与用户问题对应的tag)                                      [生成巡检规则标题 - 归类（下载、安装、其他）]

清洗日志(结合日志规则知识库，筛选出关键的日志) -> 翻译成时间线（日志含义、日志原文）-> ai（翻译后的日志和规则描述）-> 判断日志中是否有规则描述的问题










# 巡检规则数据库

这里只是普通的数据库， 我用的MongoDB， 有一个ui界面， 人工校验大模型补齐的相关字段是否正确，可以手动修改， 确认后插入数据库，查询目前demo也没做处理，就是全部查出来挨着发给大模型看当前日志有没有这里描述的问题，后续可能页面上做个输入框，用户输入当前自己碰到什么问题， 然后只找出跟描述相关的巡检规则数据进行巡检， 数据结构如下@Schema({ timestamps: true })
export class AIInspectRule {
@Prop({ required: true })
title: string; // ai生成标题
@Prop({ required: true })
rule: string; // 用户输入的自然语言问题规则描述
@Prop({ default: [] })
matchCases: string[]; // 预留，命中case日志样本，后续有需要加入prompt提高准确率
@Prop({ default: [] })
noMatchCases: string[]; // 预留，反面case日志样本，后续有需要加入prompt提高准确率
@Prop({ type: MSchema.Types.ObjectId, ref: Sdk.name, autopopulate: { select: '_id name' } })
sdk: ObjectId;
@Prop({ type: MSchema.Types.ObjectId, ref: LogRule.name, autopopulate: true })
logRules: ObjectId; // 关联的日志特征id
@Prop({ default: [], enum: TRTC_ISSUE_TAGS })
tags: string[]; // ai生成分类
@Prop({ default: false })
safeForQcloud: boolean;
@Prop()
createdBy: string;
@Prop()
updatedBy: string[];
}



# 基于时间线的日志检索

## 清洗日志 
切换前后台：tag -- Lifecycle
根据tag（Lifecycle）提取出用户的行为路径（切换的页面、切换前后台）。

根据问题tag（downloader）过滤日志，查找关键信息（scene（哪个页面场景）、retCode（返回码：失败还是错误））

知识库：
1. retCode对应的返回码解释，如： [retCode:0 - 成功] [retCode:-1 - 握手失败]
2. scene对应的场景解释，如： [scene:10975 - 云端应用-云端页]


目标：让模型看到关键日志、知道关键日志的含义。





                                -> 过滤用户行为链路（daemon文件中的 Tinker.ProcessLifecycle tag：Lifecycle） -> 日志喂给模型，解析用户链路

用户输入问题 ->  意图解析（下载问题）                                                             -> 获取两个回答，输出给模型，进行分析。

                                -> 过滤下载任务（tag：downloader）-> 查询索引，解析日志信息


## 过滤后的日志过长 怎么办？
分块构建成索引再给模型？

- 先将日志分块，不同的分块进行总结 -> 综合总结 喂给模型

1. 动态分块处理

将长日志分割为语义段落，采用"总结-分析"链式处理：
日志分块 → 分块摘要 → 关键事件提取 → 综合分析

2. 上下文动态加载
建立优先级机制，仅加载一部分给模型，问模型是否足够？不够再添加


# 优化 -- 意图check？ LangGraph Retrieval Agent？