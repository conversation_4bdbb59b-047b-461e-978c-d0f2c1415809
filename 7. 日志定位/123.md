# langchain框架 web

# 关联团队资源分摊
# 测试环境 管理员审批，需要计费

# python项目需要先改写成一个可访问的服务 -> 创建服务（可以先固定port，但是ip不固定，得看看） -> 镜像（环境） -> 关联代码库（python的话 要写一些额外的文件）

1. 完善企业微信机器人交互，支持用户输入 下载链接、问题、时间 分析日志。
2. 扩展更多的定位问题子agent，如活动页、安装等场景。
3. aisee日志分析接口对接中。已将日志分析模块封装成Python包，aisee侧正引入开发中，预计最快明天开发完。

1. 企业微信机器人交互优化。支持用户输入下载链接、问题描述及时间信息 分析日志。
2. 问题定位子Agent扩展。如活动页、安装、云游等场景。
3. aisee日志分析接口对接中。已将日志分析模块封装成Python包，aisee侧正引入开发中，预计最快明天开发完。

我们正在扩展更多的定位问题子Agent，涵盖活动页面、安装等多种场景，以提升用户问题解决的效率和准确性。

Aisee日志分析接口对接进展

Aisee日志分析接口的对接工作正在进行中。目前，我们已将日志分析模块成功封装为Python包，Aisee团队正在进行相关开发，预计最快将在明天完成。