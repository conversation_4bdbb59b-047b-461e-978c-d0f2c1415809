import logging
import sys
import os
from datetime import datetime
from collections import deque

from wecom_bot_svr import WecomBotServer, RspTextMsg, RspMarkdownMsg, ReqMsg
from wecom_bot_svr.req_msg import TextReqMsg

from logs.logger import app_logger
from agent.filter_user_action import FilterUserAction
from client.wecom_client import WecomClient
from wecom.wecom_bot_tips import WecomBotTips
from wecom.wecom_bot import WecomBot

# 参考链接：https://developer.work.weixin.qq.com/document/path/99399

# 记录已处理消息
processed_msg_ids = deque(maxlen=100)

# todo 如果因为输入问题导致的失败，可以反馈一下什么原因，如bug时间不匹配
def msg_handler(req_msg: ReqMsg, server: WecomBotServer):
    ret = RspMarkdownMsg()
    app_logger.info(f'用户输入：{req_msg.content}')
    app_logger.info(f'会话id：{req_msg.chat_id}')
    app_logger.info(f'用户id：{req_msg.from_user.en_name}')
    # self.from_user = UserInfo(user.find('Alias').text, user.find('Name').text, user.find('UserId').text)
    app_logger.info(f'消息ID：{req_msg.msg_id}')
    # 消息id
    msg_id = req_msg.msg_id
    if msg_id in processed_msg_ids:
        app_logger.info(f'消息已处理过，跳过')
        ret.content = WecomBotTips.retry_markdown()
        return ret
    # 将消息ID添加到已处理集合中
    processed_msg_ids.append(msg_id)
    app_logger.info(f'已处理消息：{processed_msg_ids}')

    if req_msg.msg_type == 'text' and isinstance(req_msg, TextReqMsg):
        if req_msg.content.strip() == 'help':
            ret.content = help_md()
            return ret
        elif req_msg.content.strip().lower().startswith('日志链接') and server is not None:
            # 发送加载提示
            WecomBotTips.send_loading_tips(req_msg.chat_id)
            # 获取日志分析结果
            wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name)
            result_save_path = wecom_bot.analyze_log()
            if result_save_path:
                server.send_file(req_msg.chat_id, result_save_path)
                return RspTextMsg()
            else:
                ret.content = WecomBotTips.help_markdown()
                return ret
            
    # 返回默认帮助信息
    ret.content = WecomBotTips.help_markdown()
    return ret

# 事件消息
# 参考链接：https://developer.work.weixin.qq.com/document/path/91881
def event_handler(req_msg):
    app_logger.info(f'事件消息: {req_msg.event_type}')
    ret = RspMarkdownMsg()
    # 项目框架只实现了出群事件处理
    if req_msg.event_type == 'add_to_chat':  # 入群事件处理
        print('add_to_chat')
        app_logger.info(f'机器人加入群聊：{req_msg.chat_id}')
        ret.content = help_md()
        # ret.content = f'msg_type: {req_msg.msg_type}\n群会话ID: {req_msg.chat_id}\n查询用法请回复: help'
    elif req_msg.event_type == 'delete_from_chat':  # 出群事件处理 待开发
        print('delete_from_chat')
        ret.content = bot_delete_from_chat()
    elif req_msg.event_type == 'enter_chat':  # 进入会话事件处理 待开发
        print('enter_chat')
        ret.content = help_md()
    return ret


def main():
    logging.basicConfig(stream=sys.stdout)
    logging.getLogger().setLevel(logging.INFO)

    token = '4rwXKaSJ9Tvh2rboLAsAJ5P'
    aes_key = 'xbjt1vsILWYRtF7R9emM5nwt58dMMUJFaOC9pxzvlIF'
    corp_id = ''
    host = '0.0.0.0'
    port = 5001
    # https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec740cad-1a6f-440e-9e37-a869724b8085
    bot_key = 'ec740cad-1a6f-440e-9e37-a869724b8085'  # 机器人配置中的webhook key

    # 这里要跟机器人名字一样，用于切分群组聊天中的@消息
    bot_name = 'lichenlin-test'
    server = WecomBotServer(name=bot_name, host=host, port=port, path='/log_wecom_bot', token=token, aes_key=aes_key, corp_id=corp_id,
                            bot_key=bot_key)

    server.set_message_handler(msg_handler)
    server.set_event_handler(event_handler)
    server.run()


if __name__ == '__main__':
    main()
