# Git global setup
git config --global user.name "lichen<PERSON>"
git config --global user.email "<EMAIL>"

# Create a new repository
<NAME_EMAIL>:yyb-android/logasistent.git
cd logasistent
git switch --create master
touch README.md
git add README.md
git commit -m "add README"
git push --set-upstream origin master

# Push existing folder.
cd existing_folder
git init --initial-branch=master
git remote <NAME_EMAIL>:yyb-android/logasistent.git
git add .
git commit -m "Initial commit"
git push --set-upstream origin master

# Push existing Git repository.
cd existing_folder
git remote rename origin old-origin
git remote <NAME_EMAIL>:yyb-android/logasistent.git
git push --set-upstream origin --all
git push --set-upstream origin --tags