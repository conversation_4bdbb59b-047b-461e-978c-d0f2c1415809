# 设计思路
出现bug - 定位bug时间 - 前后五分钟 - 过滤用户的行为路径（过长如何解决） - 导出用户行为日志作为时间线

用户行为日志（根据onResume）划分每个页面 - 提取出时间线 - 根据时间线过滤日志

主要的还是得给出 1. tag 2. 告诉模型查找bug的思路【排查规则】（查找一元购的状态为什么没有刷新？1. 获取对应的tag 2. 查看接口返回 3. 接口返回没问题，查看相关逻辑是否正确。）

用户进出页面的时间线 - 逐步分析每个页面的日志 - 一个页面的日志至少1k行
                                       - 请求模型 分析 也要很多次
                                       - 列出异常点？ - 模型只能根据字面意思分析。异常点如何得到？
                                       - 列出用户的行为即可？列出其他的信息无意义，也会比较冗余

用户行为链分析的话，我觉得能列出一个时间线，分析出用户在哪些时间点进出页面，做了什么点击（点击的话，有一些场景上报内容模糊，也看不出是点击哪里。）
如果要分析每个页面是否有异常，有几个问题：
1. 定位异常的话需要输入 不同异常 准确的tag，并且还要让模型知道哪些日志内容是有异常。（标注tag和日志异常内容还是需要时间，需要积累）
2. 单个页面日志太多，切分请求模型分析，需要分析很多次，请求多次模型。（随机看了一个页面，至少1k行的日志）


所以 要在单个页面分析异常，需要 获取 这个页面中 可能出现异常的 tag。然后依次对这些tag给模型请求，分析出异常。
如果 在编写代码的时候，异常用[w]打日志，大部分情况 可以直接通过 w 字符 定位出，这个页面有异常。（减少模型请求，更易实现异常定位）

- 得让用户输入关键tag进行过滤。

感觉定位bug的路径 最好是 根据出现bug的tag 和 时间，定位到异常点。如果日志内容模糊，模型需要知道 什么日志内容是对应什么错误原因。（如果很多场景的话，这个需要花时间积累的）

# todo

- 用户路径 漏报？-- 203502 关于页面
  - STPageEventsInfo][][autoTestOnPageEnter: pageId = [203502], pageHeight = [0]
- 行为日志 太多。
  - 用 baseActivity 的 onresume。日志时间：onresume 早于 上报。（Fragment是否上报？）
  - 过滤掉一些不必要的场景号？如2033-这应该是滑动了中间页内容展示横滑卡？
- 无日志的bug：ui相关

# 固定tag
- RuntimeCrash


# 示例
告诉模型查找bug的思路【排查规则】
为什么AI分析中 crash
- 1. 获取对应的tag 2. 查看接口返回 3. 接口返回没问题，查看相关逻辑是否正确。

- tag:sendAIHelpRequest tag:SecondNavigationTitleViewV5 tag:AISummaryDialogFragment


# 对于错误信息 模糊的日志---解决方法（人工标注）
- 可能出现错误的日志信息是什么？（比如 出现什么日志内容 是什么错误？）
  - 人工标注错误信息（详细的日志可以不用，可以遇到排查错误再标注，加入知识库）
    - （q: 日志tag & 可能错误原因，v: 对应错误日志）
    - v: 对应错误日志

# 开发也不知道的错误，模型要怎么定位？
- 异常现象。


# time
3月25日前：完成用户日志行为链分析，并能识别到错误类型调用错误分类子agent；
3月28日前：下载、安装、活动反馈、crash等子agent能够准确分析出错误原因；
4月5日前：集成到aisee、TAPD；

分析错误原因：updateTaskStatus:STARTED

# 优化方案
下载[点击下载的行为]   下载上报的时机会晚一点，估计还得看其他日志
- 预设问题：下载[report_element: downloadlistentrance]、安装[InstallRetryMgr：event add:AppBeginInstall]、活动页[tag: Kuikly-KRCommonActivity]（冷起、热起、外call）、云游[点击进入云游的操作]、crash[tag：RuntimeCrash]

一长条时间线
userAction
downloadlog 下载
安装[InstallRetryMgr：event add:AppBeginInstall]
loadKuiklyRenderView onComplete pageInfo
RuntimeCrash

根据行为链 判断用户有哪些意图 -- 有没有发生异常 
- 如果有crash 就走 crash异常分析类
- 有什么异常

# 过滤无用tag
- fileType=PLUGIN
- onGetSection:
- 堆栈信息 at ...


# 最后下载会落到这个地方
com.tencent.pangu.manager.ipc.DownloadServiceProxy.NormalDownloadTaskListener#onTaskFailed
# 开始下载
com.tencent.pangu.manager.ipc.DownloadServiceProxy#startDownloadTask(com.tencent.pangu.download.DownloadInfo, com.tencent.pangu.utils.tracer.ICommonTracer)


# 下载日志过滤
- tag ： halley-downloader-SectionTransport  中的 retCode是关键。
  - 2025-02-16 15:25:59.204 I halley-downloader-SectionTransport 1-538A000AF07F75755411BC1DFEF72719:[20|sche] Direct:false send req retCode:-31,msg:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6ed896b088: I/O error during system call, Connection reset by peer

- tag ：halley-downloader-TaskManager 中的 retCode也是关键（看上去和halley-downloader-SectionTransport中的retCode一样），failInfo:
  - 2025-02-16 15:25:58.462 I halley-downloader-TaskManager 1-538A000AF07F75755411BC1DFEF72719 onTaskFailedMainloop retCode:-31,failInfo:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6f35344c88: I/O error during system call, Connection reset by peer











======================


# 旧方案
输入
●用户完成日志上传
●query
处理流程
1.意图识别
a.利用 llm 识别用户的 query，形成结构化的输出
b.prompt
现在你需要根据用户的[输入]提取出时间范围、分析场景、日志筛选tag、知识库索引key等关键内容，并按要求进行输出。
[输入]="""
{query}
"""
[要求]="""
根据识别的内容对[输出格式]中的 log_tags、index_key进行填充；time_ragnge 按照识别到的时间进行填充，如果没有指定结束时间则默认是在开始时间上追加10分钟，如果没有识别到时间范围则不生成time_ragnge；issue_scene 按照识别的场景填充。输出格式必须为 [输出格式]的 json 格式，时间范围严格按照[例子]中的格式输出。
"""

[输出格式]="""
{
    "issue_scene": "<场景>",
    "log_tags": ["<tag>"],
    "time_ragnge": {
        "start": "0",
        "end": "0"   
         },
    "index_key": ["<index_key>"]
}
"""

[例子]="""
{
    "issue_scene": "download",
    "log_tags": ["Downloader"],
    "time_ragnge": {
        "start": "2025-02-16 15:33:21",
        "end": "2025-02-16 15:33:31"   
         },
    "index_key": ["retCode"]
}
"""

2.意图输出解析
a.根据 llm 识别的用户意图输出，解析对应的tag、和知识索引关键字用户检索日志和知识库
b.如果用户给出的信息不够多，询问用户追加多伦问话补全意图
3.组装问题分析 Prompt
role:{role} # 角色定义
context:{log context}
query:{origin_query}
log_content:{log_content}

输出
基于 llm 分析日志的最终结果


