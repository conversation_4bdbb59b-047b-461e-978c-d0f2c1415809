git pull
Updating 77d3c22..ce10249
Fast-forward
 agent/filter_user_action.py |  7 +++++++
 agent/function_call.py      | 39 ++++++++++++++++++---------------------
 2 <USER> <GROUP>, 25 insertions(+), 21 deletions(-)



比如行为链、 下载安装  和 活动， 日志过滤优化和 rag 背景知识看看iwiki 有么有一些背景知识可以优化的


- 目前不知道有哪些问题，可以先体验，有问题日志，分析不全的再解决。
- 

先用rag筛一遍（日志有没有出现场景对应的bug），

场景 - 显而易见的错误日志




# todo

## 日志文件合并 - 时间跨度两个文件、设计daemon进程、用户输入不准确时间，默认最新连个文件

- 支持时间格式：2025-04-03 11:58

- 时间合并
2025-04-03 11:58:00
最后十分钟的，如果有12点的日子文件，可以合并了看
2025-04-03 11:08:00
前十分钟的，如果有11点的日子文件，可以合并了看
没给时间的、匹配不到时间的
最新的两个日志文件看


- daemon进程合并


## 日志分析 自动 收集背景知识？
解决bug的时候，有表单收集


## 知识库内容
如测试环境的知识。

但是bug是不会重复的？新bug怎么解析呢？
- MCP 联合代码解析
