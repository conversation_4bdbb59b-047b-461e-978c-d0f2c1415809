
日志工作参考
https://km.woa.com/articles/show/617586?kmref=search&from_page=1&no=2
https://iwiki.woa.com/p/4012978436




function call 
- 模型

# 待做事项

#


# 流程

                                用户输入
                                    ↓
              用户输入 -> 知识库 -> 模型参考 
                                    ↑
提供tag、需要过滤规则、精确匹配规则 -> 日志过滤

 
- 业务背景（tapd单子规范 -- 业务场景标识）
怎么通过用户问题匹配呢？desc、scene作为key

问题：云游场景，房主卡在游戏进入加载页面

# 配置格式

云游场景，房主卡在游戏进入加载页面，相关tag：CloudGame.EndgameSignalingManager，关键字：handleEndgamesStatus status。房主进入游戏，正常日志逻辑：PREPARING之后需要收到GAME_BEGIN。如果PREPARING之后没有收到GAME_BEGIN。

需求逻辑


```json
{
    "scene": "云游",  
    "log_rule":[ 
        {
            "tag": ["CloudGame.EndgameSignalingManager"],
            "keywords": ["handleEndgamesStatus status"],
            "desc": "房主进入游戏，正常日志逻辑：PREPARING之后需要收到GAME_BEGIN"
        },
    ],
    "bug_case":[
        {
            "tag": [""],
            "keywords": [""],
            "bug_desc":"房主卡在游戏进入加载页面"
        }
    ],
    "filter_rule":[
        {
            "filter_type":"删除、保留、去重",
            "tag":"",
            "log_content":"",
            "start_divide":"",
            "end_divide":""
        }
    ]
}
```

# rag格式
1. value: 配置内容
2. key:场景 + desc + bug_desc value: 配置内容


# tapd单 -- 5天
- api权限申请
- 自定义规则拉取 bug列表，定时 拉取 
- 查询指定tapd单详情，获取 问题、时间、日志
- 评论发送api，发送模型分析结果（文件格式是否支持）
- 不确定因素，api调用是否遇到问题；接收、发送的数据格式是否需要进行处理



自然语言转化json agent


相关业务知识辅助过滤关键日志
