
# 总结模型优化

最后的总结模型，会把信息遗漏（中间人信息差）。或许直接把输出结果照抄。或者不请求最后的模型，直接拼接？


# 日志分行输入模型，步骤拆解。


# 先过滤出关键日志，做成Prompt给模型。


# 缺少相关知识，模型反馈出，及时补充。

# 整个日志压缩包，怎么判断分析哪个日志文件？（AIsee上有时候分析不到 有时候第三个文件才分析到）

# 意图识别不准 == 人来选择是哪个场景进行分析？
1. 自动下载，容易识别到 下载。如：“看个广告，一点进来就给我下载无关应用”
2. 活动页反馈的总结 怎么分类到 活动页（常见反馈）

# 把相关知识库（错误码）、过滤后的日志也输出？

# 更换api 维纳斯（混元比较差）

# 四个文件 每个文件都出现了相关日志，但是 最后一个才是真正的有用的日志。怎么过滤？（9-应用宝活动领的Q币发货失败 == orderStatus=3）
- 举例：11-充值未到账。如果合并全部日志，处理的时间会很长（文件多的话合并至少5s，过滤日志的处理时间也会边长，四个文件，整体的时间是9s，合并后的文件也很大，），过滤后的日志内容也会很多（用户行为链的日志也会很多）  === 最多一次性四个文件？目前看日志，两个未搜素到相关日志内容的，最多第四个文件就能找到
- 判断过滤出的日志是否有关键字？-orderStatus=3？ === 那可能是其他问题。领取之类的。
## 指令抓取的怎么选择日志文件 -- 包破损6，关键的日志在最旧的两个
  - 获取必须失败的条件，进行过滤？
