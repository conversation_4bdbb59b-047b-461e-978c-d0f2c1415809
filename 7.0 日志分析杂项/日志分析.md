# 已完成
1. 企微机器人后台部署 调研-开发-调试-优化
   - 企微机器人（后台部署）调研
   - 调试企微机器人demo，跑通企微机器人聊天链路
   - 用户输入解析、日志文件获取逻辑（下载解压）
   - 企微机器人重试机制，调研解决多次回复用一个消息的问题
   - 用户交互优化，模糊时间、根据时间动态选取1个或2个日志文件
2. 提供aisee接口，aisee侧部署联调。
   - python包接入冲突解决、接入参数联调
   - 日志文件获取逻辑（支持本地路径、下载链接、单个文件、压缩包）
   - 选取关键日志文件分析逻辑
   - bug时间兼容，传入的bug时间不准确，影响日志文件查找、日志过滤。
   - 流式处理逻辑，解决超时问题
   - 持续自测，发现bug，解决bug
3. 场景识别functioncall，知识库建立
   - 调通functioncall，识别意图  
   - 不同业务场景的日志tag、业务场景背景知识
4. 日志清洗
   - 日志过滤方法，根据case持续优化，提高灵活性
   - 过滤后的日志，需要人工判断（无用、重复）、减少噪声

# 待开发
    
1. aisee 一键分析
2. 部分场景需要分析daemon日志文件，还需要整合一下分析。
3. 目前的场景只有五类，意图识别时，部分业务会归到其他。其他场景目前只有默认的几个常见tag过滤分析。
4. 背景知识优化。开放一个配置接口，大家往里面 添加 配置，丰富业务背景知识。
5. 日志过滤（重复、无用）。根据场景tag过滤后的日志，如果不再进一步过滤，还是会出现过多日志，影响模型分析。
6. tapd 单子


1. aisee 一键分析（联调1-2d，aisee侧开发量未知）
2. 结合daemon日志文件分析。（1d）
3. tapd 分析日志（5d）
4. function call，子agent工具封装（1-2d）
5. 知识库共建：背景知识切割、总结，向量化入库（1-2d）；子agent调用RAG接口召回日志（1d）；场景tag提取agent（1d）；业务代码知识库开发（4-5d）
6. 多轮交互增强回答准确度（2-3d）

潜在工作：日志过滤（去重、无用）




给出场景代码、日志文件

为什么用户第一次进入没有视频向下引导动画？

git怎么拉取最新改动代码？


根据代码 提取tag -> 结合代码 和 过滤日志 分析


# 流程

                                用户输入
                                    ↓
              用户输入 -> 知识库 -> 模型参考 
                                    ↑
提供tag、需要过滤规则、精确匹配规则 -> 日志过滤


- 业务背景（tapd单子规范 -- 业务场景标识）
怎么通过用户问题匹配呢？desc、scene作为key

问题：云游场景，房主卡在游戏进入加载页面

# 配置格式

```json
{
    "scene": "云游",  
    "log_rule":[ 
        {
            "tag": ["CloudGame.EndgameSignalingManager", ""],
            "log_content": ["handleEndgamesStatus status=PREPARING","handleEndgamesStatus status=GAME_BEGIN"],
            "keywords": ["handleEndgamesStatus status",""],
            "desc": "房主进入游戏，正常日志逻辑：PREPARING之后需要收到GAME_BEGIN"
        },
        {
        },
    ],
    "bug_case":[
        {
            "tag": [""],
            "log_content":[""],
            "keywords": ["handleEndgamesStatus status",""],
            "bug_desc":"房主卡在游戏进入加载页面"
        }
    ],
    "filter_rule":[
        {
            "filter_type":"删除、保留、去重",
            "tag":"",
            "log_content":"",
            "start_divide":"",
            "end_divide":""
        }
    ]
}
```

# rag格式
1. value: 配置内容
2. key:场景 + desc + bug_desc value: 配置内容


# tapd单 -- 5天
- api权限申请
- 自定义规则拉取 bug列表，定时 拉取 
- 查询指定tapd单详情，获取 问题、时间、日志
- 评论发送api，发送模型分析结果（文件格式是否支持）
- 不确定因素，api调用是否遇到问题；接收、发送的数据格式是否需要进行处理

