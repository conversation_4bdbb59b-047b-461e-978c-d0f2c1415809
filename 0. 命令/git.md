以下是一些常用的 Git 命令，适用于日常的版本控制操作：

### 基本命令

1. **初始化一个新的 Git 仓库**：
   ```bash
   git init
   ```

2. **克隆一个已有的仓库**：
   ```bash
   git clone <repository-url>
   ```

3. **查看当前状态**：
   ```bash
   git status
   ```

4. **添加文件到暂存区**：
   ```bash
   git add <file>          # 添加单个文件
   git add .               # 添加当前目录下的所有文件
   ```

5. **提交更改**：
   ```bash
   git commit -m "Your commit message"
   ```

6. **查看提交历史**：
   ```bash
   git log
   ```

### 分支管理

1. **查看所有分支**：
   ```bash
   git branch
   ```

2. **创建新分支**：
   ```bash
   git branch <branch-name>
   ```

3. **切换到指定分支**：
   ```bash
   git checkout <branch-name>
   ```

4. **创建并切换到新分支**：
   ```bash
   git checkout -b <branch-name>
   ```

5. **合并分支**：
   ```bash
   git merge <branch-name>
   ```

6. **删除分支**：
   ```bash
   git branch -d <branch-name>  # 删除已合并的分支
   git branch -D <branch-name>  # 强制删除分支
   ```

### 远程操作

1. **查看远程仓库**：
   ```bash
   git remote -v
   ```

2. **添加远程仓库**：
   ```bash
   git remote add <name> <repository-url>
   ```

3. **推送到远程仓库**：
   ```bash
   git push <remote-name> <branch-name>
   ```

4. **从远程仓库拉取更改**：
   ```bash
   git pull <remote-name> <branch-name>
   ```

5. **获取远程更新**：
   ```bash
   git fetch <remote-name>
   ```

### 标签管理

1. **创建标签**：
   ```bash
   git tag <tag-name>
   ```

2. **推送标签到远程**：
   ```bash
   git push <remote-name> <tag-name>
   ```

3. **查看所有标签**：
   ```bash
   git tag
   ```

### 其他常用命令

1. **撤销未暂存的更改**：
   ```bash
   git checkout -- <file>
   ```

2. **撤销已暂存的更改**：
- 与 git add <file> 相对
   ```bash
   git reset <file>
   ```
- 撤销全部：git reset

3. **重置到某个提交**：
   ```bash
   git reset --hard <commit-id>
   ```

4. **查看差异**：
   ```bash
   git diff                # 查看未暂存的更改
   git diff --cached       # 查看已暂存的更改
   ```

这些命令涵盖了 Git 的基本操作，帮助你进行版本控制和协作开发。根据你的需求，可以进一步学习更高级的 Git 功能和命令。