#### 什么是混元Embedding

《索引服务的数据相关操作的文档： https://iwiki.woa.com/p/4010689738》

《索引服务的数据召回：https://iwiki.woa.com/p/4008514368#索引服务OpenAPI接口》

背景
本文描述如何使用混元一站式中向量与索引能力，用于构建大模型的外挂知识库服务，或自定义召回服务。

向量与索引是一种有用的外挂知识库工具，可以帮助读者快速找到所需的信息，为大模型补充知识，从而达到以下目的：

不需要再次训练，即可使大模型获得特定领域的知识，快速构建特定领域的大模型应用。
通过检索知识库内的真实信息，使大模型的输出“有据可依”，减少大模型的幻觉现象。
一个最简单的外挂知识库大模型应用为如下图：

首先开发者将外挂的知识语料进行文字处理，如清洗、切分，之后送入向量大模型（下称emb模型或emb model）进行向量推理；一站式将向量推理的结果灌入向量索引，构建知识库检索服务
用户输入的句子将作为知识库检索的关键词，在知识库检索服务中检索到与用户输入最相关的事实性文段
检索到的事实性文段拼接到大模型的输入prompt中，使大模型在输入端获得知识补充。
混元一站式中，我们集成了向量索引的两大核心功能，一是向量模型的一站式能力，二是向量索引的构建能力。

快速搭建混元Embedding索引
创建知识库索引
通过Agent表单引导创建
● 混元「智能问答Agent」与「角色扮演Agent」均支持通过表单的方式创建知识库索引

● 支持选择已有数据集或直接上线本地知识库，目前已支持：pdf文字版、txt、markdown、jsonl，我们将陆续开放支持更多原始数据格式

基于混元Embedding自主创建
Step1：选择数据集创建向量计算任务
详细说明可查看：https://iwiki.woa.com/p/4008514365

Step2：向量计算运行完成后，创建向量索引服务
https://iwiki.woa.com/p/4008678727

Step3：发布知识库索引
已完成向量索引服务的创建，接下来需要发布知识库索引，让用户能够搜索到知识文档。
● 点击发布后，在知识库的首页会出现一个搜索框，输入想要搜索的内容，即可在结果中看到知识文档的摘要