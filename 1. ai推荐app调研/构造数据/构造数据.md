#### 模版构造

app推荐泛化问题抓取
泛化问题抓取，用于app推荐
- 模型底座：混元7B-MoE-SFT-256k-v2-250106



假设你是一个思维导图专家，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出结果。
[步骤]=
    仿照[示例]，生成{{problem_cnt}}个类似的[问题]，要求: 1、生成{{field}}领域的数据，例如{{field_exmple}}。2、注重生成的多样性，尽可能生成有区别的数据。3、随机给生成的[问题]添加一些要求。4、不少于{{word_cnt}}字。
        [示例]={{seed_problem}}。
[格式]=
    1. xxx
    ...
    {{problem_cnt}}. xxx
    
{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}


===== 老师 =====

提示词模板：假如你是一个老师，请对下面的作业进行批改，[学科]={{subject}}，[题目]={{problem}}，[答案]={{answer}}。请根据[学科]+[题目]判断[答案]是否正确，不正确请告诉我正确的答案。

配置变量：{{subject}},{{problem}},{{answer}}


批量上传变量的值，用于进行变量替换

示例：{"subject":"数学","problem":"1+1等于几","answer":"2"}{"subject":"英语","problem":"学习的英文是什么","answer":"study"}

====

假设你是一个app推荐达人，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出回答。
[步骤]=
    仿照[示例]，生成{{problem_cnt}}个类似的[问题]，要求: 1、生成{{field}}领域的数据，例如{{field_exmple}}。2、注重生成的多样性，尽可能生成有区别的数据。3、随机给生成的[问题]添加一些要求。4、不少于{{word_cnt}}字。
        [示例]={{seed_problem}}。


{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}

[格式]="""
1、对话语气要活泼自然。
2、明确需要推荐的app<个数>。按<个数>分点作答。如果只需要推荐1个app，则不需要分点作答。
3、可以根据appStore的评分作为依据来推荐。
4、每款<app>首先用“为您推荐<app>。这款应用”开头简要介绍<app>。<app>名称加粗。
5、其次用 “以下是关于<app>的详细信息：”引出<app>的特点。
6、分点列出<app>的特点。
7、借助关键的[资料]和[模型]以及自身知识来作答。
8、最后可以根据用户的意图。以“我还为您推荐以下内容：”开头，推荐一些相关app。
"""


=== 深度

根据上面给出的[维度]描述和示例，为每一个约束维度生成只包含一个要求的明确具体，符合逻辑的[具体约束条件]，然后为[种子指令]添加这些具体的约束条件，生成[深度指令]

[种子指令]= 推荐一款app

要求：不要有废话，直接输出10条[深度指令]

```
推荐一款适用于学习英语的app，要求有丰富的词汇和语法课程。
推荐一款能提高工作效率的app，要求具备任务管理和时间规划功能。
推荐一款适合健身的app，要求提供专业的健身指导和实时运动数据追踪。
推荐一款音乐播放app，要求支持多种音频格式和个性化推荐。
推荐一款阅读类app，要求拥有大量的书籍资源和舒适的阅读界面。
推荐一款旅行规划app，要求提供全球范围内的景点信息和行程规划工具。
推荐一款财务管理app，要求具备预算制定和账单查看功能。
推荐一款摄影后期处理app，要求提供丰富的滤镜和特效功能。
推荐一款社交聊天app，要求支持多种聊天方式和兴趣群组。
推荐一款个人健康管理app，要求提供运动记录、饮食监测和健康建议等功能。
```

=== 广度

根据上面给出的[维度]描述和示例，为每一个约束维度生成只包含一个要求的明确的、符合逻辑的[具体约束条件]，然后为上面给出的[深度指令]添加这些[具体约束条件]，生成[多样化指令]

[维度] = 功能需求

[深度指令] =

要求：不要有废话，直接输出10条[多样化指令]



#### 种子问题

热门推荐：
- 目前最热门的App有哪些？
- 哪些App在用户评价中表现最好？
  
分类推荐：
- 对于初学者来说，哪些学习类App值得推荐？
- 有哪些高质量的摄影和图像编辑App？
- 推荐几款旅行规划和导航App。

功能需求推荐：
- 有没有健身爱好者的App？
- 推荐几款的任务管理和时间管理App。
- 哪些App能帮助你提高语言学习效率？

预算友好推荐：
- 在预算有限的情况下，有哪些性价比高的App？
- 哪些免费App提供了丰富的功能？

特定人群推荐：
- 适合儿童的有趣且教育性的App有哪些？
- 针对青少年设计的创意和娱乐App有哪些？
- 老年人适用的健康管理和社交App有哪些？

创新性和独特性推荐：
- 有哪些App在功能或用户体验上具有创新性？
- 推荐几款与众不同的音乐、视频或阅读App。

季节性和时效性推荐：
- 在当前季节（如冬季）有哪些应景的App推荐？
- 随着节日或特殊事件（如圣诞节、新年）到来，有哪些相关的App推荐？

品牌合作与独家内容推荐：
- 哪些知名品牌的App提供了独家内容或优惠？
- 如何通过App获取到最新的电影、音乐或游戏资讯？

隐私和安全推荐：
- 在保护用户隐私和数据安全方面，哪些App做得比较好？
- 如何选择和使用安全的支付和购物App？
  
用户反馈和社区支持推荐：
- 哪些App拥有活跃的用户社区和良好的用户反馈机制？
- 用户对哪些App的忠诚度最高，并愿意推荐给他人？

按使用频率推荐：
- 你每天最常用的几款App是哪些？
- 一周中最不可或缺的App有哪些？
- 每月哪些App为你节省了大量时间或金钱？

按行业趋势推荐：
- 当下最热门的行业趋势是什么，相关App有哪些？
- 人工智能领域有哪些值得关注的App？
- 区块链技术在App中的应用有哪些实例？

按地域文化推荐：
- 不同国家和地区的特色App有哪些？
- 适合不同文化背景用户的App如何挑选？
- 如何通过App了解全球不同地区的文化和风俗？

按功能组合推荐：
- 有没有一款App能同时满足你的多种需求，如工作、学习和娱乐？
- 推荐几款集成了多种工具的综合性App。
- 如何找到既适合工作又适合休闲的App组合？

按更新频率和迭代速度推荐：
- 哪些App的开发团队更新频繁，能提供更好的用户体验？
- 如何判断一个App是否具备持续创新的潜力？
- 哪些App能够及时跟进用户需求和市场变化？

按用户评价星级推荐：
- 高评分App通常具备哪些共同特点？
- 如何从众多高评分App中挑选出最适合自己的那一款？
- 低评分App中是否也有隐藏的好应用？

按个人兴趣和爱好推荐：
- 根据你的兴趣爱好，有哪些相关的App可以推荐给你？
- 如何通过App发现新的兴趣和爱好？
- 能否推荐几款能够激发创造力和想象力的App？

按技术规格和兼容性推荐：
- 哪些App对硬件配置要求较低，适合老旧设备使用？
- 如何确保所选App与你的操作系统版本兼容？
- 在不同设备和屏幕尺寸上，哪些App的适配效果最佳？

按隐私政策和用户权益推荐：
- 如何评估一个App的隐私保护政策是否完善？
- 哪些App在用户权益保障方面做得比较好？
- 如何在使用App时保护自己的个人信息和数据安全？

按年龄层推荐：
- 儿童适合哪些寓教于乐的App？
- 青少年喜欢的社交和娱乐App有哪些？
- 成人需要的办公、学习和生活管理App分别是什么？

按学习领域细分推荐：
- 语言学习App中，哪些适合不同学习阶段和水平？
- 专业技能提升类App，如编程、设计、营销等，有哪些推荐？
- 兴趣爱好培养类App，如绘画、音乐、摄影等，如何挑选？

按平台特性推荐：
- 微信小程序中有哪些实用且便捷的App？
- 支付宝除了支付功能外，还有哪些贴心服务App推荐？
- 头条系App（如抖音、西瓜视频）之外，还有哪些不错的内容消费App？

按社交需求推荐：
- 陌生人社交App中，哪些更安全、更有趣？
- 职场人士需要的社交和协作工具App有哪些？
- 如何通过App建立和维护专业人脉网络？

按健康和健身需求推荐：
- 健康监测类App，如心率监测、睡眠分析等，哪些推荐？
- 健身指导和训练计划App，如何根据个人情况选择？
- 饮食记录和营养管理App，哪些能够帮助有效管理饮食？

按生活和娱乐需求推荐：
- 智能家居控制App有哪些，如何方便地管理家居设备？
- 娱乐休闲类App，如游戏、音乐、视频等，哪些值得一试？
- 生活服务类App，如外卖订餐、在线购物、交通出行等，哪些更优惠、更方便？

按安全和防护需求推荐：
- 手机安全类App，如病毒扫描、隐私保护等，哪些推荐？
- 数据备份和恢复类App，如何确保数据安全？
- 防诈骗和防恶意软件类App有哪些，如何使用？

按创意和灵感激发需求推荐：
- 创意写作和灵感激发类App，哪些能够帮助提升创作能力？
- 设计和绘画工具类App，如何找到适合自己的那一款？
- 音乐制作和创作类App，哪些适合音乐爱好者使用？

按旅行和探险需求推荐：
- 旅行规划和预订App，哪些更全面、更便捷？
- 探险和户外活动类App，如徒步、登山、骑行等，哪些推荐？
- 当地文化和美食探索类App，如何更好地体验当地风情？

按个人发展和自我提升需求推荐：
- 个人成长和职业规划类App，哪些能够帮助设定目标并跟踪进度？
- 心理健康和自我提升类App，如何保持积极心态和提升自我认知？
- 阅读和学习资源类App，哪些提供了丰富的书籍、课程和讲座？