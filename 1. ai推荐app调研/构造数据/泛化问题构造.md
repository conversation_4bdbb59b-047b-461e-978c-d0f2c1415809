
#### 构造[问题] 作为输入

### 示例
假设你是一个思维导图专家，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出结果。
[步骤]=
    仿照[示例]，生成{{problem_cnt}}个类似的[问题]，要求: 1、生成{{field}}领域的数据，例如{{field_exmple}}。2、注重生成的多样性，尽可能生成有区别的数据。3、随机给生成的[问题]添加一些要求。4、不少于{{word_cnt}}字。
        [示例]={{seed_problem}}。
[格式]=
    1. xxx
    ...
    {{problem_cnt}}. xxx
    
{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}


===== 老师 =====

提示词模板：假如你是一个老师，请对下面的作业进行批改，[学科]={{subject}}，[题目]={{problem}}，[答案]={{answer}}。请根据[学科]+[题目]判断[答案]是否正确，不正确请告诉我正确的答案。

配置变量：{{subject}},{{problem}},{{answer}}


批量上传变量的值，用于进行变量替换

示例：{"subject":"数学","problem":"1+1等于几","answer":"2"}{"subject":"英语","problem":"学习的英文是什么","answer":"study"}


### app推荐 构造 泛化种子

- 构造的句子 应满足：
  - 结构需要多样，因为人们说话的方式是多样的
  - 具有深度和广度

- 多样的词
  - app、应用程序、软件

- 定义prompt
  
假设你是一个app推荐达人，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出回答。
[步骤]=
    仿照[示例]，生成{{problem_cnt}}个类似的[问题]，要求: 1、生成{{field}}的数据，例如{{field_example}}。2、注重生成的多样性，尽可能生成有区别的数据。确保生成数据的广度和深度。3、随机给生成的[问题]添加一些要求。4、不少于{{word_cnt}}字。
        [示例]={{seed_problem}}。


[格式]=
    1. xxx
    ...
    {{problem_cnt}}. xxx

{{problem_cnt}}, {{field}}, {{field_example}}, {{word_cnt}}, {{seed_problem}}


- 调试结果

假设你是一个app推荐达人，推荐各种类型的安卓app，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出回答。
[步骤]=
    仿照[示例]，生成30个类似的[问题]，要求: 1、生成教育领域的数据，例如能够帮助学习的app。2、注重生成的多样性，尽可能生成有区别的数据。3、句子结构多样化，符合人类自然语言，注重增加句子深度与广度。4、随机给生成的[问题]添加一些要求。5、不少于30字。
        [示例]=推荐一款app。

[格式]=
    1. xxx
    ...
    30. xxx
   

{"problem_cnt":30, "field":"健康类", "field_example":"睡眠监测", "word_cnt":30, "seed_problem":"求推荐一款精准监测睡眠周期且支持数据导出的健康类App？最好有深度分析报告，界面简洁无广告"}


- 数据抓取
app_recommend v1
{"problem_cnt":30, "field":"教育", "field_example":"帮助学习的app", "word_cnt":30, "seed_problem":"推荐一款适用于学习英语的app，要求有丰富的词汇和语法课程，且界面简洁易懂"}
app_recommend v2
{"problem_cnt":30, "field":"健康与健身", "field_example":"记录运动数据的软件", "word_cnt":30, "seed_problem":"想要找一款能够帮忙记录运动数据的应用"}

app_hot v1
{"problem_cnt":30, "field":"热门推荐软件", "field_example":"目前最热门的App有哪些", "word_cnt":30, "seed_problem":"哪些App在用户评价中表现最好"}

app_music v1
{"problem_cnt":30, "field":"音乐领域", "field_example":"关于寻找音乐，播放音乐的软件", "word_cnt":30, "seed_problem":"推荐一款音乐播放器"}

app_social v1
{"problem_cnt":30, "field":"社交聊天领域", "field_example":"寻找一款能够帮助交友的软件", "word_cnt":40, "seed_problem":"推荐一款能够交朋友的应用程序，用户活跃度高"}

app_tool v1
{"problem_cnt":50, "field":"工具领域", "field_example":"推荐一款音乐制作和创作类的应用程序", "word_cnt":40, "seed_problem":"寻找一款能够提供一些小工具的app，例如在线尺子、单位换算"}

app_finance v1
{"problem_cnt":20, "field":"金融与理财", "field_example":"关于个人记账本app", "word_cnt":40, "seed_problem":"推荐一款简单易用的个人记账本app，喜欢页面简单一些的"}

app_buy v1
{"problem_cnt":15, "field":"购物领域", "field_example":"书籍购买平台，上架了许多优质书籍的软件", "word_cnt":40, "seed_problem":"想找一款购物app，品类丰富，物美价廉"}

app_game v1
{"problem_cnt":60, "field":"游戏领域", "field_example":"适合低幼龄儿童玩的游戏软件", "word_cnt":40, "seed_problem":"想找一款双人合作游戏，要求难度适中，古风，评分高。"}

app_subject v1
{"problem_cnt":30, "field":"针对不同专业人士", "field_example":"推荐一些适合法律专业同学的app", "word_cnt":40, "seed_problem":"可以推荐一些评分高，适合医学生使用的软件吗"}

app_entertainment v1
{"problem_cnt":30, "field":"娱乐", "field_example":"有没有一些娱乐的软件，比如追剧", "word_cnt":40, "seed_problem":"想挖掘一下娱乐类的软件，休息时间放松一下"}



### 让混元帮助构造种子问题

- 领域
  - 教育
  - 健康与健身
  - 热门app
  - 音乐
  - 社交聊天
  - 工具类
    - 音乐制作和创作类App，哪些适合音乐爱好者使用！！
  - 金融与理财
  - 购物
  - 游戏
  - 专业领域
    - 计算机
    - 法律
  - 娱乐
  - 阅读 
    - 小说
  - 视频播放
 
- 维度
  - 适用人群
  - 满足特定需求
  - 满足特定的喜好
  - 用户评级与口碑




- 获取维度

请告诉我，如果想让别人推荐一款app，我可以提出哪些要求，请帮我列举不同的维度。


- 获取深度指令

根据上面给出的[维度]描述和示例，为每一个约束维度生成只包含一个要求的明确具体，符合逻辑的[具体约束条件]，然后为[种子指令]添加这些具体的约束条件，生成[深度指令]

[种子指令]= 推荐一款app

要求：不要有废话，直接输出10条[深度指令]


```
推荐一款适用于学习英语的app，要求有丰富的词汇和语法课程。
推荐一款能提高工作效率的app，要求具备任务管理和时间规划功能。
推荐一款适合健身的app，要求提供专业的健身指导和实时运动数据追踪。
推荐一款音乐播放app，要求支持多种音频格式和个性化推荐。
推荐一款阅读类app，要求拥有大量的书籍资源和舒适的阅读界面。
推荐一款旅行规划app，要求提供全球范围内的景点信息和行程规划工具。
推荐一款财务管理app，要求具备预算制定和账单查看功能。
推荐一款摄影后期处理app，要求提供丰富的滤镜和特效功能。
推荐一款社交聊天app，要求支持多种聊天方式和兴趣群组。
推荐一款个人健康管理app，要求提供运动记录、饮食监测和健康建议等功能。
```


- 获取广度指令

根据上面给出的[维度]描述和示例，为每一个约束维度生成只包含一个要求的明确的、符合逻辑的[具体约束条件]，然后为上面给出的[深度指令]添加这些[具体约束条件]，生成[多样化指令]
要求：不要有废话，直接输出10条[多样化指令]