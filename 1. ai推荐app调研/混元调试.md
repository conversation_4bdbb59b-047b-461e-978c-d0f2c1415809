### api key申请
- 业务场景
在应用宝内搜索app时，推荐的app可能不太智能。比如搜索出国必备的app，推荐给用户的app，用户下载后发现并不太满意。现在希望通过收集一些对app的评价数据，来喂入ai模型，从而优化app的推荐。
- 期望目标
希望通过大模型，推荐更符合用户喜好的app，以提升搜索推荐的精准度和用户体验。



- 应用组申请
学习混元能力，测试调研混元能否帮助应用宝优化app推荐，利用应用组训练app推荐精调模型。

- 资源申请 -- 任务描述
1. 训练模式：生成式。
   服务的业务：应用宝app搜索。
   应用及落地场景：目前还是调研阶段，应用宝app搜索场景。
   阶段目标：调研混元能力是否可以帮助优化应用宝app搜索。
   预期收益：优化app推荐，提高用户体验。
2. 底座模型：混元7B-MoE-SFT-256k-v2-250106
   精调方式：FULL Finetune
   训练模版：netmoe_sft_full_template
   训练数据集：app_recommend_pair_v1（178条数据）
   验证数据集：按比例配置（5%）。
    - sample_merge：否
    - shuffle_jsonl：是
    - tolerance：20%
    - eod_replace_eor_ratio：0
   训练配置：
    - seq_len：32768
    - train_epoch：5
    - global_batch_size：16
    - micro_batch_size：1
    - lr：1e-5
    - min_lr：1e-6
    - eval_interval：50
    - save_interval：50
    - checkpoint_nums：2
    - warmup_tokens_ratio：0
    - lr_decay_tokens_ratio：1.0
    - mp_size：8
    - pp_size：2
    - ep_parallel_size：16
    - cp_size：1
    - loss_mask_token_id：300000
    - sample_task_id：0

- 资源申请 -- 资源推导
资源用于训练app推荐精调模型，目前是调研阶段。底座模型：混元7B-MoE-SFT-256k-v2-250106。精调方式：FULL Finetune。训练数据集：app_recommend_pair_v1（178条数据）。目前预计2组实验，利用构造的数据集训练模型，调研混元能否优化app推荐。


训练： 请提供业务场景/所使用的模型size/数据量/实验计划/实验目标

示例： 用于xx业务客服服务场景，主要是用7bdense-32k模型，数据量20w，预计2组实验，达到xxx目标


我们在预研 利用混元来优化应用宝app的推荐。希望申请应用组 在混元大模型基础上做app推荐的精调训练。

已有的混元API和体验模型 API无法满足业务场景，需要在混元大模型基础上做精调训练，输出定制化大模型能力的业务场景。

预研 利用混元来优化应用宝app的推荐。申请应用组 在混元大模型基础上做app推荐的精调训练。

学习混元能力，预研 利用混元来优化应用宝app的推荐。申请应用组 在混元大模型基础上做app推荐的精调训练。


为了提升应用宝推荐app的用户体验和满意度，我们计划深入学习混元能力，并在此基础上预研如何利用混元大模型的强大功能来优化app推荐。尝试调研应用组 在混元大模型基础上做app推荐的精调训练 的效果。

具体而言，我们将申请加入应用组，专注于在混元大模型的基础上进行app推荐的精调训练。通过这一系列的措施，我们期望能够为用户提供更加精准、个性化的应用推荐，从而增强用户粘性，提升app的整体活跃度和下载量。


为了进一步提升应用宝推荐app的用户体验与满意度，我们计划深入研究混元能力，并在此基础上探讨如何利用混元大模型的强大功能来优化app推荐，尝试调研应用组在混元大模型基础上进行app推荐精调训练的效果。



### 逻辑编排
#### prompt字符串
请根据文档结果，优化文档的编排的方式，并输出为markdown格式，并根据文档回答问题。
文档结果：{content}
问题：{query}

请根据提问回答问题，并输出为markdown格式。
问题：{query}

### 索引
query 问。value 回答
- 背单词的app  - app1、app2。。。

那意味着 爬的是搜索出来的app 然后推荐的app封装到value 来进行回答



给定主题生成SEO关键词。
作为 {{旅游网站}} 的 SEO 专家，生成一个相关的 SEO 关键词列表，以优化网站内容并提高其搜索引擎排名。考虑因素要包含搜索量、竞争力和目标受众的相关性，并使用关键词研究工具来寻找优化的机会。


作为app推荐专家，请根据提问，推荐一些符合用户喜好的app






====

作为app推荐专家，参考提供的[资料]，对于[用户问题]，推荐一些符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和自己的知识进行准确回答。回答格式：只需要回答app的名称，app之间用逗号隔开，根据app推荐优先级，从高到低排序。

[资料]="""
{result}
"""

[用户问题]="""
{output}
"""


====
作为app推荐专家，参考提供的[资料]和[模型]的知识，对于[用户问题]，推荐一些符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和[模型]的知识以及自己的知识进行准确回答。回答格式：只需要回答app的名称，app之间用逗号隔开。要列出所有你所学习到的app的名称，根据app推荐优先级，从高到低排序。

[资料]="""
{content}
"""
[模型]="""
{model}
"""
[用户问题]="""
{output}
"""

作为app推荐专家，根据[模型]输出结果，推荐一些符合用户喜好的app。回答格式：只需要回答app的名称，app之间用逗号隔开。要列出所有你所学习到的app的名称，根据app推荐优先级，从高到低排序。

[模型]="""
{result}
"""

======

作为app推荐专家，参考提供的[资料]和[模型]的知识，对于[用户问题]，推荐一些符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和[模型]的知识以及自己的知识进行准确回答。回答格式：要列出十个你所知道的app的名称，只需要回答app的名称，app之间用逗号隔开，根据app推荐优先级，从高到低排序。

[资料]="""
{content}
"""
[模型]="""
{model}
"""
[用户问题]="""
{query}
"""

====

作为app推荐专家，根据自己的自身知识，对于[用户问题]，推荐一些符合用户喜好的app。回答格式：只需要回答app的名称，app之间用逗号隔开。要列出所有你所学习到的app的名称，根据app推荐优先级，从高到低排序。

[用户问题]="""
{query}
"""


====

作为app推荐专家，根据自己的自身知识，分析[用户问题]，明确需要推荐的app个数，推荐符合用户喜好的app。回答格式：列出你推荐的app，并详细介绍app。

[用户问题]="""
{query}
"""


======

作为app推荐专家，参考提供的[资料]和[模型]的知识，分析[用户问题]，明确需要推荐的app个数，推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和[模型]的知识以及自己的知识进行准确回答。回答格式：列出你推荐的app，并详细介绍app。

[资料]="""
{content}
"""
[模型]="""
{model}
"""
[用户问题]="""
{query}
"""



=============  222 能够根据问题分析推荐个数 ==========

- 分析推荐个数 promot
  
分析[用户问题]，明确[需要推荐的app个数]。
[用户问题]="""
{query}
"""

[需要推荐的app个数]="""
{num}
"""

- 根据问题和个数推荐

作为app推荐专家，参考提供的[资料]和自身的知识，分析[用户问题]，明确[需要推荐的app个数]，推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和[需要推荐的app个数]以及自己的知识进行准确回答。回答格式：列出[需要推荐的app个数]个你推荐的app并做简要介绍，随后分点详细介绍app。

[资料]="""
{content}
"""
[需要推荐的app个数]="""
{num}
"""
[用户问题]="""
{query}
"""

===========

按[要求]输出回答。

[要求]="""
1. 根据用户输入解析出用户想要什么样的app。以及需要推荐的app个数。
2. 首先总结你推荐的app。然后一一列出推荐app的详细信息。
3. 必须要借助自身知识和[资料]进行理解。
"""


====== 22222 ======

作为app推荐专家，参考提供的[资料]和自身的知识，分析[用户问题]，明确[需要推荐的app个数]，推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。结合关键资料和[需要推荐的app个数]以及自己的知识进行准确回答。回答格式：语气礼貌。列出[num]个你推荐的app，先用一句话介绍app，随后分点详细介绍app。

[资料]="""
{content}
"""
[num]="""
{num}
"""
[用户问题]="""
{query}
"""



======

作为app推荐专家，参考提供的[资料]和[模型]，并结合自身的知识，分析[用户问题]，明确[需要推荐的app个数]，准确推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。回答格式：语气礼貌。列出[需要推荐的app个数]个你推荐的app，先用一句话介绍app，随后分点详细介绍app。


