好的，下面给你写一个完整示例，结合了：

- `dataclass` 定义事件和评价数据结构
- 两个日志类 `EventLogger` 和 `ReviewLogger` 负责文件读写和统计
- 详细注释说明
- 最后有示例调用演示写入和统计

---

```python
import os
import json
from dataclasses import dataclass, asdict, field
from typing import Dict, Optional, List
from datetime import datetime, timedelta

# -------------------------------
# 数据结构定义：使用 dataclass
# -------------------------------

@dataclass
class Event:
    """
    埋点事件数据结构
    """
    timestamp: str  # ISO格式时间字符串，如 "2024-06-01T12:00:00Z"
    event_type: str
    user_id: str
    device_info: Optional[str] = None
    event_params: Dict = field(default_factory=dict)

    def to_dict(self) -> dict:
        """
        转换为字典，方便序列化写入文件
        """
        return asdict(self)

    @staticmethod
    def from_dict(data: dict) -> "Event":
        """
        从字典反序列化为 Event 对象
        """
        return Event(**data)


@dataclass
class Review:
    """
    评价数据结构
    """
    timestamp: str
    name: str
    order_id: str
    score: Optional[int] = None
    comment: Optional[str] = None

    def to_dict(self) -> dict:
        """
        转换为字典，方便序列化写入文件
        """
        return asdict(self)

    @staticmethod
    def from_dict(data: dict) -> "Review":
        """
        从字典反序列化为 Review 对象
        """
        return Review(**data)


# -------------------------------
# 基础日志类，封装文件读写
# -------------------------------

class BaseLogger:
    """
    基础日志类，负责按日期写入和读取JSON Lines格式文件
    """

    def __init__(self, base_dir: str):
        """
        初始化，确保目录存在
        :param base_dir: 存储日志文件的目录
        """
        self.base_dir = base_dir
        os.makedirs(self.base_dir, exist_ok=True)

    def _get_file_path(self, date_str: str) -> str:
        """
        根据日期字符串获取对应日志文件路径
        :param date_str: 日期字符串，格式 "YYYYMMDD"
        :return: 文件路径
        """
        return os.path.join(self.base_dir, f"{date_str}.log")

    def write(self, data: dict, dt: Optional[datetime] = None):
        """
        追加写入一条日志数据（字典形式）
        :param data: 要写入的数据字典
        :param dt: 数据对应的时间，默认当前UTC时间
        """
        date_str = dt.strftime("%Y%m%d") if dt else datetime.utcnow().strftime("%Y%m%d")
        filepath = self._get_file_path(date_str)
        with open(filepath, "a", encoding="utf-8") as f:
            f.write(json.dumps(data, ensure_ascii=False) + "\n")

    def read(self, date_str: str) -> List[dict]:
        """
        读取指定日期的所有日志数据，返回字典列表
        :param date_str: 日期字符串，格式 "YYYYMMDD"
        :return: 数据字典列表
        """
        filepath = self._get_file_path(date_str)
        if not os.path.exists(filepath):
            return []
        results = []
        with open(filepath, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                try:
                    results.append(json.loads(line))
                except json.JSONDecodeError:
                    # 忽略格式错误的行
                    continue
        return results


# -------------------------------
# 埋点日志类
# -------------------------------

class EventLogger(BaseLogger):
    """
    埋点事件日志管理类
    """

    def __init__(self, base_dir: str = "./data/events"):
        super().__init__(base_dir)

    def write_event(self, event: Event):
        """
        写入一条事件日志
        :param event: Event对象
        """
        self.write(event.to_dict())

    def read_events(self, date_str: str) -> List[Event]:
        """
        读取指定日期的所有事件，返回 Event 对象列表
        :param date_str: 日期字符串 "YYYYMMDD"
        :return: Event对象列表
        """
        dicts = self.read(date_str)
        return [Event.from_dict(d) for d in dicts]

    def count_events(self, date_str: str, event_type: Optional[str] = None) -> int:
        """
        统计指定日期的事件数量，可按事件类型过滤
        :param date_str: 日期字符串 "YYYYMMDD"
        :param event_type: 事件类型，默认统计所有
        :return: 事件数量
        """
        events = self.read_events(date_str)
        if event_type:
            return sum(1 for e in events if e.event_type == event_type)
        return len(events)

    def count_events_in_range(self, start_date: str, end_date: str, event_type: Optional[str] = None) -> int:
        """
        统计时间范围内的事件数量，可按事件类型过滤
        :param start_date: 起始日期 "YYYYMMDD"
        :param end_date: 结束日期 "YYYYMMDD"
        :param event_type: 事件类型，默认统计所有
        :return: 事件数量
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        count = 0
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            count += self.count_events(day_str, event_type)
        return count


# -------------------------------
# 评价日志类
# -------------------------------

class ReviewLogger(BaseLogger):
    """
    评价日志管理类
    """

    def __init__(self, base_dir: str = "./data/reviews"):
        super().__init__(base_dir)

    def write_review(self, review: Review):
        """
        写入一条评价日志
        :param review: Review对象
        """
        self.write(review.to_dict())

    def read_reviews(self, date_str: str) -> List[Review]:
        """
        读取指定日期的所有评价，返回 Review 对象列表
        :param date_str: 日期字符串 "YYYYMMDD"
        :return: Review对象列表
        """
        dicts = self.read(date_str)
        return [Review.from_dict(d) for d in dicts]

    def count_reviews_by_name(self, date_str: str, name: str) -> int:
        """
        统计指定日期某人的评价数量
        :param date_str: 日期字符串 "YYYYMMDD"
        :param name: 用户姓名
        :return: 评价数量
        """
        reviews = self.read_reviews(date_str)
        return sum(1 for r in reviews if r.name == name)

    def get_reviews_by_order(self, date_str: str, order_id: str) -> List[Review]:
        """
        获取指定日期某订单的所有评价
        :param date_str: 日期字符串 "YYYYMMDD"
        :param order_id: 订单号
        :return: Review对象列表
        """
        reviews = self.read_reviews(date_str)
        return [r for r in reviews if r.order_id == order_id]

    def average_score_by_name(self, date_str: str, name: str) -> Optional[float]:
        """
        计算指定日期某人的平均评分
        :param date_str: 日期字符串 "YYYYMMDD"
        :param name: 用户姓名
        :return: 平均分，若无评分返回 None
        """
        reviews = self.read_reviews(date_str)
        scores = [r.score for r in reviews if r.name == name and isinstance(r.score, (int, float))]
        if not scores:
            return None
        return sum(scores) / len(scores)

    def count_reviews_in_range(self, start_date: str, end_date: str, name: Optional[str] = None) -> int:
        """
        统计时间范围内的评价数量，可按姓名过滤
        :param start_date: 起始日期 "YYYYMMDD"
        :param end_date: 结束日期 "YYYYMMDD"
        :param name: 用户姓名，默认统计所有
        :return: 评价数量
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        count = 0
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            if name:
                count += self.count_reviews_by_name(day_str, name)
            else:
                count += len(self.read_reviews(day_str))
        return count


# -------------------------------
# 示例演示
# -------------------------------

if __name__ == "__main__":
    # 初始化日志管理器
    event_logger = EventLogger()
    review_logger = ReviewLogger()

    # 当前UTC时间
    now = datetime.utcnow()

    # 创建一个事件对象
    event = Event(
        timestamp=now.isoformat() + "Z",
        event_type="click_button",
        user_id="user123",
        device_info="iPhone 13",
        event_params={"button_id": "submit_order"}
    )
    # 写入事件日志
    event_logger.write_event(event)

    # 创建一个评价对象
    review = Review(
        timestamp=now.isoformat() + "Z",
        name="张三",
        order_id="order456",
        score=5,
        comment="服务很好"
    )
    # 写入评价日志
    review_logger.write_review(review)

    # 获取今天日期字符串
    today_str = now.strftime("%Y%m%d")

    # 统计今天点击按钮事件数量
    click_count = event_logger.count_events(today_str, event_type="click_button")
    print(f"今日点击按钮事件数量: {click_count}")

    # 统计今天张三的评价数量
    review_count = review_logger.count_reviews_by_name(today_str, "张三")
    print(f"今日张三的评价数量: {review_count}")

    # 计算今天张三的平均评分
    avg_score = review_logger.average_score_by_name(today_str, "张三")
    print(f"今日张三的平均评分: {avg_score}")

    # 读取今天所有事件对象
    events_today = event_logger.read_events(today_str)
    print(f"今日所有事件: {events_today}")

    # 读取今天张三的所有评价
    reviews_today = review_logger.read_reviews(today_str)
    print(f"今日所有评价: {reviews_today}")
```

---

# 说明

- `Event` 和 `Review` 用 `dataclass` 定义，字段清晰，方便管理。
- `BaseLogger` 负责文件的追加写入和读取，按日期分文件。
- `EventLogger` 和 `ReviewLogger` 继承 `BaseLogger`，实现各自的统计和业务逻辑。
- 示例中演示了写入数据和统计查询的用法。
- 文件存储路径默认是当前目录下的 `./data/events` 和 `./data/reviews`，你可以根据需要修改。

---

如果你需要，我也可以帮你写成支持异步写入、或者用SQLite存储的版本。