import os

from AiLogs.logs.logger import app_logger
from AiLogs.client.fetch_log_client import FetchLogClient
from AiLogs.agent import UserIntent
from AiLogs.agent.filter_user_action import FilterUserAction
from datetime import datetime, timedelta
from AiLogs.tools import log_process as lp

class AiLogs:
    def __init__(self):
        pass
    
    def analyze_log(self, query, log_save_path=None, bug_time=None, log_download_link="", log_path="", is_check_all_logs=False):
        if log_download_link == "" and log_path == "":
            raise ValueError("log_download_link 和 log_path 不能同时为空")

        # 1. 解析日志路径，获取对应日志文件，判断bug时间是否可信
        
        # 是否根据bug时间找到日志文件
        isFileFoundByBugTime = False

        # daemon 进程日志文件
        daemon_log_path = ""
        # 所有日志文件名
        log_filenames = ""
        # 日志文件夹 路径
        logs_path = ""

        # 如果没有传入log_path，根据 log_download_link 解压
        if log_path == "":
            # 解析下载链接 获取日志文件路径
            fetch_log = FetchLogClient(download_link=log_download_link, 
                                        bug_time=bug_time,
                                        query=query, 
                                        log_save_path=log_save_path, 
                                        is_analyze_daemon=True)
            log_path, daemon_log_path, log_filenames, logs_path = fetch_log.fetch_log_from_url()
            isFileFoundByBugTime = fetch_log.isFileFoundByBugTime()
        else:
            # 检查是不是压缩包
            is_zip_file = self._is_zip_file(log_path)
            if is_zip_file:
                # 是压缩包，解压并返回对应的日志文件路径
                fetch_log = FetchLogClient(download_link=log_path, 
                                        bug_time=bug_time,
                                        query=query, 
                                        log_save_path=log_save_path, 
                                        is_analyze_daemon=True)
                log_path, daemon_log_path, log_filenames, logs_path = fetch_log.unzip_and_fetch_log(log_path)
                isFileFoundByBugTime = fetch_log.isFileFoundByBugTime()
            else:
                if bug_time:
                    # 解析bug时间字符串为datetime对象
                    datetime_obj = datetime.strptime(bug_time, "%Y-%m-%d %H:%M:%S")
                    # 转换为目标格式的字符串
                    target_time_str = datetime_obj.strftime("%Y%m%d%H")
                    app_logger.info(f"用户输入bug时间：{target_time_str}")
                    if target_time_str in log_path:
                        isFileFoundByBugTime = True
                    else:
                        isFileFoundByBugTime = False
                else:
                    isFileFoundByBugTime = False
        
        print("日志文件路径：", log_path)
        app_logger.info(f'日志文件路径： = {log_path}')
        app_logger.info(f'daemon日志文件路径： = {daemon_log_path}')
        app_logger.info(f'日志文件夹 路径： = {logs_path}')
        app_logger.info(f'isFileFoundByBugTime = {isFileFoundByBugTime}')
        if not log_path:
            app_logger.info("未找到日志文件")
            return None

        # 2. bug_time 处理
        # 时间不确定可以不传
        start_time = None
        end_time = None
        if isFileFoundByBugTime and bug_time and len(bug_time) > 15:
            bug_time += '.000'
            start_time, end_time = self._calculate_time_range(bug_time)
        
        # 3. 开始分析日志
        print("AI 开始分析 您的日志... ")
        userActionAgent = FilterUserAction(log_path, start_time, end_time, query, daemon_log_path, is_check_all_logs, logs_path)
        for result in userActionAgent.parse_user_action():
            yield {"data": result['data'], "type": result['type']}
        # return userActionAgent.parse_user_action()

    def _is_zip_file(self, file_path: str) -> bool:
        """检查是否为ZIP文件（根据文件头）"""
        try:
            with open(file_path, "rb") as f:
                return f.read(4) in (b"PK\x03\x04", b"PK\x05\x06", b"PK\x07\x08")
        except Exception:
            return False

    def _calculate_time_range(self, time_str: str) -> tuple[datetime, datetime]:
        # 将输入字符串解析为datetime对象（支持微秒）
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")

        # 计算时间偏移量
        delta_backward = timedelta(minutes=20)  # 前推20分钟
        delta_forward = timedelta(minutes=10)  # 后延10分钟

        # 生成结果时间
        past_time = dt - delta_backward
        future_time = dt + delta_forward

        return past_time, future_time

    def save_result_to_file(self, result, query):
        os.makedirs('.test_AI/', exist_ok=True)
        save_path = os.path.join('.test_AI/', f'{query}.md')
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(result)

if __name__ == "__main__":
    # log_download_link = 'https://cms.myapp.com/xy/yybtech/74q9dxpn.zip'
    log_download_link = ""
    # log_path = "/Users/<USER>/yyb/logasistent/data/com.tencent.android.qqdownloader_2025040312.xlog.log"
    log_path =  "/Users/<USER>/Desktop/日志分析/activity - 用户问题不准确，aisee协助将分类输入到问题中/aisee/1. 领取-发货失败/1.我群星纪元首发活动，5w站力值领的2qb发送失败了，幸幸苦苦刚打完，发送失败怎么回事！要求人工重发！！！/TDOSLog_20250419_000553462_21752_58453_decrypted.zip"
    # query = '帮忙看看是否安装失败'
    # query = '参加活动，条件不满足1'
    query = '发货失败'
    log_save_path='data_test1/log_1'
    bug_time='2025-03-20 09:33:00'
    log_analyzer = AiLogs()
    for result in log_analyzer.analyze_log(log_download_link=log_download_link, query=query, log_save_path=log_save_path, bug_time=bug_time, log_path=log_path):
        if result['type'] == 'result_answer':
            # app_logger.info(f"AiLogs最终结果：{result['data']}")
            log_analyzer.save_result_to_file(result['data'], query)
            # print(f"数据: {result['data']}, 类型: {result['type']}")

