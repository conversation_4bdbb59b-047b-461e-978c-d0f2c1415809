import os
import re
from datetime import datetime

from AiLogs.tools.log_process import LogProcessor
from AiLogs.logs.logger import app_logger
from AiLogs.prompt.kuikly_activity_prompt import KUIKLY_ACTIVITY_PROMPT
from AiLogs.client.hunyuan_client import HunyuanClient


class KuiklyActivityAgent():
    def __init__(self, logPath, startTime, endTime, query, user_action, logs_path):
        self._log_path = logPath
        self._start_time = startTime
        self._end_time = endTime
        self._query = query
        self._user_action = user_action
        self._logs_path = logs_path
        self._log_tags = ['PageReporter_beaconReport', 'HttpProtocolInterceptor', 'HttpRequest',
                          'HTTPRequester', 'RuntimeCrash',
                          'ReceivingRewardViewModel', 'YybActCommonReceiveManager',
                          'YybLotteryView','YybLotteryViewModel']

    def parse_kuikly_activity_log(self):
        app_logger.info('======= 开始 活动场景异常 分析  =========')

        filteredLogs, lottery_item_info, obtain_present_info, click_info = self._filtered_log(self._log_path)

        # 如果过滤的日志为空(行数太少就继续合并)
        if not filteredLogs:
            filteredLogs, lottery_item_info, obtain_present_info, click_info = self._filtered_log_again()

        


        # 请求大模型
        prompt = KUIKLY_ACTIVITY_PROMPT.format(rag="", log_content="".join(filteredLogs),
                                     query=self._query, lottery_item_info=lottery_item_info, user_action=self._user_action,
                                     obtain_present_info=obtain_present_info, click_info=click_info)
        yield from self._request_model(prompt)

    def _filtered_log(self, log_path):
        log_processor = LogProcessor()
        log_processor.process(log_path)
        filteredLogs = log_processor.filter_log_by_tags_and_time(self._start_time, self._end_time,
                                                                 self._log_tags)

        lottery_item_info = log_processor.format_log_to_lottery_item_info(filteredLogs)
        app_logger.info(f"lottery_item_info = {lottery_item_info}")

        obtain_present_info = log_processor.format_log_to_obtain_present_info(filteredLogs)
        app_logger.info(f"obtain_present_info = {obtain_present_info}")

        click_info = log_processor.format_log_to_click_info(filteredLogs)
        app_logger.info(f"click_info = {click_info}")

        # app_logger.info(f'===== 首次过滤日志 ======\n{"".join(filteredLogs)}')

        # 保存的关键日志，连续重复的日志，只保存第一条。
        save_logs = [('ReceivingRewardViewModel', 'orderStatus=3'),
                        ('YybActCommonReceiveManager', 'doShowResult instance'),
                        ('PageReporter_beaconReport', 'reportActivityComponentClick'), # reportActivityComponentClick
                        ('YybLotteryView',''),
                        ('YybLotteryViewModel','')]
        # 将日志行中无用的部分截除
        split_info = [('ReceivingRewardViewModel','img'),
                        ('YybActCommonReceiveManager','propertyData')]
        dedup_targets = [('ReceivingRewardViewModel', 'doQueryLotteryResult item')]
        filteredLogs = log_processor.save_log_by_tags_and_content(filteredLogs, save_logs, split_info = split_info, is_fuzzy_match_tag=True, dedup_targets=dedup_targets)

        # 堆栈
        filteredLogs = log_processor.remove_at_com_content(filteredLogs)

        # app_logger.info(f'===== 二次过滤日志 ======\n{"".join(filteredLogs)}')


        return filteredLogs, lottery_item_info, obtain_present_info, click_info
    
    def _filtered_log_again(self):
        """如果最新的两个日志文件过滤出的日志内容为空，将日志文件新到旧排序，继续过滤。"""
        logs = []
        filteredLogs = [] 
        lottery_item_info = '' 
        obtain_present_info = '' 
        click_info = ''
        file_in_path = os.listdir(self._logs_path)
        for file_name in file_in_path:
            if '@' not in file_name:
                logs.append(file_name)
        logs = sorted(logs, key=self._extract_key)
        app_logger.info(f'filtered_log_again === logs: {logs}')
        if len(logs) > 1:
            # 从第2个日志文件开始
            for log_name in logs[1:]:
                filteredLogs, lottery_item_info, obtain_present_info, click_info = self._filtered_log(os.path.join(self._logs_path, log_name))
                if filteredLogs:
                    app_logger.info(f'filtered_log_again === log_name: {log_name}')
                    break
        return filteredLogs, lottery_item_info, obtain_present_info, click_info
    
    def _filtered_all_log(self):
        """对所有主进程日志文件排序日期，合并，进行过滤分析"""
        logs = []
        file_in_path = os.listdir(self._logs_path)
        for file_name in file_in_path:
            if '@' not in file_name:
                logs.append(file_name)
        logs = sorted(logs, key=self._extract_key)
        app_logger.info(f'filtered_all_log === logs: {logs}')
        log_files = []
        if len(logs) > 1:
            for i, log_name in enumerate(logs):
                if i == 4:  # 到第5个日志文件时退出循环
                    break
                log_files.append(os.path.join(self._logs_path, log_name))
            return self._filtered_log(self._merge_logs(log_files))
        else:
            return [],'','',''

    def _extract_key(self, log):
        """
        获取日志文件名的时间戳，以便排序。
        支持格式示例：
        - xxx_2025042009_1.xlog.log
        - xxx_2025042007.xlog.log
        """
        pattern = re.compile(r'_(\d{10})(?:_(\d+))?\.xlog\.log$')
        match = pattern.search(log)
        if not match:
            # 匹配失败，返回最小值，保证排序时放在最后
            return ('0000000000', 0)
        timestamp = match.group(1)
        suffix_str = match.group(2)
        try:
            suffix = int(suffix_str) if suffix_str else 0
        except ValueError:
            suffix = 0
        return (timestamp, suffix)
    
    def _merge_logs(self, log_files):
        """合并日志文件"""
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        merged_log_path = os.path.join(self._logs_path, f'merged_log_{current_time}.log')
        with open(merged_log_path, 'wb') as merged_file:
            for log_file in log_files:
                app_logger.info(f'正在合并 {log_file}')
                with open(log_file, 'rb') as f:
                    merged_file.write(f.read())
                    merged_file.write(b'\n')  # 添加换行符以分隔日志
        app_logger.info(f"合并日志文件成功，保存到: {merged_log_path}")
        return merged_log_path
    
    def _request_model(self, prompt):
        app_logger.info(prompt)

        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "Hunyuan-T1-32K"
        # model = "DeepSeek-R1"
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        yield from self._stream_results(hunyuan_client.request(prompt))
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}

if __name__ == '__main__':
    # log_processor = LogProcessor()
    # filteredLogs = ['2025-04-03 08:54:16.177 I HTTPRequester|08:54.16.142|KLog HTTPRequester]:request start: HTTPReq(interfaceKeyword=/trpc.component_plat.obtain.Obtain/GetObtainInfo, interfaceType=ActExec, baseURL=https://ovactapi.iwan.yyb.qq.com, url=/trpc.activity_plat.controller.ControllerService/Exec, method=post, data={"activity_iid": "aiid_ed78b03e-7e0e-4102-ad78-238c974ba5ef","component_iid": "iid_obtain_7087000e-e619-42ca-8509-18437e405d90","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_7087000e-e619-42ca-8509-18437e405d90\"}"},"qualifier_params": []}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=yyb-obtain, componentID=moka-ui-obtain_40a0915d, headers={})',
    # '2025-04-03 08:54:16.178 I HTTPRequester|08:54.16.151|KLog HTTPRequester]:request start: HTTPReq(interfaceKeyword=/trpc.component_plat.obtain.Obtain/GetObtainInfo, interfaceType=ActExec, baseURL=https://ovactapi.iwan.yyb.qq.com, url=/trpc.activity_plat.controller.ControllerService/Exec, method=post, data={"activity_iid": "aiid_ed78b03e-7e0e-4102-ad78-238c974ba5ef","component_iid": "iid_obtain_391c7ccf-91a1-41b0-9d9d-c2cbc5a4d6d0","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_391c7ccf-91a1-41b0-9d9d-c2cbc5a4d6d0\"}"},"qualifier_params": []}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=yyb-obtain, componentID=moka-ui-obtain_9b8ab1ed, headers={})',
    # '2025-04-03 08:54:16.546 I HTTPRequester|08:54.16.362|KLog HTTPRequester]:request start: HTTPReq(interfaceKeyword=/trpc.component_plat.obtain.Obtain/GetObtainInfo, interfaceType=ActExec, baseURL=https://ovactapi.iwan.yyb.qq.com, url=/trpc.activity_plat.controller.ControllerService/Exec, method=post, data={"activity_iid": "aiid_ed78b03e-7e0e-4102-ad78-238c974ba5ef","component_iid": "iid_obtain_d7f4279a-6dc7-4538-864e-47e9b37646cd","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_d7f4279a-6dc7-4538-864e-47e9b37646cd\"}"},"qualifier_params": []}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=yyb-obtain, componentID=moka-ui-obtain_1654a68f, headers={})',
    # '2025-04-03 08:54:17.686 I HTTPRequester|08:54.17.681|KLog HTTPRequester]:request start: HTTPReq(interfaceKeyword=/trpc.component_plat.obtain.Obtain/DoObtain, interfaceType=ActExec, baseURL=https://ovactapi.iwan.yyb.qq.com, url=/trpc.activity_plat.controller.ControllerService/Exec, method=post, data={"activity_iid": "aiid_ed78b03e-7e0e-4102-ad78-238c974ba5ef","component_iid": "iid_obtain_7087000e-e619-42ca-8509-18437e405d90","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/DoObtain","data": "{\"obtain_iid\": \"iid_obtain_7087000e-e619-42ca-8509-18437e405d90\",\"extends\": \"{\\\"partition\\\": \\\"1600010\\\",\\\"plat\\\": \\\"1\\\",\\\"role_id\\\": \\\"4503599627486034\\\",\\\"role_name\\\": \\\"cccczzzz\\\"}\"}"},"qualifier_params": [{"bizName": "idip_54375369_1","paramName": "partition","value": "1600010"},{"bizName": "idip_54375369_1","paramName": "role_id","value": "4503599627486034"},{"bizName": "idip_54375369_1","paramName": "role_name","value": "cccczzzz"},{"bizName": "idip_54375369_1","paramName": "plat_id","value": "1"},{"bizName": "idip_54375369_8","paramName": "partition","value": "1600010"},{"bizName": "idip_54375369_8","paramName": "role_id","value": "4503599627486034"},{"bizName": "idip_54375369_8","paramName": "role_name","value": "cccczzzz"},{"bizName": "idip_54375369_8","paramName": "plat_id","value": "1"}]}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=yyb-obtain, componentID=moka-ui-obtain_40a0915d, headers={})']
    # # tag - key 匹配不到 -- 加入
    # # tag 一样 key 不同 -- 检查是否加入
    # # tag 一样 key 相同 -- 移除
    # contents_to_remove_duplicates = [('HTTPRequester', 'interfaceKeyword', "=", ","),('HTTPRequester', '"ret"', ':', '}')]
    # filteredLogs = log_processor.remove_duplicates_content_with_tag_key(filteredLogs,
    #                                                                     contents_to_remove_duplicates)
    # app_logger.info("".join(filteredLogs))

    logPath = '/Users/<USER>/Desktop/日志分析/activity - 用户问题不准确，aisee协助将分类输入到问题中/aisee/1. 领取-发货失败/11-充值未到账/TDOSLog_20250417_155253101_20769_39011/com.tencent.android.qqdownloader_2025041715.xlog.log'
    startTime = None 
    endTime = None
    query = '活动参与失败'
    logs_path = '/Users/<USER>/yyb/logasistent/data_test1/log_1/log_2025-05-07 14-17-31/TDOSLog_20250417_155253101_20769_39011'
    kuikly_activity_agent = KuiklyActivityAgent(logPath, startTime, endTime, query, '', logs_path)
    kuikly_activity_agent.parse_kuikly_activity_log()
