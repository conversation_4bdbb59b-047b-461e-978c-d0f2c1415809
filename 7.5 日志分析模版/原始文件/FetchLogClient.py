import requests
import zipfile
import os
import re
import mimetypes
from datetime import datetime, timedelta

from AiLogs.logs.logger import app_logger

# 优化：例子
# com.tencent.android.qqdownloader_2025040310.xlog.log
# com.tencent.android.qqdownloader_2025040311.xlog.log
# com.tencent.android.qqdownloader_2025040312.xlog.log
# 2025-04-03 11:58:00
# 最后十分钟的，如果有12点的日志文件，合并两个日志文件（），返回 log_path
# 2025-04-03 11:08:00
# 前十分钟的，如果有10点的日志文件，合并两个日志文件，返回 log_path
# 没给时间的、匹配不到时间的
# 合并最新的两个日志文件，返回 log_path

class FetchLogClient:
    def __init__(self, user_input=None, download_link=None, bug_time=None, query=None, log_save_path=None, is_analyze_daemon=False):
        self._isFileFoundByBugTime = False
        self._user_input = user_input
        self._download_link = download_link
        self._bug_time = bug_time
        self._query = query
        self._zip_file_name = log_save_path
        self._unzip_folder = log_save_path
        self._is_single_file = False
        self._single_file_path = None
        self._is_analyze_daemon = is_analyze_daemon
        # 设置文件存储路径
        self._set_zip_file_name()

    # 根据 download_link 获取日志路径
    def fetch_log_from_url(self):
        # 下载
        self.download_log()
        
        # 如果是单个文件直接返回文件路径
        if self._is_single_file:
            return self._single_file_path, '' , '', ''
            
        # 如果是ZIP文件则继续解压、获取相应日志文件
        return self.unzip_and_fetch_log()
    
    # 解压 并 返回对应的日志文件路径
    def unzip_and_fetch_log(self, zip_path =""):
        base_path = self.unzip_file(zip_path)
        log_path = self.get_log_path(base_path)
        
        if not log_path or log_path == '':
            raise ValueError("未能找到有效的日志文件，请检查日志压缩包是否为bugly格式")
            
        return log_path
    
    # 设置文件存储路径
    def _set_zip_file_name(self):
        app_logger.info(f"self._zip_file_name:{self._zip_file_name}")
        app_logger.info(f"self._unzip_folder:{self._unzip_folder}")
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置压缩文件的路径
        # 如果没有传入文件路径 默认设置根目录下的data文件夹下。
        if self._zip_file_name:
            self._zip_file_name = os.path.join(self._zip_file_name, f'log_{current_time}.zip')
        else:
            # 压缩文件的路径
            self._zip_file_name = os.path.join(current_dir, '../data', f'log_{current_time}.zip')
        zip_file_target_dir = os.path.dirname(os.path.abspath(self._zip_file_name))
        if not os.path.exists(zip_file_target_dir):
            os.makedirs(zip_file_target_dir)

        
        # 设置解压文件的路径
        # 如果没有传入文件路径 默认设置根目录下的data文件夹下。
        if self._unzip_folder:
            self._unzip_folder = os.path.join(self._unzip_folder, f'log_{current_time}')
        else:
            # 解压文件的路径
            self._unzip_folder = os.path.join(current_dir, '../data', f'log_{current_time}')
        unzip_folder_target_dir = os.path.dirname(os.path.abspath(self._unzip_folder))
        if not os.path.exists(unzip_folder_target_dir):
            os.makedirs(unzip_folder_target_dir)

    
    # 用户输入由 下载链接、bug时间、问题 组成。 如 
    # 日志链接：https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
    # bug时间：2025-02-16 15:23:07 
    # 问题：用户反馈下载失败，定位一下原因
    # 函数功能：提取出 下载链接（https://cms.myapp.com/xy/yybtech/74q9dxpn.zip）、bug时间（2025-02-16 15:23:07.000）、问题（用户反馈下载失败，定位一下原因）
    def extract_log_info(self):
        # 定义正则表达式模式
        link_pattern = r'日志链接：(https?://[^\s]+)'
        # 捕获日期和时间部分（包含可选的秒）。(?:\S*)?：匹配后续的非空白字符（如“左右”），但不捕获。
        time_pattern = r'bug时间：(\d{4}-\d{2}-\d{2} \d{2}:\d{2}(?::\d{2})?)'
        query_pattern = r'问题：(.*)'

        # 使用正则表达式搜索匹配
        link_match = re.search(link_pattern, self._user_input)
        time_match = re.search(time_pattern, self._user_input)
        query_match = re.search(query_pattern, self._user_input)

        # 如果下载链接匹配不成功，抛出异常
        if link_match:
            download_link = link_match.group(1)
        else:
            app_logger.error("下载链接匹配失败")
            raise ValueError("日志文件下载链接匹配失败，请检查输入格式")

        # 处理 bug 时间
        if time_match:
            bug_time = time_match.group(1)
            if len(bug_time) == 16:  # "YYYY-MM-DD HH:MM"
                bug_time += ':00'  # 添加秒
            bug_time += '.000'  # 添加毫秒
        else:
            bug_time = None

        # 处理问题描述
        query = query_match.group(1) if query_match else None

        # 保存提取的信息
        self._download_link = download_link
        self._bug_time = bug_time
        self._query = query
    
    # 下载日志
    def download_log(self):
        # 解析 企微机器人 的 用户输入。获取日志信息
        # 企微机器人需要传入的参数user_input
        if self._user_input:
            self.extract_log_info()
        else:
            app_logger.info("非企微机器人场景，无须解析用户输入")

        if not self._download_link:
            app_logger.error("下载链接为空")
            raise ValueError("日志文件下载链接为空，请提供有效的下载链接")
            
        # 发送 HEAD 请求获取文件类型
        try:
            head_response = requests.head(self._download_link)
            content_type = head_response.headers.get('Content-Type', '')
            content_disposition = head_response.headers.get('Content-Disposition', '')
            
            # 检查是否是单个日志文件而非 ZIP 文件
            if 'application/zip' not in content_type:
                app_logger.info(f"检测到单个日志文件，Content-Type: {content_type}")
                return self._download_single_file()
        except Exception as e:
            app_logger.warning(f"HEAD 请求失败，将尝试直接下载: {str(e)}")
        
        # 下载 ZIP 文件
        response = requests.get(self._download_link)

        # 检查请求是否成功
        if response.status_code == 200:
            # 检查响应内容类型，确定是否为 ZIP 文件
            content_type = response.headers.get('Content-Type', '')
            
            # 如果不是 ZIP 文件，作为单个日志文件处理
            if 'application/zip' not in content_type and not self._is_zip_content(response.content):
                app_logger.info(f"下载的内容不是 ZIP 文件，将作为单个日志文件处理, Content-Type: {content_type}")
                return self._save_as_single_file(response.content)
            
            # 打开文件并写入内容
            with open(self._zip_file_name, "wb") as file:
                file.write(response.content)
            app_logger.info(f"ZIP 文件下载成功，下载到目录:{self._zip_file_name}")
            return True
        else:
            error_msg = f"下载失败，状态码：{response.status_code}"
            app_logger.error(error_msg)
            raise RuntimeError(error_msg)
            
    # 下载单个日志文件
    def _download_single_file(self):
        app_logger.info(f"开始下载单个日志文件: {self._download_link}")
        response = requests.get(self._download_link)
        
        if response.status_code == 200:
            return self._save_as_single_file(response.content)
        else:
            error_msg = f"单个文件下载失败，状态码：{response.status_code}"
            app_logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    # 保存为单个日志文件
    def _save_as_single_file(self, content):
        # 从 URL 或内容类型推断文件扩展名
        filename = os.path.basename(self._download_link)
        if not filename or '.' not in filename:
            # 尝试从 Content-Type 猜测扩展名
            content_type = requests.head(self._download_link).headers.get('Content-Type', '')
            ext = mimetypes.guess_extension(content_type) or '.log'
            current_time = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"log_{current_time}{ext}"
        
        # 创建保存目录
        save_dir = os.path.dirname(self._unzip_folder)
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        # 设置单个文件的保存路径
        self._single_file_path = os.path.join(save_dir, filename)
        
        # 保存文件
        with open(self._single_file_path, 'wb') as file:
            file.write(content)
            
        app_logger.info(f"单个日志文件下载成功，保存到: {self._single_file_path}")
        self._is_single_file = True
        return True
    
    # 检查内容是否为 ZIP 文件
    def _is_zip_content(self, content):
        # ZIP 文件的魔数是 'PK\x03\x04'
        return content.startswith(b'PK\x03\x04')

    # 解压ZIP文件
    def unzip_file(self, zip_path =""):
        # 创建解压目录（如果不存在）
        if not os.path.exists(self._unzip_folder):
            os.makedirs(self._unzip_folder)

        # 解压ZIP文件
        if zip_path != "":
            self._zip_file_name = zip_path
        with zipfile.ZipFile(self._zip_file_name, 'r') as zip_ref:
            zip_ref.extractall(self._unzip_folder)
        app_logger.info(f"文件解压成功，解压到目录：{self._unzip_folder}")
        return self._unzip_folder
    
    # 根据 Base路径 获取日志文件夹路径
    def get_log_path(self, base_path):
        # 定义目标路径
        contents = os.listdir(base_path)
        # 找到日志文件夹
        subfolder_path = None
        for item in contents:
            item_path = os.path.join(base_path, item)
            
            # 如果item_path是文件的话, 就直接用base_path下的所有文件
            if not os.path.isdir(item_path):
                subfolder_path = base_path
                break
            # 过滤mac系统的文件夹
            if os.path.isdir(item_path) and not item.startswith('__MACOSX'):
                subfolder_path = item_path
                break
                
        if not subfolder_path:
            raise ValueError(f"在解压目录 {base_path} 中未找到有效的日志子文件夹")
            
        return self.get_log_files(subfolder_path)

    # 根据时间来匹配文件名获取日志文件
    def get_log_files(self, file_path):
        if not file_path:
            raise ValueError("日志文件路径为空")
            
        # 获取子文件夹下的所有文件
        file_in_path = os.listdir(file_path)
        
        if not file_in_path:
            raise ValueError(f"日志文件夹 {file_path} 为空，未找到任何日志文件")

        # 没有 bug_time 选取最新时间，选择最新日志文件
        print(f'time = {self._bug_time}')
        app_logger.info(f'time = {self._bug_time}')
        if not self._bug_time:
            print('no time')
            app_logger.info("no time")

            # 获取一个最新日期的日志文件
            # file = self.get_latest_timestamp_log(file_in_path)
            # 获取两个最新时间戳的日志文件
            file = self.get_latest_two_logs(file_in_path)
            log_path = self.get_final_filename(file, file_path, is_get_daemon_filename=False)

            daemon_log_path = ''
            if self._is_analyze_daemon:
                # 选中的daemon文件名 列表
                daemon_files = self.get_daemon_files(file, file_in_path)
                # 获取daemon进程 日志文件 路径
                daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)     
            
            # 返回：主进程日志路径、daemon进程日志路径、所有日志文件名、日志文件夹 路径
            return log_path, daemon_log_path, file_in_path, file_path

        if not self._user_input:
            # 非企微机器人场景，bug_time 格式化为 %Y-%m-%d %H:%M:%S.%f
            self._bug_time += '.000'
        
        # 判断是否 相邻整点 相差在10分钟内，得到相邻整点字符串
        near_clock_str = self.is_within_ten_minutes()
        
        # 解析原始时间字符串为datetime对象
        datetime_obj = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        # 转换为目标格式的字符串
        target_time_str = datetime_obj.strftime("%Y%m%d%H")
        app_logger.info(f"用户输入bug时间：{target_time_str}")
        
        # 有 bug_time 根据bug时间选取日志文件
        log_files = []
        # 用来获取daemon文件
        files_for_daemon = []
        for file in file_in_path:
            if '@' not in file and target_time_str in file:
                files_for_daemon.append(file)
                log_files.append(os.path.join(file_path, file))
            elif near_clock_str and '@' not in file and near_clock_str in file:
                files_for_daemon.append(file)
                log_files.append(os.path.join(file_path, file))
        app_logger.info(f"要合并的日志路径：{log_files}")

        log_path = None
        daemon_log_path = None
        # 如果只有一个日志文件，直接返回该文件路径
        if len(log_files) == 1:
            log_path = os.path.join(file_path, file)
        # 合并日志文件，获取合并后的日志文件路径
        elif len(log_files) > 1:
            log_path = self.merge_logs(log_files)
        
        if self._is_analyze_daemon:
            # 选中的daemon文件名 列表
            daemon_files = self.get_daemon_files(files_for_daemon, file_in_path)
            # 获取daemon进程 日志文件 路径
            daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)

        
        # 如果根据bug_time找不到匹配的日志文件，则返回最新日期的日志文件
        if not log_path:
            self._isFileFoundByBugTime = False
            app_logger.warning(f"根据用户输入的bug时间 {target_time_str}，未找到对应的日志文件，将返回最新日志文件")
            # 获取一个最新日期的日志文件
            # file = self.get_latest_timestamp_log(file_in_path)
            # 获取两个最新时间戳的日志文件
            file = self.get_latest_two_logs(file_in_path)
            # 获取 主进程 日志文件路径
            log_path = self.get_final_filename(file, file_path, is_get_daemon_filename=False)
            
            if self._is_analyze_daemon:
                # 选中的daemon文件名 列表
                daemon_files = self.get_daemon_files(file, file_in_path)
                # 获取daemon进程 日志文件 路径
                daemon_log_path = self.get_final_filename(daemon_files, file_path, is_get_daemon_filename=True)
        else:
            self._isFileFoundByBugTime = True
        
        app_logger.info(f"找到的主进程日志文件路径：{log_path}")
        app_logger.info(f"找到的daemon日志文件路径：{daemon_log_path}")
        # 返回：主进程日志路径、daemon进程日志路径、所有日志文件名、日志文件夹 路径
        return log_path, daemon_log_path, file_in_path, file_path
    
    def get_final_filename(self, file, file_path, is_get_daemon_filename):
        if file:
            if len(file) > 1:
                # 合并两个文件
                file_to_merge = []
                file_to_merge.append(os.path.join(file_path, file[0]))
                file_to_merge.append(os.path.join(file_path, file[1]))
                return self.merge_logs(file_to_merge)
            else:
                # 获取一个最新日期文件的路径
                return os.path.join(file_path, file[0])
            app_logger.info(f"返回最新时间戳的日志文件：{log_path}")
        else:
            if is_get_daemon_filename:
                return ''
            else:
                error_msg = "未找到任何符合条件的日志文件"
                app_logger.error(error_msg)
                raise ValueError(error_msg)
    
    def get_daemon_files(self, file, file_in_path):
        # 选中的daemon文件名 列表
        app_logger.info(f'== get_daemon_files ==\n主进程日志文件： {file}')
        daemon_files = []
        for file_name in file:
            daemon_file = self.add_log_filename_with_daemon(file_name)
            if daemon_file in file_in_path:
                daemon_files.append(daemon_file)
        app_logger.info(f'== get_daemon_files ==\ndaemon日志文件： {daemon_files}')
        return daemon_files


    def add_log_filename_with_daemon(self, filename: str) -> str:
        """
        将形如 'com.tencent.android.qqdownloader_2025032615.xlog.log' 的文件名
        转换成 'com.tencent.android.qqdownloader@daemon_2025032615.xlog.log'。

        规则：
        - 找到第一个下划线，将其替换为 '@daemon_'。
        - 其余部分保持不变。

        :param filename: 原始文件名字符串
        :return: 转换后的文件名字符串
        """
        # 找到第一个下划线的位置
        idx = filename.find('_')
        if idx == -1:
            # 如果没有下划线，返回原字符串
            return filename

        # 在第一个下划线处插入 '@daemon'
        return filename[:idx] + '@daemon' + filename[idx:]
    
    # 根据时间判断是否在十分钟内，并返回相邻整点格式为：%Y%m%d%H
    def is_within_ten_minutes(self):
        # 将字符串转换为 datetime 对象
        current_time = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        
        # 获取当前时间的小时
        current_hour = current_time.hour
        
        # 计算上一个整点和下一个整点
        previous_hour = current_hour - 1 if current_hour > 0 else 23
        next_hour = current_hour + 1 if current_hour < 23 else 0
        
        # 创建上一个整点和下一个整点的 datetime 对象
        previous_time = current_time.replace(hour=previous_hour, minute=0, second=0, microsecond=0)
        next_time = current_time.replace(hour=next_hour, minute=0, second=0, microsecond=0)
        
        # 检查与上一个整点的间隔
        if (current_time - previous_time) < timedelta(minutes=10):
            return previous_time.strftime("%Y%m%d%H")
        
        # 检查与下一个整点的间隔
        if (next_time - current_time) < timedelta(minutes=10):
            return next_time.strftime("%Y%m%d%H")
        
        return None
    
    # 合并日志文件
    def merge_logs(self, log_files):
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        merged_log_path = os.path.join(self._unzip_folder, f'merged_log_{current_time}.log')
        with open(merged_log_path, 'wb') as merged_file:
            for log_file in log_files:
                app_logger.info(f'正在合并 {log_file}')
                with open(log_file, 'rb') as f:
                    merged_file.write(f.read())
                    merged_file.write(b'\n')  # 添加换行符以分隔日志
        app_logger.info(f"合并日志文件成功，保存到: {merged_log_path}")
        return merged_log_path
    
    # 给出logs（日志文件名的列表） 根据日志文件名称获取最新的两个日志文件。
    def get_latest_two_logs(self, logs):
        # todo 可选项：是否开启daemon搜索
        # 排除包含 '@' 的日志文件名
        filtered_logs = [log for log in logs if '@' not in log]
        app_logger.info(f'get_latest_two_logs ===== filtered_logs = {filtered_logs}')

        sort_logs_by_timestamp = self.sort_logs_by_timestamp(filtered_logs)

        app_logger.info(f'get_latest_two_logs ===== sort_logs_by_timestamp[:2] = {sort_logs_by_timestamp[:2]}')
        return sort_logs_by_timestamp[:2]
    
    # 根据时间戳排序日志文件
    def sort_logs_by_timestamp(self, logs):
        def extract_timestamp(log):
            # 使用正则表达式提取时间戳部分
            match = re.search(r'_(\d{10})(?:_\d+)?\.xlog\.log$', log)
            if match:
                return match.group(1)
            return ''

        # 使用sorted函数和自定义键进行排序
        sorted_logs = sorted(logs, key=extract_timestamp, reverse=True)
        return sorted_logs
    
    # 根据日志文件名称获取最新的日志文件
    # 获取时间最晚的（2025021615）且没有_（com.tencent.android.qqdownloader_2025021615_1.xlog.log）的尾缀的字符串。
    def get_latest_timestamp_log(self, logs):
        app_logger.info("get_latest_timestamp_log")
        timestamp_pattern = re.compile(r'_(\d{10})(?!_)')
        
        latest_timestamp = None
        latest_log = None
        
        for log in logs:
            # 排除包含 '@' 的日志文件名
            if '@' in log:
                continue

            match = timestamp_pattern.search(log)
            if match:
                timestamp = match.group(1)
                if latest_timestamp is None or timestamp > latest_timestamp:
                    latest_timestamp = timestamp
                    latest_log = log
        app_logger.info(f'get_latest_timestamp_log ===== latest_log = {latest_log}')
        return latest_log

    def get_query(self):
        return self._query

    def get_bug_time(self):
        return self._bug_time
    
    # 是否 根据bug时间找到日志文件
    def isFileFoundByBugTime(self):
        return self._isFileFoundByBugTime

    # 查询
    def checkFileFoundByBugTime(self, bug_time):
        # 解析原始时间字符串为datetime对象
        datetime_obj = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        # 转换为目标格式的字符串
        target_time_str = datetime_obj.strftime("%Y%m%d%H")
        app_logger.info(f"用户输入bug时间：{target_time_str}")

        for file in file_in_path:
            if '@' not in file and target_time_str in file:
                log_files.append(os.path.join(file_path, file))
            elif near_clock_str and '@' not in file and near_clock_str in file:
                log_files.append(os.path.join(file_path, file))


if __name__ == "__main__":
    # 场景1：下载单个日志文件
    print("\n===== 场景1：下载单个日志文件 =====")
    download_link = 'https://cms.myapp.com/xy/yybtech/U5sFGrWn.log'
    query = '分析单个日志文件'
    log_save_path = 'log'
    downloader = FetchLogClient(download_link=download_link, query=query, log_save_path=log_save_path, is_analyze_daemon=True)
    log_path, daemon_log_path, log_filenames, logs_path = downloader.fetch_log_from_url()
    print(f'单个文件下载路径: {log_path}')
    print(f'daemon单个文件下载路径: {daemon_log_path}')
    print(f'日志文件名: {log_filenames}')
    print(f'文件夹路径: {logs_path}')


    
    # 场景2：下载ZIP包并匹配bug时间
    print("\n===== 场景2：下载ZIP包并匹配bug时间 =====")
    user_input = '''
日志链接：https://cms.myapp.com/xy/yybtech/WsnFAVsj.zip
bug时间：2025-02-16 13:54 左右
问题：用户反馈下载失败，定位一下原因
    '''
    downloader = FetchLogClient(user_input=user_input, is_analyze_daemon=True)
    log_path, daemon_log_path, log_filenames, logs_path = downloader.fetch_log_from_url()
    print(f'匹配bug时间的日志路径: {log_path}')
    print(f'匹配bug时间的daemon日志路径: {daemon_log_path}')
    print(f'日志文件名: {log_filenames}')
    print(f'文件夹路径: {logs_path}')

    # 场景3：下载ZIP包但提供的bug时间不匹配（将返回最新文件）
    print("\n===== 场景3：下载ZIP包但提供的bug时间不匹配 =====")
    download_link = 'https://cms.myapp.com/xy/yybtech/9qiXeswz.zip'
    # 使用一个不可能匹配的bug时间
    # bug_time = '2025-02-16 23:55:00'
    bug_time = '2025-02-16 22:23:07'
    log_save_path = 'loog'
    downloader = FetchLogClient(download_link=download_link, query=query, bug_time=bug_time, log_save_path=log_save_path, is_analyze_daemon=True)
    log_path, daemon_log_path, log_filenames, logs_path = downloader.fetch_log_from_url()
    print(f'找不到匹配时间但返回最新日志的路径: {log_path}')
    print(f'找不到匹配时间但返回最新daemon日志的路径: {daemon_log_path}')
    print(f'日志文件名: {log_filenames}')
    print(f'文件夹路径: {logs_path}')

    # 场景4：下载ZIP包并匹配bug时间
    print("\n===== 场景4：下载ZIP包 没有bug时间 =====")
    user_input = '''
日志链接：https://cms.myapp.com/xy/yybtech/WsnFAVsj.zip
问题：用户反馈下载失败，定位一下原因
    '''
    downloader = FetchLogClient(user_input=user_input, is_analyze_daemon=True)
    log_path, daemon_log_path, log_filenames, logs_path = downloader.fetch_log_from_url()
    print(f'没有bug时间的日志路径: {log_path}')
    print(f'没有bug时间的daemon日志路径: {daemon_log_path}')
    print(f'日志文件名: {log_filenames}')
    print(f'文件夹路径: {logs_path}')

