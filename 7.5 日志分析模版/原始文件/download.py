from AiLogs.rag.log_rag import log_rag
from AiLogs.tools.log_process import LogProcessor
from AiLogs.logs.logger import app_logger
from AiLogs.prompt.download_prompt import DOWNLOAD_PROMPT
from AiLogs.client.hunyuan_client import HunyuanClient

# todo 跑通下载链路
class DownloadAgent():
    def __init__(self, logPath, bug_time, userAction, query, logs_path):
        self._log_path = logPath
        self._bug_time = bug_time
        self._user_action = userAction
        self._logs_path = logs_path
        self._query = '用户在app内产生了下载行为，请分析下载环节是否存在异常。'
        self._download_tags = ['DownloadTag', 'halley-downloader-SectionTransport',
                               'FLog_download_task_failed', 
                               'RuntimeCrash'
                               ]
        self._indexKeys = {'scene', 'retCode'}

    def parse_download_log(self):
        app_logger.info('======= 开始 下载异常 分析  =========')

        log_processor = LogProcessor()
        log_processor.process(self._log_path)
        filteredLogs = log_processor.filter_log_by_tags_and_time(self._start_time, self._end_time,
                                                                 self._download_tags)

        # RAG 补充下载相关知识， 构建索引
        indexValues = set()
        indexKeys = set()
        log_rag.prepareLogRag()
        for logLine in filteredLogs:
            for indexKey in self._indexKeys:
                key = log_processor.findIndexKeyFromLog(indexKey, logLine)
                if key:
                    indexKeys.add(key)
                    key = key.replace(" ", "")
                    key = key.replace("=", ":")
                    indexValues.add(log_rag.queryExplainByCode(key))
        rag = ""
        for indexValue in indexValues:
            rag += f"{indexValue}\n"
        app_logger.info("".join(rag))
        # app_logger.info(f'===== 首次过滤下载日志 ======\n{"".join(filteredLogs)}')

        # 保存的关键日志，连续重复的日志，只保存第一条。
        save_logs = [('DownloadTag', ''), 
                     ('halley-downloader-SectionTransport', 'readData retCode:'),
                     ('halley-downloader-SectionTransport', 'Direct:true send req'),
                     ('halley-downloader-SectionTransport', 'Transport finish on retCode:')]
        filteredLogs = log_processor.save_log_by_tags_and_content(filteredLogs, save_logs)

        # 无用日志行
        delete_logs = [('DownloadTag', 'fileType=PLUGIN'),
                       ('DownloadTag', 'generateTraceId'),
                       ('DownloadTag', 'PAG动画插件'),
                       ('DownloadTag', '二维码插件'),
                       ('DownloadTag', 'plugin'),
                       ('DownloadTag', '洗包监控'),
                       ('DownloadTag', 'startAllWaitingForMobileNetworkDownloadTask'),
                       ('DownloadTag', 'startAllWaitingForWifiDownloadTask')]
        filteredLogs = log_processor.delete_log_by_tags_and_content(filteredLogs, delete_logs)

        extract_fields = {
                "download_info": "format_log_to_download_info"
            }

        download_info = log_processor.format_log_to_download_info(filteredLogs)

        app_logger.info(f"download_info = {download_info}")

        app_logger.info(f'===== 二次过滤下载日志 ======\n{"".join(filteredLogs)}')

        log_filter = LogFilter(
            logs_path=self._logs_path,
            log_tags=self._download_tags,
            bug_time=self._bug_time,
            save_logs=save_logs, 
            delete_logs=delete_logs,
            split_info=None, 
            dedup_targets=None, 
            extract_fields=extract_fields,
            is_fuzzy_match_tag=True,
            is_analyze_daemon=False
        )

        # 获取过滤后的日志
        filtered_logs, rag, extracted_info = log_filter.get_filtered_log()

        app_logger.info(f'filteredLogs： {"".join(filteredLogs)}')

        # 请求大模型
        prompt = DOWNLOAD_PROMPT.format(rag=rag, log_content="".join(filteredLogs),
                                        indexKey=";".join(indexKeys), user_action=self._user_action,
                                        query=self._query, download_info=download_info)
        yield from self._request_model(prompt)

    def _request_model(self, prompt):
        app_logger.info(prompt)

        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "Hunyuan-T1-32K"  # DeepSeek-R1
        # model = "DeepSeek-R1"
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        yield from self._stream_results(hunyuan_client.request(prompt))
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}


if __name__ == '__main__':
    logPath = '/Users/<USER>/Desktop/日志分析/download/aisee反馈/11-为什么安装包不能下载/TDOSLog_20250418_234300299_20214_66993/com.tencent.android.qqdownloader_2025041823.xlog.log'
    startTime = None 
    endTime = None
    query = '下载失败'
    downloadAgent = DownloadAgent(logPath, startTime, endTime, "", query)
    downloadAgent.parse_download_log()
