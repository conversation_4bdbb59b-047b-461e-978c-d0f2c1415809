from client.wecom_client import WecomClient
from logs.logger import app_logger

WEBHOOK_KEY = 'a87f69e7-c781-4ce0-a556-308a985ce5c9'

class WecomBotTips:
    @staticmethod
    def send_loading_tips(chat_id: str):
        wecom_client = WecomClient(WEBHOOK_KEY)
        try:
            print("send loading tips")
            response = wecom_client.send_message(chat_id, "开始分析日志，请稍等...")
            app_logger.info(f'Message sent successfully: {response}')
        except Exception as e:
            app_logger.error(f'Failed to send message:{e}')

    @staticmethod
    def send_evaluate_and_save_prompt(chat_id: str, ticket_id: str):
        tips_evaluate_and_save_prompt = f"""
【满意度回访】日志分析完成，请您对本次的分析进行评价！您的工单id为：{ticket_id}

=== 输入格式 ===
【满意度回访】
### 工单ID
{ticket_id}
### 满意度:
5星    4星    3星    2星    1星
（5星代表非常满意，1星代表非常不满意，星级越高代表越满意！）
### 是否保存prompt
是/否

=== 输入示例 ===
【满意度回访】
### 工单ID
{ticket_id}
### 满意度
5星
### 是否保存提示
是

"""
        wecom_client = WecomClient(WEBHOOK_KEY)
        try:
            print("send loading tips")
            response = wecom_client.send_message(chat_id, tips_evaluate_and_save_prompt)
            app_logger.info(f'Message sent successfully: {response}')
        except Exception as e:
            app_logger.error(f'Failed to send message:{e}')

    @staticmethod
    def help_markdown():
        return """
1. 日志分析 请输入：1
2. prompt保存 请输入：2
3. 获取已有全部预设场景 请输入：3
4. 改写场景prompt 请输入：4
"""

    @staticmethod
    def tips_log_analyze():
        return """
# 【日志分析】
## 输入格式
```text
【日志分析】
### 日志链接（必填）
日志下载链接 
### bug时间（可选）
yyyy-mm-dd hh:mm:ss 
### 场景（必填）
需要分析的场景
### 用户问题
用户的问题
### 是否需要改写prompt
是/否
```
## 输入示例
```text
【日志分析】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-05-13 14:10:00
### 场景
下载
### 用户问题
下载失败
### 是否需要改写prompt
是
```
"""

    # 企微机器人超过5秒未响应，企业微信会重试
    @staticmethod
    def retry_text():
        return """
日志在快马加鞭分析中...
"""

    @staticmethod
    def tips_evaluate_and_save_prompt(ticket_id: str):
        return f"""
#【满意度回访】日志分析完成，请您对本次的分析进行评价！您的工单id为：{ticket_id}
## 输入格式：
```text
### 工单ID
{ticket_id}
### 满意度:
5星    4星    3星    2星    1星
（5星代表非常满意，1星代表非常不满意，星级越高代表越满意！）
### 是否保存prompt
是/否
```
## 输入示例
```text
### 工单ID
{ticket_id}
### 满意度:
5星
### 是否保存提示
是
```
"""

    @staticmethod
    def tips_no_scene(scene: str):
        return f"""
# 【未找到日志分析场景「{scene}」】若需继续分析，请按以下 输入格式 输入，我们将为您进行分析。
## 输入格式
```text
【日志分析-未预设场景】
### 日志链接（必填）
日志下载链接
### bug时间（可选）
yyyy-mm-dd hh:mm:ss
### 用户问题
用户的问题
### 场景（必填）
定义场景名称。
### 描述（可选）
场景的详细描述
### 过滤tag（必填）
 ['tag1', 'tag2']
### prompt（必填）
提示词
### 删除的日志行（可选）
[('tag1', 'content1'),('tag2', 'content2')]
```
## 输入示例
```text
【日志分析-未预设场景】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-05-13 14:10:00
### 用户问题
下载失败
### 场景
下载
### 描述
下载失败
### 过滤tag
 ['tag1', 'tag2']
### prompt
你是一名资深Android日志分析专家，擅长逐行阅读日志，通过日志轨迹精准还原用户操作流程，深入分析日志信息。请仔细理解[用户问题]，结合提供的[用户日志]，运用[知识库]中的相关知识和你的专业经验，严格按照[分析流程]进行分析。分析过程中，所有结论必须基于[用户日志]和[知识库]，不得进行无依据的推测或编造。最终请严格按照[格式说明]要求，完整且规范地输出分析结果。

# [知识库]  
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
```
"""


    @staticmethod
    def tips_rewrite_prompt(scene: str = ''):
        if scene:
            start_str = f'【重写预设场景「{scene}」Prompt】'
        else:
            start_str = '【重写Prompt】'
        return f"""
# {start_str}请按以下 输入格式 输入，我们将为您进行分析。
## 输入格式
```text
【重写Prompt】
### 日志链接（必填）
日志下载链接
### bug时间（可选）
yyyy-mm-dd hh:mm:ss
### 用户问题
用户的问题
### 场景（必填）
定义场景名称。
### 描述（可选）
场景的详细描述
### 过滤tag（必填）
 ['tag1', 'tag2']
### prompt（必填）
提示词
### 删除的日志行（可选）
[('tag1', 'content1'),('tag2', 'content2')]
```
## 输入示例
```text
【重写Prompt】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-05-13 14:10:00
### 用户问题
下载失败
### 场景
下载
### 描述
下载失败
### 过滤tag
 ['tag1', 'tag2']
### prompt
你是一名资深Android日志分析专家，擅长逐行阅读日志，通过日志轨迹精准还原用户操作流程，深入分析日志信息。请仔细理解[用户问题]，结合提供的[用户日志]，运用[知识库]中的相关知识和你的专业经验，严格按照[分析流程]进行分析。分析过程中，所有结论必须基于[用户日志]和[知识库]，不得进行无依据的推测或编造。最终请严格按照[格式说明]要求，完整且规范地输出分析结果。

# [知识库]  
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
```
"""

    @staticmethod
    def tips_save_prompt():
        return """
# 【保存场景prompt】
## 输入格式
```text
【保存场景prompt】
### 场景（必填）
需要分析的场景
### 描述（可选）
下载失败第一版
### 过滤tag（必填）
 ["tag1", "tag2"]
### prompt（必填）
提示词
### 删除的日志行（可选）
[('tag1', 'content1'),('tag2', 'content2')]
```
## 输入示例
```text
【保存场景prompt】
### 场景
下载
### 描述
下载失败第一版
### 过滤tag
 ['tag1', 'tag2']
### prompt
提示词
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
```
"""

    @staticmethod
    def delete_markdown():
        return """
我宣布，下班！！！
"""
