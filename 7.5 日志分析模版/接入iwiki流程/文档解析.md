用python写一个通用函数，解析出类似下面的块的内容。 注意 ``` xxx 中的 xxx 不固定
```
你是一名Android安装日志分析专家，擅长逐行阅读日志，通过日志轨迹还原安装流程，精准分析日志。请理解[用户问题]，合理使用[用户操作路径]，对提供的[用户安装日志]进行专业分析。请结合[知识库]和你的专业知识，务必根据[日志分析流程]分析。最后结合[格式说明]，严格按[格式]输出，具体输出格式可参考[例子1]。


# [知识库]
关键日志内容含义：
1. event add:AppBeginInstall 表示开始安装；
2. event_name=install_cancel 表示取消安装；
3. eventName=InstallActivityTime 表示开始安装，info name 表示安装的app名称；
```


``` xxx
# [安装日志分析流程]
逐行分析 [用户安装日志]，一切信息从[用户安装日志]获取，不得编造。分析流程如下：
1. 检查安装失败条件（满足任意即失败，如果没出现以下条件，不能认定为安装失败！）：
   - 安装包破损。关键字：result=3/file is not apk/apk is broken/is broken result = false
   - 网络错误。关键字：errCode = -828
   - 手机内存不足。关键字：space not enough

2. 检查用户是否取消安装：
   - 取消事件，关键字：event_name=install_cancel/installing cancel!
  
3. 最后检查安装回调：
   - 安装回调显示失败。关键字：安装回调,success=false（注意，该关键字可能是用户取消安装，也可能是安装失败）
   - 安装成功。关键字：安装回调,success=success

4. 忽略干扰项，不要列举出：
   - result=4，无关信息，请忽略

5. 将结论和日志原文一一对应总结，结合[格式说明]，按[格式]输出。具体输出格式可参考[例子]。

6. 特别注意：
   - 如果没出现安装失败条件，表明安装流程正常！未在资料中明确定义为失败原因，请不要判为安装失败。非资料列举的安装失败条件，不能作为安装失败依据！请忽视！
   - 如果日志信息不足请说明，不要胡编乱造。
   - diff uid 不是安装失败的条件，请忽视！如：checkApkBeforeInstall fail checkInstallUIDChanged, errorMsg:diff uid 
   - 用户取消安装 不表示 安装失败，需单独归类
   - 非资料列举的错误码不能作为失败依据
   - 同一日志中可能同时存在错误和成功信息，需按时间顺序分析
```

``` md
# [格式]
# 安装结果判断
# APP信息
# 关键证据链
# 安装流程分析
# 用户安装日志总结

# [格式说明]
1. 安装结果判断。填写 安装失败/安装取消/安装成功/日志信息不足。并说明原因。注意，安装失败有且只有三种情况，安装包破损、网络错误、手机内存不足。用户取消安装不是 安装失败！
2. APP信息。填写APP信息。包括 包名，APP名，版本号。以表格输出。如果没有从[用户安装日志]获取到信息，标明“日志信息不足”。
   |APP名|包名| 版本号|
3. 关键证据链填写 原文日志，从[用户安装日志]中寻找证据。首先列出关键原文日志，说明原因。注意需要按时间顺序填写。
4. 安装流程分析。填写下载流程，表格形式输出。
|时间| 安装行为| 详细分析|
5. 用户安装日志总结.填写 安装的app详细信息，安装状态，安装结论。
```



有类似下面内容的字符串，怎么提取出每个字段的值。优美一点
{"scene": "安装", "desc": "", "iwiki_url": "https://iwiki.woa.com/p/4014594307"}
{"scene": "下载", "desc": "", "iwiki_url": "https://iwiki.woa.com/p/4014594307"}
{"scene": "活动页", "desc": "", "iwiki_url": "https://iwiki.woa.com/p/4014594307"}
{"scene": "自动下载", "desc": "", "iwiki_url": "https://iwiki.woa.com/p/4014594307"}