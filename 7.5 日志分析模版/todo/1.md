- 场景唯一，用户需要知道是否唯一

- 需要输出现有的预设场景

- 如果用户不选择要不要改写呢？

- 打日志

格式不对 提示
- Missing required fields in text: ['is_rewrite_prompt']
- 格式合法检查

- 模型过载需要 抛出给用户，其他错误是否需要抛出？


- iwiki链接
- 场景迭代


# 
1. 只会识别到 一个场景  
2. 修改意图识别模版
3. 预设场景补充完成
4. raise 错误补充
5. 下载场景的日志过滤优化
6. ailogs包的完善
   1. 意图识别的Prompt保存到aisee
   2. prompt的内容全都转到 iwiki，不然后期若增加改动，还要改。
   3. save_logs逻辑改写，去重啥的，已经做了简单改写，要测试一下

# 5.20
1. daemon 进程，
   1. 再次过滤的逻辑需要增加
   2. 首次过滤的逻辑
2. 正则匹配

# 5.21
1. crash场景的增加
2. 调研灯塔上报
3. 发布版本新包，配合联调
4. 归一化去重

# 5.22
1. 上报
2. crash场景的测评
3. 联调
4. 增加video错误码
5. tag支持正则


# 5.23
iwiki格式
如果与正则匹配冲突，需要转义字符
如 ()  -> \(\)

弹窗场景 需要过滤全部的日志文件
- todo 脚本预处理 输出弹窗次数

注意：日志格式化，是把前面的去掉了，只留下 VideoViewComponent了

模型过载反馈，待解决
1. 根据使用过程中，常见的问题，完善错误反馈机制，（输入不是英文，{}冲突）
遇到格式规范检查，
   - {{}}
   - 中英文书写
2. 配合aisee
   - 上报
   - 联调
3. 增加分析全部日志文件的选项

# 5.26
1. 支持字段代码块输入
2. daemon场景bug修复
3. 配合场景完善
4. 兼容iwiki书写错误：支持代码块输入
5. iwiki输入格式异常、格式不对 抛出提示

# 5.27
临时文件机制，解压的日志都删掉






后续如果有tag 直接传入tag分析


意图识别，只会识别出一个意图？识别不到还要去用户操作路径？

还需要总结吗？

不需要Prompt 只要tag，不创建场景，设置入口




# 待解决问题
1. iwiki请求优化
   1. 是否每次都要加载场景列表

