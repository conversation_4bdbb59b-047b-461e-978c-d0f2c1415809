import json
from dataclasses import dataclass, asdict, field
from typing import Optional, List, Tuple, Dict, Iterator

from logs.logger import app_logger

FILE_PATH = "scenes.jsonl"

@dataclass
class SceneData:
    """
    场景数据结构，支持后续字段扩展
    """
    scene: str  # 场景名称，作为唯一标识
    desc: Optional[str] = None  # 描述信息
    tag: Optional[List[str]] = field(default_factory=list)  # 标签列表
    prompt: Optional[str] = None  # 提示信息
    index_keys: Optional[set] = field(default_factory=set)  # 索引键值对
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)  # 保存日志
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)  # 删除日志
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)  # 拆分日志
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)  # 去重日志
    extract_fields: Optional[Dict] = field(default_factory=dict) # 格式化日志指定内容（转化json）
    is_fuzzy_match_tag: bool = False  # 是否模糊匹配标签
    is_analyze_daemon: bool = False  # 是否启动分析守护进程
    is_deleted: bool = False  # 逻辑删除标记
    version: int = 0  # 版本号，自动递增


class SceneManager:
    """
    场景管理器，负责管理SceneData的增删改查及版本控制
    数据以JSON Lines格式存储，每行一个SceneData的JSON序列化
    """

    def __init__(self):
        """
        初始化SceneManager，加载文件中的数据
        """
        self.filepath = FILE_PATH
        self._data: List[SceneData] = []
        self._load()

    def _load(self):
        """
        从文件加载所有数据到内存
        兼容旧数据（无version字段默认为0）
        """
        self._data.clear()
        try:
            with open(self.filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    obj = json.loads(line)
                    if 'version' not in obj:
                        obj['version'] = 0
                    scene_data = SceneData(**obj)
                    self._data.append(scene_data)
        except FileNotFoundError:
            # 文件不存在，初始化为空
            pass

    def _save_all(self):
        """
        将内存中所有数据写回文件，覆盖写入
        """
        with open(self.filepath, 'w', encoding='utf-8') as f:
            for scene_data in self._data:
                json_line = json.dumps(asdict(scene_data), ensure_ascii=False)
                f.write(json_line + '\n')

    def add_scene(self, scene_data: SceneData):
        """
        新增一个SceneData，自动版本迭代
        :param scene_data: 待添加的SceneData对象
        """
        # 获取该scene当前最大版本号，+1作为新版本号
        max_version = max(
            (d.version for d in self._data if d.scene == scene_data.scene), default=0
        )
        scene_data.version = max_version + 1
        self._data.append(scene_data)

        # 追加写入文件，避免每次都重写
        with open(self.filepath, 'a', encoding='utf-8') as f:
            json_line = json.dumps(asdict(scene_data), ensure_ascii=False)
            f.write(json_line + '\n')

    def logical_delete(
        self,
        scene: str,
        version: Optional[int] = None,
        all_versions: bool = False
    ):
        """
        逻辑删除操作：
        - all_versions=True：删除该scene所有版本
        - version指定：删除该scene指定版本
        - 否则删除该scene最新版本
        :param scene: 场景名称
        :param version: 指定版本号，默认None
        :param all_versions: 是否删除所有版本，默认False
        """
        changed = False
        if all_versions:
            # 删除该scene所有未删除版本
            for d in self._data:
                if d.scene == scene and not d.is_deleted:
                    d.is_deleted = True
                    changed = True
        elif version is not None:
            # 删除指定版本
            for d in self._data:
                if d.scene == scene and d.version == version and not d.is_deleted:
                    d.is_deleted = True
                    changed = True
                    break
        else:
            # 删除最新版本
            latest = self.get_latest_version(scene)
            if latest and not latest.is_deleted:
                latest.is_deleted = True
                changed = True

        if changed:
            self._save_all()

    def get_all_latest(self) -> List[SceneData]:
        """
        获取所有未删除的最新版本SceneData列表
        :return: SceneData列表
        """
        latest_map = {}
        for d in self._data:
            if d.is_deleted:
                continue
            # 只保留每个scene版本号最大的
            if d.scene not in latest_map or d.version > latest_map[d.scene].version:
                latest_map[d.scene] = d
        return list(latest_map.values())

    def get_versions(self, scene: str) -> List[SceneData]:
        """
        获取指定scene的所有版本，按版本升序排序
        :param scene: 场景名称
        :return: SceneData列表
        """
        versions = [d for d in self._data if d.scene == scene]
        versions.sort(key=lambda x: x.version)
        return versions

    def get_latest_version(self, scene: str) -> Optional[SceneData]:
        """
        获取指定scene的最新版本
        :param scene: 场景名称
        :return: SceneData或None
        """
        versions = self.get_versions(scene)
        if not versions:
            return None
        return max(versions, key=lambda x: x.version)

    def get_version(self, scene: str, version: int) -> Optional[SceneData]:
        """
        获取指定scene指定版本
        :param scene: 场景名称
        :param version: 版本号
        :return: SceneData或None
        """
        for d in self._data:
            if d.scene == scene and d.version == version:
                return d
        return None

    def iterate_scenes(self) -> Iterator[SceneData]:
        """
        迭代所有SceneData
        :return: 迭代器
        """
        yield from self._data

    def __repr__(self):
        return f"<SceneManager file={self.filepath} scenes={len(set(d.scene for d in self._data))}>"

# 全局单例实例
scene_manager = SceneManager()


if __name__ == "__main__":
    # 使用示例

    # 初始化管理器，文件不存在时自动创建
    manager = scene_manager

    # 新增场景数据
    scene1_v1 = SceneData(scene="scene1", desc="第一版描述", prompt="prompt1")
    manager.add_scene(scene1_v1)

    scene1_v2 = SceneData(scene="scene1", desc="第二版描述", prompt="prompt2")
    manager.add_scene(scene1_v2)

    scene2_v1 = SceneData(scene="scene2", desc="场景2描述", prompt="prompt3")
    manager.add_scene(scene2_v1)

    print("=== 所有未删除的最新版本 ===")
    for s in manager.get_all_latest():
        print(f"scene={s.scene}, version={s.version}, desc={s.desc}, is_deleted={s.is_deleted}")

    print("\n=== scene1所有版本 ===")
    for s in manager.get_versions("scene1"):
        print(f"version={s.version}, desc={s.desc}, is_deleted={s.is_deleted}")

    print("\n=== scene1最新版本 ===")
    latest = manager.get_latest_version("scene1")
    if latest:
        print(f"version={latest.version}, desc={latest.desc}, is_deleted={latest.is_deleted}")

    # 逻辑删除操作示例
    print("\n=== 删除scene1最新版本 ===")
    manager.logical_delete("scene1")
    latest_after_delete = manager.get_latest_version("scene1")
    print(f"scene1最新版本删除后: version={latest_after_delete.version}, is_deleted={latest_after_delete.is_deleted}")

    print("\n=== 删除scene1指定版本1 ===")
    manager.logical_delete("scene1", version=1)
    versions_after_delete = manager.get_versions("scene1")
    for s in versions_after_delete:
        print(f"version={s.version}, is_deleted={s.is_deleted}")

    print("\n=== 删除scene2所有版本 ===")
    manager.logical_delete("scene2", all_versions=True)
    latest_scene2 = manager.get_latest_version("scene2")
    print(f"scene2最新版本: {latest_scene2}, (应为None或已删除)")

    print("\n=== 删除后所有未删除的最新版本 ===")
    for s in manager.get_all_latest():
        print(f"scene={s.scene}, version={s.version}, is_deleted={s.is_deleted}")
