from enum import Enum, unique


@unique
class ErrorCode(Enum):
    INVALID_AGE = 1001
    EMPTY_USERNAME = 1002
    # 你可以继续添加更多错误码


# 错误码对应的默认错误信息
ERROR_MESSAGES = {
    ErrorCode.INVALID_AGE: "年龄必须是非负整数",
    ErrorCode.EMPTY_USERNAME: "用户名不能为空",
}


class DetailedValueError(ValueError):
    def __init__(self, code: ErrorCode, message=None, context=None):
        if message is None:
            message = ERROR_MESSAGES.get(code, "未知错误")
        super().__init__(message)
        self.code = code
        self.context = context or {}

    def __str__(self):
        base = super().__str__()
        return f"{base} (code={self.code.name}[{self.code.value}], context={self.context})"


def raise_value_error(code: ErrorCode, context=None):
    raise DetailedValueError(code, context=context)


class User:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def set_age(self, age):
        if not isinstance(age, int) or age < 0:
            raise_value_error(ErrorCode.INVALID_AGE, context={"param": "age", "value": age})
        self.age = age

    def greet(self):
        if not self.name:
            raise_value_error(ErrorCode.EMPTY_USERNAME, context={"param": "name", "value": self.name})
        return f"你好，{self.name}！"


if __name__ == "__main__":
    user = User("Alice", 30)

    try:
        user.set_age(-5)
    except DetailedValueError as e:
        print("捕获到详细错误:")
        print("错误信息:", e)
        print("错误码:", e.code)
        print("错误码名称:", e.code.name)
        print("错误码值:", e.code.value)
        print("上下文:", e.context)
        if e.code == ErrorCode.INVALID_AGE:
            print("这是年龄无效错误，做相应处理")

    try:
        user.name = ""
        print(user.greet())
    except DetailedValueError as e:
        print("捕获到详细错误:")
        print("错误信息:", e)
        print("错误码:", e.code)
        print("错误码名称:", e.code.name)
        print("错误码值:", e.code.value)
        print("上下文:", e.context)
        if e.code == ErrorCode.EMPTY_USERNAME:
            print("这是用户名为空错误，做相应处理")