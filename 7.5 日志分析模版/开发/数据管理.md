现在有一个dataclass是SceneData（这个SceneData后续可能还会增加字段），想要写一个SceneManager类，外部传入SceneData，来管理SceneData。要求代码优美，用python实现
1. 保存数据到文件，每条一行 JSON
2. 从文件加载数据
3. 增加数据到文件
4. 将文件中的数据逻辑删除
5. 获取所有未删除的最新版本 SceneData
6. 查看指定 scene 的所有版本，按版本升序
7. 查看指定 scene 的最新版本
8. 查看置顶 scene 的指定版本
9. 版本迭代
@dataclass
class SceneData:
    scene: str
    desc: Optional[str] = None
    tag: Optional[List[str]] = field(default_factory=list)
    prompt: Optional[str]
    index_keys: Dict = field(default_factory=dict)
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list)
    is_fuzzy_match_tag: bool = False
    is_deleted: bool = False  # 逻辑删除标记



删除优化一下，默认删除最新版本，支持删除指定版本，也支持删除一整个scene


### 场景
下载
### 过滤tag
['YybLotteryViewModel', 'YybActCommonReceiveManager', 'PageReporter_beaconReport']
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
### prompt
你是一名Android日志分析专家，擅长逐行阅读日志，通过日志轨迹还原用户操作流程，精准分析日志。请理解[用户问题]，对提供的[用户日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
我的奖品发货失败

# [知识库]
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`

# [分析流程]
1. 逐行扫描[用户日志]，定位包含关键字“actBtnConfigPropertyActBtnConfig”的日志行，从中提取“orderStatus”字段值。  
2. 根据orderStatus判断发货状态：  
   - 若orderStatus=3，判定为发货失败，结合[知识库]输出相应提示。
3. 进一步分析同一订单中orderStatus的变化轨迹：  
   - 若存在orderStatus由1变更为3，视为正常发货失败流程。  
   - 若orderStatus为1且未变为3，视为发货成功。  
4. 分析过程中所有结论均基于日志和知识库，不得无依据推断。
