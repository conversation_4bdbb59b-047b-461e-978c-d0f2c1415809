import re
import textwrap
import json
import ast
from dataclasses import dataclass, fields, MISSING, field
from typing import (
    Any, Dict, List, Optional, Tuple, Type, ClassVar, Set, get_origin, get_args
)


class ParseUserInput:
    """
    解析器类，负责从文本中提取指定格式的区块内容，
    并根据传入的配置类自动映射到对应字段，自动转换字段类型。
    """

    def __init__(self):
        """
        初始化解析器。
        :param section_prefix: 区块标题前缀，默认是 "### "
        """

    def _convert_value(self, value: Optional[str], field_type: Any) -> Any:
        if value is None:
            return None

        origin = get_origin(field_type)
        args = get_args(field_type)
        origin = get_origin(field_type)

        # 先处理Optional[T]，即Union[T, NoneType]
        Union = getattr(__import__('typing'), 'Union', None)
        if origin is Union:
            # 过滤NoneType，取第一个非NoneType类型
            non_none_args = [a for a in args if a is not type(None)]
            if len(non_none_args) == 1:
                return self._convert_value(value, non_none_args[0])
            # 多个类型的Union暂不支持，直接返回字符串
            return value

        # 处理基础类型
        if field_type in {str, int, float, bool}:
            # bool特殊处理
            if field_type is bool:
                # 简单判断字符串是否表示True
                val_lower = value.strip().lower()
                if val_lower in {"true", "1", "yes", "y"}:
                    return True
                elif val_lower in {"false", "0", "no", "n"}:
                    return False
                else:
                    # 不能判断，返回原字符串
                    return value
            try:
                return field_type(value)
            except Exception:
                return value

        # 处理List和Dict等容器类型
        if origin in {list, List}:
            # 期望是列表，尝试用ast.literal_eval解析
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, list):
                    # 如果是List[Tuple[str,str]]，需要进一步转换
                    if args:
                        elem_type = args[0]
                        elem_origin = get_origin(elem_type)
                        elem_args = get_args(elem_type)
                        # 判断是否是Tuple[str,str]
                        if elem_origin in {tuple, Tuple} and len(elem_args) == 2 and all(
                            t == str for t in elem_args
                        ):
                            # 确保列表元素是元组且元素是字符串
                            # 如果元素是列表或其他，转换成tuple
                            new_list = []
                            for e in parsed:
                                if isinstance(e, tuple) and len(e) == 2 and all(isinstance(x, str) for x in e):
                                    new_list.append(e)
                                elif isinstance(e, (list, tuple)) and len(e) == 2:
                                    new_list.append((str(e[0]), str(e[1])))
                                else:
                                    # 不符合格式，跳过或抛错，这里跳过
                                    pass
                            return new_list
                    # 否则直接返回列表
                    return parsed
                else:
                    # 解析结果不是列表，返回原字符串
                    return value
            except Exception:
                # 解析失败，返回原字符串
                return value

        if origin in {dict, Dict}:
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, dict):
                    return parsed
                else:
                    return value
            except Exception:
                return value
        
        if origin in {set, Set}:
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, set):
                    # 如果有元素类型限制，可以在这里做进一步转换
                    if args:
                        elem_type = args[0]
                        # 简单示例：尝试转换每个元素
                        new_set = set()
                        for e in parsed:
                            try:
                                new_set.add(elem_type(e))
                            except Exception:
                                new_set.add(e)
                        return new_set
                    return parsed
                else:
                    return value
            except Exception:
                return value

        # 其他类型暂时不处理，直接返回字符串
        return value

    def parse(self, text: str, config_cls: Type, section_prefix: str = "### "):
        """
        解析文本，返回对应配置类的实例。

        :param text: str: 输入文本
        :param config_cls: 目标配置类，必须包含 _title_map 和 _required_fields 属性
        :return: config_cls 的实例，字段值从文本中提取并转换
        :raises ValueError: 如果必填字段缺失或为空，抛出异常
        """
        text = textwrap.dedent(text)
        title_map = getattr(config_cls, "_title_map", {})
        required_fields = getattr(config_cls, "_required_fields", set())

        if not title_map:
            return config_cls()

        # 匹配所有区块标题
        pattern = re.compile(rf"^\s*{re.escape(section_prefix)}(.+)$", re.MULTILINE)
        matches = list(pattern.finditer(text))

        all_sections = {}
        for i, match in enumerate(matches):
            title = match.group(1).strip()
            start = match.end()
            end = matches[i + 1].start() if i + 1 < len(matches) else len(text)
            content = text[start:end].strip('\n\r ')
            all_sections[title] = content

        result_kwargs = {}
        missing_required = []

        for f in fields(config_cls):
            if f.name.startswith("_"):
                continue

            title = title_map.get(f.name)
            raw_value = all_sections.get(title)

            if raw_value is None or raw_value.strip() == "":
                # 缺失或空字符串
                if f.name in required_fields:
                    missing_required.append(f.name)
                # 赋默认值或None
                if f.default is not MISSING:
                    result_kwargs[f.name] = f.default
                elif f.default_factory is not MISSING:  # type: ignore
                    result_kwargs[f.name] = f.default_factory()  # type: ignore
                else:
                    result_kwargs[f.name] = None
            else:
                # 转换字段类型
                converted = self._convert_value(raw_value, f.type)
                result_kwargs[f.name] = converted

        if missing_required:
            raise ValueError(f"缺少必填字段: {missing_required}")

        return config_cls(**result_kwargs)
    
    def extract_code_block(self, code_block: str) -> Optional[str]:
        """
        从只包含一个 ```xxx ... ``` 格式代码块的文本中提取代码块内容（...）
        """
        pattern = re.compile(
            r"```[^\n]*\n(.*?)```",
            re.DOTALL
        )
        match = pattern.search(code_block)
        if match:
            content = match.group(1).rstrip('\n')
            return content
        else:
            raise ValueError("未找到代码块")

    def parse_json_lines(self, text: str):
        """
        逐行解析JSON字符串，返回字典列表
        """
        results = []
        for line in text.strip().splitlines():
            try:
                obj = json.loads(line)
                results.append(obj)
            except json.JSONDecodeError as e:
                print(f"解析失败: {e}，内容: {line}")
        return results
    
    def get_field_by_scene(self, items, target_scene, field):
        for item in items:
            if item.get('scene') == target_scene:
                return item.get(field)
        return None



@dataclass
class LogAnalyzeConfig:
    log_link: str # 日志链接
    scene: str # 场景
    is_rewrite_prompt: str # 是否需要改写prompt
    bug_time: Optional[str] = None # bug时间
    query: Optional[str] = None # 用户问题
    filter_tag: Optional[List[str]] = field(default_factory=list) # 过滤tag
    delete_logs:Optional[List[Tuple[str, str]]] = field(default_factory=list)   # 删除的日志行

    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"log_link", "is_rewrite_prompt"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_link": "日志链接",
        "bug_time": "bug时间",
        "query": "用户问题",
        "scene": "场景",
        "is_rewrite_prompt": "是否需要改写prompt",
        "filter_tag": "过滤tag",
        "delete_logs": "删除的日志行"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)


@dataclass
class LogSceneConfig:
    scene: str  # 场景名称
    prompt: Optional[str] # prompt
    desc: Optional[str] = None # 场景描述
    filter_tag: Optional[List[str]] = field(default_factory=list) # 过滤tag
    index_keys: Set[str] = field(default_factory=set) # 索引键
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 保存的日志行
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 删除的日志行
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 分割的日志行
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 去重的日志行
    extract_fields: Optional[Dict] = field(default_factory=dict) # 格式化日志指定内容（转化json）
    is_fuzzy_match_tag: bool = False # 是否模糊匹配tag
    is_analyze_daemon: bool = False # 是否分析守护进程
    is_deleted: bool = False  # 逻辑删除标记

    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({})
    _title_map: ClassVar[Dict[str, str]] = {
        "scene": "场景",
        "desc": "描述",
        "filter_tag": "过滤tag",
        "index_keys": "RAG索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "extract_fields": "日志指定内容转化为json",
        "prompt": "prompt",
        "is_fuzzy_match_tag": "是否模糊匹配tag",
        "is_analyze_daemon": "是否分析守护进程"
    }


    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)


@dataclass
class AiLogConfig:
    log_download_link: str  # 日志下载链接
    scene: str  # 场景名称
    iwiki_url: Optional[str] # 描述日志分析知识的iwiki链接
    filter_tag: List[str] # 过滤tag
    prompt: Optional[str] # prompt
    bug_time: Optional[str] = None # bug时间
    query: Optional[str] = None # 用户问题
    desc: Optional[str] = None # 场景描述
    index_keys: Set[str] = field(default_factory=set) # 索引键
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 保存的日志行
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 删除的日志行
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 分割的日志行
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 去重的日志行
    extract_fields: Optional[Dict] = field(default_factory=dict) # 格式化日志指定内容（转化json）
    is_fuzzy_match_tag: bool = False # 是否模糊匹配tag
    is_analyze_daemon: bool = False # 是否分析守护进程
    
    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"log_download_link"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_download_link": "日志链接",
        "scene": "场景",
        "iwiki_url": "描述日志分析知识的iwiki链接",
        "filter_tag": "过滤tag",
        "prompt": "prompt",
        "bug_time": "bug时间",
        "query": "用户问题",
        "decs": "描述",
        "index_keys": "索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "extract_fields": "日志指定内容转化为json",
        "is_fuzzy_match_tag": "是否模糊匹配tag",
        "is_analyze_daemon": "是否分析守护进程"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)

@dataclass
class EvaluateConfig:
    ticket_id: str  # 工单ID
    evaluate: str = '' # 满意度
    is_send_analyze_process: bool = False # 是否需要查看分析过程

    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"ticket_id"})
    _title_map: ClassVar[Dict[str, str]] = {
        "ticket_id": "工单ID",
        "evaluate": "满意度",
        "is_send_analyze_process": "是否需要查看分析过程"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)




if __name__ == "__main__":
    # 示例文本A，包含必填和可选字段
    text_a = """
    【保存场景prompt】
    ### 用户问题
    下载失败
    ### 日志链接
    https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
    ### 场景
    下载
    ### 是否需要改写prompt
    是
    ### RAG索引键
{'scene', 'retCode'}
    ###  过滤tag
['PageReporter_beaconReport', 'RuntimeCrash',
                          'ReceivingRewardViewModel', 'YybActCommonReceiveManager',
                          'YybLotteryView','YybLotteryViewModel']
    """

    # 示例文本B，另一种格式
    text_b = """
    ### 场景
    下载
    ### 是否需要改写prompt
    是
    """

    # 解析文本A
    parser_a = ParseUserInput()
    try:
        config_a = parser_a.parse(text_a, LogSceneConfig)
        print("Config A:", config_a)
        config_a.index_keys
        print("Index keys:", config_a.index_keys, type(config_a.index_keys))
        print("filter_tag:", config_a.filter_tag, type(config_a.filter_tag))
    except ValueError as e:
        print("Config A error:", e)


    # 测试文本A缺少必填字段 log_link
#     text_a_missing = """
#     ### 用户问题
#     下载失败
#     """
#     try:
#         config_a_missing = parser_a.parse(text_a_missing, LogAnalyzeConfig)
#         print("Config A missing:", config_a_missing)
#     except ValueError as e:
#         print("Config A missing error:", e)

#     text_a_missing = """
#     【日志分析-未预设场景】
# ### 日志链接
# https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
# ### bug时间
# 2025-05-13 14:10:00
# ### 用户问题
# 下载失败
# ### 场景
# 下载
# ### 描述
# 下载失败
# ### 过滤tag
#  ["tag1", "tag2"]
# ### 是否模糊匹配tag
# True
# ### prompt
# 你是一名资深Android日志分析专家，擅长逐行阅读日志，通过日志轨迹精准还原用户操作流程，深入分析日志信息。请仔细理解[用户问题]，结合提供的[用户日志]，运用[知识库]中的相关知识和你的专业经验，严格按照[分析流程]进行分析。分析过程中，所有结论必须基于[用户日志]和[知识库]，不得进行无依据的推测或编造。最终请严格按照[格式说明]要求，完整且规范地输出分析结果。

# # [知识库]  
# 1. 发货失败相关知识点：  
#    - orderStatus字段表示物品发货状态，取值含义如下：  
#      * 1：已发货  
#      * 2：未发货  
#      * 3：发货失败  
#     """
#     try:
#         ai_log_config = parser_a.parse(text_a_missing, AiLogConfig)
#         print("ai_log_config:", ai_log_config.to_json())
#         print(f'type : {type(ai_log_config.is_fuzzy_match_tag)}')
#     except ValueError as e:
#         print("ai_log_config missing error:", e)
    
