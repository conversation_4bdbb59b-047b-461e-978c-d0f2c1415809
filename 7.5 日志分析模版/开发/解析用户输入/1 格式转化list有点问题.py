import re
import textwrap
import json
import ast
from dataclasses import dataclass, fields, MISSING, asdict, field
from typing import Optional, Type, Dict, Set, ClassVar, Any, List, Tuple



@dataclass
class ParseConfigB:
    scenario: Optional[str] = None
    is_rewrite_prompt: Optional[str] = None

    _required_fields: ClassVar[Set[str]] = frozenset({"scenario"})
    _title_map: ClassVar[Dict[str, str]] = {
        "scenario": "场景",
        "is_rewrite_prompt": "是否需要改写prompt"
    }


class ParseUserInput:
    """
    解析器类，负责从文本中提取指定格式的区块内容，
    并根据传入的配置类自动映射到对应字段。
    """

    def __init__(self, text: str, section_prefix: str = "### "):
        """
        初始化解析器。

        :param text: 待解析的多段文本，区块以 section_prefix 开头
        :param section_prefix: 区块标题前缀，默认是 "### "
        """
        # 使用 textwrap.dedent 去除文本公共缩进，方便多行字符串处理
        self.text = textwrap.dedent(text)
        self.section_prefix = section_prefix

    def parse(self, config_cls: Type):
        """
        解析文本，返回对应配置类的实例。

        :param config_cls: 目标配置类，必须包含 _title_map 和 _required_fields 属性
        :return: config_cls 的实例，字段值从文本中提取
        :raises ValueError: 如果必填字段缺失或为空，抛出异常
        """
        # 从配置类获取字段名到标题的映射字典
        title_map = getattr(config_cls, "_title_map", {})
        # 从配置类获取必填字段集合
        required_fields = getattr(config_cls, "_required_fields", set())

        # 如果映射字典为空，直接返回空实例
        if not title_map:
            return config_cls()

        # 构造正则表达式，匹配所有以 section_prefix 开头的标题行
        # ^\s*### (.+)$  例如匹配 "### 用户问题"
        pattern = re.compile(rf"^\s*{re.escape(self.section_prefix)}(.+)$", re.MULTILINE)
        matches = list(pattern.finditer(self.text))

        # 提取所有区块内容，存入字典 all_sections，key 是标题，value 是内容
        all_sections = {}
        for i, match in enumerate(matches):
            title = match.group(1).strip()  # 标题文本
            start = match.end()              # 内容起始位置（标题行结束后）
            # 内容结束位置是下一个标题行的起始，或者文本末尾
            end = matches[i+1].start() if i+1 < len(matches) else len(self.text)
            # 提取内容，去除首尾空白符
            content = self.text[start:end].strip('\n\r ')
            all_sections[title] = content

        result_kwargs = {}    # 用于存放最终传给 dataclass 构造函数的字段值
        missing_required = [] # 记录缺失的必填字段名

        # 遍历配置类的所有字段
        for f in fields(config_cls):
            # 跳过内部字段（以下划线开头的属性，如 _required_fields）
            if f.name.startswith("_"):
                continue

            # 根据字段名找到对应的标题
            title = title_map.get(f.name)
            if title is None:
                # 如果映射中没有该字段对应的标题，赋默认值或 None
                if f.default is not MISSING:
                    result_kwargs[f.name] = f.default
                elif f.default_factory is not MISSING:  # type: ignore
                    result_kwargs[f.name] = f.default_factory()  # type: ignore
                else:
                    result_kwargs[f.name] = None
            else:
                # 从提取的区块中获取对应内容
                value = all_sections.get(title)
                # 如果是必填字段，且内容缺失或为空字符串，记录错误
                if f.name in required_fields:
                    if value is None or value.strip() == "":
                        missing_required.append(f.name)
                # 赋值给结果字典
                result_kwargs[f.name] = value

        # 如果有必填字段缺失，抛出异常
        if missing_required:
            raise ValueError(f"Missing required fields in text: {missing_required}")

        # 返回配置类实例，字段值为解析结果
        return config_cls(**result_kwargs)


@dataclass
class LogAnalyzeConfig:
    log_link: str # 日志链接
    scene: str # 场景
    is_rewrite_prompt: str # 是否需要改写prompt
    bug_time: Optional[str] = None # bug时间
    user_question: Optional[str] = None # 用户问题
    filter_tag: Optional[List[str]] = field(default_factory=list) # 过滤tag
    delete_logs:Optional[List[Tuple[str, str]]] = field(default_factory=list)   # 删除的日志行

    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"log_link", "scene", "is_rewrite_prompt"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_link": "日志链接",
        "bug_time": "bug时间",
        "user_question": "用户问题",
        "scene": "场景",
        "is_rewrite_prompt": "是否需要改写prompt",
        "filter_tag": "过滤tag",
        "delete_logs": "删除的日志行"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典，
        对可能是 Python 表达式的字符串字段尝试用 ast.literal_eval 转换，
        转换失败则保留原字符串。
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            if isinstance(value, str):
                # 尝试把字符串解析成 Python 对象（列表、元组、字典等）
                try:
                    parsed = ast.literal_eval(value)
                    result[f.name] = parsed
                except Exception:
                    # 解析失败，保留原字符串
                    result[f.name] = value
            else:
                # 不是字符串，直接赋值（可能是 None 或已经是对象）
                result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)



@dataclass
class LogSceneConfig:
    scene: str  # 场景名称
    prompt: Optional[str] # prompt
    desc: Optional[str] = None # 场景描述
    filter_tag: Optional[List[str]] = field(default_factory=list) # 过滤tag
    index_keys: Optional[Dict] = field(default_factory=dict) # 索引键
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 保存的日志行
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 删除的日志行
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 分割的日志行
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 去重的日志行
    is_fuzzy_match_tag: bool = False # 是否模糊匹配tag
    is_deleted: bool = False  # 逻辑删除标记

    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"scene", "filter_tag", "prompt"})
    _title_map: ClassVar[Dict[str, str]] = {
        "scene": "场景",
        "decs": "描述",
        "filter_tag": "过滤tag",
        "index_keys": "索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "prompt": "prompt"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典，
        对可能是 Python 表达式的字符串字段尝试用 ast.literal_eval 转换，
        转换失败则保留原字符串。
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            if isinstance(value, str):
                # 尝试把字符串解析成 Python 对象（列表、元组、字典等）
                try:
                    parsed = ast.literal_eval(value)
                    result[f.name] = parsed
                except Exception:
                    # 解析失败，保留原字符串
                    result[f.name] = value
            else:
                # 不是字符串，直接赋值（可能是 None 或已经是对象）
                result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)

@dataclass
class AiLogConfig:
    log_download_link: str  # 日志下载链接
    scene: str  # 场景名称
    filter_tag: List[str] # 过滤tag
    prompt: Optional[str] # prompt
    bug_time: Optional[str] = None # bug时间
    query: Optional[str] = None # 用户问题
    desc: Optional[str] = None # 场景描述
    index_keys: Optional[Dict] = field(default_factory=dict) # 索引键
    save_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 保存的日志行
    delete_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 删除的日志行
    split_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 分割的日志行
    dedup_logs: Optional[List[Tuple[str, str]]] = field(default_factory=list) # 去重的日志行
    is_fuzzy_match_tag: bool = False # 是否模糊匹配tag
    is_analyze_daemon: bool = False # 是否分析守护进程
    
    # 使用 ClassVar 标注，表示类变量，不作为实例字段
    _required_fields: ClassVar[Set[str]] = frozenset({"log_download_link", "scene", "filter_tag", "prompt"})
    _title_map: ClassVar[Dict[str, str]] = {
        "log_download_link": "日志链接",
        "scene": "场景",
        "filter_tag": "过滤tag",
        "prompt": "prompt",
        "bug_time": "bug时间",
        "query": "用户问题",
        "decs": "描述",
        "index_keys": "索引键",
        "save_logs": "保存的日志行",
        "delete_logs": "删除的日志行",
        "split_logs": "分割的日志行",
        "dedup_logs": "去重的日志行",
        "is_fuzzy_match_tag": "是否模糊匹配tag",
        "is_analyze_daemon": "是否分析守护进程"
    }

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典，
        对可能是 Python 表达式的字符串字段尝试用 ast.literal_eval 转换，
        转换失败则保留原字符串。
        """
        result = {}
        for f in fields(self):
            if f.name.startswith("_"):
                continue
            value = getattr(self, f.name)
            if isinstance(value, str):
                # 尝试把字符串解析成 Python 对象（列表、元组、字典等）
                try:
                    parsed = ast.literal_eval(value)
                    result[f.name] = parsed
                except Exception:
                    # 解析失败，保留原字符串
                    result[f.name] = value
            else:
                # 不是字符串，直接赋值（可能是 None 或已经是对象）
                result[f.name] = value
        return result

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)



if __name__ == "__main__":
    # 示例文本A，包含必填和可选字段
    text_a = """
    【保存场景prompt】
    ### 用户问题
    下载失败
    ### 日志链接
    https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
    ### 场景
    下载
    ### 是否需要改写prompt
    是
    """

    # 示例文本B，另一种格式
    text_b = """
    ### 场景
    下载
    ### 是否需要改写prompt
    是
    """

    # 解析文本A
    parser_a = ParseUserInput(text_a)
    try:
        config_a = parser_a.parse(LogAnalyzeConfig)
        print("Config A:", config_a)
    except ValueError as e:
        print("Config A error:", e)

    # 解析文本B
    parser_b = ParseUserInput(text_b)
    try:
        config_b = parser_b.parse(ParseConfigB)
        print("Config B:", config_b)
        scenario = config_b.scenario
        print("scenario:", scenario)
    except ValueError as e:
        print("Config B error:", e)

    # 测试文本A缺少必填字段 log_link
    text_a_missing = """
    ### 用户问题
    下载失败
    """
    parser_a_missing = ParseUserInput(text_a_missing)
    try:
        config_a_missing = parser_a_missing.parse(LogAnalyzeConfig)
        print("Config A missing:", config_a_missing)
    except ValueError as e:
        print("Config A missing error:", e)

    # AiLogConfig
    text_a_missing = """
    【日志分析-未预设场景】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-05-13 14:10:00
### 用户问题
下载失败
### 场景
下载
### 描述
下载失败
### 过滤tag
 ["tag1", "tag2"]
### prompt
你是一名资深Android日志分析专家，擅长逐行阅读日志，通过日志轨迹精准还原用户操作流程，深入分析日志信息。请仔细理解[用户问题]，结合提供的[用户日志]，运用[知识库]中的相关知识和你的专业经验，严格按照[分析流程]进行分析。分析过程中，所有结论必须基于[用户日志]和[知识库]，不得进行无依据的推测或编造。最终请严格按照[格式说明]要求，完整且规范地输出分析结果。

# [知识库]  
1. 发货失败相关知识点：  
   - orderStatus字段表示物品发货状态，取值含义如下：  
     * 1：已发货  
     * 2：未发货  
     * 3：发货失败  
   - 同一订单中，orderStatus从1变更为3属于正常的发货失败流程。  
   - 针对orderStatus=3的发货失败情况，可能原因包括黑产、礼包限量不足、重复领取（同一QQ号重复领取Q币）等。  
   - 处理建议：  
     * 建议客户端同学通过物品补发平台链接确认奖品发放具体信息。  
     * 物品补发平台链接：`https://act.woa.com/xy/app/prod/hdzt/compensator?appid=45`
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
    """
    parser_a_missing = ParseUserInput(text_a_missing)
    try:
        ai_log_config = parser_a_missing.parse(AiLogConfig)
        print("ai_log_config:", ai_log_config.to_json())
    except ValueError as e:
        print("ai_log_config missing error:", e)
