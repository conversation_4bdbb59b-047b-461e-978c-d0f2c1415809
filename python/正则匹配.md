好的！下面我给你详细讲解 Python 中的正则表达式（regex）匹配的基本用法，并配合示例说明。

---

# Python 正则表达式详解

Python 通过内置模块 `re` 来支持正则表达式操作。正则表达式是一种用来描述字符串匹配规则的工具，常用于查找、替换、验证字符串等。

---

## 1. 导入模块

```python
import re
```

---

## 2. 常用函数

| 函数 | 作用 | 返回值 |
|-------|-------|--------|
| `re.match(pattern, string)` | 从字符串开头匹配正则表达式 | 匹配对象或 None |
| `re.search(pattern, string)` | 在字符串任意位置搜索匹配 | 匹配对象或 None |
| `re.findall(pattern, string)` | 找出所有匹配的子串，返回列表 | 列表 |
| `re.finditer(pattern, string)` | 找出所有匹配，返回迭代器 | 迭代器，元素是匹配对象 |
| `re.sub(pattern, repl, string)` | 替换匹配的子串 | 替换后的字符串 |
| `re.split(pattern, string)` | 按匹配分割字符串 | 列表 |

---

## 3. 匹配对象（Match Object）

匹配成功后返回一个 Match 对象，可以调用：

- `.group()`：返回匹配的字符串
- `.start()`：匹配子串的起始位置
- `.end()`：匹配子串的结束位置
- `.span()`：返回(start, end)元组

---

## 4. 正则表达式基础语法

| 符号 | 说明 | 例子 |
|-------|-------|-------|
| `.` | 匹配任意单个字符（除换行符） | `a.c` 匹配 `abc`, `a1c` |
| `^` | 匹配字符串开头 | `^Hello` 匹配以 Hello 开头的字符串 |
| `$` | 匹配字符串结尾 | `world$` 匹配以 world 结尾的字符串 |
| `*` | 匹配前面字符0次或多次 | `ab*c` 匹配 `ac`, `abc`, `abbc` |
| `+` | 匹配前面字符1次或多次 | `ab+c` 匹配 `abc`, `abbc`，不匹配 `ac` |
| `?` | 匹配前面字符0次或1次 | `ab?c` 匹配 `ac` 或 `abc` |
| `{m,n}` | 匹配前面字符至少m次，至多n次 | `a{2,4}` 匹配 `aa`, `aaa`, `aaaa` |
| `[]` | 字符集，匹配其中任意一个字符 | `[abc]` 匹配 `a`、`b` 或 `c` |
| `|` | 或操作 | `a|b` 匹配 `a` 或 `b` |
| `()` | 分组 | `(abc)+` 匹配 `abc`, `abcabc` |

---

## 5. 常用转义字符

| 转义字符 | 说明 |
|----------|-------|
| `\d` | 匹配数字，等价于 `[0-9]` |
| `\D` | 匹配非数字 |
| `\w` | 匹配字母数字下划线，等价于 `[a-zA-Z0-9_]` |
| `\W` | 匹配非字母数字下划线 |
| `\s` | 匹配空白字符（空格、制表符等） |
| `\S` | 匹配非空白字符 |

---

## 6. 示例讲解

### 示例1：简单匹配

```python
import re

text = "Hello, world!"

# match 从开头匹配
m = re.match(r"Hello", text)
if m:
    print("match:", m.group())  # 输出 Hello

# search 任意位置匹配
s = re.search(r"world", text)
if s:
    print("search:", s.group())  # 输出 world
```

---

### 示例2：查找所有数字

```python
text = "My phone is ************ and office is ************."

numbers = re.findall(r"\d+", text)
print(numbers)  # ['123', '456', '7890', '987', '654', '3210']
```

---

### 示例3：分组提取

```python
text = "Name: Alice, Age: 30"

pattern = r"Name: (\w+), Age: (\d+)"
m = re.search(pattern, text)
if m:
    print("Name:", m.group(1))  # Alice
    print("Age:", m.group(2))   # 30
```

---

### 示例4：替换字符串

```python
text = "Hello 123, this is 456."

# 将所有数字替换成 #
new_text = re.sub(r"\d", "#", text)
print(new_text)  # Hello ###, this is ###.
```

---

### 示例5：复杂匹配-邮箱验证

```python
email = "<EMAIL>"

pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"

if re.match(pattern, email):
    print("Valid email")
else:
    print("Invalid email")
```

---

## 7. 常用标志（flags）

| 标志 | 说明 |
|-------|-------|
| `re.I` 或 `re.IGNORECASE` | 忽略大小写 |
| `re.M` 或 `re.MULTILINE` | 多行模式，`^` 和 `$` 匹配每行开头结尾 |
| `re.S` 或 `re.DOTALL` | 让 `.` 匹配换行符 |
| `re.X` 或 `re.VERBOSE` | 允许正则表达式中写注释和换行，增强可读性 |

---

## 8. 小结

- 正则表达式是强大的文本匹配工具。
- Python 的 `re` 模块提供了丰富的函数和方法。
- 先掌握基础语法，再结合实际需求写正则。
- 使用在线工具（如 regex101.com）调试正则表达式很方便。

---

如果你需要，我可以帮你写具体的正则表达式，或者帮你分析某个正则表达式的含义。欢迎继续提问！






好的，下面详细讲解一下 Python `re` 模块中 `pattern.search()` 和 `pattern.fullmatch()` 的区别和用法。

---

# `pattern.search()` 和 `pattern.fullmatch()` 区别

这两个方法都是 `re.Pattern` 对象（即编译后的正则表达式）的方法，用于匹配字符串，但匹配规则不同。

---

## 1. `pattern.search(string)`

- **作用**：在字符串中搜索第一个匹配正则表达式的子串。
- **匹配范围**：字符串的任意位置，只要有符合正则的子串就匹配成功。
- **返回值**：返回一个匹配对象（`Match`），如果没有匹配则返回 `None`。

### 示例

```python
import re

pattern = re.compile(r"\d+")

text = "abc123def456"

m = pattern.search(text)
if m:
    print("search匹配:", m.group())  # 输出: 123
else:
    print("没有匹配")
```

这里 `search` 找到字符串中第一个数字序列 `"123"`，即使它不是从字符串开头开始。

---

## 2. `pattern.fullmatch(string)`

- **作用**：要求整个字符串完全匹配正则表达式。
- **匹配范围**：整个字符串必须完全符合正则表达式，不能多也不能少。
- **返回值**：返回匹配对象（`Match`），否则返回 `None`。

### 示例

```python
import re

pattern = re.compile(r"\d+")

text1 = "123"
text2 = "abc123"

m1 = pattern.fullmatch(text1)
m2 = pattern.fullmatch(text2)

print("fullmatch text1:", m1.group() if m1 else None)  # 输出: 123
print("fullmatch text2:", m2)  # 输出: None，因为 text2 不是完全数字
```

---

## 3. 总结对比

| 方法 | 匹配范围 | 是否要求整个字符串匹配 | 返回值 | 典型用途 |
|-------|----------|------------------------|--------|----------|
| `search` | 字符串任意位置 | 否 | 第一个匹配对象或 None | 查找字符串中是否包含符合规则的子串 |
| `fullmatch` | 整个字符串 | 是 | 匹配对象或 None | 验证字符串是否完全符合规则 |

---

## 4. 额外示例

```python
import re

pattern = re.compile(r"\w+")

text = "hello world"

print(pattern.search(text).group())    # 输出: hello
print(pattern.fullmatch(text))         # 输出: None，因为字符串中有空格，不完全匹配 \w+
print(pattern.fullmatch("hello"))      # 输出: <re.Match object ...>，完全匹配
```

---

## 5. 相关方法补充

- `pattern.match(string)`：从字符串开头匹配，匹配成功返回匹配对象，否则 None。不同于 `search`，`match` 只匹配开头。

---

如果你需要，我可以帮你写示例代码或者帮你理解某个具体的正则表达式。