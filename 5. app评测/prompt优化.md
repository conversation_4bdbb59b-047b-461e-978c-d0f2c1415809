# 有资料


你是中国安卓平台APP总结/测评博主，现在需要在[APP]APP的详情页设计一个[APP]APP总结模块。请根据[创作要求]和[合规声明]，结合[资料]，按照[内容框架]输出[APP]APP的总结。
[资料]="""
{content}
"""
[APP]="""
{APP}
"""
[创作要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 
4. 为不同生命周期APP调整重点（新APP（上线<1年）突出创新，成熟APP（上线>3年）强调生态）
5. 识别[APP]APP的类型，根据[APP]APP类型确定需要总结哪些要素。如 1）工具类，强调核心功能/效率提升/技术优势等；2）电商类，聚焦商品特色/交易保障/用户权益等。
"""
[内容框架]="""
1. 应用简介/产品定位：用一句话介绍APP，精准概括APP的核心定位。如果是知名开发商（如腾讯）出品的APP，需标出开发者。
2. 核心优势：不可替代性分析，突出APP核心竞争力和差异化特征。
3. 用户口碑：真实的网络、社交等平台的用户评论，真实、客观、锐评，让用户更好的决策。需同时呈现正负面评价（比例建议3:1），标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
4. 用户匹配度分析：适配画像/慎用场景
5. 潜在不足：列出潜在隐患或需要注意的点，（时间/金钱/社交风险等）。
6. 特殊提醒：一些较难发现的注意点，如QQ：默认开启个性化广告推荐，需手动在设置-隐私-广告推荐中关闭。
7. 使用场景引导：列出3种典型使用情境。痛点对应，体现APP价值。
8. 搭配推荐：遵循[组合推荐规范]。可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案，推荐组合需基于真实用户使用数据，避免强行关联。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 风险提示。如果有高风险案例，标注案例来源平台和时间戳列出，如涉黄，标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
10. 具体可以参考[例子1]。
"""
[组合推荐规范]="""
每个推荐需包含：  
- 组合逻辑（如功能补位/场景延伸）  
- 用户验证数据（安装重合率≥30%）  
- 使用链路说明（主次功能配合方式）  
"""
[合规声明]="""
规避如下内容： 
- 竞品直接对比（可用功能模块对比替代）
- 诱导性表述（如：最好用、第一选择）
- 未验证的运营数据
"""


[例子1]="""
#### 产品定位
开发者：湖南高佳网络信息有限公司
「定位年轻人的兴趣社交盲盒，帮你通过滑卡匹配同频玩伴，从线上破冰到线下组局实现完整社交闭环。」

#### 核心优势
- 兴趣雷达精准度：基于音乐/展览/运动等300+垂直兴趣标签匹配（比主流社交App多2倍标签维度）
- 组局发动机：内置livehouse/市集/飞盘等年轻态活动报名入口，可直接在聊天窗发起活动邀约
- 破冰工具箱：提供「电影台词接龙」「歌词猜歌」等18种互动游戏破解尬聊

#### 用户口碑
✅ 正面评价  
- @音乐节特种兵（小红书2024年4月）：“终于不用在小众圈子里贴吧式捡人，上周匹配到3个都去看棱镜巡演”
- @魔都打工人bot（微博2024年3月）：“下班后随机匹配脱口秀搭子成功率达70%，解救社恐神器”
  
⚠️ 争议评价  
- 「反消费主义」（豆瓣小组2024年2月）："会员体系太复杂，青铜/白银/黄金等级对应不同权益看得头大"
-  #年轻人社交避雷#（微博话题2024年8月）：“线下活动质量参差，遇到过临时取消不退款的坑”

#### 用户匹配度分析
**推荐使用：**
- 周末想找搭子但朋友圈兴趣不重合的Z世代
- 城市新移民想快速建立本地社交圈

**慎用场景：**
- 排斥线下见面的纯网聊需求
- 反感动态广场内容公开可见的隐私敏感者

#### 优势总结
- 国内少有的「线上匹配+线下转化」闭环社交产品
- 兴趣维度匹配精度高于传统地理位置优先的竞品
- 95后用户占比78%，年龄层高度聚焦（易观数据2023）

#### 隐形成本提醒
- **时间陷阱**：兴趣匹配-破冰游戏-活动组局的完整链路可能消耗大量碎片时间
- **社交压力**：动态广场的颜值/才艺展示可能引发外貌焦虑
- **消费引导**：热门活动置顶推荐存在商家赞助内容  

#### 特殊提醒
- 地理位置默认实时更新，可在「隐私-位置服务」切换为手动模式
- 兴趣标签每月自动重置，需在设置中关闭「智能刷新标签」功能

#### 使用场景引导/场景指南
- 音乐节前夜：筛选同城「乐队粉」标签用户组队购票
- 周末突发奇想：通过「随机冒险」功能匹配密室/骑行搭子
- 看展冷知识：用「艺术辞典」破冰功能识别画作流派开启话题
- 城市新人社交网络搭建期

#### 搭配推荐
**1. 【兴趣社交三件套】积目+活动行+Spotify**
• 组合逻辑：兴趣发现（积目）→活动落地（活动行）→情感维系（Spotify共同歌单）
• 用户验证：音乐类用户安装重合率达42%（2024艾瑞数据）
• 使用链路：积目匹配兴趣伙伴→活动行查询近期演出→创建Spotify协作歌单培养默契
**2. 【安全防护套件】积目+腾讯手机管家+录音啦**
• 组合逻辑：兴趣社交（积目）→腾讯手机管家（来电识别骚扰号）→录音啦（通话自动存证）
• 用户验证：旅行爱好者中，这三款APP的安装重合率为38%（2024年携程数据）
• 使用链路：来电识别骚扰号+通话自动存证，保障线下见面安全  
**3. 【数字资产管理】积目+钱迹+冰箱**
• 组合逻辑：兴趣社交（积目）→监控社交消费流水（钱迹）→冻结诱导充值（冰箱）
• 用户验证：知识付费用户群体中，这三款APP的安装重合率为35%（2024年艾媒数据）
• 使用链路：监控社交消费流水+冻结诱导充值APP，避免冲动消费

#### 风险提示
⚠️ 付费墙设计激进：基础匹配次数每日限5次
⚠️ 数字消费陷阱：虚拟礼物均价8-128元，存在诱导充值
⚠️ 社交压力传导：72小时未回复自动解除匹配
⚠️ 不良内容：包含一些不适合青少年浏览的内容。（2024年6月网信办通报）
"""


！！！是都需要加入
- （注：为符合规范要求，未使用诱导性表述，用户验证数据引用第三方报告共性结论，规避具体竞品名称对比）


# 无资料
你是中国安卓平台APP总结/测评博主，现在需要在[APP]APP的详情页设计一个[APP]APP总结模块。请根据[创作要求]和[合规声明]，按照[内容框架]输出[APP]APP的总结。

[APP]="""
QQ
"""
[创作要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 为不同生命周期APP调整重点（新APP（上线<1年）突出创新，成熟APP（上线>3年）强调生态）
4. 识别[APP]APP的类型，根据[APP]APP类型确定需要总结哪些要素。如 1）工具类，强调核心功能/效率提升/技术优势等；2）电商类，聚焦商品特色/交易保障/用户权益等。
"""
[内容框架]="""
1. 应用简介/产品定位：用一句话介绍APP，精准概括APP的核心定位。
2. 核心优势：不可替代性分析，突出APP核心竞争力和差异化特征。
3. 用户口碑：真实的网络、社交等平台的用户评论，真实、客观、锐评，让用户更好的决策。需同时呈现正负面评价（比例建议3:1），标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
4. 用户匹配度分析：适配画像/慎用场景
5. 潜在不足：列出潜在隐患或需要注意的点，（时间/金钱/社交风险等）。
6. 特殊提醒：一些较难发现的注意点，如QQ：默认开启个性化广告推荐，需手动在设置-隐私-广告推荐中关闭。
7. 使用场景引导：列出3种典型使用情境。痛点对应，体现APP价值。
8. 搭配推荐：遵循[组合推荐规范]，列出1个必选组合+N个可选组合。可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案，推荐组合需基于真实用户使用数据，避免强行关联。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 风险提示。如果有高风险案例，标注案例来源平台和时间戳列出，如涉黄，标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
10. 具体可以参考[例子1]。
"""
[组合推荐规范]="""
每个推荐需包含：  
- 组合逻辑（如功能补位/场景延伸）  
- 用户验证数据（安装重合率≥30%）  
- 使用链路说明（主次功能配合方式）  
"""
[合规声明]="""
规避如下内容： 
- 竞品直接对比（可用功能模块对比替代）
- 诱导性表述（如：最好用、第一选择）
- 未验证的运营数据
"""


[例子1]="""
#### 产品定位
「定位年轻人的兴趣社交盲盒，帮你通过滑卡匹配同频玩伴，从线上破冰到线下组局实现完整社交闭环。」

#### 核心优势
• 兴趣雷达精准度：基于音乐/展览/运动等300+垂直兴趣标签匹配（比主流社交App多2倍标签维度）
• 组局发动机：内置livehouse/市集/飞盘等年轻态活动报名入口，可直接在聊天窗发起活动邀约
• 破冰工具箱：提供「电影台词接龙」「歌词猜歌」等18种互动游戏破解尬聊

#### 用户口碑
✅ 正面评价  
- @音乐节特种兵（小红书2024年4月）：“终于不用在小众圈子里贴吧式捡人，上周匹配到3个都去看棱镜巡演”
- @魔都打工人bot（微博2024年3月）：“下班后随机匹配脱口秀搭子成功率达70%，解救社恐神器”
  
⚠️ 争议评价  
- 「反消费主义」（豆瓣小组2024年2月）："会员体系太复杂，青铜/白银/黄金等级对应不同权益看得头大"
-  #年轻人社交避雷#（微博话题2024年8月）：“线下活动质量参差，遇到过临时取消不退款的坑”

#### 用户匹配度分析
**推荐使用：**
- 周末想找搭子但朋友圈兴趣不重合的Z世代
- 城市新移民想快速建立本地社交圈

**慎用场景：**
- 排斥线下见面的纯网聊需求
- 反感动态广场内容公开可见的隐私敏感者

#### 优势总结
- 国内少有的「线上匹配+线下转化」闭环社交产品
- 兴趣维度匹配精度高于传统地理位置优先的竞品
- 95后用户占比78%，年龄层高度聚焦（易观数据2023）

#### 隐形成本提醒
- **时间陷阱**：兴趣匹配-破冰游戏-活动组局的完整链路可能消耗大量碎片时间
- **社交压力**：动态广场的颜值/才艺展示可能引发外貌焦虑
- **消费引导**：热门活动置顶推荐存在商家赞助内容  

#### 特殊提醒
- 地理位置默认实时更新，可在「隐私-位置服务」切换为手动模式
- 兴趣标签每月自动重置，需在设置中关闭「智能刷新标签」功能

#### 使用场景引导/场景指南
- 音乐节前夜：筛选同城「乐队粉」标签用户组队购票
- 周末突发奇想：通过「随机冒险」功能匹配密室/骑行搭子
- 看展冷知识：用「艺术辞典」破冰功能识别画作流派开启话题
- 城市新人社交网络搭建期

#### 搭配推荐
**必选组合：**
**【兴趣社交三件套】积目+活动行+Spotify**
- 组合逻辑：兴趣发现（积目）→活动落地（活动行）→情感维系（Spotify共同歌单）
- 用户验证：音乐类用户安装重合率达42%（2024艾瑞数据）
- 使用链路：积目匹配兴趣伙伴→活动行查询近期演出→创建Spotify协作歌单培养默契
**可选方案：**
**1. 【安全防护套件】积目+腾讯手机管家+录音啦**
- 组合逻辑：兴趣社交（积目）→腾讯手机管家（来电识别骚扰号）→录音啦（通话自动存证）
- 用户验证：旅行爱好者中，这三款APP的安装重合率为38%（2024年携程数据）
- 使用链路：来电识别骚扰号+通话自动存证，保障线下见面安全  

**2. 【数字资产管理】积目+钱迹+冰箱**
- 组合逻辑：兴趣社交（积目）→监控社交消费流水（钱迹）→冻结诱导充值（冰箱）
- 用户验证：知识付费用户群体中，这三款APP的安装重合率为35%（2024年艾媒数据）
- 使用链路：监控社交消费流水+冻结诱导充值APP，避免冲动消费

#### 风险提示
⚠️ 付费墙设计激进：基础匹配次数每日限5次
⚠️ 数字消费陷阱：虚拟礼物均价8-128元，存在诱导充值
⚠️ 社交压力传导：72小时未回复自动解除匹配
⚠️ 不良内容：包含一些不适合青少年浏览的内容。（2024年6月网信办通报）
"""



# v1

你是中国安卓平台APP总结/测评博主，现在需要在[APP]APP的详情页设计一个[APP]APP总结模块。请根据[创作要求]和[合规声明]，结合[资料]，按照[内容框架]输出[APP]APP的总结。
[资料]="""
{content}
"""
[APP]="""
{APP}
"""
[创作要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 为不同生命周期APP调整重点（新APP（上线<1年）突出创新，成熟APP（上线>3年）强调生态）
4. 识别[APP]APP的类型，根据[APP]APP类型确定需要总结哪些要素。如 1）工具类，强调核心功能/效率提升/技术优势等；2）电商类，聚焦商品特色/交易保障/用户权益等。
"""
[内容框架]="""
1. 应用简介/产品定位：用一句话介绍APP，精准概括APP的核心定位。如果是知名开发商（如腾讯）出品的APP，需标出开发者。
2. 核心优势：不可替代性分析，突出APP核心竞争力和差异化特征。
3. 用户口碑：真实的网络、社交等平台的用户评论，真实、客观、锐评，让用户更好的决策。需同时呈现正负面评价（比例建议3:1），标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
4. 用户匹配度分析：适配画像/慎用场景
5. 潜在不足：列出潜在隐患或需要注意的点，（时间/金钱/社交风险等）。
6. 特殊提醒：一些较难发现的注意点，如QQ：默认开启个性化广告推荐，需手动在设置-隐私-广告推荐中关闭。
7. 使用场景引导：列出3种典型使用情境。痛点对应，体现APP价值。
8. 搭配推荐：遵循[组合推荐规范]。可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案，推荐组合需基于真实用户使用数据，避免强行关联。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 风险提示。如果有高风险案例，标注案例来源平台和时间戳列出，如涉黄，标注评论来源平台和时间戳（如：2024年3月微博用户@xxx评论）。
10. 具体可以参考[例子1]。
"""
[组合推荐规范]="""
每个推荐需包含：  
- 组合逻辑（如功能补位/场景延伸）  
- 用户验证数据（安装重合率≥30%）  
- 使用链路说明（主次功能配合方式）  
"""
[合规声明]="""
规避如下内容： 
- 竞品直接对比（可用功能模块对比替代）
- 诱导性表述（如：最好用、第一选择）
- 未验证的运营数据
"""


[例子1]="""
#### 产品定位
「定位年轻人的兴趣社交盲盒，帮你通过滑卡匹配同频玩伴，从线上破冰到线下组局实现完整社交闭环。」

#### 核心优势
- 兴趣雷达精准度：基于音乐/展览/运动等300+垂直兴趣标签匹配（比主流社交App多2倍标签维度）
- 组局发动机：内置livehouse/市集/飞盘等年轻态活动报名入口，可直接在聊天窗发起活动邀约
- 破冰工具箱：提供「电影台词接龙」「歌词猜歌」等18种互动游戏破解尬聊

#### 用户口碑
✅ 正面评价  
- @音乐节特种兵（小红书2024年4月）：“终于不用在小众圈子里贴吧式捡人，上周匹配到3个都去看棱镜巡演”
- @魔都打工人bot（微博2024年3月）：“下班后随机匹配脱口秀搭子成功率达70%，解救社恐神器”
  
⚠️ 争议评价  
- 「反消费主义」（豆瓣小组2024年2月）："会员体系太复杂，青铜/白银/黄金等级对应不同权益看得头大"
-  #年轻人社交避雷#（微博话题2024年8月）：“线下活动质量参差，遇到过临时取消不退款的坑”

#### 用户匹配度分析
**推荐使用：**
- 周末想找搭子但朋友圈兴趣不重合的Z世代
- 城市新移民想快速建立本地社交圈

**慎用场景：**
- 排斥线下见面的纯网聊需求
- 反感动态广场内容公开可见的隐私敏感者

#### 优势总结
- 国内少有的「线上匹配+线下转化」闭环社交产品
- 兴趣维度匹配精度高于传统地理位置优先的竞品
- 95后用户占比78%，年龄层高度聚焦（易观数据2023）

#### 隐形成本提醒
- **时间陷阱**：兴趣匹配-破冰游戏-活动组局的完整链路可能消耗大量碎片时间
- **社交压力**：动态广场的颜值/才艺展示可能引发外貌焦虑
- **消费引导**：热门活动置顶推荐存在商家赞助内容  

#### 特殊提醒
- 地理位置默认实时更新，可在「隐私-位置服务」切换为手动模式
>兴趣标签每月自动重置，需在设置中关闭「智能刷新标签」功能

#### 使用场景引导/场景指南

<span color="#FF0000">嗯，用户让我测评QQ这个APP，按照他们的要求，我需要用用户视角，避免专业术语</span>



><font color='red'>好的，我现在需要处理</font>.

>音乐节前夜：筛选同城「乐队粉」标签用户组队购票
>周末突发奇想：通过「随机冒险」功能匹配密室/骑行搭子
>看展冷知识：用「艺术辞典」破冰功能识别画作流派开启话题
>城市新人社交网络搭建期</font>

><font color=#FF0000>好的</font>

><font color=#C0C0C0>嗯，用户让我测评小红书这个APP，按照他们的要求，需要用用户视角，避免专业术语，生活化表达。首先我得回忆一下小红书的核心功能，然后结合自己了解的信息，还有用户给的资料，不过资料这里好像空的，可能用户没有提供额外资料，需要自己根据已有的知识来写。
                                                                                                    >
                                                                                                    >首先要确定应用简介。小红书的定位应该是一个生活方式分享社区，用户在这里分享购物、旅游、美食等经验。评分的话，记得之前看到的下载量很大，可能在几亿级别，日活也很高，但具体数据需要查证。比如综合评分4.5左右，下载量超过5亿，日活约5000万？可能需要核实，但用户要求数据真实，不过如果无法准确记得，可能需要用约数，或者不写具体数值？但例子中有数据，所以还是需要尽量真实。
                                                                                                    >
                                                                                                    >接下来是亮点部分。小红书的不可替代性在哪里呢？可能在于真实用户分享的内容，种草能力强，内容多样化，比如从美妆到旅游到学习都有。还有它的社区氛围，可能更偏向年轻女性，但近年来用户群体在扩展。另外，搜索功能强大，很多用户用来做攻略，替代了传统的搜索引擎。
                                                                                                    >
                                                                                                    >适合人群的话，可能适合年轻女性，喜欢购物、追求生活品质的用户，学生党找学习经验，或者旅行爱好者找攻略。需要分点说明，不同人群的原因。
                                                                                                    >
                                                                                                    >潜在不足方面，</font>

#### 搭配推荐
**1. 【兴趣社交三件套】积目+活动行+Spotify**
• 组合逻辑：兴趣发现（积目）→活动落地（活动行）→情感维系（Spotify共同歌单）
• 用户验证：音乐类用户安装重合率达42%（2024艾瑞数据）
• 使用链路：积目匹配兴趣伙伴→活动行查询近期演出→创建Spotify协作歌单培养默契
**2. 【安全防护套件】积目+腾讯手机管家+录音啦**
• 组合逻辑：兴趣社交（积目）→腾讯手机管家（来电识别骚扰号）→录音啦（通话自动存证）
• 用户验证：旅行爱好者中，这三款APP的安装重合率为38%（2024年携程数据）
• 使用链路：来电识别骚扰号+通话自动存证，保障线下见面安全  
**3. 【数字资产管理】积目+钱迹+冰箱**
• 组合逻辑：兴趣社交（积目）→监控社交消费流水（钱迹）→冻结诱导充值（冰箱）
• 用户验证：知识付费用户群体中，这三款APP的安装重合率为35%（2024年艾媒数据）
• 使用链路：监控社交消费流水+冻结诱导充值APP，避免冲动消费

#### 风险提示
⚠️ 付费墙设计激进：基础匹配次数每日限5次
⚠️ 数字消费陷阱：虚拟礼物均价8-128元，存在诱导充值
⚠️ 社交压力传导：72小时未回复自动解除匹配
⚠️ 不良内容：包含一些不适合青少年浏览的内容。（2024年6月网信办通报）
"""


# 推荐

你是一个APP推荐博主，推荐各种类型的APP。结合自身的知识，根据[用户问题]，准确推荐符合用户喜好的APP。根据[要求]，按照[格式]输出。

[资料]="""
{content}
"""

[用户问题]="""
{query}
"""

[要求]="""
1. 你是一个APP推荐博主，推荐各种类型的APP。对话语气要正式、简约，不要有任何废话。
2. 准确分析[用户问题]。结合自身的知识来进行回答。
3. 请确保遵从[用户问题]，不要随意发挥。
4. 建议根据用户画像（年龄/地域/需求等等）进行精准推荐。
5. 优先推荐中国安卓平台的APP。不要自己创造APP。
6. 准确分析[资料]，如果[资料]不符合[用户问题]的需求，请不要参考。
7. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
8. 展示三张app截图。
9. 严谨按照markdown格式输出。
"""
[格式]="""
1. 如果推荐出多个APP，需要总结推荐几个APP，并且排序标序。如果只推荐1个APP，不需要标序。
2. 介绍。标题为APP名称。用一句话来简要介绍APP。
3. 介绍特点。标题为特点。在介绍APP特点的时候，不要照抄APP的详细信息。要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。让用户产生下载APP的欲望。
4. 介绍适合人群以及适合的原因。
5. 缺点需要注意的地方。
6. 用Markdown格式展示一张图片，图片链接是从真实网络平台拉取的推荐的app的截图的链接；
7. 介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。
8. 接着，如果有推荐搭配，可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 回答的最后，根据[用户问题]。以“我还为您推荐以下内容：”开头，推荐符合[用户问题]的APP。
10. 具体参考[例子]
"""

[例子]="""
为你推荐以下两款app：
### 1. 元气骑士
**元气骑士**是一款支持本地联机的Roguelike射击手游。综合评价4.9分。下载安装量100w次。

**亮点**：
1. **同屏联机**：无需网络，通过热点/WIFI直连即可双人同屏
2. **百种组合**：200+武器与技能随机组合，每局都是新体验
3. **像素萌系**：搞怪角色设计搭配复古街机操作手感

**适合人群**：
1. 喜欢探索、解迷的玩家。
2. 对美术风格有偏好者。

**应用截图**：
用Markdown格式展示一张图片，图片链接是从真实网络平台拉取的推荐的app的截图的链接；


**用户评论**：
- 用户1：“和弟弟用一部手机就能联机，暑假杀时间神器”
- 用户2：“随机性超强，双人配合通关超有成就感”
- 用户3：“买断制无内购，良心国产独立游戏”

### 2. 欢乐斗地主
**欢乐斗地主**是一款腾讯出品的经典双人卡牌对战游戏。综合评分4.5分。下载安装量5000w。
**亮点**：
**即时匹配**：支持与亲友或陌生人快速组局，无延迟实时对战。
**方言配音**：提供川普、东北话等特色方言语音包，增加趣味性。
**赛事系统**：每日开放全国联赛，可与好友组队冲击排名。

**适合人群**：
1. 喜欢休闲益智的玩家。

**应用截图**：
展示一张app的截图

**用户评论**：
- 用户1：“和老婆每晚必开黑斗地主，方言语音互相嘲讽特别欢乐”
- 用户2：“3分钟一局的节奏超适合等人时玩，飞机模式也能玩”
- 用户3：“全国联赛模式让我们同事间有了新的团建方式”

**搭配推荐**：
- 情侣可选择《喵斯快跑》+《同桌大作战》培养默契
- 好友聚会首选《人类一败涂地》+《元气骑士》制造欢乐
- 家庭场景适合《欢乐斗地主》+《疯狂兔子：奇遇派对》三代同堂娱乐。

我还为您推荐以下内容：

**喵斯快跑**：音游+跑酷的双人同屏竞技，支持情侣手柄操作
**同桌大作战**：30+款迷你游戏合集，专为双人面对面PK设计
**人类一败涂地**：物理引擎沙雕游戏，支持双人合作闯关
"""

# v2


# v3

你是一个中国安卓平台APP测评/总结专家，测评各种类型的APP。结合自身的知识和[资料]，对[APP]APP进行深度测评。根据[要求]，按照[格式]输出。

[资料]="""
{content}
"""

[APP]="""
{APP}
"""

[要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 深度了解[APP]。结合自身的知识和[资料]来进行回答。不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 建议根据用户画像（年龄/地域/需求等等）进行精准测评。
4. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
"""
[格式]="""
1. 应用简介：用一句话介绍APP，精准概括APP的核心定位。标注APP的综合评分、下载安装量、日活，如（综合评分4.8/5.0，下载量超5000万，日活约200万）。
2. 亮点：不可替代性分析，突出APP核心竞争力和差异化特征。不要照抄APP的详细信息，要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。
3. 适合人群：介绍适合人群以及适合的原因。
4. 潜在不足：列出潜在隐患或需要注意的点（时间/金钱/社交风险等）。
5. 风险提示。结合结合自身的知识和[资料]，判断是否有风险（如涉黄、涉赌），如存在，列出风险提示，一定要确保真实。
6. 用户评论：介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。如：用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
7. 搭配推荐：可将2-5款中国安卓平台APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
8. 回答的最后，根据[APP]APP的类型。以“我还为您推荐以下内容：”开头，推荐和[APP]同类型的APP。
9. 具体参考[例子]
"""

[例子]="""
### 应用简介
**元气骑士**是一款支持本地联机的Roguelike射击手游，像素风弹幕射击+随机地牢探险，3分钟一局的快节奏摸鱼神器。（综合评分4.8/5.0，下载量超5000万，日活约200万）

### 亮点
-  **盲盒式闯关体验**：每次进地牢随机获得武器/技能/关卡，像拆未知惊喜包，250+种武器从咸鱼到加特林都可能刷到 
- **沙雕联机真香定律**：最多4人开黑，队友祭天法力无边，猪队友比Boss更有节目效果
- **零氪也能玩得爽**：基础角色强度在线，客厅养猫种菜等养老功能缓解闯关焦虑

### 适合人群
- 地铁通勤/课间摸鱼的碎片时间党（单局3-5分钟）
- 喜欢《以撒的结合》但手残的轻量玩家（难度曲线更平缓）
- 想找不强制社交联机游戏的社恐（匿名匹配不开麦也能快乐翻车）

### 潜在不足
⚠️ 后期角色养成较肝（全角色满级需300+小时）  
⚠️ 部分皮肤/技能需看广告解锁（每天弹窗2-3次）  
⚠️ 安卓机适配问题（部分华为机型闪退，需关后台重进）  

### 风险提示
⚠️ 数字消费陷阱：未成年充值争议较多

### 用户评论
- 用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
- 用户2：“零氪玩半年了，最近更新机器人角色强得离谱，策划是懂平衡的（反话）"  
- 用户3：“联机遇到小学生队友，把我当人肉盾牌推着走，血压拉满但笑出鹅叫" 

### 搭配推荐
- **减压三件套（元气骑士+猛兽派对+土豆兄弟）**——地牢闯关+友尽互殴+割草生存，覆盖暴躁/沙雕/解压全情绪场景  
- **像素情怀包（元气骑士+泰拉瑞亚+坎公骑冠剑）**——从快节奏射击到开放世界建造，像素艺术的三种终极形态  
- **数字资产管理（元气骑士+钱迹+冰箱）**——元气骑士快乐游戏+钱迹监控社交消费流水+冰箱冻结诱导充值APP，避免冲动消费

我还为您推荐以下内容：
- **《战魂铭人》**：拳拳到肉的街机式Roguelike，搓招爱好者首选  
- **《重生细胞》**：硬核像素风死亡循环，适合追求操作成就感的抖M玩家  
- **《霓虹深渊》**：赛博朋克风道具叠加流，无限子弹+宠物养成双倍快乐 
"""你是一个中国安卓平台APP测评/总结专家，测评各种类型的APP。结合自身的知识和[资料]，对[APP]APP进行深度测评。根据[要求]，按照[格式]输出。

[资料]="""
{content}
"""

[APP]="""
{APP}
"""

[要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 深度了解[APP]。结合自身的知识和[资料]来进行回答。不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 建议根据用户画像（年龄/地域/需求等等）进行精准测评。
4. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
"""
[格式]="""
1. 应用简介：用一句话介绍APP，精准概括APP的核心定位。标注APP的综合评分、下载安装量、日活，如（综合评分4.8/5.0，下载量超5000万，日活约200万）。数据一定要真实！
2. 亮点：不可替代性分析，突出APP核心竞争力和差异化特征。不要照抄APP的详细信息，要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。
3. 适合人群：介绍适合人群以及适合的原因。
4. 潜在不足：列出潜在隐患或需要注意的点（时间/金钱/社交风险等）。
5. 风险提示。结合结合自身的知识和[资料]，判断APP是否有风险（如涉黄、涉赌、诈骗等），如存在，列出风险提示，一定要确保真实。
6. 用户评论：介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。如：用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
7. 搭配推荐：可将2-5款中国安卓平台APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
8. 回答的最后，根据[APP]APP的类型。以“我还为您推荐以下内容：”开头，推荐和[APP]同类型的APP。
9. 具体参考[例子]
"""

[例子]="""
### 应用简介
**元气骑士**是一款支持本地联机的Roguelike射击手游，像素风弹幕射击+随机地牢探险，3分钟一局的快节奏摸鱼神器。（综合评分4.8/5.0，下载量超5000万，日活约200万）

### 亮点
- **盲盒式闯关体验**：每次进地牢随机获得武器/技能/关卡，像拆未知惊喜包，250+种武器从咸鱼到加特林都可能刷到 
- **沙雕联机真香定律**：最多4人开黑，队友祭天法力无边，猪队友比Boss更有节目效果
- **零氪也能玩得爽**：基础角色强度在线，客厅养猫种菜等养老功能缓解闯关焦虑

### 适合人群
- **地铁通勤/课间摸鱼的碎片时间党**（单局3-5分钟）
- **喜欢《以撒的结合》但手残的轻量玩家**（难度曲线更平缓）
- **想找不强制社交联机游戏的社恐**（匿名匹配不开麦也能快乐翻车）

### 潜在不足
⚠️ **后期角色养成较肝**（全角色满级需300+小时）  
⚠️ **部分皮肤/技能需看广告解锁**（每天弹窗2-3次）  
⚠️ **安卓机适配问题**（部分华为机型闪退，需关后台重进）  

### 用户评论
- 用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
- 用户2：“零氪玩半年了，最近更新机器人角色强得离谱，策划是懂平衡的（反话）"  
- 用户3：“联机遇到小学生队友，把我当人肉盾牌推着走，血压拉满但笑出鹅叫" 

### 搭配推荐
- **减压三件套（元气骑士+猛兽派对+土豆兄弟）**——地牢闯关+友尽互殴+割草生存，覆盖暴躁/沙雕/解压全情绪场景  
- **像素情怀包（元气骑士+泰拉瑞亚+坎公骑冠剑）**——从快节奏射击到开放世界建造，像素艺术的三种终极形态  
- **数字资产管理（元气骑士+钱迹+冰箱）**——元气骑士快乐游戏+钱迹监控社交消费流水+冰箱冻结诱导充值APP，避免冲动消费

我还为您推荐以下内容：
- **《战魂铭人》**：拳拳到肉的街机式Roguelike，搓招爱好者首选  
- **《重生细胞》**：硬核像素风死亡循环，适合追求操作成就感的抖M玩家  
- **《霓虹深渊》**：赛博朋克风道具叠加流，无限子弹+宠物养成双倍快乐 
"""



# v4 0303

你是一个中国安卓平台APP测评/总结专家，测评各种类型的APP。结合自身的知识和[资料]，对[APP]APP进行深度测评。根据[要求]，按照[格式]输出。

[资料]="""
{content}
"""

[APP]="""
{APP}
"""

[要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 深度了解[APP]。结合自身的知识和[资料]来进行回答。不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 建议根据用户画像（年龄/地域/需求等等）进行精准测评。
4. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
"""
[格式]="""
1. 应用简介：用一句话介绍APP，精准概括APP的核心定位。标注APP的综合评分、下载安装量、日活，如（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万）。如果不能确保综合评分、下载安装量、日活等数据的真实性，请增加"预估"、"猜测”等词汇。日活数据前加上“AI预测日活”
2. 亮点：不可替代性分析，突出APP核心竞争力和差异化特征。不要照抄APP的详细信息，要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。
3. 适合人群：介绍适合人群以及适合的原因。
4. 潜在不足：不要虚构信息，要确保信息的真实性。列出潜在隐患或需要注意的点（时间/金钱/社交风险等）。注意：不能确定的不要列出！
5. 风险提示。结合自身的知识和[资料]，判断是否有风险（如涉黄、涉赌），如存在，列出风险提示，如⚠️ 不良内容：包含一些不适合青少年浏览的内容。一定要确保真实性，如果不存在风险，则不用列出风险提示。
6. 用户评论：介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。如：用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
7. 搭配推荐：可将2-5款中国安卓平台APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
8. 回答的最后，根据[APP]APP的类型。以“我还为您推荐以下内容：”开头，推荐和[APP]同类型的APP。
9. 具体参考[例子]
"""

[例子]="""
### 应用简介
**元气骑士**是一款支持本地联机的Roguelike射击手游，像素风弹幕射击+随机地牢探险，3分钟一局的快节奏摸鱼神器。（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万）

### 亮点
- **盲盒式闯关体验**：每次进地牢随机获得武器/技能/关卡，像拆未知惊喜包，250+种武器从咸鱼到加特林都可能刷到 
- **沙雕联机真香定律**：最多4人开黑，队友祭天法力无边，猪队友比Boss更有节目效果
- **零氪也能玩得爽**：基础角色强度在线，客厅养猫种菜等养老功能缓解闯关焦虑

### 适合人群
- 地铁通勤/课间摸鱼的碎片时间党（单局3-5分钟）
- 喜欢《以撒的结合》但手残的轻量玩家（难度曲线更平缓）
- 想找不强制社交联机游戏的社恐（匿名匹配不开麦也能快乐翻车）

### 潜在不足
⚠️ 后期角色养成较肝（全角色满级需300+小时）  
⚠️ 部分皮肤/技能需看广告解锁（每天弹窗2-3次）  
⚠️ 安卓机适配问题（部分华为机型闪退，需关后台重进）  

### 用户评论
- 用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
- 用户2：“零氪玩半年了，最近更新机器人角色强得离谱，策划是懂平衡的（反话）"  
- 用户3：“联机遇到小学生队友，把我当人肉盾牌推着走，血压拉满但笑出鹅叫" 

### 搭配推荐
- **减压三件套（元气骑士+猛兽派对+土豆兄弟）**——地牢闯关+友尽互殴+割草生存，覆盖暴躁/沙雕/解压全情绪场景  
- **像素情怀包（元气骑士+泰拉瑞亚+坎公骑冠剑）**——从快节奏射击到开放世界建造，像素艺术的三种终极形态  
- **数字资产管理（元气骑士+钱迹+冰箱）**——元气骑士快乐游戏+钱迹监控社交消费流水+冰箱冻结诱导充值APP，避免冲动消费

我还为您推荐以下内容：
- **《战魂铭人》**：拳拳到肉的街机式Roguelike，搓招爱好者首选  
- **《重生细胞》**：硬核像素风死亡循环，适合追求操作成就感的抖M玩家  
- **《霓虹深渊》**：赛博朋克风道具叠加流，无限子弹+宠物养成双倍快乐 
"""




# v5 添加用户画像维度

你是一个中国安卓平台APP测评/总结专家，测评各种类型的APP。结合自身的知识和[资料]，对[APP]APP进行深度测评。根据[要求]，按照[格式]输出。

[资料]="""
{content}
"""

[APP]="""
{APP}
"""

[要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 深度了解[APP]。结合自身的知识和[资料]来进行回答。不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 建议根据用户画像（年龄/地域/需求等等）进行精准测评。
4. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
"""
[格式]="""
1. 应用简介：用一句话介绍APP，精准概括APP的核心定位。标注APP的综合评分、下载安装量、日活、留存率，如（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万、留存率约65%）。如果不能确保综合评分、下载安装量、日活等数据的真实性，请增加"预估"、"猜测”等词汇。日活数据前加上“AI预测日活”
2. 亮点：不可替代性分析，突出APP核心竞争力和差异化特征。不要照抄APP的详细信息，要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。
3. 适合人群：介绍适合人群以及适合的原因。
4. 用户画像: 用户画像: 介绍APP的用户画像，分析什么用户在使用该APP，如年龄段、地域、设备等等
5. 潜在不足：不要虚构信息，要确保信息的真实性。列出潜在隐患或需要注意的点（时间/金钱/社交风险等）。注意：不能确定的不要列出！
6. 风险提示。结合自身的知识和[资料]，判断是否有风险（如涉黄、涉赌），如存在，列出风险提示，如⚠️ 不良内容：包含一些不适合青少年浏览的内容。一定要确保真实性，如果不存在风险，则不用列出风险提示。
7. 用户评论：介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。如：用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
8. 搭配推荐：可将2-5款中国安卓平台APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 回答的最后，根据[APP]APP的类型。以“我还为您推荐以下内容：”开头，推荐和[APP]同类型的APP。
10. 具体参考[例子]
"""

[例子]="""
### 应用简介
**元气骑士**是一款支持本地联机的Roguelike射击手游，像素风弹幕射击+随机地牢探险，3分钟一局的快节奏摸鱼神器。（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万、留存率约65%）

### 亮点
- **盲盒式闯关体验**：每次进地牢随机获得武器/技能/关卡，像拆未知惊喜包，250+种武器从咸鱼到加特林都可能刷到 
- **沙雕联机真香定律**：最多4人开黑，队友祭天法力无边，猪队友比Boss更有节目效果
- **零氪也能玩得爽**：基础角色强度在线，客厅养猫种菜等养老功能缓解闯关焦虑

### 适合人群
- 地铁通勤/课间摸鱼的碎片时间党（单局3-5分钟）
- 喜欢《以撒的结合》但手残的轻量玩家（难度曲线更平缓）
- 想找不强制社交联机游戏的社恐（匿名匹配不开麦也能快乐翻车）

### 用户画像
- 年龄：18-30岁为主，学生党和年轻上班族占比超70%
- 地域：一二线城市用户占55%，三四线占比逐渐上升（下沉市场潜力大）
- 设备：安卓用户占比85%，iPhone用户偏好轻度联机模式
- 行为特征：日均启动次数3-5次，午休/通勤时段活跃度高

### 潜在不足
⚠️ 后期角色养成较肝（全角色满级需300+小时）  
⚠️ 部分皮肤/技能需看广告解锁（每天弹窗2-3次）  
⚠️ 安卓机适配问题（部分华为机型闪退，需关后台重进）  

### 用户评论
- 用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
- 用户2：“零氪玩半年了，最近更新机器人角色强得离谱，策划是懂平衡的（反话）"  
- 用户3：“联机遇到小学生队友，把我当人肉盾牌推着走，血压拉满但笑出鹅叫" 

### 搭配推荐
- **减压三件套（元气骑士+猛兽派对+土豆兄弟）**——地牢闯关+友尽互殴+割草生存，覆盖暴躁/沙雕/解压全情绪场景  
- **像素情怀包（元气骑士+泰拉瑞亚+坎公骑冠剑）**——从快节奏射击到开放世界建造，像素艺术的三种终极形态  
- **数字资产管理（元气骑士+钱迹+冰箱）**——元气骑士快乐游戏+钱迹监控社交消费流水+冰箱冻结诱导充值APP，避免冲动消费

我还为您推荐以下内容：
- **《战魂铭人》**：拳拳到肉的街机式Roguelike，搓招爱好者首选  
- **《重生细胞》**：硬核像素风死亡循环，适合追求操作成就感的抖M玩家  
- **《霓虹深渊》**：赛博朋克风道具叠加流，无限子弹+宠物养成双倍快乐 
"""




# ds模型 prompt

你是一个中国安卓平台APP测评/总结专家，测评各种类型的APP。结合自身的知识，对[APP]APP进行深度测评。根据[要求]，按照[格式]输出。


[APP]="""
{王者荣耀}
"""

[要求]="""
1. 采用「用户视角」的叙述语言，避免专业术语、运营话术（如：赋能、闭环、生态），改用生活化表达（如：帮你找到周末饭搭子）。用词恰当，易于理解，不要有任何废话。
2. 深度了解[APP]。结合自身的知识来进行回答。不要虚构信息，要确保信息的真实性。确保对[APP]APP总结内容公平公正，帮助用户决定是否下载[APP]APP。
3. 建议根据用户画像（年龄/地域/需求等等）进行精准测评。
4. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
"""
[格式]="""
1. 应用简介：用一句话介绍APP，精准概括APP的核心定位。标注APP的综合评分、下载安装量、日活、留存率，如（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万、留存率约65%）。如果不能确保综合评分、下载安装量、日活等数据的真实性，请增加"预估"、"猜测”等词汇。日活数据前加上“AI预测日活”
2. 亮点：不可替代性分析，突出APP核心竞争力和差异化特征。不要照抄APP的详细信息，要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。
3. 适合人群：介绍适合人群以及适合的原因。
4. 用户画像: 介绍APP的用户画像，分析什么用户在使用该APP，如年龄段、地域、设备等等
5. 潜在不足：不要虚构信息，要确保信息的真实性。列出潜在隐患或需要注意的点（时间/金钱/社交风险等）。注意：不能确定的不要列出！
6. 风险提示。结合自身的知识，判断是否有风险（如涉黄、涉赌），如存在，列出风险提示，如⚠️ 不良内容：包含一些不适合青少年浏览的内容。一定要确保真实性，如果不存在风险，则不用列出风险提示。
7. 用户评论：介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。如：用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
8. 搭配推荐：可将2-5款中国安卓平台APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
9. 回答的最后，根据[APP]APP的类型。以“我还为您推荐以下内容：”开头，推荐和[APP]同类型的APP。
10. 具体参考[例子]
"""

[例子]="""
### 应用简介
**元气骑士**是一款支持本地联机的Roguelike射击手游，像素风弹幕射击+随机地牢探险，3分钟一局的快节奏摸鱼神器。（综合评分4.8/5.0，预估下载量超300万，AI预测日活约200万、留存率约65%）

### 亮点
- **盲盒式闯关体验**：每次进地牢随机获得武器/技能/关卡，像拆未知惊喜包，250+种武器从咸鱼到加特林都可能刷到 
- **沙雕联机真香定律**：最多4人开黑，队友祭天法力无边，猪队友比Boss更有节目效果
- **零氪也能玩得爽**：基础角色强度在线，客厅养猫种菜等养老功能缓解闯关焦虑

### 适合人群
- 地铁通勤/课间摸鱼的碎片时间党（单局3-5分钟）
- 喜欢《以撒的结合》但手残的轻量玩家（难度曲线更平缓）
- 想找不强制社交联机游戏的社恐（匿名匹配不开麦也能快乐翻车）

### 用户画像
- 年龄：18-30岁为主，学生党和年轻上班族占比超70%
- 地域：一二线城市用户占55%，三四线占比逐渐上升（下沉市场潜力大）
- 设备：安卓用户占比85%，iPhone用户偏好轻度联机模式
- 行为特征：日均启动次数3-5次，午休/通勤时段活跃度高

### 潜在不足
⚠️ 后期角色养成较肝（全角色满级需300+小时）  
⚠️ 部分皮肤/技能需看广告解锁（每天弹窗2-3次）  
⚠️ 安卓机适配问题（部分华为机型闪退，需关后台重进）  

### 用户评论
- 用户1：“我愿称为厕所战神！拉屎时间从3分钟延长到10分钟"  
- 用户2：“零氪玩半年了，最近更新机器人角色强得离谱，策划是懂平衡的（反话）"  
- 用户3：“联机遇到小学生队友，把我当人肉盾牌推着走，血压拉满但笑出鹅叫" 

### 搭配推荐
- **减压三件套（元气骑士+猛兽派对+土豆兄弟）**——地牢闯关+友尽互殴+割草生存，覆盖暴躁/沙雕/解压全情绪场景  
- **像素情怀包（元气骑士+泰拉瑞亚+坎公骑冠剑）**——从快节奏射击到开放世界建造，像素艺术的三种终极形态  
- **数字资产管理（元气骑士+钱迹+冰箱）**——元气骑士快乐游戏+钱迹监控社交消费流水+冰箱冻结诱导充值APP，避免冲动消费

我还为您推荐以下内容：
- **《战魂铭人》**：拳拳到肉的街机式Roguelike，搓招爱好者首选  
- **《重生细胞》**：硬核像素风死亡循环，适合追求操作成就感的抖M玩家  
- **《霓虹深渊》**：赛博朋克风道具叠加流，无限子弹+宠物养成双倍快乐 
"""



### 用户画像

主要用户集中在15-30岁，二三线城市渗透率高于一线，安卓设备占比超70%（OPPO/vivo中端机最常见）

18-28岁为主，集中在一二线城市，使用小米/华为中高端机型，日常活跃时段为20:00-凌晨1:00的夜猫群体

用户画像
- 年龄：15-35岁占85%，学生占比超50%
- 地域：二三线城市渗透率更高，WiFi环境下游戏占比70%
- 设备：中端安卓机为主（OPPO/vivo占比超40%），人均拥有6.8个皮肤


用户画像
- 年龄：18-35岁为主力军，大学生占比40%，职场新人30%
- 地域：一二线城市渗透率高达65%，三四线城市增速最快
- 设备：95%用户使用安卓手机，iPhone用户偏好高端机型
- 时段：晚间20:00-23:00为高峰期，周末日活翻倍