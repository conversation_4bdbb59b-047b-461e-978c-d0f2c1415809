{"query": "派派", "id": "20250221000001", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京畅聊天下科技股份有限公司。"}
{"query": "有信", "id": "20250221000002", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市有信网络技术有限公司。"}
{"query": "小红书", "id": "20250221000003", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:行吟信息科技（上海）有限公司。"}
{"query": "探探", "id": "20250221000004", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:探探文化发展（北京）有限公司。"}
{"query": "一伴婚恋专业版", "id": "20250221000005", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:广州一伴信息科技有限公司。"}
{"query": "玩吧", "id": "20250221000006", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京默契破冰科技有限公司。"}
{"query": "Soul", "id": "20250221000007", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:上海任意门科技有限公司。"}
{"query": "新浪微博4G版", "id": "20250221000008", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:微梦创科网络技术(中国)有限公司。"}
{"query": "百度知道", "id": "20250221000009", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京百度网讯科技有限公司。"}
{"query": "233乐园", "id": "20250221000010", "value": "应用分类:社交。安全标签:专项-游戏辅助类。checkLevel:普通应用 (40)。开发者:北京龙威互动科技有限公司。"}
{"query": "同城品质婚恋", "id": "20250221000011", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:广州四叶文化传播有限公司。"}
{"query": "他趣", "id": "20250221000012", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:湖北海豹他趣信息技术有限公司。"}
{"query": "Blued", "id": "20250221000013", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:北京蓝城兄弟文化传媒有限公司。"}
{"query": "TT语音", "id": "20250221000014", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:广州趣丸网络科技有限公司。"}
{"query": "连信", "id": "20250221000015", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:浙江简信科技有限公司。"}
{"query": "同桌派对", "id": "20250221000016", "value": "应用分类:社交。安全标签:高风险-涉赌。checkLevel:普通应用 (40)。开发者:北京同桌游戏科技有限公司。"}
{"query": "知富美聊", "id": "20250221000017", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳尤艾奇科技有限公司。"}
{"query": "微博极速版", "id": "20250221000018", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京微梦创科网络技术有限公司。"}
{"query": "聊聊吧", "id": "20250221000019", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市三木森电子科技有限公司。"}
{"query": "豆瓣", "id": "20250221000020", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京豆网科技有限公司。"}
{"query": "约附近陌生同城交友聊天", "id": "20250221000021", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京陌觅缘分网络科技有限公司。"}
{"query": "美V聊天", "id": "20250221000022", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:安化威通科技有限公司。"}
{"query": "MarryU相亲交友", "id": "20250221000023", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州迈优文化创意有限公司。"}
{"query": "伊对", "id": "20250221000024", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:北京米连科技有限公司。"}
{"query": "恋人空间", "id": "20250221000025", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京发现角科技有限公司。"}
{"query": "同城知趣聊天", "id": "20250221000026", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市缤客电子商务有限公司。"}
{"query": "恰聊", "id": "20250221000027", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:湖北觅聊网络科技有限公司。"}
{"query": "珍爱", "id": "20250221000028", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市珍爱网信息技术有限公司。"}
{"query": "十点聊天", "id": "20250221000029", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市金涛装饰设计工程有限公司。"}
{"query": "堆糖", "id": "20250221000030", "value": "应用分类:社交。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:堆糖信息科技（上海）有限公司。"}
{"query": "第一弹", "id": "20250221000031", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:上海斯干网络科技有限公司。"}
{"query": "捞月狗", "id": "20250221000032", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:上海凌巨文化科技有限公司。"}
{"query": "爆聊", "id": "20250221000033", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市冰璃网络科技有限公司。"}
{"query": "爱聊", "id": "20250221000034", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:深圳市爱聊科技有限公司。"}
{"query": "百合网", "id": "20250221000035", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:百合佳缘网络集团股份有限公司。"}
{"query": "网易LOFTER", "id": "20250221000036", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州网易元气科技有限公司。"}
{"query": "世纪佳缘", "id": "20250221000037", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:上海花千树信息科技有限公司。"}
{"query": "探遇漂流瓶", "id": "20250221000038", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市陌启网络科技有限公司。"}
{"query": "成人爱交友", "id": "20250221000039", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州鑫澈科技有限公司。"}
{"query": "Yo交友", "id": "20250221000040", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:广州津虹网络传媒有限公司。"}
{"query": "乡遇", "id": "20250221000041", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州首秀网络科技有限公司。"}
{"query": "微脉圈", "id": "20250221000042", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京助梦工场科技有限公司。"}
{"query": "饭友", "id": "20250221000043", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:深圳陌趣科技有限公司。"}
{"query": "为你诵读", "id": "20250221000044", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京诵读文化发展有限公司。"}
{"query": "老来健康", "id": "20250221000045", "value": "应用分类:社交。安全标签:专项- AI类（深度合成，一件脱衣）。checkLevel:普通应用 (40)。开发者:湖南老来科技有限公司。"}
{"query": "Only婚恋", "id": "20250221000046", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州四叶文化传播有限公司。"}
{"query": "钓鱼人", "id": "20250221000047", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京联创鸿瑞网络技术有限公司。"}
{"query": "想恋爱", "id": "20250221000048", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京想恋爱科技有限公司。"}
{"query": "默往", "id": "20250221000049", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:厦门默往科技有限公司。"}
{"query": "附近热聊", "id": "20250221000050", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:深圳市云淞袅科技有限公司。"}
{"query": "幻塔", "id": "20250221000051", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:苏州幻塔网络科技有限公司。"}
{"query": "组CP", "id": "20250221000052", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市橙子互动科技有限公司。"}
{"query": "畅说108", "id": "20250221000053", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:浙江崇汉信息科技有限公司。"}
{"query": "Uki", "id": "20250221000054", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:上海辰龙信息科技有限公司。"}
{"query": "虚拟恋人", "id": "20250221000055", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市易优缘科技有限公司。"}
{"query": "兴聊", "id": "20250221000056", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州零新网络科技有限公司。"}
{"query": "互动吧", "id": "20250221000057", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京佐敦网络科技有限公司。"}
{"query": "恋爱记", "id": "20250221000058", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:武汉恋爱记网络科技有限公司。"}
{"query": "即陌", "id": "20250221000059", "value": "应用分类:社交。安全标签:高风险-马甲包。checkLevel:普通应用 (40)。开发者:杭州鑫滢科技有限公司。"}
{"query": "语玩", "id": "20250221000060", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:贵阳语玩科技有限公司。"}
{"query": "再婚相亲网", "id": "20250221000061", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:杭州知心红娘网络科技有限公司。"}
{"query": "知识星球", "id": "20250221000062", "value": "应用分类:社交。安全标签:高风险-隐匿传销。checkLevel:普通应用 (40)。开发者:深圳市大成天下信息技术有限公司。"}
{"query": "微核", "id": "20250221000063", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:广西云之初网络科技有限公司。"}
{"query": "附近约会", "id": "20250221000064", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:深圳市蕉鹿梦科技有限公司。"}
{"query": "克拉克拉", "id": "20250221000065", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳有咖互动科技有限公司。"}
{"query": "新漂流瓶", "id": "20250221000066", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:成都小胖猪科技有限公司。"}
{"query": "同城约会吧", "id": "20250221000067", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:大连金锤网络科技有限公司。"}
{"query": "知味社区", "id": "20250221000068", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:成都色差网络科技有限公司。"}
{"query": "觅伊", "id": "20250221000069", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳市仁凯网络科技有限公司。"}
{"query": "两性交友", "id": "20250221000070", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:南宁觅趣科技有限公司。"}
{"query": "西影视频", "id": "20250221000071", "value": "应用分类:社交。安全标签:专项-版权类。checkLevel:普通应用 (40)。开发者:陕西西影数码传媒科技有限责任公司。"}
{"query": "城信", "id": "20250221000072", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:南京魔苹网络科技有限公司。"}
{"query": "美丽约", "id": "20250221000073", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:南宁美丽约网络科技有限公司。"}
{"query": "情缘同城交友视频聊天", "id": "20250221000074", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:海南诗恩网络科技有限公司。"}
{"query": "ForU", "id": "20250221000075", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:广西蜻萌网络科技有限公司。"}
{"query": "与你", "id": "20250221000076", "value": "应用分类:社交。安全标签:专项-华为在线特殊上架。checkLevel:普通应用 (40)。开发者:北京与你科技有限公司。"}
{"query": "Nico", "id": "20250221000077", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:武汉稀有科技有限责任公司。"}
{"query": "心遇", "id": "20250221000078", "value": "应用分类:社交。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:杭州乐读科技有限公司。"}
{"query": "耳海", "id": "20250221000079", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:杭州米络文化传播有限公司。"}
{"query": "tell", "id": "20250221000080", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:深圳西西里信息技术有限公司。"}
{"query": "LesPark", "id": "20250221000081", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北海帕克信息科技有限公司。"}
{"query": "积目", "id": "20250221000082", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:湖南高佳网络信息有限公司。"}
{"query": "红信圈", "id": "20250221000083", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京森航时代科技有限公司。"}
{"query": "Drug药", "id": "20250221000084", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:温州嘿哈科技有限公司。"}
{"query": "声吧", "id": "20250221000085", "value": "应用分类:社交。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:深圳市金耀阳科技有限公司。"}
{"query": "有恋", "id": "20250221000086", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:北京易弘盛创科技有限公司。"}
{"query": "句苗岛", "id": "20250221000087", "value": "应用分类:社交。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:重庆舍果文化传媒有限公司。"}
{"query": "带带陪玩", "id": "20250221000088", "value": "应用分类:社交。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:武汉盛游互娱网络科技有限公司。"}
{"query": "CSDN", "id": "20250221000089", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京创新乐知网络技术有限公司。"}
{"query": "名人朋友圈", "id": "20250221000090", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:武汉华中时讯科技有限责任公司。"}
{"query": "盖乐世社区", "id": "20250221000091", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:三星（中国）投资有限公司。"}
{"query": "渔获", "id": "20250221000092", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:索罗特信息科技江苏有限公司。"}
{"query": "一罐", "id": "20250221000093", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:宁波意赋科技有限公司。"}
{"query": "海归直聘", "id": "20250221000094", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京海归科技集团有限公司。"}
{"query": "赫兹", "id": "20250221000095", "value": "应用分类:社交。安全标签:高风险-涉黄。checkLevel:普通应用 (40)。开发者:海南喵咖网络科技有限公司。"}
{"query": "橙瓜", "id": "20250221000096", "value": "应用分类:社交。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:杭州富文网络科技有限公司。"}
{"query": "礼成旅行婚礼", "id": "20250221000097", "value": "应用分类:社交。安全标签:低风险。checkLevel:普通应用 (40)。开发者:杭州幻熊科技有限公司。"}
{"query": "作业帮", "id": "20250221000098", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:广州极目未来文化科技有限公司。"}
{"query": "一起作业", "id": "20250221000099", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:上海合煦信息科技有限公司。"}
{"query": "一起学", "id": "20250221000100", "value": "应用分类:教育。安全标签:。checkLevel:普通应用 (40)。开发者:上海合煦信息科技有限公司。"}
{"query": "小盒学习", "id": "20250221000101", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京知识印象科技有限公司。"}
{"query": "金山词霸", "id": "20250221000102", "value": "应用分类:教育。安全标签:专项-小语种。checkLevel:普通应用 (40)。开发者:珠海金山办公软件有限公司。"}
{"query": "百词斩", "id": "20250221000103", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:成都超爱学习科技有限公司。"}
{"query": "英语趣配音", "id": "20250221000104", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:杭州菲助科技有限公司。"}
{"query": "掌通家园", "id": "20250221000105", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:厦门神州鹰软件科技有限公司。"}
{"query": "纳米盒", "id": "20250221000106", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:上海进馨网络科技有限公司。"}
{"query": "同步学", "id": "20250221000107", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:深圳市方直科技股份有限公司。"}
{"query": "智学网", "id": "20250221000108", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:安徽知学科技有限公司。"}
{"query": "优题宝", "id": "20250221000109", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:长沙希赛千亿教育科技有限公司。"}
{"query": "小猿口算", "id": "20250221000110", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州九举科技有限公司。"}
{"query": "洋葱学园", "id": "20250221000111", "value": "应用分类:教育。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:光合新知（北京）科技有限公司。"}
{"query": "超级课程表", "id": "20250221000112", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州超级周末科技有限公司。"}
{"query": "链工宝", "id": "20250221000113", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京华夏安科信息技术有限公司。"}
{"query": "沪江开心词场", "id": "20250221000114", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:上海行藏科技（集团）股份公司。"}
{"query": "作业帮直播课", "id": "20250221000115", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:北京作业帮线上学科培训学校。"}
{"query": "导学号", "id": "20250221000116", "value": "应用分类:教育。安全标签:。checkLevel:普通应用 (40)。开发者:西安导学教育科技有限公司。"}
{"query": "知到", "id": "20250221000117", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:上海卓越睿新数码科技股份有限公司。"}
{"query": "得到", "id": "20250221000118", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:北京优视米网络科技有限公司。"}
{"query": "学习通", "id": "20250221000119", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京世纪超星信息技术发展有限责任公司。"}
{"query": "少年得到", "id": "20250221000120", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:酷得少年（天津）文化传播有限公司。"}
{"query": "网易公开课", "id": "20250221000121", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:网易传媒科技（北京）有限公司。"}
{"query": "作业帮口算", "id": "20250221000122", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州极目未来文化科技有限公司。"}
{"query": "一米阅读", "id": "20250221000123", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京一米蓝科技有限公司。"}
{"query": "天天练", "id": "20250221000124", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:乐乐启航（北京）教育科技有限公司。"}
{"query": "猿辅导", "id": "20250221000125", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京猿辅导线上学科培训学校。"}
{"query": "好分数", "id": "20250221000126", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州奥伦治信息科技有限公司。"}
{"query": "复真书法", "id": "20250221000127", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:北京复真科技有限公司。"}
{"query": "状元共享课堂", "id": "20250221000128", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京龙腾八方文化有限责任公司。"}
{"query": "学而思网校", "id": "20250221000129", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:深圳市可乐堂教育咨询有限公司。"}
{"query": "一点英语", "id": "20250221000130", "value": "应用分类:教育。安全标签:高风险-潜在变更-用户举报。checkLevel:普通应用 (40)。开发者:二木教育科技（深圳）有限公司。"}
{"query": "掌门1对1辅导", "id": "20250221000131", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:上海掌小门教育科技有限公司。"}
{"query": "字典词典大全", "id": "20250221000132", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:千蝉科技（厦门）有限公司。"}
{"query": "汉语国学大师起名", "id": "20250221000133", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:濮信科技（厦门）有限公司。"}
{"query": "准题库", "id": "20250221000134", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:中大英才（北京）网络教育科技有限公司。"}
{"query": "高途", "id": "20250221000135", "value": "应用分类:教育。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:广州高途科技有限公司。"}
{"query": "爱作业", "id": "20250221000136", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:杭州大拿科技股份有限公司。"}
{"query": "英语翻译君", "id": "20250221000137", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:濮信科技（厦门）有限公司。"}
{"query": "英语单词君", "id": "20250221000138", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:千蝉科技（厦门）有限公司。"}
{"query": "粉笔", "id": "20250221000139", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京粉笔蓝天科技有限公司。"}
{"query": "象棋微学堂", "id": "20250221000140", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:河南学云电子科技有限公司。"}
{"query": "公务员准题库", "id": "20250221000141", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:中大英才（北京）网络教育科技有限公司。"}
{"query": "CCtalk", "id": "20250221000142", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:上海仙栎网络科技有限公司。"}
{"query": "千聊", "id": "20250221000143", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:广州思坞信息科技有限公司。"}
{"query": "斑马AI学", "id": "20250221000144", "value": "应用分类:教育。安全标签:白名单-商业化。checkLevel:普通应用 (40)。开发者:京猿力科技有限公司。"}
{"query": "可可英语", "id": "20250221000145", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:北京可可网络科技有限公司。"}
{"query": "大师一百", "id": "20250221000146", "value": "应用分类:教育。安全标签:高风险-潜在变更。checkLevel:普通应用 (40)。开发者:杭州无届网络科技有限公司。"}
{"query": "升学e网通", "id": "20250221000147", "value": "应用分类:教育。安全标签:低风险。checkLevel:普通应用 (40)。开发者:杭州铭师堂数字科技有限公司。"}
{"query": "开心消消乐", "id": "20250221000148", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:北京乐启华章科技有限公司。"}
{"query": "天天爱消除", "id": "20250221000149", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "节奏大师", "id": "20250221000150", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "贪吃蛇大作战", "id": "20250221000151", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:武汉微派网络科技有限公司。"}
{"query": "消灭星星全新版", "id": "20250221000152", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:掌游天下（北京）信息技术股份有限公司。"}
{"query": "宾果消消消", "id": "20250221000153", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:北京柠檬微趣科技股份有限公司。"}
{"query": "滚动的天空", "id": "20250221000154", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:北京猎豹网络科技有限公司。"}
{"query": "你画我猜", "id": "20250221000155", "value": "应用分类:休闲益智。安全标签:高风险-马甲包。checkLevel:。开发者:北京默契破冰科技有限公司。"}
{"query": "逃跑吧！少年", "id": "20250221000156", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:专项-联运游戏。"}
{"query": "蛋仔派对", "id": "20250221000157", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:杭州网易雷火科技有限公司。"}
{"query": "海滨消消乐", "id": "20250221000158", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:上海乐响网络科技发展有限公司。"}
{"query": "纪念碑谷", "id": "20250221000159", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市创梦天地科技有限公司。"}
{"query": "蛇蛇争霸", "id": "20250221000160", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市抱一网络科技有限公司。"}
{"query": "元梦之星", "id": "20250221000161", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "割绳子2", "id": "20250221000162", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:北京游道易网络文化有限公司。"}
{"query": "跳舞的线", "id": "20250221000163", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:北京猎豹网络科技有限公司。"}
{"query": "汤姆猫水上乐园", "id": "20250221000164", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:广州金科文化科技有限公司。"}
{"query": "拳皇·命运", "id": "20250221000165", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:广州银汉科技有限公司。"}
{"query": "纪念碑谷2", "id": "20250221000166", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:USTWO。"}
{"query": "弹弹岛2", "id": "20250221000167", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:广州有玩科技有限公司。"}
{"query": "飞吧龙骑士", "id": "20250221000168", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:深圳雷霆信息技术有限公司。"}
{"query": "我爱拼模型", "id": "20250221000169", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:海南芒斯特科技有限公司。"}
{"query": "太空杀", "id": "20250221000170", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:上海巨人统平网络科技有限公司。"}
{"query": "劲舞时代", "id": "20250221000171", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:杭州网易雷火科技有限公司。"}
{"query": "隐形守护者", "id": "20250221000172", "value": "应用分类:休闲益智。安全标签:白名单-商业化。checkLevel:。开发者:N1创新中心。"}
{"query": "泡泡精灵传奇", "id": "20250221000173", "value": "应用分类:休闲益智。安全标签:专项-联运游戏。checkLevel:。开发者:深圳云步互娱网络科技有限公司。"}
{"query": "和平精英", "id": "20250221000174", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:工作室相关信息。"}
{"query": "穿越火线-枪战王者", "id": "20250221000175", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "全民飞机大战", "id": "20250221000176", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "全民突击", "id": "20250221000177", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:腾讯科技（深圳）有限公司。"}
{"query": "元气骑士", "id": "20250221000178", "value": "应用分类:枪战射击STG。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市中领世纪科技有限公司。"}
{"query": "弓箭手大作战", "id": "20250221000179", "value": "应用分类:枪战射击STG。安全标签:专项-联运游戏。checkLevel:。开发者:北京猎豹网络科技有限公司。"}
{"query": "魂斗罗：归来", "id": "20250221000180", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:腾讯科技（深圳）有限公司。"}
{"query": "使命召唤手游", "id": "20250221000181", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:天美J3工作室。"}
{"query": "生死狙击", "id": "20250221000182", "value": "应用分类:枪战射击STG。安全标签:专项-联运游戏。checkLevel:。开发者:江无端科技股份有限公司。"}
{"query": "战舰帝国", "id": "20250221000183", "value": "应用分类:枪战射击STG。安全标签:专项-联运游戏。checkLevel:。开发者:北京华清飞扬网络股份有限公司。"}
{"query": "超凡先锋", "id": "20250221000184", "value": "应用分类:枪战射击STG。安全标签:专项-联运游戏。checkLevel:。开发者:杭州网易雷火科技有限公司。"}
{"query": "三角洲行动", "id": "20250221000185", "value": "应用分类:枪战射击STG。安全标签:。checkLevel:。开发者:腾讯。"}
{"query": "尘白禁区", "id": "20250221000186", "value": "应用分类:枪战射击STG。安全标签:白名单-商业化。checkLevel:。开发者:海南西山居互动娱乐科技有限公司。"}
{"query": "天天酷跑", "id": "20250221000187", "value": "应用分类:AVG动作冒险。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "天天炫斗", "id": "20250221000188", "value": "应用分类:AVG动作冒险。安全标签:白名单-商业化。checkLevel:。开发者:腾讯。"}
{"query": "火影忍者", "id": "20250221000189", "value": "应用分类:AVG动作冒险。安全标签:白名单-商业化。checkLevel:。开发者:腾讯科技魔术师工作室。"}
{"query": "合金弹头：觉醒", "id": "20250221000190", "value": "应用分类:AVG动作冒险。安全标签:白名单-商业化。checkLevel:。开发者:深圳市腾讯计算机系统有限公司。"}
{"query": "迷你世界", "id": "20250221000191", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市迷你玩科技有限公司。"}
{"query": "地铁跑酷", "id": "20250221000192", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市创梦天地科技有限公司。"}
{"query": "神庙逃亡2", "id": "20250221000193", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:深圳市创梦天地科技有限公司。"}
{"query": "时空猎人", "id": "20250221000194", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:广州银汉科技有限公司。"}
{"query": "我的世界：移动版", "id": "20250221000195", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:上海网之易吾世界网络科技有限公司。"}
{"query": "汤姆猫跑酷", "id": "20250221000196", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:广州金科文化科技有限公司。"}
{"query": "明日之后", "id": "20250221000197", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:杭州网易雷火科技有限公司。"}
{"query": "光·遇", "id": "20250221000198", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:杭州网易雷火科技有限公司。"}
{"query": "疯狂动物园", "id": "20250221000199", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:北京游道易网络文化有限公司。"}
{"query": "滑雪大冒险", "id": "20250221000200", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:北京游道易网络文化有限公司。"}
{"query": "创造与魔法", "id": "20250221000201", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:天津英雄互娱科技有限公司。"}
{"query": "被尘封的故事", "id": "20250221000202", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:河北优果网络科技有限公司。"}
{"query": "敢达 争锋对决", "id": "20250221000203", "value": "应用分类:AVG动作冒险。安全标签:专项-联运游戏。checkLevel:。开发者:上饶盛罗网络科技有限公司。"}