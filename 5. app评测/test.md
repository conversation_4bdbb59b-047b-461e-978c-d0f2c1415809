
# 简版 流式 请求

try {
    val response = client.newCall(request).execute()
    val responseBody = response.body()

    if (response.code() != 200) {
        runUiTaskCompat {
            callback?.onRequestFailed(response.code(), response.body()?.string() ?: "")
        }
        return
    }

    // 不使用 use 块，而是直接使用 responseBody
    if (responseBody != null) {
        val source = responseBody.source()
        
        try {
            // 逐行读取响应数据
            while (!source.exhausted()) {
                val line = source.readUtf8Line() ?: continue
                
                // 处理每一行数据
                if (line.startsWith("data: ")) {
                    try {
                        val jsonString = line.substringAfter("data: ")
                        val jsonObject = JSONObject(jsonString)
                        val result = jsonObject.optString("result", "")
                        XLog.i(TAG, "Line --- result: $result")
                        
                        // 将结果回调给UI
                        if (result.isNotEmpty()) {
                            runUiTaskCompat {
                                callback?.onRequestSuccess(result)
                            }
                        }
                    } catch (e: Exception) {
                        XLog.e(TAG, "Parse response line failed: $line", e)
                    }
                }
            }
        } finally {
            // 在读取完成后关闭资源
            try {
                source.close()
                responseBody.close()
            } catch (e: Exception) {
                XLog.e(TAG, "Close resource failed", e)
            }
        }
    }
} catch (e: Exception) {
    XLog.e(TAG, "request = $request, network request exception: ", e)
    runUiTaskCompat {
        callback?.onRequestFailed(-1, "")
    }
}




# UI 基础版本

class YourActivity : Activity(), AIHelpRequestCallback {
    
    private lateinit var textView: TextView
    private val stringBuilder = StringBuilder()
    
    override fun onRequestSuccess(result: String) {
        // 将新的文本追加到 StringBuilder
        stringBuilder.append(result)
        
        // 更新 TextView
        textView.text = stringBuilder.toString()
        
        // 可选：自动滚动到底部
        val scrollView = textView.parent as? ScrollView
        scrollView?.post {
            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }
    
    override fun onRequestFailed(code: Int, message: String) {
        // 处理错误情况
        Toast.makeText(this, "请求失败：$message", Toast.LENGTH_SHORT).show()
    }
    
    // 在开始新的对话时，清空之前的内容
    private fun clearChat() {
        stringBuilder.clear()
        textView.text = ""
    }
}



// 添加打字机效果
private fun addTypingEffect(newText: String) {
    var index = 0
    val handler = Handler()
    val runnable = object : Runnable {
        override fun run() {
            if (index < newText.length) {
                stringBuilder.append(newText[index])
                textView.text = stringBuilder.toString()
                index++
                handler.postDelayed(this, 50) // 每个字符之间的延迟时间
            }
        }
    }
    handler.post(runnable)
}

// 支持复制文本
textView.apply {
    setTextIsSelectable(true)
    customSelectionActionModeCallback = object : ActionMode.Callback {
        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            return true
        }
        
        override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            return false
        }
        
        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
            return false
        }
        
        override fun onDestroyActionMode(mode: ActionMode?) {}
    }
}


# 文字等待 动画效果

class YourActivity : Activity(), AIHelpRequestCallback {
    
    private lateinit var textView: TextView
    private val stringBuilder = StringBuilder()
    private var isLoading = false
    private val loadingHandler = Handler()
    private var loadingDots = ""
    
    private val loadingAnimation = object : Runnable {
        override fun run() {
            if (isLoading) {
                // 更新加载动画的点
                loadingDots = when (loadingDots) {
                    "" -> "."
                    "." -> ".."
                    ".." -> "..."
                    else -> ""
                }
                
                // 更新文本显示
                textView.text = stringBuilder.toString() + loadingDots
                
                // 继续动画
                loadingHandler.postDelayed(this, 500) // 每500毫秒更新一次
            }
        }
    }
    
    override fun onRequestSuccess(result: String) {
        if (result.isEmpty()) {
            // 如果收到空结果，可能是对话结束
            stopLoading()
            return
        }
        
        // 将新的文本追加到 StringBuilder
        stringBuilder.append(result)
        
        // 开始或继续加载动画
        startLoading()
        
        // 更新 TextView（包含加载动画）
        textView.text = stringBuilder.toString() + loadingDots
        
        // 自动滚动到底部
        scrollToBottom()
    }
    
    private fun startLoading() {
        if (!isLoading) {
            isLoading = true
            loadingHandler.post(loadingAnimation)
        }
    }
    
    private fun stopLoading() {
        isLoading = false
        loadingHandler.removeCallbacks(loadingAnimation)
        // 移除加载动画，只显示实际文本
        textView.text = stringBuilder.toString()
    }
    
    private fun scrollToBottom() {
        val scrollView = textView.parent as? ScrollView
        scrollView?.post {
            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }
    
    // 在开始新的对话时调用
    private fun startNewChat() {
        stringBuilder.clear()
        stopLoading()
        textView.text = ""
    }
    
    // 在 Activity 销毁时清理
    override fun onDestroy() {
        super.onDestroy()
        loadingHandler.removeCallbacks(loadingAnimation)
    }
    
    override fun onRequestFailed(code: Int, message: String) {
        stopLoading()
        Toast.makeText(this, "请求失败：$message", Toast.LENGTH_SHORT).show()
    }
}


# 文字等待效果 简版实现

class AISummaryDialogFragment : DialogFragment() {
    // ... existing code ...
    
    private val loadingDots = StringBuilder()
    private var loadingRunnable: Runnable? = null
    private var isLoading = false
    
    private fun startLoading() {
        if (!isLoading) {
            isLoading = true
            loadingDots.clear()
            
            // 创建加载动画 Runnable
            loadingRunnable = object : Runnable {
                override fun run() {
                    if (isLoading) {
                        // 更新加载动画的点
                        when (loadingDots.length) {
                            0 -> loadingDots.append(".")
                            1 -> loadingDots.append(".")
                            2 -> loadingDots.append(".")
                            else -> loadingDots.clear()
                        }
                        
                        // 更新显示内容
                        markwon.setMarkdown(tvContent, contents.toString() + loadingDots.toString())
                        
                        // 自动滚动到底部
                        val scrollView = tvContent.parent as? ScrollView
                        scrollView?.post {
                            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
                        }
                        
                        // 继续动画
                        HandlerUtils.getMainHandler().postDelayed(this, 500)
                    }
                }
            }
            
            // 开始动画
            HandlerUtils.getMainHandler().post(loadingRunnable!!)
        }
    }
    
    private fun stopLoading() {
        isLoading = false
        loadingRunnable?.let {
            HandlerUtils.getMainHandler().removeCallbacks(it)
            loadingRunnable = null
        }
        loadingDots.clear()
        // 更新最终内容（不带加载动画）
        markwon.setMarkdown(tvContent, contents.toString())
    }
    
    fun setContentText(content: String) {
        // 将新的文本追加到 contents
        contents.append(content)
        XLog.i(TAG, "content: $content")
        
        // 根据内容判断是否需要显示/停止加载动画
        if (content.contains("</answer>")) {
            stopLoading()
        } else {
            startLoading()
        }
        
        // 更新显示内容（包含加载动画）
        markwon.setMarkdown(tvContent, contents.toString() + loadingDots.toString())
        
        // 自动滚动到底部
        val scrollView = tvContent.parent as? ScrollView
        scrollView?.post {
            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        // 清理资源
        stopLoading()
    }
}


# 点击close 关闭请求

class AIHelpRequest {
    // ... existing code ...
    
    private var currentCall: okhttp3.Call? = null
    
    fun sendAIHelpRequestForSummary(query: String, isStream: Boolean, forwardService: String, callback: AIHelpRequestCallback ?= null) {
        XLog.i(TAG, "sendAIHelpRequest")
        val client = OkHttpClient()
        val newClient = setHttpClient(client)
        val request = setRequest(query, isStream, forwardService, Request.Builder())
        
        try {
            // 保存当前请求的 Call
            currentCall = newClient.newCall(request)
            val response = currentCall?.execute()
            
            // 如果请求已经被取消，直接返回
            if (currentCall?.isCanceled() == true) {
                XLog.i(TAG, "Request was canceled")
                return
            }
            
            // ... existing code ...
            
        } catch (e: Exception) {
            XLog.e(TAG, "request = $request, network request exception: ", e)
            // 判断是否是因为取消导致的异常
            if (e is java.io.IOException && currentCall?.isCanceled() == true) {
                XLog.i(TAG, "Request was canceled by user")
                return
            }
            runUiTaskCompat {
                callback?.onRequestFailed(-1, "网络请求失败，请重试")
            }
        } finally {
            currentCall = null
        }
    }
    
    /**
     * 取消当前正在进行的请求
     */
    fun cancelRequest() {
        currentCall?.let {
            if (!it.isCanceled()) {
                XLog.i(TAG, "Canceling current request")
                it.cancel()
            }
        }
        currentCall = null
    }
}




class AISummaryDialogFragment : DialogFragment() {
    
    private var aiHelpRequest: AIHelpRequest? = null
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 初始化 AIHelpRequest
        aiHelpRequest = AIHelpRequest()
        
        // 初始化关闭按钮
        val ivClose = rootView.findViewById<ImageView>(R.id.iv_close)
        ivClose.setOnClickListener {
            // 取消正在进行的请求
            aiHelpRequest?.cancelRequest()
            // 停止加载动画
            stopLoading()
            // 关闭弹窗
            dismissAllowingStateLoss()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        // 确保请求被取消
        aiHelpRequest?.cancelRequest()
        aiHelpRequest = null
        // 清理加载动画
        stopLoading()
    }
}


# 缓存 + 引用展示 think

class AIHelpRequest {
    companion object {
        // 添加缓存 Map
        private val summaryCache = mutableMapOf<String, String>()
        
        // 获取缓存的内容
        fun getCachedSummary(key: String): String? {
            return summaryCache[key]
        }
        
        // 保存内容到缓存
        fun saveSummaryToCache(key: String, content: String) {
            summaryCache[key] = content
        }
        
        // 格式化内容，将 <think></think> 转换为 markdown 引用格式
        fun formatContent(content: String): String {
            val thinkPattern = "<think>(.*?)</think>".toRegex(RegexOption.DOT_MATCHES_ALL)
            return content.replace(thinkPattern) { matchResult ->
                val thinkContent = matchResult.groupValues[1].trim()
                "\n> $thinkContent\n"
            }
        }
    }
}


public class SecondNavigationTitleViewV5 extends LinearLayout {
    // ... existing code ...
    
    private void setAIBtnClick() {
        if (mAIBtn == null) {
            return;
        }
        
        mAIBtn.setOnClickListener(v -> {
            // 生成缓存 key（可以根据实际需求修改）
            String cacheKey = "summary_" + appModel.getAppId();
            
            // 检查是否有缓存
            String cachedContent = AIHelpRequest.Companion.getCachedSummary(cacheKey);
            if (cachedContent != null) {
                // 有缓存直接显示
                showAISummaryDialog(cachedContent);
                return;
            }
            
            // 没有缓存则发起请求
            String prompt = "帮我总结一下" + appModel.getAppName() + "这个应用";
            AIHelpRequest aiHelpRequest = new AIHelpRequest();
            aiHelpRequest.sendAIHelpRequestForSummary(prompt, true, "ds", new AIHelpRequest.AIHelpRequestCallback() {
                @Override
                public void onRequestSuccess(String content) {
                    // 格式化内容
                    String formattedContent = AIHelpRequest.Companion.formatContent(content);
                    // 保存到缓存
                    AIHelpRequest.Companion.saveSummaryToCache(cacheKey, formattedContent);
                    // 显示对话框
                    showAISummaryDialog(formattedContent);
                }

                @Override
                public void onRequestFailed(int code, String msg) {
                    ToastUtils.showToast(msg);
                }
            });
        });
    }
    
    private void showAISummaryDialog(String content) {
        if (context instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) context;
            AISummaryDialogFragment dialogFragment = AISummaryDialogFragment.newInstance(appModel.getAppName());
            dialogFragment.show(activity.getSupportFragmentManager(), "AISummaryDialog");
            // 设置内容
            dialogFragment.setContentText(content);
        }
    }
    
    // ... existing code ...
}



class AISummaryDialogFragment : DialogFragment() {
    // ... existing code ...
    
    fun setContentText(content: String) {
        contents.append(content)
        XLog.i(TAG, "content: $content")
        
        // 如果是缓存的内容，直接显示，不需要加载动画
        if (content == AIHelpRequest.getCachedSummary("summary_${arguments?.getString(ARG_APP_NAME)}")) {
            showContent()
            markwon.setMarkdown(tvContent, content)
            return
        }
        
        // 非缓存内容的处理逻辑
        if (content.contains("</answer>")) {
            stopLoading()
        } else {
            startLoading()
        }
        
        markwon.setMarkdown(tvContent, contents.toString() + loadingDots.toString())
        
        val scrollView = tvContent.parent as? ScrollView
        scrollView?.post {
            scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }
    
    // ... existing code ...
}


# 改造单例

- 避免重复创建实例

class AIHelpRequest private constructor() {
    companion object {
        private const val TAG = "sendAIHelpRequest"
        private const val REQUEST_URL = "http://stream-server-online-hyaide-app.turbotke.production.polaris:8080/openapi/app_platform/app_create"
        
        @Volatile
        private var instance: AIHelpRequest? = null
        
        @JvmStatic
        fun getInstance(): AIHelpRequest {
            return instance ?: synchronized(this) {
                instance ?: AIHelpRequest().also { instance = it }
            }
        }
    }

}

使用：
AIHelpRequest aiHelpRequest = AIHelpRequest.getInstance();



# by lazy

// ... existing code ...

class AISummaryDialogFragment : DialogFragment() {
    // 将 markwon 的初始化移到属性声明处
    private val markwon: Markwon by lazy { 
        Markwon.create(requireContext())
    }
    
    // 不再需要在 initView 中初始化 markwon
    private fun initView(rootView: View) {
        val appName = arguments?.getString(ARG_APP_NAME) ?: ""

        // 初始化标题
        val tvTitle = rootView.findViewById<TextView>(R.id.tv_title)
        tvTitle.text = "一眼看懂" + appName

        // 初始化各个视图
        loadingView = rootView.findViewById(R.id.loading_layout)
        contentView = rootView.findViewById(R.id.content_layout)
        tvContent = rootView.findViewById(R.id.tv_content)
        errorView = rootView.findViewById(R.id.error_layout)
        tvError = rootView.findViewById(R.id.tv_error)

        // 初始显示loading
        showLoading()

        // 初始化关闭按钮
        val ivClose = rootView.findViewById<ImageView>(R.id.iv_close)
        ivClose.setOnClickListener {
            dismissAllowingStateLoss()
        }
    }

    // ... rest of the code ...
}

改动：
- 使用 by lazy 委托来延迟初始化 markwon 属性
- 移除了原来在 initView 中的 markwon 初始化代码
这样可以确保：
- markwon 在第一次使用时才会被初始化
- 初始化时一定有可用的 Context
- 避免了 UninitializedPropertyAccessException 异常
- 这个改动可以解决 lateinit property markwon has not been initialized 的问题，因为使用 by lazy 可以保证属性在使用前一定会被初始化。


#  Fragment AISummaryDialogFragment{7220d0c} (4785dbcb-b65c-4617-9316-cbed87f1dec6 tag=ai_summary) not attached to a context.

// ... existing code ...

public void setAIBtnClick(String appName) {
    if (mAIBtn == null) {
        XLog.e(TAG, "mAIBtn is null");
        return;
    }
    mAIBtn.setOnClickListener(new OnTMAParamClickListener() {
        @Override
        public void onTMAClick(View v) {
            if (!(context instanceof FragmentActivity)) {
                return;
            }

            FragmentActivity activity = (FragmentActivity) context;
            if (activity.isFinishing()) {
                return;
            }

            if (appName == null) {
                XLog.i(TAG, "AIBtn click, appName is null");
                return;
            }

            AIHelpRequest aiHelpRequest = AIHelpRequest.getInstance();
            String cacheKey = "summary_" + appName;
            String cachedContent = aiHelpRequest.getCachedSummary(cacheKey);
            
            // 创建并显示对话框
            final AISummaryDialogFragment dialog = AISummaryDialogFragment.newInstance(appName);
            dialog.show(activity.getSupportFragmentManager(), "ai_summary");

            // 使用 DefaultLifecycleObserver 替代 @OnLifecycleEvent
            dialog.getLifecycle().addObserver(new DefaultLifecycleObserver() {
                @Override
                public void onResume(@NonNull LifecycleOwner owner) {
                    if (cachedContent != null) {
                        XLog.i(TAG, "AIBtn click, show cached content: " + cachedContent);
                        dialog.setContentText(cachedContent, appName);
                    } else {
                        try {
                            HandlerUtils.getDefaultHandler().post(() -> {
                                try {
                                    sendAIHelpRequest(dialog, appName, cacheKey, aiHelpRequest);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    XLog.e(TAG, "aiHelpListener: request hunyuan api failed.");
                                }
                            });
                        } catch (Exception e) {
                            e.printStackTrace();
                            XLog.e(TAG, "aiHelpListener: click exe failed.");
                        }
                    }
                    // 完成后移除观察者
                    dialog.getLifecycle().removeObserver(this);
                }
            });
        }
    });
}

// ... existing code ...


主要修改点:
- 将内容设置和请求逻辑移到 Fragment 的 ON_RESUME 生命周期中执行,确保 Fragment 已经完全 attach 到 Context
- 使用 LifecycleObserver 来监听 Fragment 的生命周期状态,只有在 Fragment 完全准备好后才执行相关操作
- 在完成操作后移除 LifecycleObserver,避免内存泄漏
这样修改后,可以确保在 Fragment 完全准备好后再执行需要 Context 的操作,从而避免 IllegalStateException。
让我知道如果你需要更多解释或有任何其他问题。


# 注意要 添加这个 HandlerUtils.getMainHandler().post 否则弹窗会显示 dialog is not added

HandlerUtils.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            if (!dialog.isAdded()) {
                                XLog.i(TAG, "dialog is not added");
                                return;
                            }
                            if (!dialog.isContentShowing()) {
                                XLog.i(TAG, "dialog is not content showing");
                                dialog.showContent();
                            }
                            XLog.i(TAG, "cachedSummary: " + cachedSummary);
                            dialog.setContentTextFromCache(cachedSummary);
                        }
                    });


# markdown支持改变字体颜色，需要引入支持html解析的插件

<font color="#FF0000">嗯，用户让我测评QQ这个APP，按照他们的要求，我需要用用户视角，避免专业术语</font>

