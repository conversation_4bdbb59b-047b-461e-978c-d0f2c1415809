
# 大模型协助
prompt提出根据不同类型的app，针对用户的下载决策关注点，介绍总结app。

# 构建索引-决策点

# 社交类

- 核心功能与社交效率
  - 目标用户群体匹配度
    - 用户社交目的（高知、灵魂、职场、陌生人、LGBTQ+）：如「Soul」主打兴趣灵魂匹配，「LinkedIn」专注职场社交，「Tinder」侧重陌生人约会。 
    - 用户基数与活跃度（高DAU）：高DAU（日活用户）意味着更高的匹配成功率（如微信“附近的人” vs 小众社交APP的空旷感）。
  - 连接方式创新性
    - 匹配机制：算法精准度（如根据音乐品味推荐好友）、破冰功能（如「探探」的滑动匹配、「Houseparty」的实时视频游戏）。 
    - 内容互动形式：动态广场（如微博）、语音聊天室（如Clubhouse）、匿名树洞（如Jodel）。
- 隐私与安全
  - 身份控制与匿名性
    - 是否支持匿名或虚拟身份（如「啫喱」的3D虚拟形象），实名认证强制程度（如职场类APP需真实职业信息）。
    - 隐私设置自由度：如隐藏在线状态、限制陌生人查看朋友圈。
    - 特定人群（如LGBTQ+）的隐私保护
  - 内容安全与举报机制
    - 敏感信息过滤（如AI屏蔽色情图片）、骚扰拦截（如「Bumble」仅允许女性先发起聊天）。
    - 用户对举报反馈速度的看重（差评中常出现“客服不处理骚扰信息”）。
- 社交体验与粘性
  - 界面与互动流畅度
    - 消息发送稳定性（延迟、崩溃率）、语音/视频通话清晰度（如「Zoom」在疫情期间被用于社交）。 
    - 信息流推荐逻辑：是否过度信息轰炸（如部分陌生人社交APP的推送频繁引发卸载）。
  - 社区氛围管理
    - 用户质量审核：如「知乎」早期邀请制塑造精英社区，「小红书」打击炫富内容后的用户评价变化。  
    - 社交货币体系：积分、等级、虚拟礼物等设计（如「贴吧」的等级头衔促进活跃度）。


# 游戏类

- 下载建议
推荐人群：偏好写实画风/团队协作/深度竞技体验的玩家
慎选人群：①存储空间<5GB设备 ②偏好二次元画风用户
最佳体验配置：骁龙870/天玑1200及以上处理器 + 12GB RAM

- 特色功能亮点
震动反馈系统：256种场景震动差异
听声辩位优化：7.1声道虚拟环绕声
赛事系统：PEL职业联赛同步观赛功能


同类型FPS手游横向评测

维度	和平精英	CODM	荒野行动
安装包体	3.8GB	2.1GB	1.9GB
帧率上限	120fps	90fps	60fps
特色玩法	火力对决模式	连杀技能系统	天气事件系统
氪金影响平衡	无属性加成	部分枪械皮肤加成	外观类道具
反外挂系统	实时巡查系统	举报反馈系统	常规检测系统