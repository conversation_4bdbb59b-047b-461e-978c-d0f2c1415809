^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[31737,31937][2025-04-02 +0800 11:32:02]
get mmap time: 3
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:76846891008 available:76712673280
log dir space info, capacity:117409054720 free:76691701760 available:76691701760
[I][2025-04-02 +80 11:32:02.795][31737, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 11:32:02.796][31737, 40][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922671.cd.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2671', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][RightlySDKManager][initSDK time =319
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk, false
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:32:02.798][31737, 40][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[E][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 11:32:02.799][31737, 40][temporary-2][unknown][AstApp][][xlog init finished:unknown
[E][2025-04-02 +80 11:32:02.856][31737, 37][temporary-1][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 11:32:02.883][31737, 56][StartThread-56][unknown][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:32:02.887][31737, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 11:32:02.888][31737, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 11:32:02.925][31737, 53][temporary-8][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:32:02.925][31737, 41][temporary-3][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:02.927][31737, 43][temporary-5][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:02.930][31737, 46][temporary-6][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:32:02.935][31737, 41][temporary-3][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:02.946][31737, 46][temporary-6][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:02.952][31737, 43][temporary-5][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:02.953][31737, 53][temporary-8][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:03.051][31737, 62][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[E][2025-04-02 +80 11:32:03.059][31737, 37][temporary-1][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 11:32:03.070][31737, 37][temporary-1][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 11:32:03.070][31737, 37][temporary-1][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 11:32:03.071][31737, 37][temporary-1][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 11:32:03.301][31737, 56][StartThread-56][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:32:03.301][31737, 56][StartThread-56][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:32:03.303][31737, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:03.303][31737, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:03.325][31737, 37][temporary-1][unknown][ActivityThreadHacker][][start Hook successful, dur = 522
[I][2025-04-02 +80 11:32:03.473][31737, 56][StartThread-56][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 11:32:03.483][31737, 56][StartThread-56][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 11:32:03.486][31737, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@5724613
[I][2025-04-02 +80 11:32:03.499][31737, 56][StartThread-56][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 11:32:03.500][31737, 56][StartThread-56][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 11:32:03.526][31737, 56][StartThread-56][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 11:32:03.580][31737, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:03.580][31737, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:03.588][31737, 56][StartThread-56][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 11:32:03.591][31737, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:03.591][31737, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:03.680][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 471, dataMap.size = 321, memSize = 325.408203125
[I][2025-04-02 +80 11:32:04.778][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 1, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:04.785][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-02 +80 11:32:04.786][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:04.786][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-02 +80 11:32:04.787][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 13, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:04.788][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-02 +80 11:32:04.788][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = UNKNOWN_VIA, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:32:04.789][31737, 70][Binder:31737_6][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[E][2025-04-02 +80 11:32:07.428][31737, 38][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922671.ha0.xc.e(ProGuard:82)
	at yyb8922671.ha0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922671.ja0.xb$xb.c(ProGuard:52)
	at yyb8922671.o90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 11:32:07.745][31737, 41][temporary-3][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 11:32:07.759][31737, 41][temporary-3][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 11:32:09.684][31737, 45][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:09.685][31737, 45][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:09.699][31737, 45][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:09.700][31737, 45][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:09.705][31737, 45][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 11:32:09.748][31737, 93][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 11:32:14.760][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 11:32:20.182][31737, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 11:32:23.561][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 11:32:24.395][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 321
[I][2025-04-02 +80 12:03:21.157][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 12:58:11.060][31737, 1*][main][unknown][FLog_MainBinderManager][][onServiceDisconnected,processFlag:unknown
[I][2025-04-02 +80 12:58:11.077][31737, 46][temporary-6][unknown][MainBinderManager][][tryToConnect process:unknown
[E][2025-04-02 +80 12:58:11.351][31737, 46][temporary-6][unknown][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+25m53s6ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 12:58:11.354][31737, 46][temporary-6][unknown][MainBinderManager][][tryToConnect process:unknown
[E][2025-04-02 +80 12:58:11.392][31737, 46][temporary-6][unknown][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+25m53s282ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 14:08:43.354][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:08:48.410][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:10:54.244][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 14:10:55.579][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-02 +80 14:10:57.274][31737, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 14:12:32.202][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:13:19.603][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-02 +80 14:15:23.250][31737, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 14:15:23.279][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 14:15:24.089][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:15:25.336][31737, 46][temporary-6][unknown][GdtAdSdkInitTask][][Not main or daemon process
[I][2025-04-02 +80 14:19:43.246][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 14:19:44.150][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:18.971][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:32.823][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 14:23:33.166][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:34.823][31737, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 14:23:35.221][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:27:32.219][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:33:19.295][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:42:32.259][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:43:19.122][31737, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
