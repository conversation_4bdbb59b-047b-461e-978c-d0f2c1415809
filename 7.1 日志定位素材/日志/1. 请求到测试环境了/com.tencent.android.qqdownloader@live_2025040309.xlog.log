^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[18224,18288][2025-04-03 +0800 08:53:26]
get mmap time: 42
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:66875658240 available:66741440512
log dir space info, capacity:117409054720 free:66720468992 available:66720468992
[I][2025-04-03 +80 08:53:26.497][18224, 37][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:53:26.497][18224, 37][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager][initSDK time =248
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:53:26.498][18224, 37][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:53:26.499][18224, 37][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:53:26.500][18224, 37][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:53:26.501][18224, 37][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:53:26.501][18224, 37][temporary-1][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:53:26.501][18224, 37][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:26.501][18224, 37][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-03 +80 08:53:26.501][18224, 45][temporary-7][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:26.503][18224, 40][temporary-2][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:53:26.503][18224, 37][temporary-1][live][AstApp][][xlog init finished:live
[I][2025-04-03 +80 08:53:26.503][18224, 45][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:53:26.504][18224, 40][temporary-2][live][BinderManager][][addService, service : true
[E][2025-04-03 +80 08:53:26.523][18224, 41][temporary-3][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 08:53:26.537][18224, 41][temporary-3][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 08:53:26.537][18224, 41][temporary-3][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 08:53:26.537][18224, 41][temporary-3][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 08:53:26.537][18224, 41][temporary-3][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 08:53:26.542][18224, 42][temporary-4][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:53:26.543][18224, 42][temporary-4][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:53:26.606][18224, 41][temporary-3][live][ActivityThreadHacker][][start Hook successful, dur = 98
[I][2025-04-03 +80 08:53:26.642][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 278, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:53:26.646][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 269, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:53:27.916][18224, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-03 +80 08:53:27.921][18224, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-03 +80 08:53:27.921][18224, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-03 +80 08:53:27.952][18224, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:53:27.956][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.956][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.956][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.956][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.957][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.958][18224, 41][temporary-3][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:53:27.958][18224, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:53:27.968][18224, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-03 +80 08:53:28.021][18224, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@fc5e462
[I][2025-04-03 +80 08:53:28.023][18224, 72][Binder:18224_6][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-03 +80 08:53:28.026][18224, 72][Binder:18224_6][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-03 +80 08:53:28.026][18224, 72][Binder:18224_6][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-03 +80 08:53:31.322][18224, 37][temporary-1][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:53:31.343][18224, 37][temporary-1][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-03 +80 08:53:31.408][18224, 45][temporary-7][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:53:33.254][18224, 52][BeaconReportHandler][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:33.255][18224, 52][BeaconReportHandler][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:53:33.265][18224, 52][BeaconReportHandler][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:53:33.269][18224, 52][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:33.271][18224, 52][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:33.278][18224, 52][BeaconReportHandler][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:53:38.335][18224, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:54:15.889][18224, 72][Binder:18224_6][live][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 1, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:54:15.896][18224, 72][Binder:18224_6][live][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:54:15.896][18224, 72][Binder:18224_6][live][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:54:15.897][18224, 72][Binder:18224_6][live][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:54:15.898][18224, 29][Binder:18224_3][live][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 13, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:54:15.899][18224, 29][Binder:18224_3][live][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:54:15.899][18224, 29][Binder:18224_3][live][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = UNKNOWN_VIA, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:54:15.901][18224, 29][Binder:18224_3][live][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:54:37.136][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:54:37.151][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:55:24.109][18224, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:55:24.111][18224, 41][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:55:24.116][18224, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:55:24.123][18224, 41][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:55:24.127][18224, 44][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:55:24.152][18224, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:55:24.154][18224, 41][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:55:24.159][18224, 44][temporary-6][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 08:55:56.022][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:55:56.025][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:56:10.220][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:57:02.499][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:57:03.061][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:57:03.095][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:58:04.238][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:58:50.452][18224, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-03 +80 08:59:20.007][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:59:20.725][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:59:20.725][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:00:17.063][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:00:17.079][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 09:01:31.152][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 09:02:13.349][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 09:02:14.292][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:02:14.311][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:03:20.142][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:03:20.150][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 09:06:52.560][18224, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 09:13:42.174][18224, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:13:42.274][18224, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
~~~~~ end of mmap ~~~~~[30531,30672][2025-04-03 +0800 09:28:23]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[30531,30672][2025-04-03 +0800 09:28:23]
get mmap time: 7
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:63323119616 available:63188901888
log dir space info, capacity:117409054720 free:63167930368 available:63167930368
