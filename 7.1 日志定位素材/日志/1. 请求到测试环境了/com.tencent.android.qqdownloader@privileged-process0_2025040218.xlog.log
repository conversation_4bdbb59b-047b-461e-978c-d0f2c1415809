[I][2025-04-02 +80 17:53:18.970][10451, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:53:18.971][10451, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:53:18.971][10451, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:53:18.971][10451, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:53:18.971][10451, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =93
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk, false
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[E][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:53:18.972][10451, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:53:18.973][10451, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 17:53:18.975][10451, 47][temporary-8][unknown][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:53:18.982][10451, 57][StartThread-57][unknown][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:18.985][10451, 1*][main][unknown][FLog_qqlive_log][][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[E][2025-04-02 +80 17:53:19.003][10451, 41][temporary-4][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 17:53:19.005][10451, 41][temporary-4][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:53:19.005][10451, 41][temporary-4][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:53:19.005][10451, 41][temporary-4][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:53:19.005][10451, 41][temporary-4][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:53:19.024][10451, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:53:19.029][10451, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:53:19.036][10451, 44][SendEventDispatcher][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:53:19.038][10451, 44][SendEventDispatcher][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:53:19.046][10451, 47][temporary-8][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:53:19.048][10451, 41][temporary-4][unknown][ActivityThreadHacker][][start Hook successful, dur = 67
[I][2025-04-02 +80 17:53:19.051][10451, 40][temporary-3][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:53:19.082][10451, 47][temporary-8][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:53:19.110][10451, 40][temporary-3][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:53:19.218][10451, 61][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:53:19.376][10451, 57][StartThread-57][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:53:19.377][10451, 57][StartThread-57][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:53:19.406][10451, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:19.408][10451, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:19.602][10451, 57][StartThread-57][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:53:19.628][10451, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@69bbd02
[I][2025-04-02 +80 17:53:19.654][10451, 57][StartThread-57][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:53:19.654][10451, 57][StartThread-57][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:53:19.666][10451, 57][StartThread-57][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:53:19.686][10451, 61][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 389, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:19.742][10451, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:19.746][10451, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:19.754][10451, 57][StartThread-57][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:53:19.769][10451, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:19.770][10451, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 17:53:23.747][10451, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:53:23.954][10451, 39][temporary-2][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:53:23.965][10451, 39][temporary-2][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:53:25.915][10451, 51][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:53:25.915][10451, 51][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:53:25.928][10451, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:25.930][10451, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:25.934][10451, 51][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:25.961][10451, 94][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:53:30.994][10451, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 17:57:34.540][10451, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:03:16.571][10451, 61][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[12573,12688][2025-04-02 +0800 18:09:19]
get mmap time: 3
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77158830080 available:77024612352
log dir space info, capacity:117409054720 free:77003640832 available:77003640832
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager][initSDK time =83
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 18:09:19.803][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][XLog][init, proccess:unknown
[E][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[E][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][MMKVInitiator][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 18:09:19.819][12573, 37][temporary-1][cache][unknown][MMKVInitiator][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:19.820][12573, 37][temporary-1][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 18:09:19.858][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 108, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:19.860][12573, 61][StartThread-61][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 18:09:19.866][12573, 61][StartThread-61][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 18:09:19.866][12573, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@c851f76
[I][2025-04-02 +80 18:09:19.869][12573, 61][StartThread-61][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 18:09:19.870][12573, 61][StartThread-61][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 18:09:19.885][12573, 61][StartThread-61][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 18:09:19.897][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 148, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:19.967][12573, 61][StartThread-61][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:19.968][12573, 61][StartThread-61][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:19.970][12573, 61][StartThread-61][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 18:09:19.974][12573, 61][StartThread-61][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:19.975][12573, 61][StartThread-61][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:22.738][12573, 47][temporary-8][unknown][GdtAdSdkInitTask][][Not main or daemon process
[E][2025-04-02 +80 18:09:24.476][12573, 38][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 18:09:24.674][12573, 43][temporary-5][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 18:09:24.684][12573, 43][temporary-5][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 18:09:26.661][12573, 53][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 18:09:26.662][12573, 53][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 18:09:26.678][12573, 53][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:26.680][12573, 53][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:26.685][12573, 53][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:26.711][12573, 97][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 18:09:31.715][12573, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:12:34.581][12573, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:13:01.534][12573, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 18:13:02.081][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:13:02.085][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.562][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.562][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:27:34.615][12573, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:29:17.638][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:29:17.652][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.737][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.737][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:42:34.648][12573, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:49:17.851][12573, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:49:17.865][12573, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
