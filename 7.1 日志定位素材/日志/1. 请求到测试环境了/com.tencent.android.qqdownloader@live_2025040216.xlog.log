[I][2025-04-02 +80 15:32:42.850][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 15:51:43.650][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 15:53:27.139][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 15:53:27.154][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 15:57:34.186][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 16:03:27.167][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:03:27.186][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[24212,25045][2025-04-02 +0800 16:11:58]
get mmap time: 2
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77363052544 available:77228834816
log dir space info, capacity:117409054720 free:77207863296 available:77207863296
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 16:11:58.542][24212, 40][temporary-3][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][RightlySDKManager][initSDK time =370
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~p0GQboOisW2kuWOdabQong==/com.tencent.android.qqdownloader-6g2Vf1eIMkMwXLO_oVAGnQ==/base.apk, true
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~p0GQboOisW2kuWOdabQong==/com.tencent.android.qqdownloader-6g2Vf1eIMkMwXLO_oVAGnQ==/base.apk
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~p0GQboOisW2kuWOdabQong==/com.tencent.android.qqdownloader-6g2Vf1eIMkMwXLO_oVAGnQ==/base.apk
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 16:11:58.543][24212, 40][temporary-3][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][RdefenseInitTask][init
[E][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][STGlobal][syncAppCallerFromDeamon result Bundle == null
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 6
[I][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[E][2025-04-02 +80 16:11:58.544][24212, 40][temporary-3][cache][live][STGlobal][syncAppViaFromDeamon result Bundle == null
[I][2025-04-02 +80 16:11:58.545][24212, 40][temporary-3][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 16:11:58.545][24212, 40][temporary-3][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 16:11:58.564][24212, 40][temporary-3][cache][live][STGlobal][setAppCaller(64, 1743581517730) from (6), from progress = live
java.lang.RuntimeException
	at yyb8922819.tb.xg.l(ProGuard:384)
	at yyb8922819.tb.xg.k(ProGuard:379)
	at yyb8922819.bb.xs.o(ProGuard:112)
	at com.tencent.assistant.db.contentprovider.WallpaperProvider.c(ProGuard:280)
	at com.live.utils.LiveUtils.startYYB(ProGuard:75)
	at com.tencent.assistant.utils.SysComponentHelper.n(ProGuard:304)
	at com.tencent.assistant.syscomponent.BaseSysComponentProvider.query(ProGuard:40)
	at android.content.ContentProvider.query(ContentProvider.java:1411)
	at android.content.ContentProvider.query(ContentProvider.java:1507)
	at android.content.ContentProvider$Transport.query(ContentProvider.java:275)
	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:107)
	at android.os.Binder.execTransactInternal(Binder.java:1197)
	at android.os.Binder.execTransact(Binder.java:1156)
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 16:11:58.565][24212, 40][temporary-3][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 332, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 530, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 518, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 475, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 16:11:58.566][24212, 40][temporary-3][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 16:11:58.571][24212, 40][temporary-3][live][AstApp][][xlog init finished:live
[E][2025-04-02 +80 16:11:58.577][24212, 43][temporary-6][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 16:11:58.577][24212, 43][temporary-6][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 16:11:58.578][24212, 43][temporary-6][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 16:11:58.578][24212, 43][temporary-6][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 16:11:58.643][24212, 43][temporary-6][live][ActivityThreadHacker][][start Hook successful, dur = 115
[I][2025-04-02 +80 16:11:58.656][24212, 43][temporary-6][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 16:11:58.657][24212, 43][temporary-6][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 16:12:59.359][24212, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 16:12:59.361][24212, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 16:12:59.379][24212, 43][temporary-6][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 16:12:59.382][24212, 39][temporary-2][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 16:12:59.389][24212, 45][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 16:12:59.390][24212, 45][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 16:12:59.593][24212, 39][temporary-2][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 16:12:59.667][24212, 43][temporary-6][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 16:12:59.668][24212, 43][temporary-6][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 16:12:59.839][24212, 47][temporary-8][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 16:12:59.993][24212, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.234][24212, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:12:59.994][24212, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.242][24212, 43][temporary-6][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 16:13:00.352][24212, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:12:59.994][24212, 44][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.355][24212, 44][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:12:59.995][24212, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.376][24212, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:12:59.995][24212, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:12:59.999][24212, 53][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.383][24212, 53][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.389][24212, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:00.401][24212, 53][BeaconReportHandler][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:00.437][24212, 42][temporary-5][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 16:13:01.026][24212, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:01.038][24212, 44][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:01.074][24212, 36][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:01.078][24212, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:01.083][24212, 39][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:01.198][24212, 44][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.203][24212, 44][temporary-7][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 16:13:01.203][24212, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.204][24212, 40][temporary-3][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 16:13:01.213][24212, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.216][24212, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.217][24212, 41][temporary-4][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 16:13:01.271][24212, 36][temporary-1][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 16:13:01.473][24212, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.475][24212, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:13:01.848][24212, 42][temporary-5][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 16:13:05.846][24212, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 16:13:07.680][24212, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:07.749][24212, 62][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:07.713][24212, 65][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:07.799][24212, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:09.329][24212, 111][Binder:24212_6][live][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 64, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 16:13:09.334][24212, 111][Binder:24212_6][live][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 54
[I][2025-04-02 +80 16:13:09.334][24212, 111][Binder:24212_6][live][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 16:13:09.336][24212, 111][Binder:24212_6][live][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 16:13:10.632][24212, 62][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:10.651][24212, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:10.657][24212, 65][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:13:10.663][24212, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:23:06.649][24212, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:23:06.688][24212, 65][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:23:06.720][24212, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 16:23:06.734][24212, 62][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
