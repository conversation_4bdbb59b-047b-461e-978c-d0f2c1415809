~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 15:32:42.850][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 15:51:43.618][31737, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[30537,30657][2025-04-02 +0800 17:39:50]
~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[30537,30657][2025-04-02 +0800 17:39:50]
get mmap time: 5
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77434183680 available:77299965952
log dir space info, capacity:117409054720 free:77278994432 available:77278994432
[I][2025-04-02 +80 17:39:50.474][30537, 37][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:39:50.474][30537, 37][temporary-1][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:39:50.475][30537, 37][temporary-1][cache][unknown][RightlySDKManager][initSDK time =64
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk, false
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:39:50.476][30537, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:39:50.477][30537, 37][temporary-1][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:39:50.478][30537, 37][temporary-1][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:39:50.478][30537, 37][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:39:50.478][30537, 37][temporary-1][unknown][AstApp][][xlog init finished:unknown
[E][2025-04-02 +80 17:39:50.487][30537, 43][temporary-5][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 17:39:50.501][30537, 43][temporary-5][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:39:50.502][30537, 43][temporary-5][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:39:50.502][30537, 43][temporary-5][unknown][Monitor][][Monitor init fail. key = activity_monitor
[I][2025-04-02 +80 17:39:50.503][30537, 62][StartThread-62][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:39:50.503][30537, 62][StartThread-62][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[E][2025-04-02 +80 17:39:50.503][30537, 43][temporary-5][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:39:50.508][30537, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:39:50.509][30537, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:39:50.510][30537, 62][StartThread-62][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:50.510][30537, 62][StartThread-62][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:50.511][30537, 45][SendEventDispatcher][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:50.512][30537, 42][temporary-4][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:39:50.512][30537, 44][temporary-6][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:50.522][30537, 45][SendEventDispatcher][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:39:50.528][30537, 42][temporary-4][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:39:50.529][30537, 44][temporary-6][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:39:50.583][30537, 43][temporary-5][unknown][ActivityThreadHacker][][start Hook successful, dur = 108
[I][2025-04-02 +80 17:39:50.603][30537, 62][StartThread-62][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:39:50.607][30537, 62][StartThread-62][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:39:50.607][30537, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@220f238
[I][2025-04-02 +80 17:39:50.611][30537, 62][StartThread-62][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:39:50.612][30537, 62][StartThread-62][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:39:50.621][30537, 62][StartThread-62][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:39:50.686][30537, 62][StartThread-62][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:50.686][30537, 62][StartThread-62][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:50.690][30537, 62][StartThread-62][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:39:50.697][30537, 62][StartThread-62][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:50.697][30537, 62][StartThread-62][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:50.801][30537, 59][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 318, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:39:50.828][30537, 63][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 345, dataMap.size = 322, memSize = 325.859375
[E][2025-04-02 +80 17:39:55.390][30537, 38][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:39:55.438][30537, 47][temporary-8][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:39:55.450][30537, 47][temporary-8][unknown][CMNetworkCallbackHook][][source map is empty
[E][2025-04-02 +80 17:39:55.528][30537, 72][pool-5-thread-1][unknown][RightlySDKManager_PandoraEx.ATTAReporter][][doPost err
java.net.SocketException: socket failed: EPERM (Operation not permitted)
	at java.net.Socket.createImpl(Socket.java:492)
	at java.net.Socket.getImpl(Socket.java:552)
	at java.net.Socket.setSoTimeout(Socket.java:1180)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:143)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at com.tencent.raft.measure.report.ATTAReporter.doPost(ProGuard:92)
	at com.tencent.raft.measure.report.ATTAReporter.doPostBatchReport(ProGuard:63)
	at yyb8922819.pf0.xb.h(ProGuard:113)
	at yyb8922819.pf0.xb$xb.run(ProGuard:65)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 17:39:57.445][30537, 53][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:57.446][30537, 53][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:39:57.461][30537, 53][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:57.463][30537, 53][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:57.471][30537, 53][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:57.494][30537, 98][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:40:02.498][30537, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[32259,32427][2025-04-02 +0800 17:42:11]
~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[32259,32427][2025-04-02 +0800 17:42:11]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77429731328 available:77295513600
log dir space info, capacity:117409054720 free:77274542080 available:77274542080
[I][2025-04-02 +80 17:42:11.279][32259, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =179
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:42:11.280][32259, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk, false
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:11.281][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:42:11.282][32259, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[E][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][cache][unknown][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:42:11.283][32259, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 17:42:11.295][32259, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:42:11.298][32259, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:42:11.299][32259, 44][temporary-6][unknown][ActivityThreadHacker][][start Hook successful, dur = 69
[I][2025-04-02 +80 17:42:11.300][32259, 43][SendEventDispatcher][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:42:11.301][32259, 41][temporary-4][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:42:11.301][32259, 43][SendEventDispatcher][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:11.306][32259, 42][temporary-5][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:42:11.316][32259, 42][temporary-5][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:11.316][32259, 41][temporary-4][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:11.332][32259, 58][StartThread-58][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:42:11.336][32259, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@ef9e4
[I][2025-04-02 +80 17:42:11.337][32259, 58][StartThread-58][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:42:11.338][32259, 58][StartThread-58][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:42:11.343][32259, 58][StartThread-58][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:42:11.373][32259, 58][StartThread-58][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:11.373][32259, 58][StartThread-58][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:11.376][32259, 58][StartThread-58][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:42:11.380][32259, 58][StartThread-58][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:11.380][32259, 58][StartThread-58][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:11.473][32259, 54][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 212, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:42:11.478][32259, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 216, dataMap.size = 322, memSize = 325.859375
[E][2025-04-02 +80 17:42:16.076][32259, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:42:16.203][32259, 44][temporary-6][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:42:16.207][32259, 44][temporary-6][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:42:18.194][32259, 51][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:42:18.195][32259, 51][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:18.207][32259, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:18.208][32259, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:18.213][32259, 51][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:42:18.248][32259, 93][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:42:23.251][32259, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 17:42:34.458][32259, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[3237,3473][2025-04-02 +0800 17:44:23]
~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[3237,3473][2025-04-02 +0800 17:44:23]
get mmap time: 6
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77384900608 available:77250682880
log dir space info, capacity:117409054720 free:77229711360 available:77229711360
[I][2025-04-02 +80 17:44:23.170][3237, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:44:23.174][3237, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:44:23.175][3237, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:44:23.175][3237, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =97
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk, false
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:44:23.176][3237, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:44:23.177][3237, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:44:23.178][3237, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[E][2025-04-02 +80 17:44:23.178][3237, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-02 +80 17:44:23.178][3237, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:44:23.178][3237, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:44:23.179][3237, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:44:23.180][3237, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:44:23.180][3237, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:44:23.180][3237, 39][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:44:23.180][3237, 39][temporary-2][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:44:23.180][3237, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 17:44:23.184][3237, 41][temporary-4][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:44:23.189][3237, 60][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:44:23.201][3237, 40][temporary-3][unknown][BinderManager][][addService, service : true
[E][2025-04-02 +80 17:44:23.239][3237, 44][temporary-6][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 17:44:23.251][3237, 44][temporary-6][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:44:23.252][3237, 44][temporary-6][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:44:23.255][3237, 44][temporary-6][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:44:23.255][3237, 44][temporary-6][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:44:23.327][3237, 44][temporary-6][unknown][ActivityThreadHacker][][start Hook successful, dur = 114
[I][2025-04-02 +80 17:44:23.384][3237, 56][StartThread-56][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:44:23.390][3237, 56][StartThread-56][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:44:23.390][3237, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@8b3d050
[I][2025-04-02 +80 17:44:23.395][3237, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 205, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:44:23.404][3237, 56][StartThread-56][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:44:23.404][3237, 56][StartThread-56][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:44:23.407][3237, 56][StartThread-56][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:44:23.485][3237, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:23.486][3237, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:44:23.491][3237, 56][StartThread-56][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:44:23.500][3237, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:23.500][3237, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 17:44:27.833][3237, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:44:28.081][3237, 39][temporary-2][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:44:28.098][3237, 39][temporary-2][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:44:30.079][3237, 52][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:44:30.081][3237, 52][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:44:30.118][3237, 52][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:30.130][3237, 52][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:30.142][3237, 52][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:44:30.163][3237, 90][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[E][2025-04-02 +80 17:44:32.919][3237, 66][pool-5-thread-1][unknown][RightlySDKManager_PandoraEx.ATTAReporter][][doPost err
java.net.SocketException: socket failed: EPERM (Operation not permitted)
	at java.net.Socket.createImpl(Socket.java:492)
	at java.net.Socket.getImpl(Socket.java:552)
	at java.net.Socket.setSoTimeout(Socket.java:1180)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:143)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at com.tencent.raft.measure.report.ATTAReporter.doPost(ProGuard:92)
	at com.tencent.raft.measure.report.ATTAReporter.doPostBatchReport(ProGuard:63)
	at yyb8922819.pf0.xb.b(ProGuard:221)
	at yyb8922819.pf0.xb$xc.run(ProGuard:177)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 17:44:35.266][3237, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[5247,5384][2025-04-02 +0800 17:45:52]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[5247,5384][2025-04-02 +0800 17:45:52]
get mmap time: 0
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77339959296 available:77205741568
log dir space info, capacity:117409054720 free:77184770048 available:77184770048
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =228
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~fRnkKj3pufRa3K_5W0v5dg==/com.tencent.android.qqdownloader-3dJlALmaIp9kB3S_lO27ag==/base.apk, false
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~fRnkKj3pufRa3K_5W0v5dg==/com.tencent.android.qqdownloader-3dJlALmaIp9kB3S_lO27ag==/base.apk
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:45:52.570][5247, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 17:45:52.575][5247, 43][temporary-5][unknown][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:45:52.581][5247, 50][temporary-7][unknown][RdefenseInitTask][][init
[I][2025-04-02 +80 17:45:52.588][5247, 40][temporary-3][unknown][WxApiWrapper][][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:45:52.599][5247, 44][temporary-6][unknown][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:45:52.599][5247, 51][temporary-8][unknown][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:45:52.602][5247, 55][StartThread-55][unknown][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:45:52.604][5247, 1*][main][unknown][FLog_qqlive_log][][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:45:52.635][5247, 56][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:45:52.635][5247, 59][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:45:52.652][5247, 55][StartThread-55][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:45:52.653][5247, 55][StartThread-55][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:45:52.656][5247, 55][StartThread-55][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:52.656][5247, 55][StartThread-55][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:45:52.694][5247, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:45:52.698][5247, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:45:52.706][5247, 51][temporary-8][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:52.710][5247, 44][temporary-6][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:52.711][5247, 39][temporary-2][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:45:52.712][5247, 44][temporary-6][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:52.713][5247, 41][temporary-4][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:45:52.765][5247, 41][temporary-4][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:52.806][5247, 56][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 158, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:45:52.896][5247, 59][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 245, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:45:52.898][5247, 39][temporary-2][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:52.903][5247, 51][temporary-8][unknown][BinderManager][][addService, service : true
[E][2025-04-02 +80 17:45:53.111][5247, 50][temporary-7][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:45:53.173][5247, 55][StartThread-55][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:45:53.188][5247, 55][StartThread-55][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:45:53.189][5247, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@f888977
[I][2025-04-02 +80 17:45:53.205][5247, 55][StartThread-55][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:45:53.206][5247, 55][StartThread-55][unknown][MainBinderManager][][addServiceOptimize, service true
[E][2025-04-02 +80 17:45:53.212][5247, 50][temporary-7][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:45:53.213][5247, 50][temporary-7][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:45:53.215][5247, 50][temporary-7][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:45:53.216][5247, 50][temporary-7][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:45:53.279][5247, 55][StartThread-55][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:45:53.361][5247, 50][temporary-7][unknown][ActivityThreadHacker][][start Hook successful, dur = 607
[I][2025-04-02 +80 17:45:53.551][5247, 55][StartThread-55][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:53.556][5247, 55][StartThread-55][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:45:53.602][5247, 55][StartThread-55][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:45:53.612][5247, 55][StartThread-55][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:53.613][5247, 55][StartThread-55][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 17:45:57.436][5247, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:45:57.575][5247, 51][temporary-8][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:45:57.588][5247, 51][temporary-8][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:45:59.559][5247, 49][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:59.559][5247, 49][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:59.573][5247, 49][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:59.575][5247, 49][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:59.583][5247, 49][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:45:59.612][5247, 93][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:46:04.614][5247, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[7078,7217][2025-04-02 +0800 17:47:17]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[7078,7217][2025-04-02 +0800 17:47:17]
get mmap time: 0
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77296840704 available:77162622976
log dir space info, capacity:117409054720 free:77141651456 available:77141651456
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:47:17.188][7078, 40][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][RightlySDKManager][initSDK time =86
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~oOXMQ9Mx-wZLSJYOvaWgog==/com.tencent.android.qqdownloader-kapZpIjlmlREAO-eWtpskA==/base.apk, false
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oOXMQ9Mx-wZLSJYOvaWgog==/com.tencent.android.qqdownloader-kapZpIjlmlREAO-eWtpskA==/base.apk
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oOXMQ9Mx-wZLSJYOvaWgog==/com.tencent.android.qqdownloader-kapZpIjlmlREAO-eWtpskA==/base.apk
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:47:17.189][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[E][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:47:17.190][7078, 40][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:47:17.191][7078, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:47:17.191][7078, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:17.191][7078, 40][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:47:17.191][7078, 40][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 17:47:17.193][7078, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:47:17.194][7078, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:47:17.196][7078, 45][temporary-6][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:17.196][7078, 46][temporary-7][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:47:17.196][7078, 47][temporary-8][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:17.197][7078, 37][temporary-1][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:47:17.197][7078, 37][temporary-1][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:17.197][7078, 47][temporary-8][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:17.198][7078, 45][temporary-6][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:17.203][7078, 46][temporary-7][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:17.206][7078, 62][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:17.213][7078, 41][temporary-3][unknown][ActivityThreadHacker][][start Hook successful, dur = 51
[I][2025-04-02 +80 17:47:17.216][7078, 57][StartThread-57][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:47:17.219][7078, 57][StartThread-57][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:47:17.222][7078, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:17.222][7078, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:17.267][7078, 57][StartThread-57][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:47:17.273][7078, 57][StartThread-57][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:47:17.273][7078, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@220f238
[I][2025-04-02 +80 17:47:17.282][7078, 57][StartThread-57][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:47:17.283][7078, 57][StartThread-57][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:47:17.288][7078, 62][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 80, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:17.296][7078, 57][StartThread-57][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:47:17.344][7078, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:17.345][7078, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:17.347][7078, 57][StartThread-57][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:47:17.352][7078, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:17.352][7078, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 17:47:21.963][7078, 38][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:47:22.111][7078, 43][temporary-5][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:47:22.118][7078, 43][temporary-5][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:47:24.109][7078, 51][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:24.110][7078, 51][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:24.135][7078, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:24.139][7078, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:24.149][7078, 51][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:24.164][7078, 92][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:47:29.263][7078, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[8726,8896][2025-04-02 +0800 17:51:22]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[8726,8896][2025-04-02 +0800 17:51:22]
get mmap time: 2
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:0 free:0 available:0
log dir space info, capacity:0 free:0 available:0
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:51:22.486][8726, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =239
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~SlJfva_u6OX5ek6aS1bs5A==/com.tencent.android.qqdownloader-B2viX1hTOCtlEbQUPPfp2w==/base.apk, false
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:51:22.487][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:51:22.488][8726, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~SlJfva_u6OX5ek6aS1bs5A==/com.tencent.android.qqdownloader-B2viX1hTOCtlEbQUPPfp2w==/base.apk
[I][2025-04-02 +80 17:51:22.488][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 17:51:22.499][8726, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][MMKVInitiator][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:51:22.500][8726, 39][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:22.501][8726, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:22.501][8726, 39][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 17:51:22.501][8726, 39][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-02 +80 17:51:22.501][8726, 39][temporary-2][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:51:22.501][8726, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[E][2025-04-02 +80 17:51:22.554][8726, 52][temporary-8][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:51:22.555][8726, 52][temporary-8][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:51:22.555][8726, 52][temporary-8][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:51:22.555][8726, 52][temporary-8][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:51:22.624][8726, 54][StartThread-54][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 17:51:22.640][8726, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@c489e9b
[I][2025-04-02 +80 17:51:22.644][8726, 54][StartThread-54][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 17:51:22.644][8726, 54][StartThread-54][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:51:22.653][8726, 54][StartThread-54][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 17:51:22.654][8726, 52][temporary-8][unknown][ActivityThreadHacker][][start Hook successful, dur = 209
[I][2025-04-02 +80 17:51:22.700][8726, 65][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 272, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:51:22.734][8726, 54][StartThread-54][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:22.735][8726, 54][StartThread-54][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:22.739][8726, 54][StartThread-54][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 17:51:22.747][8726, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 394, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:51:22.749][8726, 54][StartThread-54][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:22.749][8726, 54][StartThread-54][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 17:51:26.938][8726, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 17:51:27.160][8726, 36][temporary-1][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:51:27.175][8726, 36][temporary-1][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 17:51:29.129][8726, 48][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:51:29.130][8726, 48][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:51:29.161][8726, 48][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:29.166][8726, 48][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:29.177][8726, 48][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:29.191][8726, 93][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:51:34.255][8726, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[10451,10595][2025-04-02 +0800 17:53:18]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[10451,10595][2025-04-02 +0800 17:53:18]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77207425024 available:77073207296
log dir space info, capacity:117409054720 free:77052235776 available:77052235776
