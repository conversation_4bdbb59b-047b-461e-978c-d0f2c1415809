^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[32170,32254][2025-04-02 +0800 11:32:06]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:76845383680 available:76711165952
log dir space info, capacity:117409054720 free:76690194432 available:76690194432
[I][2025-04-02 +80 11:32:06.983][32170, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 11:32:06.983][32170, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: M<PERSON><PERSON><PERSON><PERSON>rap<PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:32:06.983][32170, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 11:32:06.983][32170, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 11:32:06.984][32170, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 11:32:06.984][32170, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 11:32:06.984][32170, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 11:32:06.984][32170, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922671.cd.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2671', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =196
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk, false
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:32:06.986][32170, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:06.987][32170, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:06.988][32170, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:32:06.988][32170, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 11:32:07.029][32170, 48][temporary-7][live][RdefenseInitTask][][init
[I][2025-04-02 +80 11:32:07.034][32170, 1*][main][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:32:07.066][32170, 60][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:32:07.066][32170, 57][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:32:07.068][32170, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:32:07.086][32170, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:32:07.092][32170, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[E][2025-04-02 +80 11:32:07.111][32170, 48][temporary-7][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 11:32:07.114][32170, 48][temporary-7][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 11:32:07.114][32170, 48][temporary-7][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 11:32:07.114][32170, 48][temporary-7][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 11:32:07.114][32170, 48][temporary-7][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 11:32:07.136][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 70, dataMap.size = 321, memSize = 325.*********
[I][2025-04-02 +80 11:32:07.148][32170, 40][temporary-3][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 11:32:07.165][32170, 40][temporary-3][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 11:32:07.165][32170, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 11:32:07.166][32170, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 11:32:07.167][32170, 41][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:07.167][32170, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 11:32:07.169][32170, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 11:32:07.169][32170, 41][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:07.169][32170, 40][temporary-3][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:07.170][32170, 43][temporary-5][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:32:07.179][32170, 40][temporary-3][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:07.180][32170, 43][temporary-5][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:07.183][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 116, dataMap.size = 321, memSize = 325.*********
[I][2025-04-02 +80 11:32:07.184][32170, 48][temporary-7][live][ActivityThreadHacker][][start Hook successful, dur = 123
[I][2025-04-02 +80 11:32:07.790][32170, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 11:32:07.792][32170, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 11:32:07.792][32170, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 11:32:07.806][32170, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 11:32:07.811][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[I][2025-04-02 +80 11:32:07.811][32170, 39][temporary-2][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 11:32:07.811][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.812][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.812][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.812][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.813][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.813][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.814][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.814][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.814][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.815][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.815][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.815][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.815][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.815][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[I][2025-04-02 +80 11:32:07.816][32170, 42][temporary-4][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 11:32:07.816][32170, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:32:07.820][32170, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 11:32:07.855][32170, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@8e5db86
[I][2025-04-02 +80 11:32:07.857][32170, 25][Binder:32170_1][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 11:32:07.859][32170, 25][Binder:32170_1][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 11:32:07.859][32170, 25][Binder:32170_1][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 11:32:12.031][32170, 42][temporary-4][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 11:32:12.045][32170, 42][temporary-4][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 11:32:12.089][32170, 44][temporary-6][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:32:13.990][32170, 53][BeaconReportHandler][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:32:13.991][32170, 53][BeaconReportHandler][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:32:13.995][32170, 53][BeaconReportHandler][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 11:32:14.004][32170, 53][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:14.004][32170, 53][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:32:14.010][32170, 53][BeaconReportHandler][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 11:32:19.049][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 11:32:20.183][32170, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 11:32:24.223][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 321
[I][2025-04-02 +80 11:32:24.242][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 321
[I][2025-04-02 +80 12:03:21.157][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 12:58:11.074][32170, 1*][main][live][FLog_MainBinderManager][][onServiceDisconnected,processFlag:live
[I][2025-04-02 +80 12:58:11.074][32170, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[I][2025-04-02 +80 12:58:11.080][32170, 49][temporary-8][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 12:58:11.362][32170, 49][temporary-8][live][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+25m53s9ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 12:58:11.362][32170, 49][temporary-8][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 12:58:11.396][32170, 49][temporary-8][live][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+25m53s321ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 14:08:43.362][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:08:48.395][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:10:54.485][32170, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 14:10:54.486][32170, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 14:10:54.491][32170, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 14:10:54.491][32170, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 14:10:54.496][32170, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 14:10:54.497][32170, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 14:10:54.498][32170, 40][temporary-3][live][genQUA][][mQUA: TMAF_892_P_2671/082671&NA/082671/8924130_2671&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2671&V3
[I][2025-04-02 +80 14:10:54.503][32170, 36][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 14:10:55.614][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:10:55.625][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-02 +80 14:10:57.273][32170, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 14:12:32.159][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:13:19.072][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:13:19.118][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-02 +80 14:15:23.240][32170, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 14:15:24.111][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:15:24.118][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:19:43.943][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:19:43.953][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:18.981][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:18.989][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:33.182][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:33.188][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:35.244][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:23:35.254][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:27:32.254][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:33:19.309][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:33:19.321][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:42:32.260][32170, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 14:43:19.133][32170, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 14:43:19.147][32170, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
