~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 16:27:34.286][24212, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[29411,29826][2025-04-02 +0800 17:39:45]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[29411,29826][2025-04-02 +0800 17:39:45]
get mmap time: 88
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77434482688 available:77300264960
log dir space info, capacity:117409054720 free:77279293440 available:77279293440
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:39:45.321][29411, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:39:45.324][29411, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =415
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk, false
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:39:45.325][29411, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~P0aVkoABtpSbi6dazgo0Iw==/com.tencent.android.qqdownloader-1eWSoggyZ1VfKtKEIUmb9g==/base.apk
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:45.326][29411, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:39:45.327][29411, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][RdefenseInitTask][init
[E][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[E][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][ActivityThreadHacker][start Hook successful, dur = 61
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:45.328][29411, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:39:45.366][29411, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:39:45.366][29411, 39][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[W][2025-04-02 +80 17:39:45.366][29411, 39][temporary-2][cache][live][LiveSyncAdapter][startSync Exception: java.lang.NoSuchMethodError: No interface method onFinished(Landroid/content/SyncResult;)V in class Landroid/content/ISyncContext; or its super classes (declaration of 'android.content.ISyncContext' appears in /system/framework/framework.jar)
	at yyb8922819.x0.xd.startSync(ProGuard:70)
	at android.content.ISyncAdapter$Stub.onTransact(ISyncAdapter.java:146)
	at android.os.Binder.execTransactInternal(Binder.java:1197)
	at android.os.Binder.execTransact(Binder.java:1156)
[I][2025-04-02 +80 17:39:45.366][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.368][29411, 39][temporary-2][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:39:45.370][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.371][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.371][29411, 39][temporary-2][cache][live][QimeiManager][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:39:45.371][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.387][29411, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 627, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:45.388][29411, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:39:45.405][29411, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.469][29411, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.471][29411, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:45.486][29411, 36][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.521][29411, 36][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:45.569][29411, 48][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.609][29411, 48][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:45.615][29411, 48][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:39:46.610][29411, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:39:46.613][29411, 42][temporary-5][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:39:47.201][29411, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:39:47.979][29411, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:39:49.567][29411, 46][temporary-7][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:39:49.733][29411, 48][temporary-8][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:39:51.925][29411, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:39:53.365][29411, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:39:53.368][29411, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:39:53.368][29411, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:39:53.402][29411, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[I][2025-04-02 +80 17:39:53.410][29411, 42][temporary-5][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.410][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:39:53.411][29411, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:39:53.412][29411, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:39:53.464][29411, 66][Binder:29411_4][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:39:53.464][29411, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@2763802
[I][2025-04-02 +80 17:39:53.464][29411, 66][Binder:29411_4][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:39:53.465][29411, 66][Binder:29411_4][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:39:54.635][29411, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[31431,31805][2025-04-02 +0800 17:42:05]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[31431,31805][2025-04-02 +0800 17:42:05]
get mmap time: 3
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77430173696 available:77295955968
log dir space info, capacity:117409054720 free:77274984448 available:77274984448
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:42:06.001][31431, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =314
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk, false
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:42:06.002][31431, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~FHLWXKxwH72rcACAm8q_rg==/com.tencent.android.qqdownloader-uYtvB38nn2mHlk9GmPlkPA==/base.apk
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:42:06.003][31431, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:06.004][31431, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:06.004][31431, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:42:06.004][31431, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:42:06.004][31431, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:42:06.043][31431, 1*][main][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:42:06.045][31431, 36][temporary-1][live][RdefenseInitTask][][init
[I][2025-04-02 +80 17:42:06.048][31431, 39][temporary-2][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:42:06.069][31431, 44][temporary-6][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:42:06.074][31431, 40][temporary-3][live][WxApiWrapper][][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:42:06.125][31431, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:42:06.148][31431, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:42:06.155][31431, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:42:06.162][31431, 58][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:42:06.162][31431, 61][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:42:06.162][31431, 64][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[E][2025-04-02 +80 17:42:06.215][31431, 36][temporary-1][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 17:42:06.221][31431, 36][temporary-1][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:42:06.221][31431, 36][temporary-1][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:42:06.221][31431, 36][temporary-1][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:42:06.221][31431, 36][temporary-1][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:42:06.276][31431, 44][temporary-6][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:42:06.298][31431, 48][temporary-8][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:42:06.311][31431, 48][temporary-8][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:42:06.318][31431, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:42:06.322][31431, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:42:06.325][31431, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:42:06.358][31431, 40][temporary-3][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:42:06.397][31431, 47][temporary-7][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:42:06.398][31431, 48][temporary-8][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:42:06.401][31431, 39][temporary-2][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:42:06.442][31431, 36][temporary-1][live][ActivityThreadHacker][][start Hook successful, dur = 263
[I][2025-04-02 +80 17:42:06.444][31431, 42][temporary-5][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:42:06.448][31431, 39][temporary-2][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:06.473][31431, 47][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:06.482][31431, 42][temporary-5][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:06.559][31431, 40][temporary-3][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:06.649][31431, 48][temporary-8][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:42:06.935][31431, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 772, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:42:06.974][31431, 44][temporary-6][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:42:07.084][31431, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 921, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:42:07.095][31431, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.113][31431, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.121][31431, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.131][31431, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.143][31431, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 981, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:42:07.151][31431, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.170][31431, 39][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:42:07.174][31431, 44][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:42:07.193][31431, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.230][31431, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:42:07.262][31431, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:42:07.265][31431, 44][temporary-6][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:42:09.384][31431, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:09.501][31431, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:09.651][31431, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:10.301][31431, 58][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:10.531][31431, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:10.730][31431, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:42:11.041][31431, 39][temporary-2][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:42:11.190][31431, 47][temporary-7][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:42:13.670][31431, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:42:14.906][31431, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:42:14.910][31431, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:42:14.910][31431, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:42:14.942][31431, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:42:14.946][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.946][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.946][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.946][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:42:14.947][31431, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:42:14.949][31431, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:42:14.949][31431, 42][temporary-5][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:42:14.990][31431, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@6e88654
[I][2025-04-02 +80 17:42:14.992][31431, 109][Binder:31431_C][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:42:14.993][31431, 109][Binder:31431_C][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:42:14.993][31431, 109][Binder:31431_C][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:42:16.139][31431, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 17:42:34.494][31431, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[1145,2374][2025-04-02 +0800 17:44:18]
~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[1145,2374][2025-04-02 +0800 17:44:18]
get mmap time: 2
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77384503296 available:77250285568
log dir space info, capacity:117409054720 free:77229314048 available:77229314048
[I][2025-04-02 +80 17:44:18.814][1145, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =294
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:44:18.815][1145, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk, false
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~LxB_E4-cQ5GDlD12_eZLKw==/com.tencent.android.qqdownloader-VAPT3__8cZOOYZQqq81qrg==/base.apk
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:18.816][1145, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:44:18.817][1145, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:44:18.819][1145, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:44:18.820][1145, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 656, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 670, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:44:18.821][1145, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:44:18.822][1145, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:44:18.822][1145, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:44:18.822][1145, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 17:44:18.822][1145, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:44:18.827][1145, 39][temporary-2][live][AstApp][][xlog init finished:live
[E][2025-04-02 +80 17:44:18.843][1145, 36][temporary-1][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 17:44:18.881][1145, 36][temporary-1][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:44:18.890][1145, 36][temporary-1][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:44:18.891][1145, 36][temporary-1][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:44:18.891][1145, 36][temporary-1][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:44:18.945][1145, 46][temporary-7][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:44:18.958][1145, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:18.964][1145, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:18.967][1145, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:18.970][1145, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:18.986][1145, 36][temporary-1][live][ActivityThreadHacker][][start Hook successful, dur = 188
[I][2025-04-02 +80 17:44:19.005][1145, 44][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:44:19.008][1145, 39][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:44:19.023][1145, 46][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:19.026][1145, 46][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:19.077][1145, 44][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:44:19.080][1145, 46][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:44:19.087][1145, 44][temporary-6][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:44:20.488][1145, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:44:20.502][1145, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:44:20.703][1145, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:44:20.721][1145, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:44:22.889][1145, 40][temporary-3][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:44:23.387][1145, 42][temporary-5][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:44:25.878][1145, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:44:26.932][1145, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:44:26.946][1145, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:44:26.946][1145, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:44:26.976][1145, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 36][temporary-1][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.980][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:44:26.981][1145, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:44:27.014][1145, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@9e78690
[I][2025-04-02 +80 17:44:27.016][1145, 73][Binder:1145_5][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:44:27.018][1145, 73][Binder:1145_5][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:44:27.018][1145, 73][Binder:1145_5][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:44:27.962][1145, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[4391,4772][2025-04-02 +0800 17:45:47]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[4391,4772][2025-04-02 +0800 17:45:47]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77339971584 available:77205753856
log dir space info, capacity:117409054720 free:77184782336 available:77184782336
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:45:47.758][4391, 38][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][RightlySDKManager][initSDK time =260
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~fRnkKj3pufRa3K_5W0v5dg==/com.tencent.android.qqdownloader-3dJlALmaIp9kB3S_lO27ag==/base.apk, false
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~fRnkKj3pufRa3K_5W0v5dg==/com.tencent.android.qqdownloader-3dJlALmaIp9kB3S_lO27ag==/base.apk
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:45:47.759][4391, 38][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:45:47.760][4391, 38][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:45:47.760][4391, 38][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:47.760][4391, 38][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:45:47.760][4391, 38][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:45:47.760][4391, 38][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:45:47.768][4391, 50][temporary-7][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:45:47.806][4391, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:45:47.824][4391, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:45:47.833][4391, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:45:47.859][4391, 59][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:45:47.869][4391, 55][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:45:47.875][4391, 35][temporary-1][live][WxApiWrapper][][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:45:47.918][4391, 50][temporary-7][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:45:47.932][4391, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:45:47.933][4391, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:45:47.933][4391, 43][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:47.935][4391, 43][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:47.936][4391, 50][temporary-7][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:47.937][4391, 50][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:47.938][4391, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:45:48.001][4391, 38][temporary-2][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:45:48.038][4391, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 168, dataMap.size = 322, memSize = 325.859375
[E][2025-04-02 +80 17:45:48.062][4391, 42][temporary-5][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:45:48.067][4391, 38][temporary-2][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:45:48.078][4391, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 219, dataMap.size = 322, memSize = 325.859375
[E][2025-04-02 +80 17:45:48.080][4391, 42][temporary-5][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:45:48.098][4391, 42][temporary-5][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:45:48.099][4391, 42][temporary-5][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:45:48.099][4391, 42][temporary-5][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:45:48.104][4391, 38][temporary-2][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:45:48.108][4391, 54][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.148][4391, 54][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.149][4391, 41][temporary-4][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:45:48.160][4391, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.163][4391, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.174][4391, 42][temporary-5][live][ActivityThreadHacker][][start Hook successful, dur = 245
[I][2025-04-02 +80 17:45:48.178][4391, 50][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:45:48.178][4391, 54][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:45:48.193][4391, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.196][4391, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.226][4391, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:45:48.232][4391, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:45:48.238][4391, 50][temporary-7][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:45:49.999][4391, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:45:50.005][4391, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:45:50.229][4391, 55][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:45:50.234][4391, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:45:52.711][4391, 42][temporary-5][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:45:52.824][4391, 39][temporary-3][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:45:55.489][4391, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:45:56.878][4391, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:45:56.883][4391, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:45:56.884][4391, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:45:56.912][4391, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.916][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:45:56.917][4391, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:45:56.918][4391, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:45:56.918][4391, 50][temporary-7][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:45:56.943][4391, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@3c0e563
[I][2025-04-02 +80 17:45:56.946][4391, 108][Binder:4391_C][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:45:56.948][4391, 108][Binder:4391_C][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:45:56.949][4391, 108][Binder:4391_C][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:45:58.162][4391, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[6267,6661][2025-04-02 +0800 17:47:12]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[6267,6661][2025-04-02 +0800 17:47:12]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77296521216 available:77162303488
log dir space info, capacity:117409054720 free:77141331968 available:77141331968
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:47:12.446][6267, 40][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:47:12.462][6267, 40][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:47:12.462][6267, 40][temporary-2][cache][live][RightlySDKManager][initSDK time =348
[I][2025-04-02 +80 17:47:12.463][6267, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:47:12.463][6267, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oOXMQ9Mx-wZLSJYOvaWgog==/com.tencent.android.qqdownloader-kapZpIjlmlREAO-eWtpskA==/base.apk, false
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oOXMQ9Mx-wZLSJYOvaWgog==/com.tencent.android.qqdownloader-kapZpIjlmlREAO-eWtpskA==/base.apk
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:12.464][6267, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:47:12.468][6267, 1*][main][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.470][6267, 43][temporary-4][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.473][6267, 42][temporary-3][live][RdefenseInitTask][][init
[I][2025-04-02 +80 17:47:12.473][6267, 40][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:47:12.480][6267, 54][temporary-8][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.515][6267, 37][temporary-1][live][WxApiWrapper][][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:47:12.572][6267, 52][temporary-7][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.577][6267, 46][temporary-6][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.588][6267, 40][temporary-2][live][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:47:12.600][6267, 57][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:12.600][6267, 60][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:12.602][6267, 70][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:12.603][6267, 67][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:12.603][6267, 63][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:12.637][6267, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:47:12.651][6267, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:47:12.652][6267, 54][temporary-8][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 17:47:12.668][6267, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:47:12.885][6267, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 274, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:12.891][6267, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:47:12.901][6267, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:47:12.909][6267, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[E][2025-04-02 +80 17:47:12.922][6267, 42][temporary-3][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:47:13.116][6267, 54][temporary-8][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:13.121][6267, 54][temporary-8][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:13.145][6267, 44][temporary-5][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:13.201][6267, 54][temporary-8][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:47:13.209][6267, 44][temporary-5][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:13.220][6267, 75][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:47:13.272][6267, 54][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.286][6267, 54][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.329][6267, 54][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:13.503][6267, 43][temporary-4][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:47:13.539][6267, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 933, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:13.588][6267, 54][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.589][6267, 54][temporary-8][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:47:13.608][6267, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 1003, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:13.609][6267, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 946, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:13.612][6267, 63][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 1001, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:47:13.618][6267, 43][temporary-4][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:47:13.644][6267, 75][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 411, dataMap.size = 322, memSize = 325.859375
[E][2025-04-02 +80 17:47:13.647][6267, 42][temporary-3][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:47:13.647][6267, 42][temporary-3][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:47:13.648][6267, 42][temporary-3][live][Monitor][][Monitor init fail. key = activity_monitor
[I][2025-04-02 +80 17:47:13.651][6267, 52][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.660][6267, 52][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.676][6267, 37][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.677][6267, 43][temporary-4][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:47:13.679][6267, 37][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[E][2025-04-02 +80 17:47:13.689][6267, 42][temporary-3][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:47:13.690][6267, 40][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.694][6267, 40][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.699][6267, 46][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.699][6267, 52][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:13.699][6267, 37][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:13.733][6267, 40][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:13.735][6267, 46][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:47:13.766][6267, 46][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:47:13.813][6267, 42][temporary-3][live][ActivityThreadHacker][][start Hook successful, dur = 1272
[I][2025-04-02 +80 17:47:14.521][6267, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.653][6267, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.671][6267, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.676][6267, 63][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.684][6267, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.691][6267, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.722][6267, 75][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.888][6267, 63][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.931][6267, 75][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.951][6267, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.955][6267, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:14.961][6267, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:47:17.502][6267, 40][temporary-2][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:47:17.696][6267, 37][temporary-1][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:47:19.808][6267, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:47:20.954][6267, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:47:20.957][6267, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:47:20.957][6267, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:47:20.981][6267, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.985][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:47:20.986][6267, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:47:20.989][6267, 42][temporary-3][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:47:21.041][6267, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@5e7cc24
[I][2025-04-02 +80 17:47:21.044][6267, 123][Binder:6267_F][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:47:21.052][6267, 123][Binder:6267_F][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:47:21.053][6267, 123][Binder:6267_F][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:47:22.590][6267, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[7990,8361][2025-04-02 +0800 17:51:18]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[7990,8361][2025-04-02 +0800 17:51:18]
get mmap time: 2
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77251854336 available:77117636608
log dir space info, capacity:117409054720 free:77096665088 available:77096665088
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 17:51:18.057][7990, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager][initSDK time =280
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:51:18.058][7990, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~SlJfva_u6OX5ek6aS1bs5A==/com.tencent.android.qqdownloader-B2viX1hTOCtlEbQUPPfp2w==/base.apk, false
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~SlJfva_u6OX5ek6aS1bs5A==/com.tencent.android.qqdownloader-B2viX1hTOCtlEbQUPPfp2w==/base.apk
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:18.059][7990, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:51:18.061][7990, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:51:18.061][7990, 40][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 17:51:18.061][7990, 40][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:51:18.062][7990, 40][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:51:18.082][7990, 40][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[E][2025-04-02 +80 17:51:18.083][7990, 40][temporary-2][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][BinderManager][addService, service : true
[E][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.087][7990, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.088][7990, 40][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:51:18.094][7990, 47][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.104][7990, 41][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.139][7990, 49][temporary-7][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:51:18.151][7990, 45][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.152][7990, 37][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.154][7990, 46][temporary-5][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.169][7990, 40][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.177][7990, 40][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.186][7990, 37][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.206][7990, 40][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.216][7990, 47][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.243][7990, 41][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.295][7990, 49][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.316][7990, 49][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.343][7990, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 724, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:51:18.352][7990, 45][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.353][7990, 37][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.364][7990, 49][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:51:18.378][7990, 45][temporary-4][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:51:18.379][7990, 41][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.380][7990, 47][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:51:18.386][7990, 47][temporary-6][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:51:18.389][7990, 41][temporary-3][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:51:18.392][7990, 37][temporary-1][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:51:19.601][7990, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:51:20.122][7990, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:51:22.554][7990, 51][temporary-8][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:51:22.692][7990, 47][temporary-6][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:51:24.950][7990, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:51:26.353][7990, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:51:26.356][7990, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:51:26.356][7990, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:51:26.384][7990, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.390][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 47][temporary-6][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:51:26.391][7990, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:51:26.396][7990, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:51:26.431][7990, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@6ae97b6
[I][2025-04-02 +80 17:51:26.432][7990, 95][Binder:7990_A][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:51:26.433][7990, 95][Binder:7990_A][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:51:26.434][7990, 95][Binder:7990_A][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:51:27.657][7990, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[9696,10046][2025-04-02 +0800 17:53:14]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[9696,10046][2025-04-02 +0800 17:53:14]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77207310336 available:77073092608
log dir space info, capacity:117409054720 free:77052121088 available:77052121088
