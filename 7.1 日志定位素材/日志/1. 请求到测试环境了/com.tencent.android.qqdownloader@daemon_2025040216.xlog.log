[I][2025-04-02 +80 15:51:38.670][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:38.670][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:38.674][31209, 478][protocalManagerStatReport-478][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/*************, /**************]
[I][2025-04-02 +80 15:51:38.675][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectStart
[I][2025-04-02 +80 15:51:38.678][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:51:38.678][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:51:38.678][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:51:38.679][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:51:38.679][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:51:38.679][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 15:51:38.687][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743578848612
[I][2025-04-02 +80 15:51:38.687][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 15:51:38.707][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectEnd protocol:http/1.1
[I][2025-04-02 +80 15:51:38.715][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:38.751][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]callEnd
[I][2025-04-02 +80 15:51:38.808][31209, 478][protocalManagerStatReport-478][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 15:51:38.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:38.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:38.830][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.830][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.830][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.830][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.831][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.831][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.850][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.852][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.863][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:38.932][31209, 40][temporary-3][daemon][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 15:51:38.932][31209, 40][temporary-3][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 15:51:38.937][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:51:38.944][31209, 40][temporary-3][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 15:51:38.944][31209, 44][temporary-5][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob
[I][2025-04-02 +80 15:51:38.944][31209, 38][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:38.944][31209, 38][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:38.947][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15581秒]
[I][2025-04-02 +80 15:51:38.950][31209, 44][temporary-5][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.nucleus.manager.usagestats.UsagestatsScheduleJob
[I][2025-04-02 +80 15:51:38.958][31209, 40][temporary-3][daemon][CmdMetrics][][all        1        100.0        1297850  未探测          {}                  
[I][2025-04-02 +80 15:51:38.958][31209, 315][Thread_TimerJobQueue][daemon][wise_download][][traceId:0 msg:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob work~~~ 
[I][2025-04-02 +80 15:51:38.959][31209, 40][temporary-3][daemon][CmdMetrics][][stat       1        100.0        1297850  未探测          {}                  
[I][2025-04-02 +80 15:51:38.965][31209, 257][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_ON
[I][2025-04-02 +80 15:51:38.970][31209, 38][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:38.984][31209, 315][Thread_TimerJobQueue][daemon][usagestats][][<UsagestatsSTManager> reportAppUsageTimely, scene : timer
[I][2025-04-02 +80 15:51:39.014][31209, 257][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=ACTION_ON_TIME_POINT
[I][2025-04-02 +80 15:51:39.318][31209, 45][temporary-6][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_ast_stat.db
[I][2025-04-02 +80 15:51:39.364][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------onActionIntercept------android.intent.action.ACTION_POWER_CONNECTED
[I][2025-04-02 +80 15:51:39.483][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateConfig----dailyCount--6--frequencyControl--600
[I][2025-04-02 +80 15:51:39.602][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions------1003
[I][2025-04-02 +80 15:51:39.620][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---0, 1
[I][2025-04-02 +80 15:51:39.645][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 1
[I][2025-04-02 +80 15:51:39.646][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 7
[I][2025-04-02 +80 15:51:39.646][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 10
[I][2025-04-02 +80 15:51:39.646][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 14
[I][2025-04-02 +80 15:51:39.646][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 15
[I][2025-04-02 +80 15:51:39.646][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 26
[I][2025-04-02 +80 15:51:39.647][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---item---1, 32
[I][2025-04-02 +80 15:51:39.647][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateActions---end---8,[, , , , , , , ]
[I][2025-04-02 +80 15:51:39.659][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:check font with cover current: false, feature : 0
[I][2025-04-02 +80 15:51:39.914][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=1, reachType=0
[I][2025-04-02 +80 15:51:40.094][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---WxCleanPush, 0
[I][2025-04-02 +80 15:51:40.150][31209, 47][temporary-8][daemon][AbstractPushItem][][scan.type = 12 thresholdValue=52428800 currentSize=0scan.result=-1
[I][2025-04-02 +80 15:51:40.151][31209, 47][temporary-8][daemon][RubbishReadyUtil][][isWXCleanReady: result = false, rubbishSize = 0B
[I][2025-04-02 +80 15:51:40.151][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:WxCleanPush---size--fail----
[I][2025-04-02 +80 15:51:40.151][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=1, reachType=1
[I][2025-04-02 +80 15:51:40.152][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-02 +80 15:51:40.172][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[E][2025-04-02 +80 15:51:40.185][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s364ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.186][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.195][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s399ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.197][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.197][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.198][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.211][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---WxCleanDeskMsg, 0
[I][2025-04-02 +80 15:51:40.212][31209, 47][temporary-8][daemon][AbstractPushItem][][scan.type = 12 thresholdValue=52428800 currentSize=0scan.result=-1
[I][2025-04-02 +80 15:51:40.212][31209, 47][temporary-8][daemon][RubbishReadyUtil][][isWXCleanReady: result = false, rubbishSize = 0B
[I][2025-04-02 +80 15:51:40.213][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:40.213][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:WxCleanDeskMsg---size--fail----
[I][2025-04-02 +80 15:51:40.213][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=7, reachType=1
[I][2025-04-02 +80 15:51:40.213][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.225][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s425ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.229][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.232][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s442ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.232][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.235][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.236][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.244][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---OptDeskMsg
[I][2025-04-02 +80 15:51:40.244][31209, 47][temporary-8][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 15:51:40.244][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=10, reachType=1
[I][2025-04-02 +80 15:51:40.245][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.247][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s457ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.247][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.248][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s458ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.249][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.249][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.249][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.255][31209, 47][temporary-8][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 15:51:40.256][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=14, reachType=1
[I][2025-04-02 +80 15:51:40.256][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-02 +80 15:51:40.256][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[E][2025-04-02 +80 15:51:40.258][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s468ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.260][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.262][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s472ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.262][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.262][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.262][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.267][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 15:51:40.267][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=15, reachType=1
[I][2025-04-02 +80 15:51:40.267][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.271][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s480ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.271][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.279][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s483ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.279][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.279][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.279][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.285][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 15:51:40.286][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=26, reachType=1
[I][2025-04-02 +80 15:51:40.287][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.295][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s501ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.298][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.302][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s511ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.306][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.307][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.308][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.319][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 15:51:40.319][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:#ignoreBySwitch: busiType=32, reachType=1
[W][2025-04-02 +80 15:51:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:51:40.320][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-02 +80 15:51:40.323][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s532ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.325][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.335][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s541ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.335][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.335][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.335][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.a70.xj.h(ProGuard:45)
	at yyb8922671.a70.xc.h(ProGuard:228)
	at yyb8922671.a70.xc.e(ProGuard:90)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.340][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 15:51:40.341][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.344][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s554ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at yyb8922671.a70.xc.c(ProGuard:201)
	at yyb8922671.a70.xc.e(ProGuard:123)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.344][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.345][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s555ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at yyb8922671.a70.xc.c(ProGuard:201)
	at yyb8922671.a70.xc.e(ProGuard:123)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.346][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.346][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.346][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at yyb8922671.a70.xc.c(ProGuard:201)
	at yyb8922671.a70.xc.e(ProGuard:123)
	at yyb8922671.c70.xb.c(ProGuard:30)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.347][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:IManager-run--getList,size:6
[I][2025-04-02 +80 15:51:40.347][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][PersonalizedMessageDataManager#sendRequest eventCode=1003
[I][2025-04-02 +80 15:51:40.347][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest eventCode=1003
[I][2025-04-02 +80 15:51:40.348][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:PersonalizedRequestPreHandler: getFilterReachBusinessItems, config empty
[I][2025-04-02 +80 15:51:40.348][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:PersonalizedRequestPreHandler: filterUnimportantMessage, config empty, just return reqList
[I][2025-04-02 +80 15:51:40.349][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][PersonalizedMessageEngine#sendRequest eventCode=1003
[I][2025-04-02 +80 15:51:40.350][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][GetPushAndPopupSystemMsgRequest#createRequest size=6
[I][2025-04-02 +80 15:51:40.350][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:---createRequest---eventType---1003
[I][2025-04-02 +80 15:51:40.350][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.350][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:7,params:{}
[I][2025-04-02 +80 15:51:40.350][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.351][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.357][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.357][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.362][31209, 47][temporary-8][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.pubgmhd, channelId = 10033016 costTime = 5
[I][2025-04-02 +80 15:51:40.363][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.366][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.366][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.367][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.367][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.367][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.367][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.368][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.368][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.369][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.369][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.370][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.370][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.371][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.371][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.372][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.372][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.373][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.373][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.373][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.374][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.375][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.375][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.375][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.375][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.376][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.376][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.377][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.377][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.378][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.378][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.379][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.379][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.380][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.380][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.381][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.381][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.381][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.382][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.382][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.382][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.383][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.383][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.384][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.384][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.385][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.385][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.386][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.386][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.387][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.387][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.388][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.388][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.389][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.389][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.390][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.390][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.391][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.391][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.393][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.393][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.393][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.394][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.394][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.394][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.395][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.395][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.396][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.396][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.397][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.397][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.398][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.398][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.399][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.399][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.400][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.400][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.401][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.401][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.401][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.402][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.402][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.402][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.403][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.403][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.404][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.404][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.408][31209, 47][temporary-8][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.dnf, channelId = 10048772 costTime = 4
[I][2025-04-02 +80 15:51:40.408][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.409][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.409][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.410][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.410][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.411][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.411][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.412][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.412][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.413][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.413][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.414][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.414][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.415][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.415][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.415][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.415][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.416][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.416][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.417][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.417][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.418][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.418][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.419][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.419][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.420][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.420][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.421][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.421][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.422][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.422][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.423][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.423][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.424][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.424][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.425][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.425][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.425][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.426][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.426][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.426][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.427][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.427][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.428][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.428][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.429][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.429][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.430][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.430][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.431][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.431][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.432][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.432][31209, 47][temporary-8][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 15:51:40.436][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 15:51:40.444][31209, 47][temporary-8][daemon][ReturnGiftChannelUtilJson updateInstalledStatus][][{"gameInfos":[{"channelid":"10033016","pkgName":"com.tencent.tmgp.pubgmhd","versionCode":-1},{"channelid":"10048772","pkgName":"com.tencent.tmgp.dnf","versionCode":-1}],"isGameInfoReady":true}
[I][2025-04-02 +80 15:51:40.448][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:32,params:{return_gift_game_info={"gameInfos":[{"channelid":"10033016","pkgName":"com.tencent.tmgp.pubgmhd","versionCode":-1},{"channelid":"10048772","pkgName":"com.tencent.tmgp.dnf","versionCode":-1}],"isGameInfoReady":true}}
[I][2025-04-02 +80 15:51:40.449][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.451][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:10,params:{storagePermission=false}
[I][2025-04-02 +80 15:51:40.452][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.452][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo reachBussinessItem.busiType is 26
[I][2025-04-02 +80 15:51:40.454][31209, 47][temporary-8][daemon][PreUpdateDownloadReport][][eventName: pre_update_trigger_install_pop
[I][2025-04-02 +80 15:51:40.479][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][need add preUpdateDownloadInfo
[I][2025-04-02 +80 15:51:40.480][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][预下载弹窗预下载弹窗 appIp=0 apkId=0  versionCodenull pkgName=null
[I][2025-04-02 +80 15:51:40.481][31209, 47][temporary-8][daemon][ReplaceMonitorMsgProxy][][isSupportV2Replace config = true
[E][2025-04-02 +80 15:51:40.482][31209, 47][temporary-8][daemon][ReplaceMonitorMsgProxy][][注册事件监听
[I][2025-04-02 +80 15:51:40.483][31209, 47][temporary-8][daemon][ReplaceMonitorMsgProxy][][尝试激活一下对面v2
[I][2025-04-02 +80 15:51:40.486][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][下载信息被删除
[I][2025-04-02 +80 15:51:40.487][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:26,params:{}
[I][2025-04-02 +80 15:51:40.487][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.487][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:14,params:{}
[I][2025-04-02 +80 15:51:40.487][31209, 47][temporary-8][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 15:51:40.487][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:-createRequest-reach:1,buss:15,params:{}
[I][2025-04-02 +80 15:51:40.488][31209, 43][temporary-4][daemon][ReplaceMonitorManager][][收到跨进程调用消息：39000, arg1 = 0
[I][2025-04-02 +80 15:51:40.488][31209, 43][temporary-4][daemon][ReplaceMonitorManager][][收到跨进程激活消息，回复 ACK：39000
[I][2025-04-02 +80 15:51:40.488][31209, 43][temporary-4][daemon][MsgManagerProxy][][qua === TMAF_892_P_2671, proxyVersion = 2
[E][2025-04-02 +80 15:51:40.489][31209, 43][temporary-4][daemon][ReplaceMonitorManager][][pkg 参数为空
[W][2025-04-02 +80 15:51:40.489][31209, 47][temporary-8][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:40.490][31209, 47][temporary-8][daemon][HttpNetWorkTaskV2][][[510]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:510,useHttp2:false
[I][2025-04-02 +80 15:51:40.491][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.493][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s703ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.I(ProGuard:182)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager.q(ProGuard:67)
	at yyb8922671.c70.xb.c(ProGuard:33)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.494][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.496][31209, 47][temporary-8][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s706ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.I(ProGuard:182)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager.q(ProGuard:67)
	at yyb8922671.c70.xb.c(ProGuard:33)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.498][31209, 47][temporary-8][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.498][31209, 47][temporary-8][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.498][31209, 47][temporary-8][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.I(ProGuard:182)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager.q(ProGuard:67)
	at yyb8922671.c70.xb.c(ProGuard:33)
	at yyb8922671.c70.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922671.wf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.514][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]callStart
[I][2025-04-02 +80 15:51:40.515][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 15:51:40.522][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:markEventTriggerRequest 1003
[I][2025-04-02 +80 15:51:40.523][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest success:20250402, 1743580300523
[I][2025-04-02 +80 15:51:40.535][31209, 479][protocalManager-479][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/*************, /**************]
[I][2025-04-02 +80 15:51:40.536][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]connectStart
[I][2025-04-02 +80 15:51:40.551][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]connectEnd protocol:http/1.1
[I][2025-04-02 +80 15:51:40.551][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:40.726][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[510]callEnd
[I][2025-04-02 +80 15:51:40.751][31209, 479][protocalManager-479][daemon][FLog_login_log][][[protocalManager-479]LoginProxy:setIdentityInfo
needTofresh:false
processName:daemon
[I][2025-04-02 +80 15:51:40.757][31209, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = daemon
[I][2025-04-02 +80 15:51:40.763][31209, 479][protocalManager-479][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:40.763][31209, 479][protocalManager-479][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:40.766][31209, 479][protocalManager-479][daemon][FLog_login_log][][[protocalManager-479]LoginProxy:id com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849isNeedSendLoginSucMsgfalse
[I][2025-04-02 +80 15:51:40.767][31209, 479][protocalManager-479][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:40.772][31209, 478][net_dedicated-478][daemon][PreUpdateAppEngine][][PersonalizedMessageEngine onRequestSucceed
[I][2025-04-02 +80 15:51:40.773][31209, 478][net_dedicated-478][daemon][FLog_TouchSysInterceptor][][[net_dedicated-478]TouchSysInterceptor:PersonalizedMessageEngine onRequestSucceed, callback new
[I][2025-04-02 +80 15:51:40.773][31209, 478][net_dedicated-478][daemon][FLog_TouchSysInterceptor][][[net_dedicated-478]TouchSysInterceptor:PersonalizedMessageDataManager#onDataSuccess: event=1003, itemsInfo=busiType=7, reachType=1; busiType=32, reachType=1; busiType=10, reachType=1; busiType=26, reachType=1; busiType=14, reachType=1; busiType=15, reachType=1; 
[I][2025-04-02 +80 15:51:40.773][31209, 478][net_dedicated-478][daemon][PreUpdateAppEngine][][onDataSuccess call
[I][2025-04-02 +80 15:51:40.774][31209, 478][net_dedicated-478][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.780][31209, 478][net_dedicated-478][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s986ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:397)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.H(ProGuard:200)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager$1.onDataSuccess(ProGuard:157)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.j(ProGuard:85)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.d(Unknown Source:0)
	at yyb8922671.d70.xc.call(Unknown Source:10)
	at com.tencent.assistant.module.callback.CallbackHelper.broadcast(ProGuard:118)
	at com.tencent.assistant.module.BaseEngine.notifyDataChanged(ProGuard:76)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.onRequestSuccessed(ProGuard:74)
	at com.tencent.assistant.module.BaseModuleEngine.processSingleRequestFinish(ProGuard:447)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:294)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:261)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:221)
	at com.tencent.assistant.netservice.xb$xe$xb.run(ProGuard:638)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at yyb8922671.wf0.xd.run(ProGuard:46)
	at yyb8922671.wf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.780][31209, 478][net_dedicated-478][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 15:51:40.782][31209, 478][net_dedicated-478][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{3211a5b u0a3080 TPSL bg:+30m55s992ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922671.e7.xc.x(ProGuard:415)
	at yyb8922671.e7.xc.w(ProGuard:407)
	at yyb8922671.e7.xc.j(ProGuard:398)
	at yyb8922671.e7.xc.b(Unknown Source:0)
	at yyb8922671.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922671.e7.xc.g(ProGuard:255)
	at yyb8922671.e7.xc.n(ProGuard:225)
	at yyb8922671.e7.xe.getService(ProGuard:32)
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.H(ProGuard:200)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager$1.onDataSuccess(ProGuard:157)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.j(ProGuard:85)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.d(Unknown Source:0)
	at yyb8922671.d70.xc.call(Unknown Source:10)
	at com.tencent.assistant.module.callback.CallbackHelper.broadcast(ProGuard:118)
	at com.tencent.assistant.module.BaseEngine.notifyDataChanged(ProGuard:76)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.onRequestSuccessed(ProGuard:74)
	at com.tencent.assistant.module.BaseModuleEngine.processSingleRequestFinish(ProGuard:447)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:294)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:261)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:221)
	at com.tencent.assistant.netservice.xb$xe$xb.run(ProGuard:638)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at yyb8922671.wf0.xd.run(ProGuard:46)
	at yyb8922671.wf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.782][31209, 478][net_dedicated-478][daemon][MainBinderManager][][queryBinder, binderCode : 1030 binderManager:false binder:false
[E][2025-04-02 +80 15:51:40.783][31209, 478][net_dedicated-478][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1030
[E][2025-04-02 +80 15:51:40.783][31209, 478][net_dedicated-478][daemon][PropertiesManager][][[PropertiesManager] load key:pop_notification_switch defaultValue:true failed!
java.lang.NullPointerException: Attempt to invoke interface method 'boolean com.tencent.assistant.setting.IPropertiesManager.load(java.lang.String, boolean)' on a null object reference
	at yyb8922671.gb.xb.a(ProGuard:21)
	at com.tencent.assistant.ILocalSettings$xt.getSettingsFromDB(ProGuard:360)
	at com.tencent.assistant.xb.c(ProGuard:253)
	at yyb8922671.d70.xd.M(ProGuard:80)
	at yyb8922671.d70.xd.H(ProGuard:200)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageDataManager$1.onDataSuccess(ProGuard:157)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.j(ProGuard:85)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.d(Unknown Source:0)
	at yyb8922671.d70.xc.call(Unknown Source:10)
	at com.tencent.assistant.module.callback.CallbackHelper.broadcast(ProGuard:118)
	at com.tencent.assistant.module.BaseEngine.notifyDataChanged(ProGuard:76)
	at com.tencent.pangu.personalizedmessage.request.PersonalizedMessageEngine.onRequestSuccessed(ProGuard:74)
	at com.tencent.assistant.module.BaseModuleEngine.processSingleRequestFinish(ProGuard:447)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:294)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:261)
	at com.tencent.assistant.module.BaseModuleEngine.onHttpProtocoRequestFinish(ProGuard:221)
	at com.tencent.assistant.netservice.xb$xe$xb.run(ProGuard:638)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at yyb8922671.wf0.xd.run(ProGuard:46)
	at yyb8922671.wf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 15:51:40.791][31209, 36][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:40.791][31209, 36][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:40.793][31209, 36][temporary-1][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 15:51:40.799][31209, 478][net_dedicated-478][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:--onDataSuccess--reachBussinessList is empty
[I][2025-04-02 +80 15:51:40.799][31209, 478][net_dedicated-478][daemon][PreUpdateAppEngine][][onDataSuccess reachBussinessList is empty
[I][2025-04-02 +80 15:51:40.799][31209, 478][net_dedicated-478][daemon][FLog_TouchSysInterceptor][][[net_dedicated-478]TouchSysInterceptor:---TouchSysEngine---onRequestSucceed---10005
[I][2025-04-02 +80 15:51:40.981][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:41.022][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:51:41.062][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[W][2025-04-02 +80 15:51:41.637][31209, 216][LogProcessService-5][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:41.639][31209, 216][LogProcessService-5][daemon][HttpNetWorkTaskV2][][[511]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:511,useHttp2:false
[I][2025-04-02 +80 15:51:41.655][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[511]callStart
[I][2025-04-02 +80 15:51:41.656][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[511]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:41.688][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[511]callEnd
[I][2025-04-02 +80 15:51:41.694][31209, 478][protocalManagerStatReport-478][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 15:51:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:41.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:43.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:43.866][31209, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[W][2025-04-02 +80 15:51:44.795][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:44.797][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:46.317][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:46.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:47.787][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:47.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:48.719][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 431937725 , reportEventTime = 431947764
[W][2025-04-02 +80 15:51:49.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:49.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:49.503][31209, 257][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[W][2025-04-02 +80 15:51:49.822][31209, 46][temporary-7][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:49.824][31209, 46][temporary-7][daemon][HttpNetWorkTaskV2][][[512]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:512,useHttp2:false
[I][2025-04-02 +80 15:51:49.840][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[512]callStart
[I][2025-04-02 +80 15:51:49.840][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[512]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[W][2025-04-02 +80 15:51:49.843][31209, 478][launch-478][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:49.843][31209, 478][launch-478][daemon][HttpNetWorkTaskV2][][[513]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:513,useHttp2:false
[I][2025-04-02 +80 15:51:49.855][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]callStart
[I][2025-04-02 +80 15:51:49.855][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 15:51:49.862][31209, 480][protocalManager-480][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/*************, /**************]
[I][2025-04-02 +80 15:51:49.863][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]connectStart
[I][2025-04-02 +80 15:51:49.888][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]connectEnd protocol:http/1.1
[I][2025-04-02 +80 15:51:49.888][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:49.929][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[512]callEnd
[I][2025-04-02 +80 15:51:49.957][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[513]callEnd
[I][2025-04-02 +80 15:51:49.985][31209, 479][protocalManager-479][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:49.990][31209, 480][protocalManager-480][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:50.034][31209, 478][net_dedicated-478][daemon][wise_download][][traceId:0 msg:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob start~~~
[E][2025-04-02 +80 15:51:50.112][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:16 requestVersion:0 version:1743580309
[E][2025-04-02 +80 15:51:50.114][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:1 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 15:51:50.114][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:17 requestVersion:5 version:5
[E][2025-04-02 +80 15:51:50.114][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:2 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 15:51:50.114][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:18 requestVersion:2 version:2
[E][2025-04-02 +80 15:51:50.115][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:19 requestVersion:1743575467 version:1743580309
[E][2025-04-02 +80 15:51:50.119][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:4 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 15:51:50.120][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:20 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 15:51:50.121][31209, 478][net_dedicated-478][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:5 requestVersion:1743575467 version:1743580309
[W][2025-04-02 +80 15:51:50.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:50.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:50.876][31209, 38][temporary-2][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:50.893][31209, 38][temporary-2][daemon][HttpNetWorkTaskV2][][[514]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:514,useHttp2:false
[I][2025-04-02 +80 15:51:50.894][31209, 38][temporary-2][daemon][GetAppUpdateEntranceManager][][sendRequest
[I][2025-04-02 +80 15:51:50.914][31209, 478][protocalManager-478][daemon][HttpNetWorkTaskV2][][[514]callStart
[I][2025-04-02 +80 15:51:50.915][31209, 478][protocalManager-478][daemon][HttpNetWorkTaskV2][][[514]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[W][2025-04-02 +80 15:51:50.945][31209, 38][temporary-2][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:50.949][31209, 38][temporary-2][daemon][HttpNetWorkTaskV2][][[515]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:515,useHttp2:false
[W][2025-04-02 +80 15:51:50.950][31209, 29][Binder:31209_3][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:50.951][31209, 29][Binder:31209_3][daemon][HttpNetWorkTaskV2][][[516]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:516,useHttp2:false
[I][2025-04-02 +80 15:51:50.958][31209, 481][protocalManager-481][daemon][HttpNetWorkTaskV2][][[515]callStart
[I][2025-04-02 +80 15:51:50.959][31209, 481][protocalManager-481][daemon][HttpNetWorkTaskV2][][[515]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:50.963][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]callStart
[I][2025-04-02 +80 15:51:50.963][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 15:51:50.964][31209, 480][protocalManager-480][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/*************, /**************]
[I][2025-04-02 +80 15:51:50.965][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]connectStart
[I][2025-04-02 +80 15:51:50.991][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]connectEnd protocol:http/1.1
[I][2025-04-02 +80 15:51:50.992][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:51.071][31209, 480][protocalManager-480][daemon][HttpNetWorkTaskV2][][[516]callEnd
[I][2025-04-02 +80 15:51:51.082][31209, 481][protocalManager-481][daemon][HttpNetWorkTaskV2][][[515]callEnd
[I][2025-04-02 +80 15:51:51.090][31209, 480][protocalManager-480][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:51.100][31209, 481][protocalManager-481][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:51.106][31209, 1*][main][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 15:51:51.107][31209, 1*][main][daemon][floatingwindow][][sendToolbarRequestFormUpdateList: switch close.
[I][2025-04-02 +80 15:51:51.114][31209, 479][net_dedicated-479][daemon][GetAppUpdateEntranceManager][][onGetAppUpdateEntranceFinish: totalNum = 13
[I][2025-04-02 +80 15:51:51.121][31209, 36][temporary-1][daemon][ToolbarRequest][][sendRequest from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 15:51:51.127][31209, 1*][main][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 15:51:51.127][31209, 1*][main][daemon][floatingwindow][][sendToolbarRequestFormUpdateList: switch close.
[I][2025-04-02 +80 15:51:51.130][31209, 40][temporary-3][daemon][ToolbarRequest][][sendRequest from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 15:51:51.136][31209, 36][temporary-1][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 15:51:51.152][31209, 40][temporary-3][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 15:51:51.266][31209, 29][Binder:31209_3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:51.298][31209, 36][temporary-1][daemon][FLog_TouchSysInterceptor][][[temporary-1]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 15:51:51.300][31209, 36][temporary-1][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 15:51:51.301][31209, 36][temporary-1][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 15:51:51.305][31209, 36][temporary-1][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 15:51:51.323][31209, 36][temporary-1][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.365][31209, 36][temporary-1][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=76698099712, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=3319, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 15:51:51.401][31209, 40][temporary-3][daemon][FLog_TouchSysInterceptor][][[temporary-3]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 15:51:51.402][31209, 40][temporary-3][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 15:51:51.403][31209, 40][temporary-3][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 15:51:51.403][31209, 40][temporary-3][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 15:51:51.415][31209, 36][temporary-1][daemon][ToolbarRequest][][doSendRequest, from:APP_UPDATE_LIST, process:daemon
[W][2025-04-02 +80 15:51:51.419][31209, 36][temporary-1][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:51.421][31209, 36][temporary-1][daemon][HttpNetWorkTaskV2][][[517]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:517,useHttp2:false
[I][2025-04-02 +80 15:51:51.423][31209, 40][temporary-3][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.430][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[517]callStart
[I][2025-04-02 +80 15:51:51.431][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[517]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:51.452][31209, 40][temporary-3][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=76698099712, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=3319, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 15:51:51.470][31209, 40][temporary-3][daemon][ToolbarRequest][][skip same request, from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 15:51:51.486][31209, 478][protocalManager-478][daemon][HttpNetWorkTaskV2][][[514]callEnd
[I][2025-04-02 +80 15:51:51.495][31209, 478][protocalManager-478][daemon][FLog_login_log][][[protocalManager-478]LoginProxy:setIdentityInfo
needTofresh:false
processName:daemon
[I][2025-04-02 +80 15:51:51.507][31209, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = daemon
[I][2025-04-02 +80 15:51:51.519][31209, 478][protocalManager-478][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:51.519][31209, 478][protocalManager-478][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:51.520][31209, 478][protocalManager-478][daemon][FLog_login_log][][[protocalManager-478]LoginProxy:id com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849isNeedSendLoginSucMsgfalse
[I][2025-04-02 +80 15:51:51.521][31209, 478][protocalManager-478][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:51.557][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:51.558][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:51.567][31209, 481][net_dedicated-481][daemon][APPUpdate][][traceId:0 msg:errCode:0|requestFlag:1|requestAction:6|serverAppUpdateTime:1743580309|serverAllIntervalTime:86400
[I][2025-04-02 +80 15:51:51.568][31209, 481][net_dedicated-481][daemon][APPUpdate][][traceId:0 msg:NotifyUIFromServer:CM_EVENT_APP_UPDATE_ENGINE_REQUEST_SUC
[I][2025-04-02 +80 15:51:51.568][31209, 481][net_dedicated-481][daemon][FLog_TouchSysInterceptor][][[net_dedicated-481]TouchSysInterceptor:--KEY_APP_UPDATE_IDS--
[I][2025-04-02 +80 15:51:51.571][31209, 46][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:51.613][31209, 1*][main][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 15:51:51.613][31209, 1*][main][daemon][floatingwindow][][sendToolbarRequestFormUpdateList: switch close.
[I][2025-04-02 +80 15:51:51.616][31209, 38][temporary-2][daemon][ToolbarRequest][][sendRequest from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 15:51:51.631][31209, 38][temporary-2][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 15:51:51.687][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:51.687][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:51.687][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:51.688][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:51.689][31209, 47][temporary-8][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:51.695][31209, 479][protocalManager-479][daemon][HttpNetWorkTaskV2][][[517]callEnd
[I][2025-04-02 +80 15:51:51.709][31209, 479][protocalManager-479][daemon][FLog_login_log][][[protocalManager-479]LoginProxy:setIdentityInfo
needTofresh:false
processName:daemon
[I][2025-04-02 +80 15:51:51.713][31209, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = daemon
[I][2025-04-02 +80 15:51:51.717][31209, 479][protocalManager-479][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:51.717][31209, 479][protocalManager-479][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:51.718][31209, 479][protocalManager-479][daemon][FLog_login_log][][[protocalManager-479]LoginProxy:id com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849isNeedSendLoginSucMsgfalse
[I][2025-04-02 +80 15:51:51.719][31209, 479][protocalManager-479][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:51.747][31209, 45][temporary-6][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.749][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:51:51.749][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:51:51.750][31209, 46][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:51.794][31209, 45][temporary-6][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.803][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:51.803][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:51.803][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:51.803][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:51.805][31209, 40][temporary-3][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:51.815][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:51.816][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:51.816][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:51.816][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:51.818][31209, 43][temporary-4][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:51.837][31209, 45][temporary-6][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.845][31209, 38][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 15:51:51.846][31209, 38][temporary-2][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 15:51:51.846][31209, 38][temporary-2][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 15:51:51.847][31209, 38][temporary-2][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 15:51:51.865][31209, 38][temporary-2][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 15:51:51.874][31209, 45][temporary-6][daemon][InstallNotificationAction][][server config = 
[I][2025-04-02 +80 15:51:51.874][31209, 29][Binder:31209_3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:51.875][31209, 45][temporary-6][daemon][InstallNotificationAction][][config json = {"package_name":"com.tencent.tmgp.dnf","start_period": 1716220800000,"end_period": 1716825600000,"title": "已下载完成","sub_title": "立即安装，开始游戏 >"}
[I][2025-04-02 +80 15:51:51.875][31209, 45][temporary-6][daemon][InstallNotificationAction][][getConfig packageName = com.tencent.tmgp.dnf;start=1716220800000;endTIme = 1716825600000
[W][2025-04-02 +80 15:51:51.875][31209, 29][Binder:31209_3][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[W][2025-04-02 +80 15:51:51.875][31209, 45][temporary-6][daemon][InstallNotificationAction][][com.tencent.tmgp.dnf;不在时间范围内
[I][2025-04-02 +80 15:51:51.875][31209, 45][temporary-6][daemon][InstallNotificationAction][][不满足服务器配置
[I][2025-04-02 +80 15:51:51.876][31209, 29][Binder:31209_3][daemon][HttpNetWorkTaskV2][][[518]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:518,useHttp2:false
[I][2025-04-02 +80 15:51:51.878][31209, 45][temporary-6][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 15:51:51.878][31209, 45][temporary-6][daemon][WildToolbarNotification][][sendEventForceRefresh: canShowRecommendNewUi = false , needRecreateToolbar = false
[I][2025-04-02 +80 15:51:51.888][31209, 481][protocalManagerStatReport-481][daemon][HttpNetWorkTaskV2][][[518]callStart
[I][2025-04-02 +80 15:51:51.889][31209, 481][protocalManagerStatReport-481][daemon][HttpNetWorkTaskV2][][[518]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:51.911][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:51:51.911][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:51:51.912][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:51:51.912][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:51:51.912][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:51:51.912][31209, 38][temporary-2][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=76698099712, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=3320, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 15:51:51.912][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 15:51:51.936][31209, 38][temporary-2][daemon][ToolbarRequest][][skip same request, from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 15:51:51.954][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:51.954][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:51.954][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:51.954][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:51.957][31209, 46][temporary-7][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:51.973][31209, 29][Binder:31209_3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 15:51:51.976][31209, 481][protocalManagerStatReport-481][daemon][HttpNetWorkTaskV2][][[518]callEnd
[I][2025-04-02 +80 15:51:51.984][31209, 481][protocalManagerStatReport-481][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 15:51:52.221][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:52.221][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:52.221][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:52.221][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:52.224][31209, 46][temporary-7][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:52.278][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 15:51:52.278][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 15:51:52.278][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 15:51:52.278][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 15:51:52.281][31209, 40][temporary-3][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 15:51:52.302][31209, 29][Binder:31209_3][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 15:51:52.303][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:52.304][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:53.814][31209, 108][LogProcessService-2][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:51:53.817][31209, 108][LogProcessService-2][daemon][HttpNetWorkTaskV2][][[519]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:519,useHttp2:false
[W][2025-04-02 +80 15:51:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:53.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:53.836][31209, 479][protocalManagerStatReport-479][daemon][HttpNetWorkTaskV2][][[519]callStart
[I][2025-04-02 +80 15:51:53.837][31209, 479][protocalManagerStatReport-479][daemon][HttpNetWorkTaskV2][][[519]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:51:53.930][31209, 479][protocalManagerStatReport-479][daemon][HttpNetWorkTaskV2][][[519]callEnd
[I][2025-04-02 +80 15:51:53.946][31209, 479][protocalManagerStatReport-479][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 15:51:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:56.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:51:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:51:58.758][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 431947764 , reportEventTime = 431957803
[W][2025-04-02 +80 15:51:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:51:59.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:00.015][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:52:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:02.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:04.316][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:04.318][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:08.757][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 30069
[I][2025-04-02 +80 15:52:08.757][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:52:08.760][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:52:08.761][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15611秒]
[I][2025-04-02 +80 15:52:08.762][31209, 44][temporary-5][daemon][CmdMetrics][][all        10       100.0        181      未探测          {}                  
[I][2025-04-02 +80 15:52:08.763][31209, 44][temporary-5][daemon][CmdMetrics][][biz        7        100.0        224      未探测          {}                  
[I][2025-04-02 +80 15:52:08.764][31209, 44][temporary-5][daemon][CmdMetrics][][stat       3        100.0        87       未探测          {}                  
[W][2025-04-02 +80 15:52:08.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:08.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:08.797][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 431957803 , reportEventTime = 431967842
[I][2025-04-02 +80 15:52:10.274][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:52:10.274][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 15:52:10.274][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[W][2025-04-02 +80 15:52:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:13.296][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:13.298][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:14.796][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:14.800][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:17.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:17.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:18.838][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 431967843 , reportEventTime = 431977883
[W][2025-04-02 +80 15:52:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:20.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:23.786][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:23.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:25.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:28.876][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 431977883 , reportEventTime = 431987921
[W][2025-04-02 +80 15:52:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:29.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:31.304][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:31.306][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:32.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:32.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:34.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:34.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:37.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:38.799][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 60111
[I][2025-04-02 +80 15:52:38.799][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:52:38.802][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15641秒]
[I][2025-04-02 +80 15:52:38.802][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:52:38.803][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 15:52:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:38.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:38.916][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 431987921 , reportEventTime = 431997961
[W][2025-04-02 +80 15:52:40.313][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:40.316][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:41.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:44.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:44.791][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:46.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:48.954][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 431997961 , reportEventTime = 432007999
[W][2025-04-02 +80 15:52:49.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:49.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:50.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:50.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:52.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:52:52.345][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:52:52.345][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:52:52.346][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:52:52.346][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:52:52.347][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:52:52.347][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:52:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:53.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:55.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:55.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:56.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:56.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:52:58.293][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:58.296][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:52:58.994][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432008000 , reportEventTime = 432018038
[W][2025-04-02 +80 15:52:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:52:59.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:00.017][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:53:01.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:01.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:02.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:02.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:04.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:04.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:05.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:05.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:07.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:08.810][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:08.813][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:08.843][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 90155
[I][2025-04-02 +80 15:53:08.843][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:53:08.846][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:53:08.848][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15671秒]
[I][2025-04-02 +80 15:53:08.849][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:53:09.032][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432018039 , reportEventTime = 432028077
[W][2025-04-02 +80 15:53:10.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:10.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:11.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:11.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:13.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:13.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:14.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:16.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:19.070][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432028077 , reportEventTime = 432038115
[W][2025-04-02 +80 15:53:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:20.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:20.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:22.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:22.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:23.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:23.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:25.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:26.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:26.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:27.252][31209, 58][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[W][2025-04-02 +80 15:53:28.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:29.110][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432038115 , reportEventTime = 432048154
[W][2025-04-02 +80 15:53:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:29.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:31.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:32.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-02 +80 15:53:37.455][31209, 89][common_task_pool-89][daemon][botid][][doInit request.head.botUid
[W][2025-04-02 +80 15:53:37.458][31209, 89][common_task_pool-89][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:53:37.628][31209, 1*][main][daemon][BookingPreDown.LongConnEngine][][onConnected
[I][2025-04-02 +80 15:53:37.628][31209, 1*][main][daemon][UpdateBookingLongConnEngine][][onConnected
[I][2025-04-02 +80 15:53:37.729][31209, 47][temporary-8][daemon][ChanelMsg][][GetChanelMsgEngine 收到长连接下发的浮层数据..
[I][2025-04-02 +80 15:53:37.757][31209, 46][temporary-7][daemon][ChanelMsg][][handleReceivedChannelMsg  msgStruct type: 0 channelMsgId:003759904899348360512 msgType: 1 msgInfo isNull: false
[W][2025-04-02 +80 15:53:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:38.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:38.884][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 120197
[I][2025-04-02 +80 15:53:38.884][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:53:38.886][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:53:38.887][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15701秒]
[I][2025-04-02 +80 15:53:38.887][31209, 44][temporary-5][daemon][CmdMetrics][][all        1        100.0        162      未探测          {}                  
[I][2025-04-02 +80 15:53:38.888][31209, 44][temporary-5][daemon][CmdMetrics][][biz        1        100.0        162      未探测          {}                  
[I][2025-04-02 +80 15:53:39.150][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432048155 , reportEventTime = 432058195
[W][2025-04-02 +80 15:53:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:40.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:41.797][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:41.801][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:43.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:44.781][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:44.785][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:47.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:49.190][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432058195 , reportEventTime = 432068235
[W][2025-04-02 +80 15:53:49.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:49.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:50.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:50.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:51.088][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[I][2025-04-02 +80 15:53:51.088][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[I][2025-04-02 +80 15:53:51.088][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[W][2025-04-02 +80 15:53:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:52.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:53.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:53:53.867][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:53:53.869][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:53:53.870][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:53:53.871][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:53:53.872][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:53:53.873][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:53:55.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:55.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:56.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:53:58.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:58.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:53:59.230][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432068235 , reportEventTime = 432078275
[W][2025-04-02 +80 15:53:59.795][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:53:59.798][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:00.076][31209, 40][temporary-3][daemon][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 15:54:00.079][31209, 44][temporary-5][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.assistant.module.timer.job.STReportTimerJob
[W][2025-04-02 +80 15:54:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:02.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:05.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:07.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:07.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:08.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:08.930][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 150242
[I][2025-04-02 +80 15:54:08.930][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:54:08.931][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:54:08.932][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15731秒]
[I][2025-04-02 +80 15:54:08.933][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:54:09.270][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432078276 , reportEventTime = 432088314
[W][2025-04-02 +80 15:54:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:13.323][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:13.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:14.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:16.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:16.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:19.307][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432088315 , reportEventTime = 432098352
[W][2025-04-02 +80 15:54:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:19.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:20.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:20.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:23.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:23.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:25.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:26.804][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:26.808][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:28.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:29.347][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432098352 , reportEventTime = 432108392
[W][2025-04-02 +80 15:54:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:29.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:32.809][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:32.813][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:35.796][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:35.798][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:38.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:38.972][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 180284
[I][2025-04-02 +80 15:54:38.972][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:54:38.974][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15761秒]
[I][2025-04-02 +80 15:54:38.974][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:54:38.974][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:54:39.385][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432108393 , reportEventTime = 432118430
[W][2025-04-02 +80 15:54:40.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:40.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:43.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:43.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:44.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:49.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:49.422][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432118430 , reportEventTime = 432128467
[W][2025-04-02 +80 15:54:50.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:50.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:52.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:52.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:53.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:53.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:55.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:54:55.348][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:54:55.349][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:54:55.349][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:54:55.350][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:54:55.351][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:54:55.352][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:54:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:56.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:54:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:58.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:54:59.462][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432128468 , reportEventTime = 432138507
[W][2025-04-02 +80 15:54:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:54:59.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:00.013][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:55:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:02.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:02.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:07.288][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:07.289][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:08.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:08.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:09.017][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 210330
[I][2025-04-02 +80 15:55:09.018][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:55:09.020][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:55:09.020][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15792秒]
[I][2025-04-02 +80 15:55:09.021][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:55:09.502][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432138507 , reportEventTime = 432148546
[W][2025-04-02 +80 15:55:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:11.809][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:11.813][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:13.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:14.817][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:14.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:17.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:19.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:19.542][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432148547 , reportEventTime = 432158587
[W][2025-04-02 +80 15:55:20.810][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:20.813][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:22.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:23.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:23.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:25.293][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:25.296][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:28.296][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:28.300][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:29.582][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432158587 , reportEventTime = 432168627
[W][2025-04-02 +80 15:55:29.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:29.826][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:31.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:32.810][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:32.814][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:33.472][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[I][2025-04-02 +80 15:55:33.472][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[I][2025-04-02 +80 15:55:33.472][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[W][2025-04-02 +80 15:55:34.308][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:34.312][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:35.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:37.296][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:37.299][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:38.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:38.826][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:39.062][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 240375
[I][2025-04-02 +80 15:55:39.062][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:55:39.064][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:55:39.066][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15822秒]
[I][2025-04-02 +80 15:55:39.067][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:55:39.622][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432168627 , reportEventTime = 432178667
[W][2025-04-02 +80 15:55:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:40.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:43.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:44.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:44.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:46.300][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:46.303][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:47.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:49.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:49.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:49.643][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 21 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10021 , taskStartElapsedRealtimeMs = 432178667 , reportEventTime = 432188688
[W][2025-04-02 +80 15:55:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:52.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:53.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:55:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:55:56.865][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:55:56.866][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:55:56.867][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:55:56.868][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:55:56.869][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:55:56.870][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:55:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:55:59.647][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 3 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10003 , taskStartElapsedRealtimeMs = 432188689 , reportEventTime = 432198692
[W][2025-04-02 +80 15:55:59.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:55:59.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:00.026][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:56:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:02.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:04.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:07.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:09.104][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 270417
[I][2025-04-02 +80 15:56:09.104][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:56:09.105][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15852秒]
[I][2025-04-02 +80 15:56:09.106][31209, 38][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:56:09.106][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:56:09.688][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 432198692 , reportEventTime = 432208733
[W][2025-04-02 +80 15:56:10.283][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:10.286][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:11.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:14.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:16.317][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:16.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:17.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:19.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:19.698][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 432208734 , reportEventTime = 432218743
[W][2025-04-02 +80 15:56:20.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:20.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:22.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:23.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:28.304][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:28.308][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:29.714][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 16 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10016 , taskStartElapsedRealtimeMs = 432218743 , reportEventTime = 432228759
[W][2025-04-02 +80 15:56:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:29.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:32.785][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:32.788][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:34.289][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:34.292][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:35.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:37.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:38.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:39.130][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 300443
[I][2025-04-02 +80 15:56:39.130][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:56:39.132][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:56:39.132][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15882秒]
[I][2025-04-02 +80 15:56:39.133][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:56:39.753][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432228759 , reportEventTime = 432238798
[W][2025-04-02 +80 15:56:40.312][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:40.314][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:41.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:43.315][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:43.316][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:44.811][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:44.813][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:46.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:46.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:47.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:47.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:49.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:49.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:49.794][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432238799 , reportEventTime = 432248839
[W][2025-04-02 +80 15:56:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:52.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:53.806][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:53.810][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:55.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:55.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:56.810][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:56.814][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:56:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:56:58.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:56:58.367][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:56:58.368][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:56:58.369][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:56:58.370][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:56:58.371][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:56:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:56:59.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:56:59.834][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432248839 , reportEventTime = 432258879
[I][2025-04-02 +80 15:57:00.011][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:57:01.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:01.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:02.815][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:02.818][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:04.312][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:04.315][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:05.800][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:05.804][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:09.143][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 330455
[I][2025-04-02 +80 15:57:09.143][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:57:09.145][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15912秒]
[I][2025-04-02 +80 15:57:09.146][31209, 38][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:57:09.146][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:57:09.874][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432258879 , reportEventTime = 432268919
[W][2025-04-02 +80 15:57:10.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:10.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:11.792][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:11.796][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:14.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:14.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:15.883][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=89
[I][2025-04-02 +80 15:57:15.885][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=89
[I][2025-04-02 +80 15:57:15.886][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=89
[W][2025-04-02 +80 15:57:16.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:17.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:17.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:19.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:19.913][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432268919 , reportEventTime = 432278958
[W][2025-04-02 +80 15:57:20.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:20.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:22.286][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:22.290][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:23.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:23.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:28.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:29.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:29.945][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 31 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10031 , taskStartElapsedRealtimeMs = 432278959 , reportEventTime = 432288990
[W][2025-04-02 +80 15:57:31.307][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:31.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:32.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:34.194][31209, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[W][2025-04-02 +80 15:57:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:34.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:38.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:39.189][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 360502
[I][2025-04-02 +80 15:57:39.189][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:57:39.191][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:57:39.191][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15942秒]
[I][2025-04-02 +80 15:57:39.192][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:57:39.986][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432288991 , reportEventTime = 432299031
[W][2025-04-02 +80 15:57:40.310][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:40.313][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:41.798][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:41.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:43.295][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:43.297][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:44.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:47.805][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:47.808][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:49.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:49.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:50.026][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432299031 , reportEventTime = 432309071
[W][2025-04-02 +80 15:57:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:52.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:52.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:53.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:53.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:55.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:55.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:56.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:56.827][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:57:58.292][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:57:58.296][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:57:59.073][31209, 36][temporary-1][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.nucleus.manager.backgroundscan.PreUpdateDownloadTimerJob
[I][2025-04-02 +80 15:57:59.075][31209, 43][temporary-4][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.nucleus.manager.backgroundscan.BackgroundScanTimerJob
[I][2025-04-02 +80 15:57:59.076][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateDownloadTimerJob][][PreUpdateDownloadTimerJob#doWork
[I][2025-04-02 +80 15:57:59.076][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateAppEngine][][preUpdate sendRequest guid : 2058663700530012608
[I][2025-04-02 +80 15:57:59.076][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateDownloadReport][][eventName: pre_update_start_request
[I][2025-04-02 +80 15:57:59.080][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 15:57:59.081][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateAppEngine][][PreUpdateAppEngine 当前已经请求过
[I][2025-04-02 +80 15:57:59.081][31209, 315][Thread_TimerJobQueue][daemon][PreUpdateDownloadReport][][eventName: pre_update_already_request
[I][2025-04-02 +80 15:57:59.228][31209, 315][Thread_TimerJobQueue][daemon][NewBackGroundScanManager][][[galtest] start scan
[W][2025-04-02 +80 15:57:59.229][31209, 315][Thread_TimerJobQueue][daemon][NewBackGroundScanManager][][未赋予存储权限
[W][2025-04-02 +80 15:57:59.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:57:59.868][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:57:59.869][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:57:59.871][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:57:59.872][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:57:59.872][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:57:59.873][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 15:58:00.013][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 15:58:00.066][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432309072 , reportEventTime = 432319111
[W][2025-04-02 +80 15:58:01.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:01.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:02.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:02.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:04.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:04.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:05.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:05.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:07.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:07.328][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:08.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:08.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:58:09.234][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 390547
[I][2025-04-02 +80 15:58:09.235][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:58:09.238][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [15972秒]
[I][2025-04-02 +80 15:58:09.238][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:58:09.239][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:58:10.091][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 25 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10025 , taskStartElapsedRealtimeMs = 432319111 , reportEventTime = 432329136
[W][2025-04-02 +80 15:58:10.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:10.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:11.797][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:11.801][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:13.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:14.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:14.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:19.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:58:20.099][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 432329137 , reportEventTime = 432339143
[W][2025-04-02 +80 15:58:20.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:20.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:22.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:23.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:25.316][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:25.319][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:26.799][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:26.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:28.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:29.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:58:30.132][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 32 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10032 , taskStartElapsedRealtimeMs = 432339144 , reportEventTime = 432349176
[W][2025-04-02 +80 15:58:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:32.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:37.297][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:37.300][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:38.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:58:39.278][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 420590
[I][2025-04-02 +80 15:58:39.278][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:58:39.279][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:58:39.280][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16002秒]
[I][2025-04-02 +80 15:58:39.281][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:58:40.170][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432349177 , reportEventTime = 432359215
[W][2025-04-02 +80 15:58:40.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:40.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:41.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:44.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:44.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:46.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:49.307][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:49.311][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:58:50.210][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432359215 , reportEventTime = 432369255
[W][2025-04-02 +80 15:58:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:50.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:52.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:53.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:53.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:56.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:56.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:58:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:58:59.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:00.018][31209, 38][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 15:59:00.249][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432369256 , reportEventTime = 432379294
[W][2025-04-02 +80 15:59:01.299][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:59:01.346][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:59:01.348][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:59:01.349][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:59:01.350][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:59:01.351][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:59:01.352][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:59:02.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:02.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:04.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:05.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:08.535][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 15:59:08.544][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 15:59:08.544][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[W][2025-04-02 +80 15:59:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:08.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:09.321][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 450633
[I][2025-04-02 +80 15:59:09.321][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:59:09.323][31209, 43][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16032秒]
[I][2025-04-02 +80 15:59:09.324][31209, 43][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 15:59:09.324][31209, 38][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:59:10.290][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432379295 , reportEventTime = 432389335
[W][2025-04-02 +80 15:59:10.300][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:10.303][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:11.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:11.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:14.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:14.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:16.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:16.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:17.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:17.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:19.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:20.306][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 16 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10016 , taskStartElapsedRealtimeMs = 432389335 , reportEventTime = 432399351
[W][2025-04-02 +80 15:59:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:20.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:22.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:22.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:23.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:25.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:26.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:28.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:29.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:30.350][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 44 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10044 , taskStartElapsedRealtimeMs = 432399351 , reportEventTime = 432409395
[W][2025-04-02 +80 15:59:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:31.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:32.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:34.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:35.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:37.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:38.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:39.367][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 480679
[I][2025-04-02 +80 15:59:39.367][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 15:59:39.369][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16062秒]
[I][2025-04-02 +80 15:59:39.369][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:59:39.371][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 15:59:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:40.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:40.390][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432409395 , reportEventTime = 432419435
[W][2025-04-02 +80 15:59:41.794][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:41.798][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:44.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:46.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:46.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:49.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:49.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:59:50.430][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432419435 , reportEventTime = 432429475
[W][2025-04-02 +80 15:59:50.785][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:50.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:52.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:53.785][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:53.788][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:55.305][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:55.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:56.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:56.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:58.282][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:58.285][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:59:59.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:59:59.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:00.034][31209, 45][temporary-6][daemon][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 16:00:00.469][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432429475 , reportEventTime = 432439514
[W][2025-04-02 +80 16:00:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:00:02.842][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:00:02.843][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:00:02.843][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:00:02.843][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:00:02.844][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:00:02.844][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:00:04.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:05.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:07.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:07.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:08.801][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:08.804][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:09.412][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 510724
[I][2025-04-02 +80 16:00:09.412][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:00:09.416][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:00:09.417][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16092秒]
[I][2025-04-02 +80 16:00:09.418][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:00:10.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:10.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:10.509][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432439515 , reportEventTime = 432449554
[W][2025-04-02 +80 16:00:11.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:11.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:13.289][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:13.292][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:14.817][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:14.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:20.543][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 33 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10033 , taskStartElapsedRealtimeMs = 432449555 , reportEventTime = 432459588
[W][2025-04-02 +80 16:00:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:20.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:22.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:23.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:28.297][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:28.299][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:29.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:29.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:30.582][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432459588 , reportEventTime = 432469627
[W][2025-04-02 +80 16:00:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:31.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:32.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:34.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:38.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:39.458][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 540771
[I][2025-04-02 +80 16:00:39.458][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:00:39.461][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:00:39.462][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16122秒]
[I][2025-04-02 +80 16:00:39.464][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:00:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:40.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:40.614][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 32 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10032 , taskStartElapsedRealtimeMs = 432469627 , reportEventTime = 432479659
[W][2025-04-02 +80 16:00:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:43.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:44.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:46.297][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:46.301][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:49.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:49.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:00:50.656][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 432479660 , reportEventTime = 432489701
[W][2025-04-02 +80 16:00:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:52.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:53.797][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:53.800][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:55.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:56.815][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:56.817][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:58.302][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:58.305][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:00:59.798][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:00:59.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:00.008][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 16:01:00.696][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432489701 , reportEventTime = 432499741
[W][2025-04-02 +80 16:01:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:02.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:01:04.358][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:01:04.359][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:01:04.360][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:01:04.360][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:01:04.361][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:01:04.361][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:01:05.799][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:05.803][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:08.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:09.499][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 570812
[I][2025-04-02 +80 16:01:09.499][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:01:09.501][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16152秒]
[I][2025-04-02 +80 16:01:09.501][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:01:09.501][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:01:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:10.734][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432499742 , reportEventTime = 432509779
[W][2025-04-02 +80 16:01:11.800][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:11.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:13.312][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:13.315][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:14.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:14.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:16.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:16.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:17.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:20.774][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432509779 , reportEventTime = 432519819
[W][2025-04-02 +80 16:01:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:20.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:23.212][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 16:01:23.212][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 16:01:23.212][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[W][2025-04-02 +80 16:01:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:23.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:25.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:28.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:29.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:29.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:30.814][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432519819 , reportEventTime = 432529859
[W][2025-04-02 +80 16:01:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:32.791][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:32.794][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:34.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:35.804][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:35.806][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:37.303][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:37.307][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:38.237][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 16:01:38.237][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[I][2025-04-02 +80 16:01:38.237][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=90
[W][2025-04-02 +80 16:01:38.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:38.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:39.543][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743580298687
[I][2025-04-02 +80 16:01:39.543][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 16:01:39.552][31209, 46][temporary-7][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 16:01:39.553][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16182秒]
[I][2025-04-02 +80 16:01:39.555][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 16:01:39.557][31209, 46][temporary-7][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 16:01:39.557][31209, 38][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[W][2025-04-02 +80 16:01:40.305][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:40.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:40.854][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432529859 , reportEventTime = 432539898
[W][2025-04-02 +80 16:01:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:41.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:43.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:43.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:44.807][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:44.809][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:47.786][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:47.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:49.308][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:49.310][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:50.809][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:50.812][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:01:50.889][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 35 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10035 , taskStartElapsedRealtimeMs = 432539899 , reportEventTime = 432549934
[W][2025-04-02 +80 16:01:52.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:52.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:53.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:56.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:56.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:58.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:58.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:01:59.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:01:59.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:00.020][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 16:02:00.930][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 432549934 , reportEventTime = 432559975
[W][2025-04-02 +80 16:02:01.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:01.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:02.792][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:02.793][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:04.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:04.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:05.799][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:02:05.845][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:02:05.846][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:02:05.847][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:02:05.848][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:02:05.849][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:02:05.850][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:02:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:08.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:09.597][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 30054
[I][2025-04-02 +80 16:02:09.597][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:02:09.601][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:02:09.602][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16212秒]
[I][2025-04-02 +80 16:02:09.604][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:02:10.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:10.971][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 432559975 , reportEventTime = 432570016
[W][2025-04-02 +80 16:02:11.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:11.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:13.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:14.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:16.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:16.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:20.806][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:20.812][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:21.010][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432570016 , reportEventTime = 432580055
[W][2025-04-02 +80 16:02:22.301][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:22.304][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:23.807][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:23.809][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:25.302][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:25.306][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:26.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:26.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:28.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:29.797][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:29.800][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:31.050][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432580056 , reportEventTime = 432590095
[W][2025-04-02 +80 16:02:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:31.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:32.804][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:32.808][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:34.305][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:34.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:35.804][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:35.806][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:38.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:39.641][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 60097
[I][2025-04-02 +80 16:02:39.641][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:02:39.642][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:02:39.642][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16242秒]
[I][2025-04-02 +80 16:02:39.643][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:02:40.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:40.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:41.090][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432590096 , reportEventTime = 432600135
[W][2025-04-02 +80 16:02:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:41.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:43.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:02:44.821][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[E][2025-04-02 +80 16:02:44.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:44.821][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[I][2025-04-02 +80 16:02:44.821][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[W][2025-04-02 +80 16:02:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:46.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:47.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:49.305][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:49.308][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:50.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:50.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:51.130][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432600135 , reportEventTime = 432610175
[W][2025-04-02 +80 16:02:52.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:52.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:53.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:02:55.422][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[I][2025-04-02 +80 16:02:55.422][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[I][2025-04-02 +80 16:02:55.422][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=91
[W][2025-04-02 +80 16:02:56.794][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:56.797][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:58.288][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:58.291][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:02:59.813][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:02:59.816][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:00.013][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 16:03:01.169][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432610175 , reportEventTime = 432620214
[W][2025-04-02 +80 16:03:01.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:01.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:02.808][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:02.810][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:04.313][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:04.316][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:05.815][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:05.818][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:07.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:03:07.358][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:03:07.359][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:03:07.361][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:03:07.362][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:03:07.365][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:03:07.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:03:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:08.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:09.682][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 90139
[I][2025-04-02 +80 16:03:09.682][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:03:09.684][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:03:09.685][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16272秒]
[I][2025-04-02 +80 16:03:09.685][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:03:10.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:11.209][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432620215 , reportEventTime = 432630254
[W][2025-04-02 +80 16:03:11.808][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:11.811][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:13.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:13.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:14.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:14.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:16.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:16.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:17.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:17.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:20.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:20.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:21.250][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432630255 , reportEventTime = 432640295
[W][2025-04-02 +80 16:03:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:23.814][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:23.817][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:25.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:25.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:26.826][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:27.050][31209, 121][timer][daemon][GetTopVViewDynamicSplashInfoTimerJob][][GetTopViewSplashInfoTimerJob invoke, get DynamicSplash
[W][2025-04-02 +80 16:03:27.089][31209, 36][temporary-1][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 16:03:27.093][31209, 36][temporary-1][daemon][HttpNetWorkTaskV2][][[521]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:521,useHttp2:false
[I][2025-04-02 +80 16:03:27.094][31209, 36][temporary-1][daemon][TopViewDynamicSplashEngine][][sendRequest, seq: 430
[I][2025-04-02 +80 16:03:27.105][31209, 485][coroutine-bg-485][daemon][Profiler][][[DynamicSplashManagerDaemon] onEvent: >> TopViewRequest << #1 in segment None
Since None Start: 3,578,593,796,850 ns (16,263,430 ms);  Since Last: 0 ns (0 ms).
[I][2025-04-02 +80 16:03:27.125][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]callStart
[I][2025-04-02 +80 16:03:27.126][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 16:03:27.139][31209, 486][protocalManager-486][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/**************, /*************]
[I][2025-04-02 +80 16:03:27.140][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]connectStart
[I][2025-04-02 +80 16:03:27.155][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]connectEnd protocol:http/1.1
[I][2025-04-02 +80 16:03:27.156][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 16:03:27.200][31209, 486][protocalManager-486][daemon][HttpNetWorkTaskV2][][[521]callEnd
[I][2025-04-02 +80 16:03:27.226][31209, 486][protocalManager-486][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 16:03:27.235][31209, 487][net_dedicated-487][daemon][TopViewDynamicSplashEngine][][onRequestSuccess, seq: 430, req: , resp: , ret: 1404, data: (0)
[I][2025-04-02 +80 16:03:27.235][31209, 487][net_dedicated-487][daemon][TopViewDynamicSplashEngine][][onRequestSuccess but no data
[I][2025-04-02 +80 16:03:27.236][31209, 1*][main][daemon][DynamicSplashManager][][received engine response: com.tencent.assistant.component.topview.TopViewResponse$NoData@50f21a4
[I][2025-04-02 +80 16:03:27.237][31209, 1*][main][daemon][DynamicSplashManager][][onResponse new thread
[I][2025-04-02 +80 16:03:27.246][31209, 487][coroutine-bg-487][daemon][Profiler][][[DynamicSplashManagerDaemon] onEvent: >> TopViewResponse << #1 in segment None
Since None Start: 3,578,746,004,142 ns (16,263,582 ms);  Since Last: 0 ns (0 ms).
isSuccess = true,
hasData = false
[I][2025-04-02 +80 16:03:27.308][31209, 58][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[W][2025-04-02 +80 16:03:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:28.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:29.791][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:29.793][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:29.919][31209, 121][timer][daemon][WxTokenRefreshManager][][RefreshWxLoginToken --- Start check, skipExpirationCheck: false
[I][2025-04-02 +80 16:03:29.925][31209, 121][timer][daemon][WxTokenRefreshManager][][RefreshWxLoginToken --- Checking. Token is not expired. Elapsed time: 698,209 ms, Token lifespan: 7,200,000 ms
[I][2025-04-02 +80 16:03:29.926][31209, 121][timer][daemon][WxTokenRefreshManager][][RefreshWxLoginToken --- Token is not expired
[I][2025-04-02 +80 16:03:31.024][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 16:03:31.024][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 16:03:31.024][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 16:03:31.025][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 16:03:31.027][31209, 44][temporary-5][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 16:03:31.070][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 16:03:31.070][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 16:03:31.070][31209, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 16:03:31.070][31209, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=1
[I][2025-04-02 +80 16:03:31.071][31209, 45][temporary-6][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 16:03:31.091][31209, 313][Binder:31209_D][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 16:03:31.280][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 30 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10030 , taskStartElapsedRealtimeMs = 432640295 , reportEventTime = 432650325
[W][2025-04-02 +80 16:03:31.281][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:31.284][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:31.844][31209, 218][LogProcessService-7][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 16:03:31.847][31209, 218][LogProcessService-7][daemon][HttpNetWorkTaskV2][][[522]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:522,useHttp2:false
[I][2025-04-02 +80 16:03:31.865][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]callStart
[I][2025-04-02 +80 16:03:31.866][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-02 +80 16:03:31.892][31209, 486][protocalManagerStatReport-486][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/**************, /*************]
[I][2025-04-02 +80 16:03:31.893][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]connectStart
[I][2025-04-02 +80 16:03:31.912][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]connectEnd protocol:http/1.1
[I][2025-04-02 +80 16:03:31.913][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 16:03:31.943][31209, 486][protocalManagerStatReport-486][daemon][HttpNetWorkTaskV2][][[522]callEnd
[I][2025-04-02 +80 16:03:31.957][31209, 486][protocalManagerStatReport-486][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 16:03:32.077][31209, 313][Binder:31209_D][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 16:03:32.079][31209, 313][Binder:31209_D][daemon][HttpNetWorkTaskV2][][[523]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:523,useHttp2:false
[I][2025-04-02 +80 16:03:32.088][31209, 487][protocalManager-487][daemon][HttpNetWorkTaskV2][][[523]callStart
[I][2025-04-02 +80 16:03:32.088][31209, 487][protocalManager-487][daemon][HttpNetWorkTaskV2][][[523]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 16:03:32.172][31209, 487][protocalManager-487][daemon][HttpNetWorkTaskV2][][[523]callEnd
[I][2025-04-02 +80 16:03:32.194][31209, 487][protocalManager-487][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 16:03:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:32.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:35.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:35.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:37.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:38.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:39.728][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 120185
[I][2025-04-02 +80 16:03:39.728][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:03:39.766][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16302秒]
[I][2025-04-02 +80 16:03:39.767][31209, 45][temporary-6][daemon][CmdMetrics][][all        3        100.0        102      未探测          {}                  
[I][2025-04-02 +80 16:03:39.768][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:03:39.768][31209, 45][temporary-6][daemon][CmdMetrics][][biz        2        100.0        103      未探测          {}                  
[I][2025-04-02 +80 16:03:39.768][31209, 45][temporary-6][daemon][CmdMetrics][][stat       1        100.0        100      未探测          {}                  
[W][2025-04-02 +80 16:03:40.307][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:40.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:41.318][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432650325 , reportEventTime = 432660362
[W][2025-04-02 +80 16:03:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:43.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:44.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:44.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:46.296][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:46.300][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:49.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:49.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:50.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:03:51.358][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432660363 , reportEventTime = 432670402
[W][2025-04-02 +80 16:03:52.289][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:52.293][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:53.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:56.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:56.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:58.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:03:59.783][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:03:59.786][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:00.025][31209, 45][temporary-6][daemon][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:04:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:01.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:01.398][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432670403 , reportEventTime = 432680443
[W][2025-04-02 +80 16:04:02.815][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:02.819][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:04.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:04.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:08.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:04:08.838][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:04:08.839][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:04:08.839][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:04:08.840][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:04:08.840][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:04:08.840][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 16:04:09.776][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 150233
[I][2025-04-02 +80 16:04:09.776][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:04:09.778][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16332秒]
[I][2025-04-02 +80 16:04:09.780][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 16:04:09.781][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[W][2025-04-02 +80 16:04:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:11.436][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432680443 , reportEventTime = 432690481
[W][2025-04-02 +80 16:04:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:11.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:14.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:14.827][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:20.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:21.474][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432690481 , reportEventTime = 432700519
[W][2025-04-02 +80 16:04:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:23.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:25.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:26.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:26.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:28.297][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:28.301][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:29.783][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:29.786][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:31.292][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:31.296][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:31.510][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 36 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10036 , taskStartElapsedRealtimeMs = 432700519 , reportEventTime = 432710555
[W][2025-04-02 +80 16:04:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:32.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:34.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:35.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:37.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:38.809][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:38.812][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:39.822][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 180279
[I][2025-04-02 +80 16:04:39.822][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:04:39.824][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:04:39.824][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16362秒]
[I][2025-04-02 +80 16:04:39.825][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:04:40.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:40.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:41.550][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432710556 , reportEventTime = 432720595
[W][2025-04-02 +80 16:04:41.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:41.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:43.295][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:43.298][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:44.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:44.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:46.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:46.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:47.730][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=92
[I][2025-04-02 +80 16:04:47.730][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=92
[I][2025-04-02 +80 16:04:47.730][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=92
[W][2025-04-02 +80 16:04:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:47.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:49.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:49.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:04:51.591][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 432720595 , reportEventTime = 432730636
[W][2025-04-02 +80 16:04:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:52.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:53.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:55.292][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:55.296][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:56.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:58.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:58.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:04:59.783][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:04:59.787][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:00.027][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:05:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:01.631][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432730636 , reportEventTime = 432740676
[W][2025-04-02 +80 16:05:02.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:02.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:04.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:04.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:05.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:05.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:07.284][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:07.287][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:08.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:09.867][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 210324
[I][2025-04-02 +80 16:05:09.868][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:05:09.869][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16392秒]
[I][2025-04-02 +80 16:05:09.870][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:05:09.870][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:05:10.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:05:10.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:05:10.368][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:05:10.369][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:05:10.370][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:05:10.371][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:05:10.371][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 16:05:11.654][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 23 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10023 , taskStartElapsedRealtimeMs = 432740676 , reportEventTime = 432750699
[W][2025-04-02 +80 16:05:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:14.786][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:14.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:16.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:17.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:19.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:19.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:20.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:20.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:21.694][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432750700 , reportEventTime = 432760739
[W][2025-04-02 +80 16:05:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:23.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:23.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:25.306][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:25.309][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:28.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:29.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:31.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:31.718][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 24 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10024 , taskStartElapsedRealtimeMs = 432760739 , reportEventTime = 432770763
[W][2025-04-02 +80 16:05:32.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:32.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:35.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:35.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:37.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:38.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:39.910][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 240367
[I][2025-04-02 +80 16:05:39.910][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:05:39.914][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16422秒]
[I][2025-04-02 +80 16:05:39.914][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:05:39.916][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:05:40.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:40.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:41.758][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432770763 , reportEventTime = 432780803
[W][2025-04-02 +80 16:05:41.812][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:41.815][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:44.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:44.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:46.288][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:46.290][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:49.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:50.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:50.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:05:51.798][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432780803 , reportEventTime = 432790843
[W][2025-04-02 +80 16:05:52.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:52.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:53.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:55.300][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:55.303][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:56.789][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:56.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:58.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:58.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:05:59.788][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:05:59.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:00.041][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:06:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:01.838][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432790843 , reportEventTime = 432800883
[W][2025-04-02 +80 16:06:02.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:02.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:05.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:05.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:09.954][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 270411
[I][2025-04-02 +80 16:06:09.954][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:06:09.956][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:06:09.956][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16452秒]
[I][2025-04-02 +80 16:06:09.958][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:06:10.308][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:10.311][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:11.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:06:11.861][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:06:11.862][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:06:11.863][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:06:11.864][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:06:11.864][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:06:11.865][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 16:06:11.875][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 432800883 , reportEventTime = 432810920
[W][2025-04-02 +80 16:06:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:13.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:14.789][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:14.792][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:16.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:16.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:19.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:20.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:20.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:21.912][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 36 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10036 , taskStartElapsedRealtimeMs = 432810921 , reportEventTime = 432820957
[W][2025-04-02 +80 16:06:22.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:22.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:23.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:23.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:26.801][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:26.804][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:28.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:29.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:31.923][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 432820957 , reportEventTime = 432830968
[W][2025-04-02 +80 16:06:32.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:32.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:34.296][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:34.300][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:35.810][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:35.814][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:37.310][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:37.314][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:38.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 16:06:39.970][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 300427
[I][2025-04-02 +80 16:06:39.970][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:06:39.972][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16482秒]
[I][2025-04-02 +80 16:06:39.973][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 16:06:39.974][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[W][2025-04-02 +80 16:06:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:40.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:41.787][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:41.790][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:41.935][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 432830969 , reportEventTime = 432840980
[W][2025-04-02 +80 16:06:43.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:43.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:44.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:44.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:46.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:49.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:49.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:50.612][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:06:50.612][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:06:50.612][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[W][2025-04-02 +80 16:06:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:06:51.943][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 432840981 , reportEventTime = 432850988
[W][2025-04-02 +80 16:06:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:52.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:53.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:53.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:55.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:55.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:56.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:58.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:06:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:06:59.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:00.071][31209, 44][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:07:01.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:01.982][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432850988 , reportEventTime = 432861027
[W][2025-04-02 +80 16:07:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:02.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:04.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:04.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:07.427][31209, 89][common_task_pool-89][daemon][halley-cloud-StateHandler][][onDisconnected
[I][2025-04-02 +80 16:07:07.427][31209, 1*][main][daemon][BookingPreDown.LongConnEngine][][onDisconnected
[I][2025-04-02 +80 16:07:07.427][31209, 1*][main][daemon][UpdateBookingLongConnEngine][][onDisconnected
[E][2025-04-02 +80 16:07:08.003][31209, 89][common_task_pool-89][daemon][botid][][doInit request.head.botUid
[W][2025-04-02 +80 16:07:08.006][31209, 89][common_task_pool-89][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 16:07:08.160][31209, 1*][main][daemon][BookingPreDown.LongConnEngine][][onConnected
[I][2025-04-02 +80 16:07:08.160][31209, 1*][main][daemon][UpdateBookingLongConnEngine][][onConnected
[I][2025-04-02 +80 16:07:08.237][31209, 36][temporary-1][daemon][ChanelMsg][][GetChanelMsgEngine 收到长连接下发的浮层数据..
[I][2025-04-02 +80 16:07:08.277][31209, 44][temporary-5][daemon][ChanelMsg][][handleReceivedChannelMsg  msgStruct type: 0 channelMsgId:003759908298991665408 msgType: 1 msgInfo isNull: false
[W][2025-04-02 +80 16:07:08.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:10.013][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 330470
[I][2025-04-02 +80 16:07:10.014][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:07:10.017][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16513秒]
[I][2025-04-02 +80 16:07:10.018][31209, 38][temporary-2][daemon][CmdMetrics][][all        1        100.0        147      未探测          {}                  
[I][2025-04-02 +80 16:07:10.019][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:07:10.019][31209, 38][temporary-2][daemon][CmdMetrics][][biz        1        100.0        147      未探测          {}                  
[W][2025-04-02 +80 16:07:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:10.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:11.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:11.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:12.022][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432861027 , reportEventTime = 432871067
[W][2025-04-02 +80 16:07:13.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:07:13.363][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:07:13.364][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:07:13.365][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:07:13.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:07:13.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:07:13.367][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:07:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:14.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:16.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:17.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:19.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:19.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:20.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:22.061][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432871068 , reportEventTime = 432881106
[W][2025-04-02 +80 16:07:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:23.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:23.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:25.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:26.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:29.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:32.102][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432881107 , reportEventTime = 432891147
[W][2025-04-02 +80 16:07:32.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:32.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:37.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:38.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:40.058][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 360514
[I][2025-04-02 +80 16:07:40.058][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:07:40.061][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:07:40.062][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16543秒]
[I][2025-04-02 +80 16:07:40.063][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:07:40.315][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:40.318][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:42.130][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 27 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10027 , taskStartElapsedRealtimeMs = 432891148 , reportEventTime = 432901175
[W][2025-04-02 +80 16:07:43.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:43.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:44.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:44.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:46.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:46.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:49.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:50.800][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:50.804][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:07:52.171][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432901176 , reportEventTime = 432911216
[W][2025-04-02 +80 16:07:52.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:52.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:53.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:55.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:55.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:56.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:58.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:58.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:07:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:07:59.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:00.067][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:08:01.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:02.211][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432911217 , reportEventTime = 432921256
[I][2025-04-02 +80 16:08:02.604][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:08:02.605][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:08:02.605][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[W][2025-04-02 +80 16:08:02.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:02.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:04.302][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:04.305][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:05.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:08.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:08.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:10.078][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 390535
[I][2025-04-02 +80 16:08:10.078][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:08:10.080][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:08:10.080][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16573秒]
[I][2025-04-02 +80 16:08:10.082][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:08:10.316][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:10.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:11.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:11.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:12.231][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 19 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10019 , taskStartElapsedRealtimeMs = 432921257 , reportEventTime = 432931276
[W][2025-04-02 +80 16:08:13.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:13.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:08:14.866][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:08:14.867][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:08:14.868][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:08:14.869][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:08:14.870][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:08:14.870][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:08:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:16.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:17.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:19.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:19.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:20.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:22.270][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 432931277 , reportEventTime = 432941315
[W][2025-04-02 +80 16:08:22.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:22.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:23.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:23.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:26.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:28.302][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:28.308][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:29.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:29.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:32.310][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432941315 , reportEventTime = 432951355
[W][2025-04-02 +80 16:08:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:32.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:33.303][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:08:33.303][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[I][2025-04-02 +80 16:08:33.303][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=93
[W][2025-04-02 +80 16:08:34.316][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:34.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:35.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:37.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:38.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:40.121][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 420578
[I][2025-04-02 +80 16:08:40.121][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:08:40.123][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16603秒]
[I][2025-04-02 +80 16:08:40.124][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:08:40.124][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:08:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:40.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:41.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:42.349][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 432951355 , reportEventTime = 432961394
[W][2025-04-02 +80 16:08:43.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:44.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:08:44.817][31209, 47][temporary-8][daemon][ToolbarRequest][][sendRequest from:LOOP, process:daemon
[E][2025-04-02 +80 16:08:44.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:44.831][31209, 47][temporary-8][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 16:08:44.918][31209, 47][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 16:08:44.919][31209, 47][temporary-8][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 16:08:44.920][31209, 47][temporary-8][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 16:08:44.920][31209, 47][temporary-8][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 16:08:44.928][31209, 47][temporary-8][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 16:08:44.957][31209, 47][temporary-8][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=77248016384, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=3406, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 16:08:44.977][31209, 47][temporary-8][daemon][ToolbarRequest][][skip same request, from:LOOP, process:daemon
[W][2025-04-02 +80 16:08:46.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:46.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:47.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:49.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:50.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:52.302][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:52.306][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:08:52.390][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432961395 , reportEventTime = 432971435
[I][2025-04-02 +80 16:08:53.478][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=94
[I][2025-04-02 +80 16:08:53.478][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=94
[I][2025-04-02 +80 16:08:53.478][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=94
[W][2025-04-02 +80 16:08:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:53.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:55.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:55.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:56.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:58.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:58.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:08:59.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:08:59.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:00.029][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:09:01.306][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:01.308][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:02.430][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 432971435 , reportEventTime = 432981475
[W][2025-04-02 +80 16:09:02.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:02.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:04.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:05.816][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:05.819][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:07.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:08.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:08.826][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:10.165][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 450622
[I][2025-04-02 +80 16:09:10.166][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:09:10.167][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:09:10.167][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16633秒]
[I][2025-04-02 +80 16:09:10.167][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:09:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:12.444][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 14 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10014 , taskStartElapsedRealtimeMs = 432981475 , reportEventTime = 432991489
[W][2025-04-02 +80 16:09:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:14.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:14.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:16.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:09:16.363][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:09:16.364][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:09:16.364][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:09:16.365][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:09:16.365][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:09:16.366][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:09:17.812][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:17.816][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:20.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:20.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:22.492][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 48 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10048 , taskStartElapsedRealtimeMs = 432991489 , reportEventTime = 433001537
[W][2025-04-02 +80 16:09:23.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:23.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:25.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:26.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:29.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:32.526][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 34 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10034 , taskStartElapsedRealtimeMs = 433001537 , reportEventTime = 433011571
[W][2025-04-02 +80 16:09:32.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:32.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:34.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:35.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:35.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:37.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:38.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:38.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:40.208][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 480665
[I][2025-04-02 +80 16:09:40.208][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:09:40.210][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16663秒]
[I][2025-04-02 +80 16:09:40.211][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:09:40.211][31209, 38][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:09:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:40.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:41.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:41.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:42.567][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 433011571 , reportEventTime = 433021612
[W][2025-04-02 +80 16:09:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:43.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:44.796][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:44.800][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:46.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:46.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:49.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:49.322][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:50.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:50.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:52.323][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:52.326][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:09:52.608][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 433021612 , reportEventTime = 433031653
[W][2025-04-02 +80 16:09:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:53.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:55.289][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:55.292][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:56.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:56.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:09:59.798][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:09:59.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:01.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:02.646][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 433031653 , reportEventTime = 433041691
[W][2025-04-02 +80 16:10:02.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:02.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:04.316][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:04.319][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:05.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:05.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:07.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:07.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:08.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:08.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:10.248][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 510704
[I][2025-04-02 +80 16:10:10.248][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:10:10.250][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:10:10.250][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16693秒]
[I][2025-04-02 +80 16:10:10.252][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:10:10.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:10.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:12.686][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 433041691 , reportEventTime = 433051731
[W][2025-04-02 +80 16:10:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:14.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:14.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:16.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:16.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:10:17.850][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:10:17.851][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:10:17.851][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:10:17.852][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:10:17.852][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:10:17.852][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:10:19.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:19.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:20.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:20.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:22.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:22.724][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 433051731 , reportEventTime = 433061769
[W][2025-04-02 +80 16:10:23.794][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:23.798][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:25.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:25.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:28.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:28.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:29.801][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:29.803][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:31.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:32.762][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 37 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10037 , taskStartElapsedRealtimeMs = 433061770 , reportEventTime = 433071807
[W][2025-04-02 +80 16:10:32.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:32.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:34.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:35.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:35.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:37.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:38.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:38.825][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:40.290][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 540747
[I][2025-04-02 +80 16:10:40.290][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[W][2025-04-02 +80 16:10:40.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:40.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:40.327][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:10:40.327][31209, 43][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16723秒]
[I][2025-04-02 +80 16:10:40.328][31209, 43][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 16:10:41.552][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:10:41.822][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:41.826][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:42.801][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 433071807 , reportEventTime = 433081846
[W][2025-04-02 +80 16:10:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:43.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:44.800][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:44.803][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:46.311][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:46.313][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:49.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:50.798][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:50.802][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:52.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:52.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:10:52.841][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 433081847 , reportEventTime = 433091886
[W][2025-04-02 +80 16:10:53.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:53.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:55.308][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:55.310][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:56.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:56.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:58.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:58.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:10:59.817][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:10:59.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:01.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:01.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:02.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:02.821][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:02.875][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 33 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10033 , taskStartElapsedRealtimeMs = 433091887 , reportEventTime = 433101920
[W][2025-04-02 +80 16:11:04.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:04.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:05.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:05.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:07.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:07.321][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:08.821][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:08.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:10.307][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:10.311][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:10.333][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 570790
[I][2025-04-02 +80 16:11:10.333][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 16:11:10.368][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16753秒]
[I][2025-04-02 +80 16:11:10.369][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:11:10.369][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[W][2025-04-02 +80 16:11:11.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:11.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:12.892][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 17 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10017 , taskStartElapsedRealtimeMs = 433101920 , reportEventTime = 433111937
[W][2025-04-02 +80 16:11:13.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:13.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:14.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:14.822][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:16.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:16.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:17.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:17.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:19.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 16:11:19.343][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 16:11:19.344][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 16:11:19.344][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 16:11:19.345][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 16:11:19.345][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 16:11:19.346][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 16:11:20.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:20.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:22.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:22.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:22.930][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 38 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10038 , taskStartElapsedRealtimeMs = 433111937 , reportEventTime = 433121975
[W][2025-04-02 +80 16:11:23.807][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:23.809][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:25.321][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:25.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:26.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:26.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:27.086][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=95
[I][2025-04-02 +80 16:11:27.086][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=95
[I][2025-04-02 +80 16:11:27.086][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=95
[W][2025-04-02 +80 16:11:28.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:28.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:29.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:29.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:31.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:31.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:32.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:32.824][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:32.970][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 433121975 , reportEventTime = 433132015
[W][2025-04-02 +80 16:11:34.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:34.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:35.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:35.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:37.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:37.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:38.284][31209, 46][temporary-7][daemon][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 16:11:38.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:38.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:40.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:40.323][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:40.344][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743580899543
[I][2025-04-02 +80 16:11:40.344][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 16:11:40.347][31209, 47][temporary-8][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 16:11:40.350][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [16783秒]
[I][2025-04-02 +80 16:11:40.350][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 16:11:40.351][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 16:11:40.353][31209, 47][temporary-8][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[W][2025-04-02 +80 16:11:41.820][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:41.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:43.010][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 40 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10040 , taskStartElapsedRealtimeMs = 433132015 , reportEventTime = 433142055
[W][2025-04-02 +80 16:11:43.319][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:43.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:44.806][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:44.809][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:46.322][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:46.325][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:47.819][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:47.823][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 16:11:49.320][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:49.324][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:50.244][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onCreated
[I][2025-04-02 +80 16:11:50.245][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onCreated;time=1743581510244
[I][2025-04-02 +80 16:11:50.247][31209, 38][temporary-2][daemon][InstallSessionObserver][][onActiveChanged sessionId = 1574280220; active = true
[I][2025-04-02 +80 16:11:50.249][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onActiveChanged
[I][2025-04-02 +80 16:11:50.250][31209, 38][temporary-2][daemon][InstallQueue~][][onActiveChanged, packageName=null, sessionId=1574280220
[I][2025-04-02 +80 16:11:50.256][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.256][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510256
[I][2025-04-02 +80 16:11:50.257][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.256][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.257][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510257
[I][2025-04-02 +80 16:11:50.257][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510257
[I][2025-04-02 +80 16:11:50.257][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.257][31209, 36][temporary-1][daemon][InstallQueue~][][record first progress change time!
[I][2025-04-02 +80 16:11:50.258][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510258
[I][2025-04-02 +80 16:11:50.258][31209, 45][temporary-6][daemon][InstallQueue~][][onSessionCreated, packageName=null, sessionId=1574280220;
[I][2025-04-02 +80 16:11:50.258][31209, 47][temporary-8][daemon][InstallQueue~][][record first progress change time!
[I][2025-04-02 +80 16:11:50.258][31209, 45][temporary-6][daemon][InstallSessionObserver][][not main process!
[I][2025-04-02 +80 16:11:50.258][31209, 40][temporary-3][daemon][InstallQueue~][][record first progress change time!
[I][2025-04-02 +80 16:11:50.259][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.010220993
[I][2025-04-02 +80 16:11:50.259][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.030662982
[I][2025-04-02 +80 16:11:50.259][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.040883973
[I][2025-04-02 +80 16:11:50.259][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.020441987
[I][2025-04-02 +80 16:11:50.280][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.280][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510280
[I][2025-04-02 +80 16:11:50.280][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.05125555
[I][2025-04-02 +80 16:11:50.281][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.282][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510282
[I][2025-04-02 +80 16:11:50.282][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.06147654
[I][2025-04-02 +80 16:11:50.308][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.309][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510309
[I][2025-04-02 +80 16:11:50.310][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.07169753
[I][2025-04-02 +80 16:11:50.311][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.311][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510311
[I][2025-04-02 +80 16:11:50.312][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.08191853
[I][2025-04-02 +80 16:11:50.323][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.323][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510323
[I][2025-04-02 +80 16:11:50.324][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.09213952
[I][2025-04-02 +80 16:11:50.352][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.352][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510352
[I][2025-04-02 +80 16:11:50.353][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.10251819
[I][2025-04-02 +80 16:11:50.357][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.357][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510357
[I][2025-04-02 +80 16:11:50.357][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.11273918
[I][2025-04-02 +80 16:11:50.387][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.388][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510388
[I][2025-04-02 +80 16:11:50.389][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.12296017
[I][2025-04-02 +80 16:11:50.394][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.394][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510394
[I][2025-04-02 +80 16:11:50.395][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.13318117
[I][2025-04-02 +80 16:11:50.412][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.412][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510412
[I][2025-04-02 +80 16:11:50.413][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.14340216
[I][2025-04-02 +80 16:11:50.418][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.418][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510418
[I][2025-04-02 +80 16:11:50.419][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.15362315
[I][2025-04-02 +80 16:11:50.443][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.445][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510445
[I][2025-04-02 +80 16:11:50.447][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.16384415
[I][2025-04-02 +80 16:11:50.448][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.449][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510449
[I][2025-04-02 +80 16:11:50.449][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.17406514
[I][2025-04-02 +80 16:11:50.469][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.469][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510469
[I][2025-04-02 +80 16:11:50.470][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.18428613
[I][2025-04-02 +80 16:11:50.475][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.476][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510475
[I][2025-04-02 +80 16:11:50.477][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.19450712
[I][2025-04-02 +80 16:11:50.486][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.486][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510486
[I][2025-04-02 +80 16:11:50.487][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.20472813
[I][2025-04-02 +80 16:11:50.517][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.518][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510518
[I][2025-04-02 +80 16:11:50.519][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.21495824
[I][2025-04-02 +80 16:11:50.524][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.524][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510524
[I][2025-04-02 +80 16:11:50.526][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.22517924
[I][2025-04-02 +80 16:11:50.543][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.543][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510543
[I][2025-04-02 +80 16:11:50.545][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.23540024
[I][2025-04-02 +80 16:11:50.561][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.561][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510561
[I][2025-04-02 +80 16:11:50.562][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.24574547
[I][2025-04-02 +80 16:11:50.568][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.569][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510569
[I][2025-04-02 +80 16:11:50.569][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.25596648
[I][2025-04-02 +80 16:11:50.590][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.591][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510591
[I][2025-04-02 +80 16:11:50.592][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.26620468
[I][2025-04-02 +80 16:11:50.598][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.598][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510598
[I][2025-04-02 +80 16:11:50.600][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.2764257
[I][2025-04-02 +80 16:11:50.624][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.624][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510624
[I][2025-04-02 +80 16:11:50.628][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.28680333
[I][2025-04-02 +80 16:11:50.633][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.633][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510633
[I][2025-04-02 +80 16:11:50.634][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.29702434
[I][2025-04-02 +80 16:11:50.652][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.653][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510653
[I][2025-04-02 +80 16:11:50.657][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.30724534
[I][2025-04-02 +80 16:11:50.658][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.658][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510658
[I][2025-04-02 +80 16:11:50.660][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.31746635
[I][2025-04-02 +80 16:11:50.677][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.678][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510678
[I][2025-04-02 +80 16:11:50.679][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.32768735
[I][2025-04-02 +80 16:11:50.700][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.700][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510700
[I][2025-04-02 +80 16:11:50.702][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.33790937
[I][2025-04-02 +80 16:11:50.708][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.709][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510709
[I][2025-04-02 +80 16:11:50.709][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.34813038
[I][2025-04-02 +80 16:11:50.718][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.718][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510718
[I][2025-04-02 +80 16:11:50.719][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.35835138
[I][2025-04-02 +80 16:11:50.761][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.762][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510762
[I][2025-04-02 +80 16:11:50.763][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.763][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510763
[I][2025-04-02 +80 16:11:50.764][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.36859468
[I][2025-04-02 +80 16:11:50.764][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.37881568
[I][2025-04-02 +80 16:11:50.784][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.785][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.785][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510785
[I][2025-04-02 +80 16:11:50.785][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510785
[I][2025-04-02 +80 16:11:50.786][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.3890367
[I][2025-04-02 +80 16:11:50.786][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.3992577
[I][2025-04-02 +80 16:11:50.799][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.800][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510800
[I][2025-04-02 +80 16:11:50.800][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.40947866
[I][2025-04-02 +80 16:11:50.803][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.803][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510803
[I][2025-04-02 +80 16:11:50.804][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.41969967
[I][2025-04-02 +80 16:11:50.816][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.816][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510816
[I][2025-04-02 +80 16:11:50.817][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.43005806
[W][2025-04-02 +80 16:11:50.818][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:50.820][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 16:11:50.844][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.845][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510845
[I][2025-04-02 +80 16:11:50.845][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.44030032
[I][2025-04-02 +80 16:11:50.847][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.847][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510847
[I][2025-04-02 +80 16:11:50.848][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.45052132
[I][2025-04-02 +80 16:11:50.873][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.873][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510873
[I][2025-04-02 +80 16:11:50.874][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.46074232
[I][2025-04-02 +80 16:11:50.877][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.877][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510877
[I][2025-04-02 +80 16:11:50.877][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.47096333
[I][2025-04-02 +80 16:11:50.900][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.901][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510901
[I][2025-04-02 +80 16:11:50.902][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.48118433
[I][2025-04-02 +80 16:11:50.925][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.926][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510926
[I][2025-04-02 +80 16:11:50.926][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.49147582
[I][2025-04-02 +80 16:11:50.933][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.934][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510934
[I][2025-04-02 +80 16:11:50.935][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5016968
[I][2025-04-02 +80 16:11:50.960][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.960][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510960
[I][2025-04-02 +80 16:11:50.961][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5119178
[I][2025-04-02 +80 16:11:50.969][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.970][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510970
[I][2025-04-02 +80 16:11:50.970][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.52213883
[I][2025-04-02 +80 16:11:50.984][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.984][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510984
[I][2025-04-02 +80 16:11:50.985][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.53235984
[I][2025-04-02 +80 16:11:50.990][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:50.991][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581510990
[I][2025-04-02 +80 16:11:50.991][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.54258084
[I][2025-04-02 +80 16:11:51.025][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.025][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511025
[I][2025-04-02 +80 16:11:51.027][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5528688
[I][2025-04-02 +80 16:11:51.037][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.038][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511038
[I][2025-04-02 +80 16:11:51.038][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5630898
[I][2025-04-02 +80 16:11:51.053][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.053][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511053
[I][2025-04-02 +80 16:11:51.054][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5733108
[I][2025-04-02 +80 16:11:51.060][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.061][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511060
[I][2025-04-02 +80 16:11:51.061][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5835318
[I][2025-04-02 +80 16:11:51.103][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.105][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.105][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511105
[I][2025-04-02 +80 16:11:51.105][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511105
[I][2025-04-02 +80 16:11:51.106][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6039738
[I][2025-04-02 +80 16:11:51.109][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.5937528
[I][2025-04-02 +80 16:11:51.132][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.134][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511132
[I][2025-04-02 +80 16:11:51.134][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.135][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511134
[I][2025-04-02 +80 16:11:51.135][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6141948
[I][2025-04-02 +80 16:11:51.135][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6244158
[I][2025-04-02 +80 16:11:51.164][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.164][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.164][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511164
[I][2025-04-02 +80 16:11:51.164][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511164
[I][2025-04-02 +80 16:11:51.165][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6448578
[I][2025-04-02 +80 16:11:51.165][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6346368
[I][2025-04-02 +80 16:11:51.191][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.191][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511191
[I][2025-04-02 +80 16:11:51.193][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.6550788
[I][2025-04-02 +80 16:11:51.193][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.193][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511193
[I][2025-04-02 +80 16:11:51.194][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.66529983
[I][2025-04-02 +80 16:11:51.218][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.218][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511218
[I][2025-04-02 +80 16:11:51.218][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.67552084
[I][2025-04-02 +80 16:11:51.220][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.220][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511220
[I][2025-04-02 +80 16:11:51.221][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.68574184
[I][2025-04-02 +80 16:11:51.246][31209, 43][temporary-4][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.246][31209, 43][temporary-4][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511246
[I][2025-04-02 +80 16:11:51.247][31209, 43][temporary-4][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.69596285
[I][2025-04-02 +80 16:11:51.250][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.251][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511250
[I][2025-04-02 +80 16:11:51.251][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.70618385
[I][2025-04-02 +80 16:11:51.283][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.284][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511283
[I][2025-04-02 +80 16:11:51.285][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.71640486
[I][2025-04-02 +80 16:11:51.291][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.291][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511291
[I][2025-04-02 +80 16:11:51.292][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.72662586
[I][2025-04-02 +80 16:11:51.306][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.306][31209, 45][temporary-6][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511306
[I][2025-04-02 +80 16:11:51.307][31209, 45][temporary-6][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.73684686
[I][2025-04-02 +80 16:11:51.309][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.310][31209, 36][temporary-1][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511309
[I][2025-04-02 +80 16:11:51.310][31209, 36][temporary-1][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.74706787
[I][2025-04-02 +80 16:11:51.340][31209, 46][temporary-7][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.340][31209, 46][temporary-7][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511340
[I][2025-04-02 +80 16:11:51.341][31209, 46][temporary-7][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.7573938
[I][2025-04-02 +80 16:11:51.349][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.349][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511349
[I][2025-04-02 +80 16:11:51.350][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.7676148
[I][2025-04-02 +80 16:11:51.414][31209, 45][temporary-6][daemon][InstallSessionObserver][][onActiveChanged sessionId = 1574280220; active = false
[I][2025-04-02 +80 16:11:51.414][31209, 36][temporary-1][daemon][InstallSessionObserver][][onActiveChanged sessionId = 1574280220; active = true
[I][2025-04-02 +80 16:11:51.581][31209, 45][temporary-6][daemon][InstallSessionPackage][][updateSessionInfo processName=onActiveChanged
[I][2025-04-02 +80 16:11:51.582][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.582][31209, 36][temporary-1][daemon][InstallSessionPackage][][updateSessionInfo processName=onActiveChanged
[I][2025-04-02 +80 16:11:51.582][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511582
[I][2025-04-02 +80 16:11:51.582][31209, 38][temporary-2][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.582][31209, 38][temporary-2][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511582
[I][2025-04-02 +80 16:11:51.583][31209, 38][temporary-2][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.7982778
[I][2025-04-02 +80 16:11:51.583][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.7778358
[I][2025-04-02 +80 16:11:51.585][31209, 47][temporary-8][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.586][31209, 47][temporary-8][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511586
[I][2025-04-02 +80 16:11:51.586][31209, 36][temporary-1][daemon][InstallQueue~][][onActiveChanged, packageName=null, sessionId=1574280220
[I][2025-04-02 +80 16:11:51.587][31209, 45][temporary-6][daemon][InstallQueue~][][onActiveChanged, packageName=null, sessionId=1574280220
[I][2025-04-02 +80 16:11:51.588][31209, 47][temporary-8][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.7880568
[I][2025-04-02 +80 16:11:51.589][31209, 44][temporary-5][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:51.589][31209, 44][temporary-5][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581511589
[I][2025-04-02 +80 16:11:51.589][31209, 44][temporary-5][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.8
[I][2025-04-02 +80 16:11:52.233][31209, 40][temporary-3][daemon][InstallSessionPackage][][updateSessionInfo processName=onProgressChanged
[I][2025-04-02 +80 16:11:52.235][31209, 40][temporary-3][daemon][InstallSessionPackage][][recordStepTime, sessionId=1574280220;processName=onProgressChanged;time=1743581512235
[I][2025-04-02 +80 16:11:52.236][31209, 40][temporary-3][daemon][InstallQueue~][][onProgressChanged, packageName=null, sessionId=1574280220; progress = 0.90000004
[W][2025-04-02 +80 16:11:52.318][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 16:11:52.320][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
~~~~~ end of mmap ~~~~~[26145,26200][2025-04-02 +0800 16:12:59]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[26145,26200][2025-04-02 +0800 16:12:59]
get mmap time: 0
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77363146752 available:77228929024
log dir space info, capacity:117409054720 free:77207957504 available:77207957504
