~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[29232,29281][2025-04-02 +0800 23:42:47]
get mmap time: 26
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77348794368 available:77214576640
log dir space info, capacity:117409054720 free:77193605120 available:77193605120
[I][2025-04-02 +80 23:42:47.351][29232, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 23:42:47.351][29232, 36][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 23:42:47.351][29232, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager][initSDK time =65
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 23:42:47.352][29232, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 23:42:47.353][29232, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 93, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 65, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 47, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 54, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][QimeiManager][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][QimeiManager][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.354][29232, 36][temporary-1][live][AstApp][][xlog init finished:live
[E][2025-04-02 +80 23:42:47.356][29232, 40][temporary-3][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-02 +80 23:42:47.363][29232, 40][temporary-3][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 23:42:47.363][29232, 40][temporary-3][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 23:42:47.363][29232, 40][temporary-3][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 23:42:47.363][29232, 40][temporary-3][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 23:42:47.398][29232, 40][temporary-3][live][ActivityThreadHacker][][start Hook successful, dur = 63
[I][2025-04-02 +80 23:42:47.403][29232, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:47.404][29232, 41][temporary-4][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 00:32:41.136][29232, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[I][2025-04-03 +80 00:32:41.165][29232, 39][temporary-2][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 00:32:41.333][29232, 43][temporary-6][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
~~~~~ end of mmap ~~~~~[31895,31973][2025-04-03 +0800 00:54:08]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[31895,31973][2025-04-03 +0800 00:54:08]
get mmap time: 3
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77349601280 available:77215383552
log dir space info, capacity:117409054720 free:77194412032 available:77194412032
