~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 02:14:15.396][6320, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 02:14:15.397][6320, 36][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: MMK<PERSON>Wrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 02:14:15.397][6320, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 02:14:15.397][6320, 36][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 02:14:15.397][6320, 36][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 02:14:15.397][6320, 36][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager][initSDK time =57
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 02:14:15.398][6320, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 02:14:15.399][6320, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[E][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][ActivityThreadHacker][start Hook successful, dur = 24
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 81, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 02:14:15.400][6320, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 59, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 02:14:15.401][6320, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 6
[I][2025-04-03 +80 02:14:15.401][6320, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 02:14:15.401][6320, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 02:14:15.435][6320, 36][temporary-1][cache][live][STGlobal][setAppCaller(64, 1743617655305) from (6), from progress = live
java.lang.RuntimeException
	at yyb8922819.tb.xg.l(ProGuard:384)
	at yyb8922819.tb.xg.k(ProGuard:379)
	at yyb8922819.bb.xs.o(ProGuard:112)
	at com.tencent.assistant.db.contentprovider.WallpaperProvider.c(ProGuard:280)
	at com.live.utils.LiveUtils.startYYB(ProGuard:75)
	at com.tencent.assistant.utils.SysComponentHelper.n(ProGuard:304)
	at com.tencent.assistant.syscomponent.BaseSysComponentProvider.query(ProGuard:40)
	at android.content.ContentProvider.query(ContentProvider.java:1411)
	at android.content.ContentProvider.query(ContentProvider.java:1507)
	at android.content.ContentProvider$Transport.query(ContentProvider.java:275)
	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:107)
	at android.os.Binder.execTransactInternal(Binder.java:1197)
	at android.os.Binder.execTransact(Binder.java:1156)
[I][2025-04-03 +80 02:14:15.435][6320, 36][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 02:14:15.435][6320, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 02:14:15.437][6320, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 64, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 64
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 02:14:15.441][6320, 36][temporary-1][live][AstApp][][xlog init finished:live
[I][2025-04-03 +80 02:14:15.575][6320, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 02:14:15.578][6320, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 02:14:15.578][6320, 40][temporary-3][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 02:14:15.580][6320, 40][temporary-3][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 02:14:15.581][6320, 45][temporary-7][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 02:14:15.581][6320, 42][temporary-5][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 02:14:15.582][6320, 45][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 02:14:15.582][6320, 43][temporary-6][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 02:14:15.583][6320, 43][temporary-6][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 02:14:15.585][6320, 42][temporary-5][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 02:14:15.587][6320, 41][temporary-4][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 02:14:15.592][6320, 41][temporary-4][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 02:14:15.605][6320, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.612][6320, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.612][6320, 39][temporary-2][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 02:14:15.620][6320, 42][temporary-5][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 02:14:15.623][6320, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.624][6320, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.628][6320, 39][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 02:14:15.766][6320, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.777][6320, 42][temporary-5][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 02:14:15.794][6320, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 02:14:15.795][6320, 39][temporary-2][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 02:14:17.426][6320, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 02:14:17.448][6320, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 02:14:17.623][6320, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 02:14:17.625][6320, 57][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 02:14:19.475][6320, 41][temporary-4][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 02:14:19.563][6320, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 02:14:20.345][6320, 36][temporary-1][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 02:14:24.556][6320, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 02:15:30.738][6320, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
~~~~~ end of mmap ~~~~~[22439,22593][2025-04-03 +0800 08:03:10]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[22439,22593][2025-04-03 +0800 08:03:10]
get mmap time: 33
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:76983357440 available:76849139712
log dir space info, capacity:117409054720 free:76828168192 available:76828168192
~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 08:03:10.569][22439, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:03:10.569][22439, 40][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:03:10.569][22439, 40][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:03:10.569][22439, 40][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 08:03:10.569][22439, 40][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager][initSDK time =223
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:10.570][22439, 40][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:03:10.571][22439, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[E][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:03:10.572][22439, 40][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-03 +80 08:03:10.573][22439, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:03:10.576][22439, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:03:10.586][22439, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:03:10.587][22439, 44][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:10.592][22439, 44][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:03:10.593][22439, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:03:10.597][22439, 41][temporary-3][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:03:10.612][22439, 52][temporary-8][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:10.652][22439, 52][temporary-8][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:03:10.679][22439, 41][temporary-3][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:03:10.748][22439, 60][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 239, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:03:10.765][22439, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 258, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:03:10.783][22439, 37][temporary-1][live][ActivityThreadHacker][][start Hook successful, dur = 276
[I][2025-04-03 +80 08:03:12.888][22439, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-03 +80 08:03:12.891][22439, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-03 +80 08:03:12.891][22439, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-03 +80 08:03:12.924][22439, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.928][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 40][temporary-2][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.929][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.930][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.930][22439, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:03:12.930][22439, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:03:12.931][22439, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-03 +80 08:03:12.979][22439, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@502b72d
[I][2025-04-03 +80 08:03:12.980][22439, 72][Binder:22439_8][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-03 +80 08:03:12.981][22439, 72][Binder:22439_8][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-03 +80 08:03:12.981][22439, 72][Binder:22439_8][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-03 +80 08:03:15.418][22439, 52][temporary-8][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:03:15.428][22439, 52][temporary-8][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-03 +80 08:03:15.531][22439, 42][temporary-4][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:03:17.357][22439, 49][BeaconReportHandler][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:17.358][22439, 49][BeaconReportHandler][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:03:17.364][22439, 49][BeaconReportHandler][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:03:17.371][22439, 49][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:17.371][22439, 49][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:17.375][22439, 49][BeaconReportHandler][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:03:22.429][22439, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:06:09.571][22439, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[E][2025-04-03 +80 08:06:09.574][22439, 1*][main][live][FLog_MainBinderManager][][onServiceDisconnected,processFlag:live
[I][2025-04-03 +80 08:06:09.582][22439, 42][temporary-4][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:06:09.593][22439, 42][temporary-4][live][MainBinderManager][][tryToConnect process:live
~~~~~ end of mmap ~~~~~[27395,27442][2025-04-03 +0800 08:10:19]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[27395,27442][2025-04-03 +0800 08:10:19]
get mmap time: 85
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:75542925312 available:75408707584
log dir space info, capacity:117409054720 free:75387736064 available:75387736064
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager][initSDK time =253
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:10:19.657][27395, 36][temporary-1][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 08:10:19.732][27395, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:10:19.732][27395, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:10:19.738][27395, 36][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:10:19.738][27395, 36][temporary-1][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:10:19.738][27395, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:10:19.738][27395, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-03 +80 08:10:19.740][27395, 36][temporary-1][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 281, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 175, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][BinderManager][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][cache][live][BinderManager][addService, service : true
[I][2025-04-03 +80 08:10:19.741][27395, 36][temporary-1][live][AstApp][][xlog init finished:live
[E][2025-04-03 +80 08:10:19.747][27395, 40][temporary-3][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 08:10:19.810][27395, 40][temporary-3][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 08:10:19.810][27395, 40][temporary-3][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 08:10:19.810][27395, 40][temporary-3][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 08:10:19.811][27395, 40][temporary-3][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 08:10:19.862][27395, 51][temporary-8][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:10:19.894][27395, 40][temporary-3][live][ActivityThreadHacker][][start Hook successful, dur = 164
[I][2025-04-03 +80 08:10:19.924][27395, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:19.932][27395, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:19.991][27395, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:10:20.019][27395, 51][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:20.032][27395, 51][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:20.036][27395, 51][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:10:20.150][27395, 51][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:10:20.151][27395, 51][temporary-8][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 08:10:23.783][27395, 36][temporary-1][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:10:23.928][27395, 51][temporary-8][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:10:28.829][27395, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:11:03.598][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:11:03.672][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:12:27.340][27395, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-03 +80 08:12:27.345][27395, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-03 +80 08:12:27.345][27395, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-03 +80 08:12:27.360][27395, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:12:27.364][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.364][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 40][temporary-3][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.365][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.366][27395, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 08:12:27.366][27395, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 08:12:27.367][27395, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-03 +80 08:12:27.387][27395, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@a3da9b0
[I][2025-04-03 +80 08:12:27.390][27395, 92][Binder:27395_6][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-03 +80 08:12:27.404][27395, 92][Binder:27395_6][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-03 +80 08:12:27.404][27395, 92][Binder:27395_6][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-03 +80 08:13:02.719][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:13:02.751][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:13:28.070][27395, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[E][2025-04-03 +80 08:13:28.071][27395, 1*][main][live][FLog_MainBinderManager][][onServiceDisconnected,processFlag:live
[I][2025-04-03 +80 08:13:28.079][27395, 47][temporary-7][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:13:28.087][27395, 47][temporary-7][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-03 +80 08:13:50.173][27395, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:13:50.910][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:13:50.922][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:13:52.649][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:15:43.854][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:17:49.245][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:17:49.950][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:17:49.986][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:19:43.378][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:19:44.700][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:19:44.710][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:20:10.944][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:20:10.966][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:20:56.013][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:20:56.758][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:20:56.783][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:22:24.773][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:23:56.197][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:23:57.369][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:23:57.488][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:23:57.672][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:23:57.774][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:25:07.525][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:25:07.535][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
~~~~~ begin of mmap ~~~~~
[E][2025-04-03 +80 08:25:31.408][27395, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:30:11.836][27395, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:37:39.820][27395, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:37:39.925][27395, 53][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
~~~~~ end of mmap ~~~~~[15183,15207][2025-04-03 +0800 08:40:58]
~~~~~ begin of mmap ~~~~~
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[15183,15207][2025-04-03 +0800 08:40:58]
get mmap time: 16
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:70448087040 available:70313869312
log dir space info, capacity:117409054720 free:70292897792 available:70292897792
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:40:58.908][15183, 36][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][RightlySDKManager][initSDK time =88
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:40:58.909][15183, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[E][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:40:58.910][15183, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:40:58.911][15183, 36][temporary-1][live][AstApp][][xlog init finished:live
[I][2025-04-03 +80 08:40:58.912][15183, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:40:58.919][15183, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[E][2025-04-03 +80 08:40:58.927][15183, 42][temporary-5][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 08:40:58.929][15183, 42][temporary-5][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 08:40:58.930][15183, 42][temporary-5][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 08:40:58.930][15183, 42][temporary-5][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 08:40:58.930][15183, 42][temporary-5][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 08:40:58.941][15183, 59][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 49, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:40:58.945][15183, 44][temporary-7][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 08:40:58.945][15183, 44][temporary-7][live][BinderManager][][connectToServiceOptimize
[I][2025-04-03 +80 08:40:58.949][15183, 42][temporary-5][live][ActivityThreadHacker][][start Hook successful, dur = 33
[I][2025-04-03 +80 08:40:58.961][15183, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:40:58.961][15183, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:40:58.961][15183, 46][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:40:58.962][15183, 46][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:40:58.963][15183, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-03 +80 08:40:58.965][15183, 44][temporary-7][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:40:58.966][15183, 44][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:40:58.966][15183, 41][temporary-4][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:40:58.970][15183, 41][temporary-4][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:40:58.972][15183, 43][temporary-6][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:40:58.984][15183, 43][temporary-6][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:40:59.004][15183, 40][temporary-3][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:40:59.015][15183, 43][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:40:59.019][15183, 43][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:40:59.025][15183, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:40:59.027][15183, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:40:59.029][15183, 43][temporary-6][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:40:59.036][15183, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:40:59.071][15183, 43][temporary-6][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:40:59.073][15183, 43][temporary-6][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 08:41:03.878][15183, 41][temporary-4][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:41:03.926][15183, 41][temporary-4][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 08:41:08.973][15183, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-03 +80 08:42:05.532][15183, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
~~~~~ end of mmap ~~~~~[18224,18288][2025-04-03 +0800 08:53:26]
