~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][MMKVInitiator][need prepare mmkv, from: MMK<PERSON><PERSON>rapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][Settings][NameValueCache: false
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 00:54:08.100][31895, 37][temporary-1][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][RightlySDKManager][initSDK time =85
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 00:54:08.101][31895, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][return NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 00:54:08.102][31895, 37][temporary-1][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][RdefenseInitTask][init
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[E][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 63, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][ActivityThreadHacker][start Hook successful, dur = 50
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 99, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 6
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-03 +80 00:54:08.103][31895, 37][temporary-1][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 00:54:08.104][31895, 37][temporary-1][cache][live][XLog][init, proccess:live
[I][2025-04-03 +80 00:54:08.104][31895, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 00:54:08.104][31895, 37][temporary-1][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 00:54:08.104][31895, 37][temporary-1][live][AstApp][][xlog init finished:live
[I][2025-04-03 +80 00:54:08.107][31895, 45][temporary-6][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-03 +80 00:54:08.108][31895, 45][temporary-6][live][BinderManager][][connectToServiceOptimize
[I][2025-04-03 +80 00:54:08.132][31895, 1*][main][live][STGlobal][][setAppCaller(54, 1743612848128) from (6), from progress = live
java.lang.RuntimeException
	at yyb8922819.tb.xg.l(ProGuard:384)
	at yyb8922819.tb.xg.k(ProGuard:379)
	at yyb8922819.bb.xs.o(ProGuard:112)
	at com.tencent.assistant.db.contentprovider.WallpaperProvider.c(ProGuard:274)
	at com.live.utils.LiveUtils.startYYB(ProGuard:75)
	at yyb8922819.a1.xh$xp.onStartProcess(ProGuard:1819)
	at yyb8922819.x0.xe.r(ProGuard:324)
	at yyb8922819.x0.xd.<init>(ProGuard:30)
	at com.live.sync.YYBLiveSyncService.onCreate(ProGuard:9)
	at android.app.ActivityThread.handleCreateService(ActivityThread.java:5717)
	at android.app.ActivityThread.access$3100(ActivityThread.java:313)
	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2937)
	at android.os.Handler.dispatchMessage(Handler.java:117)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.app.ActivityThread.loopProcess(ActivityThread.java:9986)
	at android.app.ActivityThread.main(ActivityThread.java:9975)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:586)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1240)
[I][2025-04-03 +80 00:54:09.280][31895, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 00:54:09.283][31895, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 00:54:09.284][31895, 46][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 00:54:09.286][31895, 46][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 00:54:09.286][31895, 45][temporary-6][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 00:54:09.288][31895, 45][temporary-6][live][BinderManager][][addService, service : true
[I][2025-04-03 +80 00:54:12.387][31895, 41][temporary-3][live][RdefenseIdleTask][][init
[I][2025-04-03 +80 00:54:12.946][31895, 41][temporary-3][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-03 +80 00:54:13.127][31895, 47][temporary-8][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
~~~~~ end of mmap ~~~~~[6320,6427][2025-04-03 +0800 02:14:15]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[6320,6427][2025-04-03 +0800 02:14:15]
get mmap time: 8
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77342793728 available:77208576000
log dir space info, capacity:117409054720 free:77187604480 available:77187604480
