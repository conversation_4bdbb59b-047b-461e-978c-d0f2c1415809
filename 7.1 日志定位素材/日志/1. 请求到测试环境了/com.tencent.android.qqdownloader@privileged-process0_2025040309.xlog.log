^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[17830,17955][2025-04-03 +0800 08:53:21]
get mmap time: 102
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:0 free:0 available:0
log dir space info, capacity:0 free:0 available:0
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][Settings][NameValueCache: false
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:53:21.708][17830, 36][temporary-1][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][RightlySDKManager][initSDK time =209
[I][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:53:21.709][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:53:21.752][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:53:21.752][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelIdManager][return NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:53:21.753][17830, 36][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:53:21.754][17830, 36][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:53:21.754][17830, 36][temporary-1][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:53:21.754][17830, 36][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:53:21.754][17830, 36][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:53:21.754][17830, 36][temporary-1][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-03 +80 08:53:21.756][17830, 36][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:53:21.756][17830, 36][temporary-1][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 08:53:21.756][17830, 36][temporary-1][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-03 +80 08:53:21.757][17830, 36][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][RdefenseInitTask][init
[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:53:21.758][17830, 36][temporary-1][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][MMKVInitiator][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][MMKVInitiator][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][ActivityThreadHacker][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 08:53:21.759][17830, 36][temporary-1][cache][unknown][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 08:53:21.760][17830, 36][temporary-1][cache][unknown][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 08:53:21.760][17830, 36][temporary-1][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-03 +80 08:53:21.783][17830, 56][StartThread-56][unknown][report_tag][][hookAms = false
[I][2025-04-03 +80 08:53:21.868][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 368, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:53:21.908][17830, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:21.908][17830, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:53:21.929][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 406, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:53:21.931][17830, 56][StartThread-56][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-03 +80 08:53:21.960][17830, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:21.960][17830, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-03 +80 08:53:26.108][17830, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-03 +80 08:53:26.364][17830, 42][temporary-4][unknown][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:53:26.463][17830, 42][temporary-4][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-03 +80 08:53:28.437][17830, 63][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:53:28.437][17830, 63][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:53:28.450][17830, 63][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:28.451][17830, 63][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:53:28.455][17830, 63][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:53:28.732][17830, 100][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:53:33.488][17830, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:54:15.900][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 1, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:54:15.908][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:54:15.908][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:54:15.910][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:54:15.910][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 13, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:54:15.911][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:54:15.911][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = UNKNOWN_VIA, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:54:15.912][17830, 80][Binder:17830_6][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:54:36.806][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:54:37.230][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:54:37.232][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:55:55.487][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:55:56.054][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:55:56.061][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:56:10.219][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:57:02.459][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:57:02.485][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:57:03.100][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:57:03.111][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:58:04.234][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:58:50.464][17830, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-03 +80 08:59:20.005][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:59:20.043][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:59:20.738][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:59:20.799][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:59:24.545][17830, 40][temporary-3][unknown][GdtAdSdkInitTask][][Not main or daemon process
[I][2025-04-03 +80 09:00:16.721][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 09:00:17.168][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:00:17.170][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 09:01:31.150][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 09:02:13.364][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 09:02:13.389][17830, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 09:02:14.355][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:02:14.381][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:03:20.213][17830, 57][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 09:03:20.217][17830, 53][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 09:06:52.560][17830, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[30030,30183][2025-04-03 +0800 09:28:17]
get mmap time: 0
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:63324184576 available:63189966848
log dir space info, capacity:117409054720 free:63168995328 available:63168995328
