class AISummaryDialogFragment : DialogFragment() {

    private var isDialogShown = false

    // 其他代码...

    override fun onStart() {
        super.onStart()
        isDialogShown = true
        val width = ViewUtils.dip2px(context, DIALOG_WIDTH)
        val height = ViewUtils.dip2px(context, DIALOG_HEIGHT)
        dialog?.window?.setLayout(width, height)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isDialogShown = false
        XLog.i(TAG, "onDestroyView")
        // 清理textLoading资源
        stopLoading()
    }

    fun isDialogShown(): Boolean {
        return isDialogShown
    }
}


mAIBtn.setOnClickListener(new OnTMAParamClickListener() {
    @Override
    public void onTMAClick(View v) {
        if (!(context instanceof FragmentActivity)) {
            return;
        }

        FragmentActivity activity = (FragmentActivity) context;
        if (activity.isFinishing()) {
            return;
        }

        if (appName == null) {
            XLog.i(TAG, "AIBtn click, appName is null");
            return;
        }

        final AISummaryDialogFragment dialog = AISummaryDialogFragment.newInstance(appName);
        dialog.show(activity.getSupportFragmentManager(), "ai_summary");

        // 获取缓存内容
        String cachedSummary = AIHelpRequest.INSTANCE.getCachedSummary(appName);

        // 判断是否有缓存，有---直接展示缓存内容
        if (!TextUtils.isEmpty(cachedSummary)) {
            HandlerUtils.getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    if (!dialog.isDialogShown()) {
                        XLog.i(TAG, "dialog is not added");
                        return;
                    }
                    if (!dialog.isContentShowing()) {
                        XLog.i(TAG, "dialog is not content showing");
                        dialog.showContent();
                    }
                    XLog.i(TAG, "cachedSummary: " + cachedSummary);
                    dialog.setContentTextFromCache(cachedSummary);
                }
            });
            return;
        }

        try {
            HandlerUtils.getDefaultHandler().post(new Runnable() {
                @Override
                public void run() {
                    try {
                        sendAIHelpRequestForSummary(dialog, appName);
                    } catch (Exception e) {
                        e.printStackTrace();
                        XLog.e(TAG, "aiHelpListener: request hunyuan api failed.");
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            XLog.e(TAG, "aiHelpListener: click exe failed.");
        }
    }
});