def save_log_by_tags_and_content(
        self,
        filtered_logs: List[str],
        save_logs: List[Tuple[str, str]],
        split_info: Optional[List[Tuple[str, str]]] = None,
        is_fuzzy_match_tag: bool = False,
        dedup_targets: Optional[List[Tuple[str, str]]] = None
    ) -> List[str]:
    """
    根据标签和内容筛选日志，避免重复内容的日志被多次添加。支持传入 split_info，用于截断日志行的无用内容。
    支持针对指定的 (tag, content) 组合进行全局去重。

    :param filtered_logs: 过滤后的日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
    :param save_logs: 需要保存的标签和内容列表，格式为 [(tag, content), ...]
    :param split_info: 根据split_key截取日志行中的无用内容，格式为 [(split_tag, split_key), ...]
                       例如 [('tag1', ' - ')] 表示当日志tag为'tag1'时，内容按' - '分割，保留分割前部分，截断后续无用内容
    :param is_fuzzy_match_tag: 是否模糊匹配标签，True表示标签包含关系，False表示完全匹配
    :param dedup_targets: 需要全局去重的 (tag, content) 列表，只有匹配的才全局去重，其他只去除连续重复
    :return: 过滤后的关键日志列表
    """
    if split_info is None:
        split_info = []
    if dedup_targets is None:
        dedup_targets = []

    key_logs = []
    last_log_content_map = {}  # 记录每个tag对应的上一次添加的日志内容，避免连续重复添加
    dedup_content_map = {}     # 记录每个tag对应的所有已添加内容，用于全局去重

    for log in filtered_logs:
        log_dict = self.parse_log_line(log)
        if not log_dict:
            continue  # 跳过格式不正确的日志行

        log_tag = log_dict['tag']
        log_content = log_dict['content']

        # 根据 split_info 判断是否需要截断内容
        is_split = False
        for split_tag, split_key in split_info:
            tag_match = (split_tag in log_tag) if is_fuzzy_match_tag else (log_tag == split_tag)
            if tag_match and split_key in log_content:
                log_content = log_content.split(split_key, 1)[0]
                is_split = True
                break

        # 根据 save_logs 判断是否需要保留日志
        for tag, content in save_logs:
            tag_match = (tag in log_tag) if is_fuzzy_match_tag else (log_tag == tag)
            if tag_match and content in log_content:
                # 判断当前 (tag, content) 是否在 dedup_targets 中，决定是否全局去重
                need_dedup = False
                for d_tag, d_content in dedup_targets:
                    d_tag_match = (d_tag in log_tag) if is_fuzzy_match_tag else (log_tag == d_tag)
                    if d_tag_match and d_content in log_content:
                        need_dedup = True
                        break

                if need_dedup:
                    # 全局去重
                    if tag not in dedup_content_map:
                        dedup_content_map[tag] = set()
                    if log_content in dedup_content_map[tag]:
                        continue  # 已经添加过，跳过
                    dedup_content_map[tag].add(log_content)
                    if is_split:
                        log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"
                    key_logs.append(log)
                else:
                    # 只去除连续重复内容
                    last_content = last_log_content_map.get(tag)
                    if log_content != last_content:
                        if is_split:
                            log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"
                        key_logs.append(log)
                        last_log_content_map[tag] = log_content
                break

    return key_logs