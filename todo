# 规划
1. 知识库内容召回、iwiki文档解析，辅助日志过滤
2. 知识库更新实时性低。用户Prompt，可能需要详细描述bug逻辑（tag、keywords、业务逻辑，准确过滤关键日志）。  ---- 用户 Prompt 收入知识库
3. 自然语言 转化 json agent（减少开发编辑知识库的成本）

动态收录：
- 分析完成 -     post反馈评价    -  需要：返回 json转化agent 链接 、知识库链接 
            - 需要将{问题入库吗？} 
            - 不需要


**tapd单，开发完需求需要更新知识库。**

- 测评优化
- 模型的Prompt优化





保留json


场景tag keywords，逻辑描述



知识库搜索Agent、自然语言转换json，Agent

# 问题0：企微机器人 - 文件名加上 - 问题

# 问题1：指令抓取，可能bug不是在最新的日志文件里
有些tag 找不到  需要一个个日志文件看过去有没有这个tag
- 从新到旧，过滤tag，有日志再继续，无日志，重新过滤


# 问题2：有的日志就是获取不到用户行为链：没有 BaseActivity


# 问题3：能否先来个逻辑，判断有没有常见错误keywords（知识库），直接过滤出来。


# 问题4 下载文件不存在 放过。 这种误判怎么处理？ 


# result=4 是什么原因？
logReportToServer begin, packageName=com.tencent.nba2kx, versionCode=1069, installType=0, result=4, resultDesc=


# 没有错误码5，模型编造 优化一下Prompt的示例


# 根据keywords，过滤关键日志信息。封装到Prompt中
如 下载，已知失败的日志，所以直接在Prompt中，给出关键失败的日志（如有）。


# 最后的总结，模型会根据 前面信息的随意发挥。是否不要总结？


# 文件查询的话，还是单个文件比较好？
每个文件都有错误码怎么办？如下载场景


# 日志回溯逻辑。如果只有一两行日志呢？
怎么判断是不是要找的关键日志？


# 其他2优化，过滤相邻两条相同的日志 （这个tag已经出现过了这个内容）

2025-02-12 21:14:42.125 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:42.427 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:42.728 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:43.087 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:43.397 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:43.737 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:44.041 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame
2025-02-12 21:14:44.343 I DownloadTag send download DOWNLOADING, pkg=com.tencent.tmgp.sgame


# 英文翻译 会误判 check unknown source
2025-03-25 22:44:11.639 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check broken, installDetailList.size()=1,time = 1742913851089
2025-03-25 22:44:11.648 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check external space, installDetailList.size()=2,time = 1742913851098
2025-03-25 22:44:11.650 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check internal space, installDetailList.size()=3,time = 1742913851099
2025-03-25 22:44:11.651 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check rom, installDetailList.size()=4,time = 1742913851101
2025-03-25 22:44:11.652 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check uid changed, installDetailList.size()=5,time = 1742913851102
2025-03-25 22:44:11.658 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check model downgrade, installDetailList.size()=6,time = 1742913851108
2025-03-25 22:44:11.661 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check manifest, installDetailList.size()=7,time = 1742913851111
2025-03-25 22:44:11.663 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check app version, installDetailList.size()=8,time = 1742913851112
2025-03-25 22:44:11.663 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check unknown source, installDetailList.size()=9,time = 1742913851113
2025-03-25 22:44:11.665 I InstallStManager traceId:0 msg:recordAppInstallDetailLog begin, packageName=com.tencent.nba2kx, versionCode=1050, detailType=-10, result=0, resultDesc=check enhance, installDetailList.size()=10,time = 1742913851114