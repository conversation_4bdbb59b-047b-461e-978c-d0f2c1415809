现在是哪个版本，还有多久结束

前三个直接弄工具 iwiki+灯塔
最后一个 问问其伟+模型建议

1. 查询版本日历、版本节奏、版本计划时间； 
   - 版本日历 简化到 iwiki （时间、事项）  ==== 表格视图接口，获取时间和事件
   - 发版流程信息获取到机器人 -- stephenyuan
   - 版本步骤 tedi 现在时间是哪个步骤/哪个版本，后续的步骤计划

2. 查询某个版本的需求列表
   - 版本需求列表获取 (stephenyuan) ? 
   - 版本灰度数据 sql?
   - 呈现方式设计
   - 
3. 查询版本覆盖率；---- 灯塔看板  
4. 收集并分析版本灰度数据、给出发布决策建议；
   - crash率和anr率 === bugly接口  == 通过 各版本的指标统计 计算
   - 工蜂接口 - 拉需求列表

版本工具，发版流程 -- stephenyuan


这边工作涉及到 发版流程熟悉，灯塔接口、bugly接口、iwiki接口调研，许多权限api申请配置，设计呈现方式，写方案。


100
101
102
110
111

898
899
900
901
1000
1001



# TODO 从 iwiki 获取QUA版本信息
# TODO 灰度时间获取，获取为一个list
# 注意：需要4个日期对应4天的数据  如果只有三个，默认日期为/


# 启动速度
# TODO 注意时间排序是否正确
# TODO 整合for循环的qua数据，不要重复for
# TODO 老用户（套壳）的数据


查询数据比较费时，有些数据的查询，是不是可以新用户和老用户一起查询出来，不用再两个批次进行查询。
不行 可能日期不一样。日期对查询结果有影响

# iwiki获取内容的方法 后续写到灰度数据分析api里

# 把调试信息删掉 表情删掉

# crash 精度问题 导致会18也标红 可以显示实际crash
    
# 映射正确性 如何？

# 模型更换

# 文档直接更新（就内容删掉，只保留配置内容），不追加

# 灰度分析优化！！！用户选择是否只查看指定的iwiki链接，不进行捞数据。

# 900 为什么会 list index out of range

# 修改ai异常分析冗余

# 为什么有些tapd单子是空的。

# 对一下需求列表数据对不对

检查联网数据 

把sql语句抽出来 放在常量

下载安装cvr 会不会超时？

怎么把灰度分析文档的qua解析出来？

异常数据分析 可靠性检测


三个模块

日志工具
灰度分析收集
版本问题咨询
   - 意图识别


ai分析数据 
如果数据有异常，还能不能给他一些其他相关的数据来帮助模型分析。
