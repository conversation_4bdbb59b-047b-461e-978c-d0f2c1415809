def auto_collect_gray_data(version):
    """从iwiki文档解析配置，自动收集数据并追加到iwiki文档"""
    print("\n" + "=" * 60)
    print("从iwiki文档解析配置，自动收集数据并追加到iwiki文档")
    print("=" * 60)

    api = create_gray_data_api()

    # 从iWiki文档解析配置
    # version = "900"
    iwiki_url, custom_config = api.get_config_from_iwiki(version)
    print(f"\n从iWiki文档解析配置:\n{iwiki_url}")

    # 收集数据
    gray_data, anomaly_summary = collect_gray_data(custom_config)

    # 生成报告
    # 追加到iWiki文档
    # docid = 4015240554
    docid = 4015180169
    # title = "900-0615"
    title = f"{version}灰度实验分析"
    print("\n生成报告内容...")
    api.append_to_iwiki(
        iwiki_url=iwiki_url,
        title=title,
        params=gray_data,
        anomaly_summary=anomaly_summary
    )
    print(f"访问链接: {iwiki_url}")

    return iwiki_url