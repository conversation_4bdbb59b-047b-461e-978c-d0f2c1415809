很好.我调整了下代码，你再重新看看。继续 在common下的 client/beacon_client.py和 constant/beacon_sql_constants.py中修改。增加 查询 弹窗成功率。
data_source_id=1235751
参考sql如下。

SELECT ds AS '时间' ,
       qua AS '版本号',
       pop_times AS '弹窗人数' ,
       expose_times AS '曝光人数',
       CASE
           WHEN cast(round((expose_times / pop_times) * 100,2) AS float) <= 100 THEN cast(round((expose_times / pop_times) * 100,2) AS float)
           ELSE 100
       END AS '弹窗成功率(%)'
FROM
  (SELECT substring(cast(ds AS string), 1, 8) AS ds,
          qua,
          count(DISTINCT if(event_code = 'st_pop_action_request' AND uni_start_abort IN (0), t1.guid,NULL)) AS pop_times,
          count(DISTINCT if(event_code = 'st_pop_action_response' , t1.guid,NULL)) AS expose_times
   FROM
     (SELECT ds,
             event_code AS event_code,
             CASE WHEN (event_code = 'st_pop_action_response') THEN C23 WHEN (event_code = 'st_pop_action_request') THEN C17 ELSE NULL END AS guid,
             CASE WHEN (event_code = 'st_pop_action_response') THEN C24 WHEN (event_code = 'st_pop_action_request') THEN C18 ELSE NULL END AS qua,
             CASE WHEN (event_code = 'st_pop_action_response') THEN NULL WHEN (event_code = 'st_pop_action_request') THEN CAST(C03 AS BIGINT) ELSE NULL END AS uni_start_abort
      FROM beacon_olap.t_od_light_olap_0m300etnja170g1m
      WHERE cast(substr(cast(ds as string),1,8) as bigint) BETWEEN #timerange.start# AND #timerange.end#
        AND ((event_code = 'st_pop_action_response' AND C24 IN (#qua_val#)) OR (event_code = 'st_pop_action_request' AND C18 IN (#qua_val#)))
        AND product_id = '0M300ETNJA170G1M' ) t1 
JOIN
(SELECT guid,user_type
 FROM beacon_olap.dws_yyb_dau_di
 WHERE ds BETWEEN #timerange.start# AND #timerange.end#
   AND user_type IN (#user_type_val#)
 GROUP BY guid,user_type)t2 ON t1.guid = t2.guid
   GROUP BY 1,2)t
order by 1