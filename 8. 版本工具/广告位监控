很好，我调整了下代码，你再重新看看。继续 在common下的 client/beacon_client.py和 constant/beacon_sql_constants.py中修改。增加 查询 广告数据（广告-曝光，广告-点击，广告-下载，广告-安装）。
然后再额外计算一下 点击 / 曝光，下载 / 点击，安装 / 下载。


data_source_id=1235751


参考sql如下。下面的sql是有拆分广告位（adposid）。现在要求最后的数据都是基于所有广告位（adposid）看的数，不要按照广告位（adposid）拆分看。


select imp_date,
  yyb_version,
  scene,
  adposid,
  coalesce(max(avg_exp_cnt), 0) as avg_exp_cnt,
  coalesce(max(avg_click_cnt), 0) as avg_click_cnt,
  coalesce(max(avg_dload_cnt), 0) as avg_dload_cnt,
  coalesce(max(avg_install_cnt), 0) as avg_install_cnt
from (
    select imp_date,
      yyb_version,
      scene,
      adposid,
      sum(
        case
          when event_code = 'AppExposure' then num
        end
      ) avg_exp_cnt,
      sum(
        case
          when event_code = 'AppClick' then num
        end
      ) avg_click_cnt,
      sum(
        case
          when event_code = 'AppSuccDownload' then num
        end
      ) avg_dload_cnt,
      sum(
        case
          when event_code = 'AppSuccInstall' then num
        end
      ) avg_install_cnt
    from (
        select imp_date,
          guid,
          case
            when #is_scene# = 1 then scene else 'all' end as scene
,
            adposid,
            event_code,
            case
              when 'all' in (
                #version#) 
                then 'all'
                else yyb_version
              end as yyb_version,
              count(1) as num
              from pcg_yyb_adbilling.dwd_yyb_business_event_log_di
              where imp_date >= #imp_date.start#
                and imp_date <= #imp_date.end#
                and is_yyb = 1
                and event_code in (
                  'AppExposure',
                  'AppClick',
                  'AppSuccDownload',
                  'AppSuccInstall'
                )
                and adposid in (
                  18,
                  34,
                  1,
                  1139,
                  1162,
                  13,
                  1021,
                  1160,
                  174,
                  1072,
                  1165,
                  799,
                  346
                )
              group by imp_date,
                guid,
                scene,
                adposid,
                event_code,
                yyb_version
            ) a
            group by imp_date,
              scene,
              adposid,
              yyb_version
          ) t
        where yyb_version in (
            #version#)
            group by imp_date,
              scene,
              adposid,
              yyb_version
            order by imp_date desc,
              adposid asc