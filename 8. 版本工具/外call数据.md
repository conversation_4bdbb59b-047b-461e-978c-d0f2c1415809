增加一个查询 外call开始下载率 和  外call成功下载率 的方法。
data_source_id=1235751

很好，继续 在common下的 client/beacon_client.py和 constant/beacon_sql_constants.py中修改。增加 查询 云游插件拉起成功率。
data_source_id=1235751

参考sql、和 计算指标说明 如下。其中，端内插件点击到实际执行率 就是 云游插件拉起成功率

index_0 表示 tamst跳转用户数
index_1 表示 openActivity用户数
index_2 表示 openActivity-实际启动用户数 
temp_index_3 表示 端内插件点击到拉起率
temp_index_4 表示 端内插件点击到实际执行率

select
  substring(event_time, 1, 10) as dim_0,
  app_version as dim_1,
  NDV(
    CASE
      WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
      AND (
        (
          stage_name IN ('pip_point_uri_call')
          AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
        )
      ) THEN uin
    END
  ) as index_0,
  NDV(
    CASE
      WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
      AND (
        (
          stage_name IN ('pip_point_open_activity')
          AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
        )
      ) THEN uin
    END
  ) as index_1,
  NDV(
    CASE
      WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
      AND (
        (
          stage_name IN ('pip_point_open_activity_start')
          AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
        )
      ) THEN uin
    END
  ) as index_2,(
    NDV(
      CASE
        WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
        AND (
          (
            stage_name IN ('pip_point_open_activity')
            AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
          )
        ) THEN uin
      END
    )
  ) /(
    NDV(
      CASE
        WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
        AND (
          (
            stage_name IN ('pip_point_uri_call')
            AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
          )
        ) THEN uin
      END
    )
  ) as temp_index_3,(
    NDV(
      CASE
        WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
        AND (
          (
            stage_name IN ('pip_point_open_activity_start')
            AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
          )
        ) THEN uin
      END
    )
  ) /(
    NDV(
      CASE
        WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
        AND (
          (
            stage_name IN ('pip_point_open_activity')
            AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
          )
        ) THEN uin
      END
    )
  ) as temp_index_4
from
  (
    SELECT
      app_version AS app_version,
      event_code AS event_code,
      c03 AS stage_name,
      uin AS uin,
      c01 AS plugin_name,
      event_time AS event_time
    FROM
      beacon_olap.t_od_light_olap_0m300etnja170g1m
    WHERE
      (
        (
          (
            (
              (
                ((ds >= 2025060600))
                AND ((ds < 2025060901))
              )
            )
            AND (
              (
                (event_time >= '2025-06-06 00:00:00')
                AND (event_time < '2025-06-09 00:00:00')
              )
            )
          )
        )
        AND (((event_code = 'plugin_pip_cost_event')))
        AND (
          (
            (
              (
                c01 IN ('com.tencent.assistant.plugin.cloudgame')
                AND app_version IN (
                  '8.9.9_8994130_8549',
                  '8.9.9_8994130_8551',
                  '8.9.9_8994130_8552',
                  '8.9.9_8994130_8547',
                  '8.9.9_8994130_8548',
                  '8.9.9_8994130_8550'
                )
              )
            )
          )
        )
        AND ((product_id = '0M300ETNJA170G1M'))
      )
  ) t
group by
  dim_0,
  dim_1
order by
  dim_0 desc
limit
  5000