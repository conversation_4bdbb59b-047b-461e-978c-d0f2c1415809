GRAY_DATA_OLD_USERS_CRASH_ANR = """
# 灰度老用户（套壳9） 数据
## crash率 & ANR率
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度老用户（套壳9）</th>
      <th>QUA</th>
      <th>设备crash率<br>(<=0.18%)</th>
      <th>设备平均崩溃率<br>(<=2%)</th>
      <th>前台设备崩溃率<br>(<=0.06%)</th>
      <th>ANR率<br>(<=0.05%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_old_1}</td>
      <td>{device_crash_old_1_day1}</td>
      <td>{avg_device_crash_old_1_day1}</td>
      <td>{fg_device_crash_old_1_day1}</td>
      <td>{anr_rate_old_1_day1}</td>
      <td>{date_old_1_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_old_1_day2}</td>
      <td>{avg_device_crash_old_1_day2}</td>
      <td>{fg_device_crash_old_1_day2}</td>
      <td>{anr_rate_old_1_day2}</td>
      <td>{date_old_1_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_old_1_day3}</td>
      <td>{avg_device_crash_old_1_day3}</td>
      <td>{fg_device_crash_old_1_day3}</td>
      <td>{anr_rate_old_1_day3}</td>
      <td>{date_old_1_day3}</td>
    </tr>
    <tr>
       <td>{device_crash_old_1_day4}</td>
       <td>{avg_device_crash_old_1_day4}</td>
       <td>{fg_device_crash_old_1_day4}</td>
       <td>{anr_rate_old_1_day4}</td>
       <td>{date_old_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_old_2}</td>
      <td>{device_crash_old_2_day1}</td>
      <td>{avg_device_crash_old_2_day1}</td>
      <td>{fg_device_crash_old_2_day1}</td>
      <td>{anr_rate_old_2_day1}</td>
      <td>{date_old_2_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_old_2_day2}</td>
      <td>{avg_device_crash_old_2_day2}</td>
      <td>{fg_device_crash_old_2_day2}</td>
      <td>{anr_rate_old_2_day2}</td>
      <td>{date_old_2_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_old_2_day3}</td>
      <td>{avg_device_crash_old_2_day3}</td>
      <td>{fg_device_crash_old_2_day3}</td>
      <td>{anr_rate_old_2_day3}</td>
      <td>{date_old_2_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_old_2_day4}</td>
      <td>{avg_device_crash_old_2_day4}</td>
      <td>{fg_device_crash_old_2_day4}</td>
      <td>{anr_rate_old_2_day4}</td>
      <td>{date_old_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_old_3}</td>
      <td>{device_crash_old_3_day1}</td>
      <td>{avg_device_crash_old_3_day1}</td>
      <td>{fg_device_crash_old_3_day1}</td>
      <td>{anr_rate_old_3_day1}</td>
      <td>{date_old_3_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_old_3_day2}</td>
      <td>{avg_device_crash_old_3_day2}</td>
      <td>{fg_device_crash_old_3_day2}</td>
      <td>{anr_rate_old_3_day2}</td>
      <td>{date_old_3_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_old_3_day3}</td>
      <td>{avg_device_crash_old_3_day3}</td>
      <td>{fg_device_crash_old_3_day3}</td>
      <td>{anr_rate_old_3_day3}</td>
      <td>{date_old_3_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_old_3_day4}</td>
      <td>{avg_device_crash_old_3_day4}</td>
      <td>{fg_device_crash_old_3_day4}</td>
      <td>{anr_rate_old_3_day4}</td>
      <td>{date_old_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_old_control}</td>
      <td>{device_crash_old_control_day1}</td>
      <td>{avg_device_crash_old_control_day1}</td>
      <td>{fg_device_crash_old_control_day1}</td>
      <td>{anr_rate_old_control_day1}</td>
      <td>{date_old_control_day1}</td>
    </tr>
    <tr>
      <td>{device_crash_old_control_day2}</td>
      <td>{avg_device_crash_old_control_day2}</td>
      <td>{fg_device_crash_old_control_day2}</td>
      <td>{anr_rate_old_control_day2}</td>
      <td>{date_old_control_day2}</td>
    </tr>
    <tr>
      <td>{device_crash_old_control_day3}</td>
      <td>{avg_device_crash_old_control_day3}</td>
      <td>{fg_device_crash_old_control_day3}</td>
      <td>{anr_rate_old_control_day3}</td>
      <td>{date_old_control_day3}</td>
    </tr>
    <tr>
      <td>{device_crash_old_control_day4}</td>
      <td>{avg_device_crash_old_control_day4}</td>
      <td>{fg_device_crash_old_control_day4}</td>
      <td>{anr_rate_old_control_day4}</td>
      <td>{date_old_control_day4}</td>
    </tr>
  </tbody>
</table>
"""

GRAY_DATA_OLD_USERS_STARTUP = """

## 启动速度
| 灰度老用户（套壳9） | QUA |常规热启动<br>(gap<50ms) | 常规冷启动<br>(gap<100ms) | 常规外call热启动<br>(gap<50ms) | 常规外call冷启动<br>(gap<50ms) |
|--------|--------------|---------|------|------|-------|
|实验组1|{qua_old_1}|{regular_hot_old_1}|{regular_cold_old_1}|{regular_outcall_hot_old_1}|{regular_outcall_cold_old_1}|
|实验组2|{qua_old_2}|{regular_hot_old_2}|{regular_cold_old_2}|{regular_outcall_hot_old_2}|{regular_outcall_cold_old_2}|
|实验组3|{qua_old_3}|{regular_hot_old_3}|{regular_cold_old_3}|{regular_outcall_hot_old_3}|{regular_outcall_cold_old_3}|
|对照组|{qua_old_control}|{regular_hot_old_control}|{regular_cold_old_control}|{regular_outcall_hot_old_control}|{regular_outcall_cold_old_control}|
"""

GRAY_DATA_OLD_USERS_DOWNLOAD = """

## 下载相关
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度老用户（套壳9）</th>
      <th>QUA</th>
      <th>外call开始下载率<br>(gap<0.7%)</th>
      <th>外call成功下载率<br>(gap<0.7%)</th>
      <th>下载安装CVR(%)<br>(大盘下载安装cvr)<br>(gap<1%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_old_1}</td>
      <td>{outcall_start_download_old_1_day1}</td>
      <td>{outcall_success_download_old_1_day1}</td>
      <td>{download_install_cvr_old_1_day1}</td>
      <td>{date_old_1_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_1_day2}</td>
      <td>{outcall_success_download_old_1_day2}</td>
      <td>{download_install_cvr_old_1_day2}</td>
      <td>{date_old_1_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_1_day3}</td>
      <td>{outcall_success_download_old_1_day3}</td>
      <td>{download_install_cvr_old_1_day3}</td>
      <td>{date_old_1_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_1_day4}</td>
      <td>{outcall_success_download_old_1_day4}</td>
      <td>{download_install_cvr_old_1_day4}</td>
      <td>{date_old_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_old_2}</td>
      <td>{outcall_start_download_old_2_day1}</td>
      <td>{outcall_success_download_old_2_day1}</td>
      <td>{download_install_cvr_old_2_day1}</td>
      <td>{date_old_2_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_2_day2}</td>
      <td>{outcall_success_download_old_2_day2}</td>
      <td>{download_install_cvr_old_2_day2}</td>
      <td>{date_old_2_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_2_day3}</td>
      <td>{outcall_success_download_old_2_day3}</td>
      <td>{download_install_cvr_old_2_day3}</td>
      <td>{date_old_2_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_2_day4}</td>
      <td>{outcall_success_download_old_2_day4}</td>
      <td>{download_install_cvr_old_2_day4}</td>
      <td>{date_old_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_old_3}</td>
      <td>{outcall_start_download_old_3_day1}</td>
      <td>{outcall_success_download_old_3_day1}</td>
      <td>{download_install_cvr_old_3_day1}</td>
      <td>{date_old_3_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_3_day2}</td>
      <td>{outcall_success_download_old_3_day2}</td>
      <td>{download_install_cvr_old_3_day2}</td>
      <td>{date_old_3_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_3_day3}</td>
      <td>{outcall_success_download_old_3_day3}</td>
      <td>{download_install_cvr_old_3_day3}</td>
      <td>{date_old_3_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_3_day4}</td>
      <td>{outcall_success_download_old_3_day4}</td>
      <td>{download_install_cvr_old_3_day4}</td>
      <td>{date_old_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_old_control}</td>
      <td>{outcall_start_download_old_control_day1}</td>
      <td>{outcall_success_download_old_control_day1}</td>
      <td>{download_install_cvr_old_control_day1}</td>
      <td>{date_old_control_day1}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_control_day2}</td>
      <td>{outcall_success_download_old_control_day2}</td>
      <td>{download_install_cvr_old_control_day2}</td>
      <td>{date_old_control_day2}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_control_day3}</td>
      <td>{outcall_success_download_old_control_day3}</td>
      <td>{download_install_cvr_old_control_day3}</td>
      <td>{date_old_control_day3}</td>
    </tr>
    <tr>
      <td>{outcall_start_download_old_control_day4}</td>
      <td>{outcall_success_download_old_control_day4}</td>
      <td>{download_install_cvr_old_control_day4}</td>
      <td>{date_old_control_day4}</td>
    </tr>
  </tbody>
</table>
"""

GRAY_DATA_OLD_USERS_AD = """

## 广告相关
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度老用户（套壳9）</th>
      <th>QUA</th>
      <th>广告-曝光</th>
      <th>广告-点击</th>
      <th>广告-下载</th>
      <th>广告-安装</th>
      <th>点击 / 曝光<br>(gap<=0.001)</th>
      <th>下载 / 点击<br>(gap<=0.1)</th>
      <th>安装 / 下载<br>(gap<=0.1)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_old_1}</td>
      <td>{ad_exposure_old_1_day1}</td>
      <td>{ad_click_old_1_day1}</td>
      <td>{ad_download_old_1_day1}</td>
      <td>{ad_install_old_1_day1}</td>
      <td>{click_exposure_ratio_old_1_day1}</td>
      <td>{download_click_ratio_old_1_day1}</td>
      <td>{install_download_ratio_old_1_day1}</td>
      <td>{date_old_1_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_1_day2}</td>
      <td>{ad_click_old_1_day2}</td>
      <td>{ad_download_old_1_day2}</td>
      <td>{ad_install_old_1_day2}</td>
      <td>{click_exposure_ratio_old_1_day2}</td>
      <td>{download_click_ratio_old_1_day2}</td>
      <td>{install_download_ratio_old_1_day2}</td>
      <td>{date_old_1_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_1_day3}</td>
      <td>{ad_click_old_1_day3}</td>
      <td>{ad_download_old_1_day3}</td>
      <td>{ad_install_old_1_day3}</td>
      <td>{click_exposure_ratio_old_1_day3}</td>
      <td>{download_click_ratio_old_1_day3}</td>
      <td>{install_download_ratio_old_1_day3}</td>
      <td>{date_old_1_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_1_day4}</td>
      <td>{ad_click_old_1_day4}</td>
      <td>{ad_download_old_1_day4}</td>
      <td>{ad_install_old_1_day4}</td>
      <td>{click_exposure_ratio_old_1_day4}</td>
      <td>{download_click_ratio_old_1_day4}</td>
      <td>{install_download_ratio_old_1_day4}</td>
      <td>{date_old_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_old_2}</td>
      <td>{ad_exposure_old_2_day1}</td>
      <td>{ad_click_old_2_day1}</td>
      <td>{ad_download_old_2_day1}</td>
      <td>{ad_install_old_2_day1}</td>
      <td>{click_exposure_ratio_old_2_day1}</td>
      <td>{download_click_ratio_old_2_day1}</td>
      <td>{install_download_ratio_old_2_day1}</td>
      <td>{date_old_2_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_2_day2}</td>
      <td>{ad_click_old_2_day2}</td>
      <td>{ad_download_old_2_day2}</td>
      <td>{ad_install_old_2_day2}</td>
      <td>{click_exposure_ratio_old_2_day2}</td>
      <td>{download_click_ratio_old_2_day2}</td>
      <td>{install_download_ratio_old_2_day2}</td>
      <td>{date_old_2_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_2_day3}</td>
      <td>{ad_click_old_2_day3}</td>
      <td>{ad_download_old_2_day3}</td>
      <td>{ad_install_old_2_day3}</td>
      <td>{click_exposure_ratio_old_2_day3}</td>
      <td>{download_click_ratio_old_2_day3}</td>
      <td>{install_download_ratio_old_2_day3}</td>
      <td>{date_old_2_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_2_day4}</td>
      <td>{ad_click_old_2_day4}</td>
      <td>{ad_download_old_2_day4}</td>
      <td>{ad_install_old_2_day4}</td>
      <td>{click_exposure_ratio_old_2_day4}</td>
      <td>{download_click_ratio_old_2_day4}</td>
      <td>{install_download_ratio_old_2_day4}</td>
      <td>{date_old_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_old_3}</td>
      <td>{ad_exposure_old_3_day1}</td>
      <td>{ad_click_old_3_day1}</td>
      <td>{ad_download_old_3_day1}</td>
      <td>{ad_install_old_3_day1}</td>
      <td>{click_exposure_ratio_old_3_day1}</td>
      <td>{download_click_ratio_old_3_day1}</td>
      <td>{install_download_ratio_old_3_day1}</td>
      <td>{date_old_3_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_3_day2}</td>
      <td>{ad_click_old_3_day2}</td>
      <td>{ad_download_old_3_day2}</td>
      <td>{ad_install_old_3_day2}</td>
      <td>{click_exposure_ratio_old_3_day2}</td>
      <td>{download_click_ratio_old_3_day2}</td>
      <td>{install_download_ratio_old_3_day2}</td>
      <td>{date_old_3_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_3_day3}</td>
      <td>{ad_click_old_3_day3}</td>
      <td>{ad_download_old_3_day3}</td>
      <td>{ad_install_old_3_day3}</td>
      <td>{click_exposure_ratio_old_3_day3}</td>
      <td>{download_click_ratio_old_3_day3}</td>
      <td>{install_download_ratio_old_3_day3}</td>
      <td>{date_old_3_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_3_day4}</td>
      <td>{ad_click_old_3_day4}</td>
      <td>{ad_download_old_3_day4}</td>
      <td>{ad_install_old_3_day4}</td>
      <td>{click_exposure_ratio_old_3_day4}</td>
      <td>{download_click_ratio_old_3_day4}</td>
      <td>{install_download_ratio_old_3_day4}</td>
      <td>{date_old_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_old_control}</td>
      <td>{ad_exposure_old_control_day1}</td>
      <td>{ad_click_old_control_day1}</td>
      <td>{ad_download_old_control_day1}</td>
      <td>{ad_install_old_control_day1}</td>
      <td>{click_exposure_ratio_old_control_day1}</td>
      <td>{download_click_ratio_old_control_day1}</td>
      <td>{install_download_ratio_old_control_day1}</td>
      <td>{date_old_control_day1}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_control_day2}</td>
      <td>{ad_click_old_control_day2}</td>
      <td>{ad_download_old_control_day2}</td>
      <td>{ad_install_old_control_day2}</td>
      <td>{click_exposure_ratio_old_control_day2}</td>
      <td>{download_click_ratio_old_control_day2}</td>
      <td>{install_download_ratio_old_control_day2}</td>
      <td>{date_old_control_day2}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_control_day3}</td>
      <td>{ad_click_old_control_day3}</td>
      <td>{ad_download_old_control_day3}</td>
      <td>{ad_install_old_control_day3}</td>
      <td>{click_exposure_ratio_old_control_day3}</td>
      <td>{download_click_ratio_old_control_day3}</td>
      <td>{install_download_ratio_old_control_day3}</td>
      <td>{date_old_control_day3}</td>
    </tr>
    <tr>
      <td>{ad_exposure_old_control_day4}</td>
      <td>{ad_click_old_control_day4}</td>
      <td>{ad_download_old_control_day4}</td>
      <td>{ad_install_old_control_day4}</td>
      <td>{click_exposure_ratio_old_control_day4}</td>
      <td>{download_click_ratio_old_control_day4}</td>
      <td>{install_download_ratio_old_control_day4}</td>
      <td>{date_old_control_day4}</td>
    </tr>
  </tbody>
</table>
"""

GRAY_DATA_OLD_USERS_NETUSER_POPUP_CLOUDGAME ="""

## 联网用户数 & 弹窗 & 云游
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse;">
  <thead>
    <tr>
      <th>灰度老用户（套壳9）</th>
      <th>QUA</th>
      <th>联网用户数</th>
      <th>弹窗成功率(%)<br>(gap<1%)</th>
      <th>云游插件拉起成功率<br>(>97%)</th>
      <th>灰度日期</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">实验组1</td>
      <td rowspan="4">{qua_old_1}</td>
      <td>{net_user_count_old_1_day1}</td>
      <td>{popup_success_rate_old_1_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_1_day1}</td>
      <td>{date_old_1_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_1_day2}</td>
      <td>{popup_success_rate_old_1_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_1_day2}</td>
      <td>{date_old_1_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_1_day3}</td>
      <td>{popup_success_rate_old_1_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_1_day3}</td>
      <td>{date_old_1_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_1_day4}</td>
      <td>{popup_success_rate_old_1_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_1_day4}</td>
      <td>{date_old_1_day4}</td>
     </tr>

    <tr>
      <td rowspan="4">实验组2</td>
      <td rowspan="4">{qua_old_2}</td>
      <td>{net_user_count_old_2_day1}</td>
      <td>{popup_success_rate_old_2_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_2_day1}</td>
      <td>{date_old_2_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_2_day2}</td>
      <td>{popup_success_rate_old_2_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_2_day2}</td>
      <td>{date_old_2_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_2_day3}</td>
      <td>{popup_success_rate_old_2_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_2_day3}</td>
      <td>{date_old_2_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_2_day4}</td>
      <td>{popup_success_rate_old_2_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_2_day4}</td>
      <td>{date_old_2_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">实验组3</td>
      <td rowspan="4">{qua_old_3}</td>
      <td>{net_user_count_old_3_day1}</td>
      <td>{popup_success_rate_old_3_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_3_day1}</td>
      <td>{date_old_3_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_3_day2}</td>
      <td>{popup_success_rate_old_3_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_3_day2}</td>
      <td>{date_old_3_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_3_day3}</td>
      <td>{popup_success_rate_old_3_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_3_day3}</td>
      <td>{date_old_3_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_3_day4}</td>
      <td>{popup_success_rate_old_3_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_3_day4}</td>
      <td>{date_old_3_day4}</td>
    </tr>

    <tr>
      <td rowspan="4">对照组</td>
      <td rowspan="4">{qua_old_control}</td>
      <td>{net_user_count_old_control_day1}</td>
      <td>{popup_success_rate_old_control_day1}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_control_day1}</td>
      <td>{date_old_control_day1}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_control_day2}</td>
      <td>{popup_success_rate_old_control_day2}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_control_day2}</td>
      <td>{date_old_control_day2}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_control_day3}</td>
      <td>{popup_success_rate_old_control_day3}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_control_day3}</td>
      <td>{date_old_control_day3}</td>
    </tr>
    <tr>
      <td>{net_user_count_old_control_day4}</td>
      <td>{popup_success_rate_old_control_day4}</td>
      <td>{cloud_game_plugin_launch_success_rate_old_control_day4}</td>
      <td>{date_old_control_day4}</td>
    </tr>
  </tbody>
</table>
"""
