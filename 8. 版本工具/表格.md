# 灰度测试配置

## 新用户配置

### QUA版本列表
```
TMAF_899_P_8547
TMAF_899_P_8548
/
TMAF_899_P_8550
```

### RQD版本列表
```
8.9.9_8994130_8547
8.9.9_8994130_8548
/
8.9.9_8994130_8550
```

### 测试日期
```
2025/06/06
2025/06/07
2025/06/08
```

## 老用户配置

### QUA版本列表
```
TMAF_899_P_8549
TMAF_899_P_8551
/
TMAF_899_P_8552
```

### RQD版本列表
```
8.9.9_8994130_8549
8.9.9_8994130_8551
/
8.9.9_8994130_8552
```

### 测试日期
```
2025/06/06
2025/06/07
2025/06/08
```
"""

常规热启动
crab_shell_type = 2 and launch_type=3 and start_type=1 and run_type=2

常规冷启动
crab_shell_type = 2 and launch_type=1 and start_type=1 and run_type=2

常规外call热启动
crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=2 

常规外call冷启动
crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=2

1235751

SELECT  qua,launch_type,start_type,run_type,crab_shell_type,
  case  
    when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=1 then '应用首次断层冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=3 then '当前版本首次断层冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=1 then '应用首次外call冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=3 then '当前版本首次外call冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=1 and run_type=2 then '常规冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=2 then '常规外call冷启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=1 and run_type=2 then '常规热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=2 and run_type=1 then '首次断层热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=1 then '首次外call热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=2 then '常规外call热启动'
    
    when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=1 then '套壳应用首次断层冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=3 then '套壳当前版本首次断层冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=1 then '套壳应用首次外call冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=3 then '套壳当前版本首次外call冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=1 and run_type=2 then '套壳常规冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=2 then '套壳常规外call冷启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=1 and run_type=2 then '套壳常规热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=2 and run_type=1 then '套壳首次断层热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=1 then '套壳首次外call热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=2 then '套壳常规外call热启动'
  else '其他' end as desc_str ,COUNT( *) as 次数,
AVG( tag_duration) as  平均值,
ApproxPercentile( tag_duration, 0.5) as  50分位,
ApproxPercentile( tag_duration, 0.8) as  80分位,
ApproxPercentile( tag_duration, 0.9) as  90分位  
FROM  
  [1055717].[launch_speed_event]  
WHERE  
  ds  >= 2024112400  
  AND  ds  <= 2025122923   
  AND tag = "Draw_End"
  AND  tagger_id  = 1  
  AND  qua  in ('TMAF_899_P_8547','TMAF_899_P_8548','TMAF_899_P_8550','TMAF_899_P_8549','TMAF_899_P_8551','TMAF_899_P_8552')
  AND content_type not LIKE '%ANCHOR_GAME_TAB%'
  AND content_type not LIKE '%SPLASH_IMAGE%'
  AND content_type not LIKE '%MAIN_SPLASH_VIEW%'
  group by qua,launch_type, start_type, run_type, crab_shell_type
  order by desc_str asc, qua asc



native外部拉活应用宝数据 (ID: 1055717)
https://beacon.woa.com/datamanager/yyb_bi/data-resource/detail/1055717?resourceType=PLAIN&tabType=events-manage

IMPALA数据源_yyb.ss.set1(ID:1235751)



