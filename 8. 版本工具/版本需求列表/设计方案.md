get_recent_merged_mrs_by_iids函数可以得到的不止是iid和description，还有author和title
确认一下，终端开发（author）、需求（title）是不是从这里获取的

需求: fix：对提示词prompt的修改
- **MR链接**: https://git.woa.com/yyb-android/TencentMobileAssistant/-/merge_requests/11064
- **终端开发**: jolieqzhang
- **是否预埋**: 未填写
- **测试关注重点**: 未填写
- **开关说明**: 未填写
- **实验报告地址**: 未填写


先查看现有能力。实现能够对比两个分支获取差异mr，并解析每个mr的描述等信息。获取出关键字段内容：
- 需求（mr的title）
- 需求单（MR 的 TAPD 工作项）
- MR链接
- 终端开发（mr的author）
- 是否预埋（从mr的描述信息解析，【是否预埋需求】字段，解析出 是/否）
- 测试关注重点（从mr的描述信息解析，【集成测试关注点】字段，解析出该字段所在行换行后的内容）
- 使用的开关系统，值、开关状态（1、有接入开关，填开关值，默认值 2、根据后台返回的数据来控制，比如tmast，协议数据 3、使用光子卡片来控制 4、无需开关，无需开关的备注原因）（从mr的描述信息解析，【开关说明】字段，解析出该字段所在行换行后的内容）
- 特性实验链接或特性实验报告（如无特性实验给到原因）（从mr的描述信息解析，【实验报告地址】字段，解析出该字段所在行换行后的内容）



【需求地址】- 必须，请在创建MR的工蜂上关联TAPD单即可

https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
【实验报告地址】- 必须, 标注实验的具体地址即可，如主分支开发没有实验报告，请填写“主分支开发，无实验报告”

https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843&expGroupKey=exp_yyb_92058_0_1749569617230&business=90080&appGroup=abtest&trace_id=6366dece-f7e9-43b5-8ca4-e023e11dd634&period=EXPERIMENT&controlGroupId=111788308&treatmentGroupId=111788307&beginTime=202506100000&endTime=202506120000&multiContrastCorrection=0&significanceLevel=0.05&statisticalPower=0.8&isSumCountCheck=0&dimId=&dimVal=&marked=[]&currentPage=1&pageSize=100&sort=0&sortTreatmentId=0&analyseType=basicAnalyse&rtStatus=0&sqlMode=1&detailCompare=true
【是否预埋需求】- 必须，版本灰度期间是否生效

是
【alpha配置】- 必须, 合入的需求如果已经满足体验的条件，需要配置体验入口(可以通过Global.isAlpha()打开代码)

tmast：无
server：0
desc: ""
【集成测试关注点】- 必须, 集成测试阶段需要关注的功能点，若不涉及请写"不涉及"，方便测试同学评估

不涉及
【开关说明】- 非必须， 明确开关系统、Key，取值，开关打开方式等, 如果没有，填写“无”

无
【代码改动】- 非必须，简单说明代码改动有那几部分和大概思路，若是已有技术方案wiki，可说明是技术方案哪部分, 如果没有，填写“无”

增加 IPv6 信息上报功能，包括网络栈检测、状态上报和相关逻辑修改。

新增 InetUtils 类用于 IPv4/IPv6 地址校验。
新增 NetStackUtil 类检测网络栈类型。
新增 IPv6 尝试和使用状态检测逻辑到 HttpNetWorkTaskV2。
修改多个对话框方法返回类型为 boolean 并增加错误处理。
新增 IPv6 相关统计字段和上报逻辑到多个类。
【测试说明】- 非必须，Testone里面手工测试样例地址, 如果没有，填写“无”

无


先查看现有能力。实现能够对比两个分支获取差异mr，并解析每个mr的描述等信息。获取出以下关键字段内容。

- 需求（mr的title）
- 需求单（MR 的 TAPD 工作项）
- MR链接
- 终端开发（mr的author）
- 是否预埋（从mr的描述信息解析，【是否预埋需求】字段，解析出 是/否）
- 测试关注重点（从mr的描述信息解析，【集成测试关注点】字段，解析出该字段所在行换行后的内容）
- 开关说明（从mr的描述信息解析，【开关说明】字段，解析出该字段所在行换行后的内容）
- 实验报告地址（从mr的描述信息解析，【实验报告地址】字段，解析出该字段所在行换行后的内容）


为了获取以下信息，可以先写一个解析mr详细信息的方法，把所有的字段内容获取出来（格式都是固定的，后续顶多再加字段，但字段的格式都是固定的）。然后再逐个填充。

例如。mr详细信息如下（这是mr信息的固定格式，都会按照这几个标题来填充。）
【需求地址】- 必须，请在创建MR的工蜂上关联TAPD单即可
   * https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
【实验报告地址】- 必须, 标注实验的具体地址即可，如主分支开发没有实验报告，请填写“主分支开发，无实验报告”
   * https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843&expGroupKey=exp_yyb_92058_0_1749569617230&business=90080&appGroup=abtest&trace_id=6366dece-f7e9-43b5-8ca4-e023e11dd634&period=EXPERIMENT&controlGroupId=111788308&treatmentGroupId=111788307&beginTime=202506100000&endTime=202506120000&multiContrastCorrection=0&significanceLevel=0.05&statisticalPower=0.8&isSumCountCheck=0&dimId=&dimVal=&marked=[]&currentPage=1&pageSize=100&sort=0&sortTreatmentId=0&analyseType=basicAnalyse&rtStatus=0&sqlMode=1&detailCompare=true

【是否预埋需求】- 必须，版本灰度期间是否生效
   * 是

【alpha配置】- 必须, 合入的需求如果已经满足体验的条件，需要配置体验入口(可以通过Global.isAlpha()打开代码)
   * tmast：无
   * server：0
   * desc: ""

【集成测试关注点】- 必须, 集成测试阶段需要关注的功能点，若不涉及请写"不涉及"，方便测试同学评估
   * 不涉及

【开关说明】- 非必须， 明确开关系统、Key，取值，开关打开方式等, 如果没有，填写“无”
   * 无

【代码改动】- 非必须，简单说明代码改动有那几部分和大概思路，若是已有技术方案wiki，可说明是技术方案哪部分, 如果没有，填写“无”
增加 IPv6 信息上报功能，包括网络栈检测、状态上报和相关逻辑修改。
   * 新增 InetUtils 类用于 IPv4/IPv6 地址校验。
   * 新增 NetStackUtil 类检测网络栈类型。
   * 新增 IPv6 尝试和使用状态检测逻辑到 HttpNetWorkTaskV2。
   * 修改多个对话框方法返回类型为 boolean 并增加错误处理。
   * 新增 IPv6 相关统计字段和上报逻辑到多个类。

【测试说明】- 非必须，Testone里面手工测试样例地址, 如果没有，填写“无”
   * 无


那么最后得到的结果应该如下：

需求: feature-gannicuswu-down-ipv6
需求单: https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
MR链接: https://git.woa.com/yyb-android/TencentMobileAssistant/-/merge_requests/11187
终端开发: gannicuswu
是否预埋: 是
测试关注重点: 不涉及
开关说明: 无
实验报告地址: https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843&expGroupKey=exp_yyb_92058_0_1749569617230&business=90080&appGroup=abtest&trace_id=6366dece-f7e9-43b5-8ca4-e023e11dd634&period=EXPERIMENT&controlGroupId=111788308&treatmentGroupId=111788307&beginTime=202506100000&endTime=202506120000&multiContrastCorrection=0&significanceLevel=0.05&statisticalPower=0.8&isSumCountCheck=0&dimId=&dimVal=&marked=[]&currentPage=1&pageSize=100&sort=0&sortTreatmentId=0&analyseType=basicAnalyse&rtStatus=0&sqlMode=1&detailCompare=true





【需求地址】- 必须，请在创建MR的工蜂上关联TAPD单即可
   * https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
【实验报告地址】- 必须, 标注实验的具体地址即可，如主分支开发没有实验报告，请填写“主分支开发，无实验报告”
   * https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843&expGroupKey=exp_yyb_92058_0_1749569617230&business=90080&appGroup=abtest&trace_id=6366dece-f7e9-43b5-8ca4-e023e11dd634&period=EXPERIMENT&controlGroupId=111788308&treatmentGroupId=111788307&beginTime=202506100000&endTime=202506120000&multiContrastCorrection=0&significanceLevel=0.05&statisticalPower=0.8&isSumCountCheck=0&dimId=&dimVal=&marked=[]&currentPage=1&pageSize=100&sort=0&sortTreatmentId=0&analyseType=basicAnalyse&rtStatus=0&sqlMode=1&detailCompare=true

【是否预埋需求】- 必须，版本灰度期间是否生效
   * 是

【alpha配置】- 必须, 合入的需求如果已经满足体验的条件，需要配置体验入口(可以通过Global.isAlpha()打开代码)
   * tmast：无
   * server：0
   * desc: ""

【集成测试关注点】- 必须, 集成测试阶段需要关注的功能点，若不涉及请写"不涉及"，方便测试同学评估
   * 不涉及

【开关说明】- 非必须， 明确开关系统、Key，取值，开关打开方式等, 如果没有，填写“无”
   * 无

【代码改动】- 非必须，简单说明代码改动有那几部分和大概思路，若是已有技术方案wiki，可说明是技术方案哪部分, 如果没有，填写“无”
增加 IPv6 信息上报功能，包括网络栈检测、状态上报和相关逻辑修改。
   * 新增 InetUtils 类用于 IPv4/IPv6 地址校验。
   * 新增 NetStackUtil 类检测网络栈类型。
   * 新增 IPv6 尝试和使用状态检测逻辑到 HttpNetWorkTaskV2。
   * 修改多个对话框方法返回类型为 boolean 并增加错误处理。
   * 新增 IPv6 相关统计字段和上报逻辑到多个类。

【测试说明】- 非必须，Testone里面手工测试样例地址, 如果没有，填写“无”
   * 无



# 技术需求
| 需求 | 需求单 | MR链接 |负责产品 | 负责产品 | 是否预埋 | 测试关注重点 | 使用的开关系统，值、开关状态 | 特性实验链接或特性实验报告 | 灰度班车 | 分组 | 
|------|---------|---------| --------| --------| --------| --------| --------| --------| --------| --------|
| 实验组1 | TMAF_899_P_8547 | 8.9.9_8994130_8547 |  实验组1 | TMAF_899_P_8547 | 8.9.9_8994130_8547 |  实验组1 | TMAF_899_P_8547 | 8.9.9_8994130_8547 | 实验组1 | TMAF_899_P_8547 | 8.9.9_8994130_8547 | 
| 实验组2 | TMAF_899_P_8548 | 8.9.9_8994130_8548 |
| 实验组3 | / | / |
| 对照组 | TMAF_899_P_8550 | 8.9.9_8994130_8550 |

# 产品需求


# bug



# 灰度实验配置

## 新用户配置

| 组别 | QUA版本 | RQD版本 |
|------|---------|---------|
| 实验组1 | TMAF_899_P_8547 | 8.9.9_8994130_8547 |
| 实验组2 | TMAF_899_P_8548 | 8.9.9_8994130_8548 |
| 实验组3 | / | / |
| 对照组 | TMAF_899_P_8550 | 8.9.9_8994130_8550 |

测试日期: 2025/06/06, 2025/06/07, 2025/06/08

## 老用户配置

| 组别 | QUA版本 | RQD版本 |
|------|---------|---------|
| 实验组1 | TMAF_899_P_8549 | 8.9.9_8994130_8549 |
| 实验组2 | TMAF_899_P_8551 | 8.9.9_8994130_8551 |
| 实验组3 | / | / |
| 对照组 | TMAF_899_P_8552 | 8.9.9_8994130_8552 |

测试日期: 2025/06/06, 2025/06/07, 2025/06/08