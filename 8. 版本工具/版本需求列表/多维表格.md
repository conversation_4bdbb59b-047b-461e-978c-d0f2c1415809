查看common/client/iwiki_client.py现有能力。实现一个能力，创建多维表格，然后调用获取字段id APi，得到三个默认的字段的id，其中第一个字段不能被删除，所以获取到后两个字段的field_id，调用删除API，删除他们。最后自定义添加字段


创建多维表格
result = client.create_document(
    body="",
    title="测试文档标题1",
    parentid=189268868,
    contenttype="VIKA",
    vika_mode="excel"
)
print("创建文档结果：", result)
if result['success']:
    print(f"文档创建成功，docid: {result['docid']}")
    print(f"请求ID: {result['request_id']}")
else:
    print(f"文档创建失败: {result['message']}")

获取到创建的表格的 doc_id

获取字段id
/vika/third/fields
参数：doc_id=4015298928（可选参数：viewId，String类型）
从而获取到field_id

删除字段
/vika/third/fields
参数：doc_id=4015298928，field_id=fldGLngMwclJf
进行删除字段

添加字段
/vika/third/fields
{   
    "doc_id": 0,
    "type": "string",
    "name": "string",
    "property": {}
}
其中
type = singleText 

{
  "type": "SingleText",
  "name": "标题",
  "property": {
    "defaultValue": "默认文本文本"
  }
}

type = Text
{
"type": "Text",
"name": "标题"
}

type = MultiSelect
{
  "type": "MultiSelect",
  "name": "标题",
  "property": {
    "options": [
      {
        "name": "string",
        "color": "deepPurple_0"
      }
    ],
    "defaultValue": "string"
  }
}