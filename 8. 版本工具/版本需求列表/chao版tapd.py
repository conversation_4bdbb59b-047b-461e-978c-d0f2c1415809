"""
TAPD API 操作客户端
提供 TAPD 工作项查询、Story 操作等功能
参考文档：https://tapd.woa.com/
"""

import requests
from typing import Dict, List, Any
from aflowatom.tapd.tapd_base import TapdBase, TapdStory


class TapdClientError(Exception):
    """TAPD 客户端操作异常"""
    pass


class TapdClient:
    """
    TAPD API 交互客户端

    支持的操作包括：
    - TAPD Story 查询和操作
    - 工作项信息获取
    - TAPD URL 构建
    - 与 Git MR 关联的工作项查询
    """

    # TAPD 工作项类型映射
    TAPD_TYPE_MAP = {
        'bug': 'bugtrace/bugs/view?bug_id=',
        'task': 'prong/tasks/view/',
        'story': 'prong/stories/view/',
    }

    def __init__(self,
                 app_id: str,
                 token: str,
                 workspace_id: str,
                 base_url: str = "https://tapd.woa.com"):
        """
        初始化 TAPD 客户端

        Args:
            app_id: TAPD 应用 ID
            token: TAPD 认证令牌
            workspace_id: TAPD 工作空间 ID
            base_url: TAPD 基础 URL（默认：https://tapd.woa.com）
        """
        self.app_id = app_id
        self.token = token
        self.workspace_id = workspace_id
        self.base_url = base_url.rstrip('/')

        # 设置 TAPD 默认客户端
        TapdBase.set_default_client(self.app_id, self.token)

    def _build_tapd_url(self, tapd_type: str, tapd_id: str) -> str:
        """
        构建 TAPD 工作项 URL

        Args:
            tapd_type: TAPD 工作项类型 (bug/task/story)
            tapd_id: TAPD 工作项 ID

        Returns:
            完整的 TAPD 工作项 URL

        Raises:
            TapdClientError: 不支持的工作项类型
        """
        if tapd_type not in self.TAPD_TYPE_MAP:
            raise TapdClientError(f"不支持的 TAPD 工作项类型: {tapd_type}")

        url_suffix = self.TAPD_TYPE_MAP[tapd_type]
        return f"{self.base_url}/{self.workspace_id}/{url_suffix}{tapd_id}"

    def get_story_info(self, story_id: str) -> Dict[str, Any]:
        """
        获取 TAPD Story 详细信息

        Args:
            story_id: Story ID

        Returns:
            包含 Story 信息的字典，包含以下字段：
            - 创建人: Story 创建人
            - 需求类别: 需求类型
            - 实验链接: 实验链接
            - 开关控制方式: 开关控制方式

        Raises:
            TapdClientError: 获取 Story 信息失败
        """
        try:
            story = TapdStory.create(self.workspace_id, id=story_id)
            story.load()

            # 如果是产品运营需求且是 User Story，需要获取父需求信息
            if (story['父需求'] != '0' and
                story['需求颗粒度'] == 'User Story' and
                story.get_display_value('需求类型') == '产品与运营需求'):
                parent_story = TapdStory.create(self.workspace_id, id=story['父需求'])
                parent_story.load()
                story = parent_story

            return {
                '创建人': story.get_display_value('创建人'),
                '需求类别': story.get_display_value('需求类型'),
                '实验链接': story.get_display_value('实验链接'),
                '开关控制方式': story.get_display_value('开关控制方式')
            }

        except Exception as e:
            raise TapdClientError(f"获取 Story {story_id} 信息失败: {str(e)}")

    def build_tapd_urls_from_workitems(self, workitems: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        从工作项列表批量构建 TAPD 链接

        Args:
            workitems: 工作项字典列表，每个字典应包含 tapd_id 和 tapd_type 字段

        Returns:
            tapd_id 到 URL 的映射字典
        """
        urls = {}
        for item in workitems:
            tapd_id = str(item.get('tapd_id', ''))
            tapd_type = item.get('tapd_type', 'story')

            if tapd_id:
                try:
                    urls[tapd_id] = self._build_tapd_url(tapd_type, tapd_id)
                except TapdClientError as e:
                    print(f"构建 TAPD URL 失败 (ID: {tapd_id}, Type: {tapd_type}): {e}")

        return urls

    def build_tapd_url_by_workspace_and_id(self, workspace_id: str, tapd_id: str) -> str:
        """
        根据 workspace_id 和 tapd_id 构建 TAPD 工作项链接

        Args:
            workspace_id: TAPD 工作空间 ID
            tapd_id: TAPD 工作项 ID

        Returns:
            完整的 TAPD 工作项链接 URL
        """
        return f"https://tapd.woa.com/tapd_fe/{workspace_id}/story/detail/{tapd_id}"

    def get_workitems_from_git_mr(self,
                                  mr_iid: str,
                                  git_project_id: str,
                                  git_private_token: str,
                                  git_base_url: str = "https://git.woa.com",
                                  item_type: str = 'mr') -> List[Dict[str, Any]]:
        """
        从 Git MR 获取关联的 TAPD 工作项

        Args:
            mr_iid: Git 合并请求的 IID
            git_project_id: Git 项目 ID
            git_private_token: Git 私钥
            git_base_url: Git API 基础 URL（默认：https://git.woa.com）
            item_type: 项目类型（默认：'mr'）

        Returns:
            TAPD 工作项字典列表

        Raises:
            TapdClientError: 获取工作项失败
        """
        url = f"{git_base_url.rstrip('/')}/api/v3/projects/{git_project_id}/tapd_workitems"
        params = {
            'type': item_type,
            'iid': mr_iid,
            'private_token': git_private_token
        }

        try:
            response = requests.get(url, params=params)

            if response.status_code != 200:
                raise TapdClientError(
                    f"获取 MR {mr_iid} 的 TAPD 工作项失败，状态码 {response.status_code}: {response.text}"
                )

            return response.json()

        except requests.RequestException as e:
            raise TapdClientError(f"请求 Git API 失败: {str(e)}")

    def process_mr_tapd_workitems(self,
                                  workitems: List[Dict[str, Any]],
                                  mr_info: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        处理 MR 关联的 TAPD 工作项，构建需求信息字典

        Args:
            workitems: TAPD 工作项列表
            mr_info: MR 信息字典，应包含 iid, title, author 等字段

        Returns:
            以需求名称为键的需求信息字典
        """
        result = {}

        for workitem in workitems:
            name = workitem.get('name', '')
            tapd_id = workitem.get('tapd_id', '')
            tapd_type = workitem.get('tapd_type', 'story')

            if not name:
                continue

            # 构建 MR URL
            mr_url = f"https://git.woa.com/yyb-android/TencentMobileAssistant/-/merge_requests/{mr_info.get('iid', '')}"

            # 构建 TAPD URL
            tapd_url = self._build_tapd_url(tapd_type, str(tapd_id))

            if name in result:
                # 如果需求已存在，添加到 MR 列表
                result[name]['mr_list'].append({
                    'url': mr_url,
                    'title': mr_info.get('title', ''),
                    'author': mr_info.get('author', ''),
                    'dirs': mr_info.get('dirs', [])
                })
            else:
                # 创建新的需求条目
                result[name] = {
                    'mr_list': [{
                        'url': mr_url,
                        'title': mr_info.get('title', ''),
                        'author': mr_info.get('author', ''),
                        'dirs': mr_info.get('dirs', [])
                    }],
                    'tapd_id': tapd_url,
                    'tapd_type': tapd_type,
                    '实验': mr_info.get('实验', ''),
                    '开关': mr_info.get('开关', ''),
                    '测试说明': mr_info.get('测试说明', ''),
                }

                # 如果是 story 类型，获取详细信息
                if tapd_type == 'story':
                    try:
                        story_info = self.get_story_info(str(tapd_id))
                        result[name].update(story_info)
                    except TapdClientError as e:
                        print(f"获取 Story {tapd_id} 详细信息失败: {e}")
                        result[name].update({
                            '创建人': '',
                            '需求类别': '',
                            '实验链接': '',
                            '开关控制方式': ''
                        })
                else:
                    # 非 story 类型的默认值
                    result[name].update({
                        '创建人': '',
                        '需求类别': '',
                        '实验链接': '',
                        '开关控制方式': ''
                    })

        return result

    def batch_get_workitems_from_git_mrs(self,
                                         mr_infos: List[Dict[str, Any]],
                                         git_project_id: str,
                                         git_private_token: str,
                                         git_base_url: str = "https://git.woa.com") -> Dict[str, Dict[str, Any]]:
        """
        批量从多个 Git MR 获取关联的 TAPD 工作项

        Args:
            mr_infos: MR 信息字典列表，每个字典应包含 iid 字段
            git_project_id: Git 项目 ID
            git_private_token: Git 私钥
            git_base_url: Git API 基础 URL（默认：https://git.woa.com）

        Returns:
            以需求名称为键的汇总需求信息字典
        """
        all_requirements = {}

        for mr_info in mr_infos:
            mr_iid = mr_info.get('iid')
            if not mr_iid:
                continue

            # 跳过没有目录信息的 MR（通常是插件相关）
            if mr_info.get('dirs') is None:
                print(f"跳过 MR {mr_info.get('title', 'Unknown')}，目录信息为空")
                continue

            try:
                # 获取 MR 关联的 TAPD 工作项
                workitems = self.get_workitems_from_git_mr(
                    mr_iid, git_project_id, git_private_token, git_base_url
                )

                # 处理工作项并合并到结果中
                mr_requirements = self.process_mr_tapd_workitems(workitems, mr_info)

                # 合并需求信息
                for req_name, req_info in mr_requirements.items():
                    if req_name in all_requirements:
                        # 合并 MR 列表
                        all_requirements[req_name]['mr_list'].extend(req_info['mr_list'])
                    else:
                        all_requirements[req_name] = req_info

            except TapdClientError as e:
                print(f"处理 MR {mr_iid} 的 TAPD 工作项失败: {e}")
                continue

        return all_requirements

    def sort_requirements_by_tapd_type(self, requirements: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按 TAPD 类型对需求进行分组排序

        Args:
            requirements: 需求信息字典

        Returns:
            按 TAPD 类型分组的需求字典
        """
        sorted_data = {}

        for req_name, req_info in requirements.items():
            tapd_type = req_info.pop('tapd_type', 'story')

            if tapd_type not in sorted_data:
                sorted_data[tapd_type] = []

            sorted_data[tapd_type].append({req_name: req_info})

        return sorted_data

    def sort_requirements_by_category(self, requirements: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按需求类别对需求进行分组排序

        Args:
            requirements: 需求信息字典

        Returns:
            按需求类别分组的需求字典
        """
        sorted_data = {}

        for req_name, req_info in requirements.items():
            category = req_info.pop('需求类别', '未分类')

            if category not in sorted_data:
                sorted_data[category] = []

            sorted_data[category].append({req_name: req_info})

        return sorted_data

    @classmethod
    def create_default_client(cls,
                             app_id: str = 'yyb_tapd',
                             token: str = '31F684F1-6405-28CB-03E2-E6B60B66780B',
                             workspace_id: str = '20422314') -> 'TapdClient':
        """
        使用默认配置创建 TapdClient 实例

        Args:
            app_id: TAPD 应用 ID（默认：yyb_tapd）
            token: TAPD 认证令牌（默认：项目默认令牌）
            workspace_id: TAPD 工作空间 ID（默认：20422314）

        Returns:
            配置好的 TapdClient 实例
        """
        return cls(app_id=app_id, token=token, workspace_id=workspace_id)


# 向后兼容的便捷函数
def get_tapd_info_by_id(tapd_id: str,
                       workspace_id: str = '20422314',
                       app_id: str = 'yyb_tapd',
                       token: str = '31F684F1-6405-28CB-03E2-E6B60B66780B') -> Dict[str, Any]:
    """
    向后兼容的遗留函数
    获取 TAPD Story 信息

    Args:
        tapd_id: Story ID
        workspace_id: TAPD 工作空间 ID
        app_id: TAPD 应用 ID
        token: TAPD 认证令牌

    Returns:
        Story 信息字典
    """
    client = TapdClient(app_id=app_id, token=token, workspace_id=workspace_id)
    return client.get_story_info(tapd_id)


def get_url(tapd_type: str,
           tapd_id: str,
           workspace_id: str = '20422314') -> str:
    """
    向后兼容的遗留函数
    构建 TAPD 工作项 URL

    Args:
        tapd_type: TAPD 工作项类型
        tapd_id: TAPD 工作项 ID
        workspace_id: TAPD 工作空间 ID

    Returns:
        TAPD 工作项 URL
    """
    client = TapdClient(app_id='', token='', workspace_id=workspace_id)
    return client._build_tapd_url(tapd_type, tapd_id)


if __name__ == "__main__":
    # 创建 TAPD 客户端
    tapd_client = TapdClient.create_default_client()

    # 示例1：获取 Story 信息
    try:
        story_id = "1120422314001000123"  # 示例 Story ID
        story_info = tapd_client.get_story_info(story_id)
        print(f"Story 信息: {story_info}")
    except TapdClientError as e:
        print(f"获取 Story 信息失败: {e}")

    # 示例2：构建 TAPD URL
    try:
        # 构建单个 TAPD URL
        tapd_url = tapd_client._build_tapd_url('story', '1120422314001000123')
        print(f"TAPD Story URL: {tapd_url}")

        # 从工作项列表批量构建 URL
        workitems = [
            {'tapd_id': '1120422314001000123', 'tapd_type': 'story'},
            {'tapd_id': '1120422314001000124', 'tapd_type': 'bug'},
            {'tapd_id': '1120422314001000125', 'tapd_type': 'task'}
        ]
        urls = tapd_client.build_tapd_urls_from_workitems(workitems)
        print("批量构建的 TAPD URLs:")
        for tapd_id, url in urls.items():
            print(f"  {tapd_id}: {url}")
    except TapdClientError as e:
        print(f"构建 TAPD URL 失败: {e}")

    # 示例3：从 Git MR 获取 TAPD 工作项
    try:
        git_project_id = "126979"
        git_private_token = "_zhCOZW_1nf3_bHORMGD"
        mr_iid = "12345"  # 示例 MR IID

        workitems = tapd_client.get_workitems_from_git_mr(
            mr_iid, git_project_id, git_private_token
        )
        print(f"MR {mr_iid} 关联的 TAPD 工作项: {workitems}")
    except TapdClientError as e:
        print(f"获取 MR 关联的 TAPD 工作项失败: {e}")

    # 示例4：批量处理多个 MR 的 TAPD 工作项
    try:
        mr_infos = [
            {
                'iid': '12345',
                'title': '示例 MR 1',
                'author': '张三',
                'dirs': ['src/main/java', 'src/test/java']
            },
            {
                'iid': '12346',
                'title': '示例 MR 2',
                'author': '李四',
                'dirs': ['src/main/kotlin']
            }
        ]

        all_requirements = tapd_client.batch_get_workitems_from_git_mrs(
            mr_infos, git_project_id, git_private_token
        )

        print("批量处理结果:")
        for req_name, req_info in all_requirements.items():
            print(f"  需求: {req_name}")
            print(f"    创建人: {req_info.get('创建人', 'N/A')}")
            print(f"    需求类别: {req_info.get('需求类别', 'N/A')}")
            print(f"    关联 MR 数量: {len(req_info.get('mr_list', []))}")

        # 按 TAPD 类型分组
        sorted_by_type = tapd_client.sort_requirements_by_tapd_type(all_requirements.copy())
        print("\n按 TAPD 类型分组:")
        for tapd_type, requirements in sorted_by_type.items():
            print(f"  {tapd_type}: {len(requirements)} 个需求")

    except TapdClientError as e:
        print(f"批量处理 MR 的 TAPD 工作项失败: {e}")

    # 示例5：使用向后兼容函数
    try:
        # 使用遗留函数获取 Story 信息
        legacy_story_info = get_tapd_info_by_id("1120422314001000123")
        print(f"遗留函数获取的 Story 信息: {legacy_story_info}")

        # 使用遗留函数构建 URL
        legacy_url = get_url('story', '1120422314001000123')
        print(f"遗留函数构建的 URL: {legacy_url}")

    except Exception as e:
        print(f"使用遗留函数失败: {e}")
