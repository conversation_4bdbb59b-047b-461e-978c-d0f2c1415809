============================================================
从iwiki文档解析配置，自动收集数据并追加到iwiki文档
============================================================
获取到的iwiki链接为: https://iwiki.woa.com/p/4015240554
检测到报告内容，仅解析配置部分（前23248字符）

从iWiki文档解析配置:
https://iwiki.woa.com/p/4015240554
开始收集灰度数据...
============================================================
设置基础配置...
基础配置设置完成

开始数据收集...

收集新用户数据...
----------------------------------------
    输入验证: QUA版本数=4, RQD版本数=4, 测试日期数=3
  收集crash和ANR数据: 2025-06-13 ~ 2025-06-15, RQD版本: 4个
         记录异常数据: new用户 实验组1 第1天 crash = 0.21% (超过0.18%阈值(高出0.0278%)) [QUA: 9.0.0_9001130_9135, 时间: 2025/06/13]
     crash和ANR数据收集完成
  收集new用户启动速度数据: 4个QUA版本
         记录异常数据: new用户 实验组1 常规外call热启动 launch_speed = 2752.52 (126次) (比对照组慢192.17ms) [QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]
         记录异常数据: new用户 实验组1 常规外call冷启动 launch_speed = 3536.21 (90次) (比对照组慢221.52ms) [QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]
         记录异常数据: new用户 实验组2 常规外call热启动 launch_speed = 2728.02 (124次) (比对照组慢167.67ms) [QUA: TMAF_900_P_9136, 时间: 2025/06/13~2025/06/15]
         记录异常数据: new用户 实验组3 常规外call冷启动 launch_speed = 3526.45 (124次) (比对照组慢211.76ms) [QUA: TMAF_900_P_9137, 时间: 2025/06/13~2025/06/15]
     new用户启动速度数据收集完成
  收集new用户下载数据: 4个QUA版本
     收集外call下载数据...
     获取下载数据失败: 查询失败: 查询超时, 超时时间: 300秒
  收集广告数据: 4个QUA版本
     收集广告数据...
     广告数据收集完成
  收集其他数据: 4个QUA版本
     收集联网数据...
     收集弹窗数据...
     收集云游戏数据...
     其他数据收集完成
新用户数据收集完成

收集老用户数据...
----------------------------------------
    输入验证: QUA版本数=14, RQD版本数=14, 测试日期数=3
  收集crash和ANR数据: 2025-06-13 ~ 2025-06-15, RQD版本: 14个
         记录异常数据: old用户 实验组1 第1天 anr = 0.17% (比对照组高0.17%) [QUA: 9.0.0_9001130_9139, 时间: 2025/06/13]
         记录异常数据: old用户 实验组1 第2天 anr = 0.22% (比对照组高0.22%) [QUA: 9.0.0_9001130_9139, 时间: 2025/06/14]
         记录异常数据: old用户 实验组1 第3天 anr = 0.21% (比对照组高0.21%) [QUA: 9.0.0_9001130_9139, 时间: 2025/06/15]
         记录异常数据: old用户 实验组2 第1天 anr = 0.17% (比对照组高0.17%) [QUA: 9.0.0_9001130_9140, 时间: 2025/06/13]
         记录异常数据: old用户 实验组2 第2天 anr = 0.22% (比对照组高0.22%) [QUA: 9.0.0_9001130_9140, 时间: 2025/06/14]
         记录异常数据: old用户 实验组2 第3天 anr = 0.22% (比对照组高0.22%) [QUA: 9.0.0_9001130_9140, 时间: 2025/06/15]
         记录异常数据: old用户 实验组3 第1天 anr = 0.17% (比对照组高0.17%) [QUA: 9.0.0_9001130_9141, 时间: 2025/06/13]
         记录异常数据: old用户 实验组3 第2天 anr = 0.21% (比对照组高0.21%) [QUA: 9.0.0_9001130_9141, 时间: 2025/06/14]
         记录异常数据: old用户 实验组3 第3天 anr = 0.21% (比对照组高0.21%) [QUA: 9.0.0_9001130_9141, 时间: 2025/06/15]
     crash和ANR数据收集完成
  收集old用户启动速度数据: 14个QUA版本
     old用户启动速度数据收集完成
  收集old用户下载数据: 14个QUA版本
     收集外call下载数据...
     收集下载安装CVR数据...
     获取下载数据失败: list index out of range
  收集广告数据: 14个QUA版本
     收集广告数据...
     获取广告数据失败: list index out of range
  收集其他数据: 14个QUA版本
     收集联网数据...
     收集弹窗数据...
     收集云游戏数据...
     获取其他数据失败: list index out of range
    警告: old用户crash数据结构异常: 期望4组，实际14组
    警告: old用户ANR数据结构异常: 期望4组，实际14组
    警告: old用户regular_hot数据结构异常: 期望4组，实际14组
    警告: old用户regular_cold数据结构异常: 期望4组，实际14组
    警告: old用户outcall_hot数据结构异常: 期望4组，实际14组
    警告: old用户outcall_cold数据结构异常: 期望4组，实际14组
老用户数据收集完成

所有数据收集完成

验证最终数据一致性...
==================================================
发现数据一致性问题:
   1. 老用户设备crash率数据结构错误: 应为4组，实际为14组
   2. 老用户ANR率数据结构错误: 应为4组，实际为14组
   3. 老用户常规热启动数据错误: 应为4组，实际为14组
   4. 老用户常规冷启动数据错误: 应为4组，实际为14组

总计发现 4 个问题

============================================================
灰度数据收集完成！
============================================================
数据收集总结:
  新用户设备crash率: 4组 x 4天
  老用户设备crash率: 14组 x 4天
  新用户启动速度: 4组
  老用户启动速度: 14组
  新用户下载数据: 4组 x 4天
  老用户下载数据: 4组 x 4天
  新用户广告数据: 4组 x 3天
  老用户广告数据: 4组 x 4天

数据质量报告:
  数据一致性检查: 失败
  建议检查数据收集过程中的警告信息
原始异常数据汇总 = ## Crash率异常
**新用户：**
- **实验组1** 第1天: `0.21%` (超过0.18%阈值(高出0.0278%)) `[QUA: 9.0.0_9001130_9135, 时间: 2025/06/13]`

## ANR率异常
**老用户：**
- **实验组1** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/13]`
- **实验组1** 第2天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/14]`
- **实验组1** 第3天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/15]`
- **实验组2** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/13]`
- **实验组2** 第2天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/14]`
- **实验组2** 第3天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/15]`
- **实验组3** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/13]`
- **实验组3** 第2天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/14]`
- **实验组3** 第3天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/15]`

## 启动速度异常
**新用户：**
- **实验组1** 常规外call冷启动: `3536.21 (90次)` (比对照组慢221.52ms) `[QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]`
- **实验组1** 常规外call热启动: `2752.52 (126次)` (比对照组慢192.17ms) `[QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]`
- **实验组2** 常规外call热启动: `2728.02 (124次)` (比对照组慢167.67ms) `[QUA: TMAF_900_P_9136, 时间: 2025/06/13~2025/06/15]`
- **实验组3** 常规外call冷启动: `3526.45 (124次)` (比对照组慢211.76ms) `[QUA: TMAF_900_P_9137, 时间: 2025/06/13~2025/06/15]`
AI异常总结prompt = 
你是应用宝的版本实验数据异常检测专家，你的任务是帮助用户快速了解版本实验数据的异常情况，并给出改进建议。请根据[异常总结]中的信息，生成一份简明扼要的异常总结分析。最后结合[格式说明]，严格按[格式]输出。


# [异常总结]
## Crash率异常
**新用户：**
- **实验组1** 第1天: `0.21%` (超过0.18%阈值(高出0.0278%)) `[QUA: 9.0.0_9001130_9135, 时间: 2025/06/13]`

## ANR率异常
**老用户：**
- **实验组1** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/13]`
- **实验组1** 第2天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/14]`
- **实验组1** 第3天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9139, 时间: 2025/06/15]`
- **实验组2** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/13]`
- **实验组2** 第2天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/14]`
- **实验组2** 第3天: `0.22%` (比对照组高0.22%) `[QUA: 9.0.0_9001130_9140, 时间: 2025/06/15]`
- **实验组3** 第1天: `0.17%` (比对照组高0.17%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/13]`
- **实验组3** 第2天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/14]`
- **实验组3** 第3天: `0.21%` (比对照组高0.21%) `[QUA: 9.0.0_9001130_9141, 时间: 2025/06/15]`

## 启动速度异常
**新用户：**
- **实验组1** 常规外call冷启动: `3536.21 (90次)` (比对照组慢221.52ms) `[QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]`
- **实验组1** 常规外call热启动: `2752.52 (126次)` (比对照组慢192.17ms) `[QUA: TMAF_900_P_9135, 时间: 2025/06/13~2025/06/15]`
- **实验组2** 常规外call热启动: `2728.02 (124次)` (比对照组慢167.67ms) `[QUA: TMAF_900_P_9136, 时间: 2025/06/13~2025/06/15]`
- **实验组3** 常规外call冷启动: `3526.45 (124次)` (比对照组慢211.76ms) `[QUA: TMAF_900_P_9137, 时间: 2025/06/13~2025/06/15]`

# [格式]
## 异常数据
## 异常数据分析
## 处理建议


# [格式说明]
1. 异常数据。根据[异常总结]，准确列出异常数据，不得编造。
2. 异常数据分析。根据异常数据，分析异常原因。
3. 处理建议。给出处理建议。
