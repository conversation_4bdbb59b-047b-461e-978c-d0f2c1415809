import hashlib
import time
import requests
import uuid


# 参考文档：https://doc.beacon.woa.com/page/openapi?app=%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2&interface=getCardAnalysisResultUsingGET
# API申请指南：https://iwiki.woa.com/p/4007758100


class BeaconAPIClient:
    def __init__(self, application_id: int, secret_key: str):
        self.application_id = application_id
        self.secret_key = secret_key
        self.session = requests.Session()
        self.sign_version = 'v1'
        self.base_url = "https://api.beacon.woa.com/openapi"

    def _get_sign(self):
        """
        生成签名和时间戳
        签名算法: sha256(sha256(secretKey + '-' + timestamp) + '-' + appId)
        timestamp为13位毫秒时间戳
        """
        timestamp = str(int(time.time() * 1000))
        key_hl_1 = hashlib.sha256()
        key_hl_1.update(f"{self.secret_key}-{timestamp}".encode('utf-8'))
        secret_key_sha256 = key_hl_1.hexdigest()

        key_hl_2 = hashlib.sha256()
        key_hl_2.update(f"{secret_key_sha256}-{self.application_id}".encode('utf-8'))
        sign = key_hl_2.hexdigest()

        return timestamp, sign

    def _get_headers(self, content_type="application/x-www-form-urlencoded"):
        timestamp, sign = self._get_sign()
        headers = {
            "bc-api-app-id": str(self.application_id),
            "bc-api-sign-version": self.sign_version,
            "bc-api-timestamp": timestamp,
            "bc-api-sign": sign,
            "Content-Type": content_type
        }
        return headers

    def get_card_result(self, **query_params):
        """
        查询指定图卡的数据

        :param query_params: 支持接口文档中所有query参数，如 async=1, cardId=123 等
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datainsight/analysis/getCardResult"
        headers = self._get_headers()

        # GET请求，参数放在params中
        response = self.session.get(url, headers=headers, params=query_params)
        return response

    def post_card_result(self, biz_id: str, card_query: dict):
        """
        查询指定仪表盘的数据

        :param biz_id: 空间ID，必填，作为query参数
        :param card_query: 请求体JSON，必填，符合接口文档结构
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datatalk/dataQuery/card/result"
        headers = self._get_headers(content_type="application/json")

        params = {
            "bizId": biz_id
        }

        response = self.session.post(url, headers=headers, params=params, json=card_query)
        return response

    def post_model_query(self, body: dict):
        """
        模型查询接口，直接传入完整请求体

        :param body: 请求体完整字典
        :return: requests.Response对象
        """
        url = f"{self.base_url}/datatalk/analysis/model?bizId=yyb_bi"
        headers = self._get_headers(content_type="application/json")

        response = self.session.post(url, headers=headers, json=body)
        return response


if __name__ == "__main__":
    APPLICATIONID = 2235
    SECRETKEY = "B40FF43E210D1B6363D3CA37DA2770B4"

    client = BeaconAPIClient(APPLICATIONID, SECRETKEY)

    # 查询指定图卡的数据
    params = {
        "async": 1,
        "cardId": 608812,  # 图卡id
        "bizId": "yyb_bi"  # 空间id
    }
    # response = client.get_card_result(**params)
    # print("get_card_result状态码:", response.status_code)
    # print("get_card_result响应内容:", response.text)

    # 查询指定仪表盘的数据
    biz_id = "yyb_bi"
    card_query = {
        "cacheStrategy": "FORCE",
        "cardId": "table_gg0nvee5",
        "limit": 200,
        "pageId": 239580,
        "paramsId": "7d118b20ee228ab45fc85dbd754fb643",
        "pushType": 0,
        "specialParam": {},
        "variables": [
            {
                "key": "version",
                "value": "'TMAF_858_P_9521','TMAF_858_P_9522','TMAF_858_P_9523','TMAF_857_P_9524','TMAF_858_P_9527','TMAF_858_P_9528','TMAF_858_P_9529','TMAF_857_P_9530'"
            },
            {
                "key": "imp_date.start",
                "value": 20250529
            },
            {
                "key": "imp_date.end",
                "value": 20250604
            }
        ]
    }
    # response2 = client.post_card_result(biz_id=biz_id, card_query=card_query)
    # print("post_card_result状态码:", response2.status_code)
    # print("post_card_result响应内容:", response2.text)

    sql ="""
SELECT ds AS '时间' ,
       qua AS '版本号',
       pop_times AS '弹窗人数' ,
       expose_times AS '曝光人数',
       CASE
           WHEN cast(round((expose_times / pop_times) * 100,2) AS float) <= 100 THEN cast(round((expose_times / pop_times) * 100,2) AS float)
           ELSE 100
       END AS '弹窗成功率(%)'
FROM
  (SELECT substring(cast(ds AS string), 1, 8) AS ds,
          qua,
          count(DISTINCT if(event_code = 'st_pop_action_request' AND uni_start_abort IN (0), t1.guid,NULL)) AS pop_times,
          count(DISTINCT if(event_code = 'st_pop_action_response' , t1.guid,NULL)) AS expose_times
   FROM
     (SELECT ds,
             event_code AS event_code,
             CASE WHEN (event_code = 'st_pop_action_response') THEN C23 WHEN (event_code = 'st_pop_action_request') THEN C17 ELSE NULL END AS guid,
             CASE WHEN (event_code = 'st_pop_action_response') THEN C24 WHEN (event_code = 'st_pop_action_request') THEN C18 ELSE NULL END AS qua,
             CASE WHEN (event_code = 'st_pop_action_response') THEN NULL WHEN (event_code = 'st_pop_action_request') THEN CAST(C03 AS BIGINT) ELSE NULL END AS uni_start_abort
      FROM beacon_olap.t_od_light_olap_0m300etnja170g1m
      WHERE cast(substr(cast(ds as string),1,8) as bigint) BETWEEN 20241201 AND 20241207
        AND ((event_code = 'st_pop_action_response' AND C24 IN ('TMAF_858_P_9521','TMAF_858_P_9522')) OR (event_code = 'st_pop_action_request' AND C18 IN ('TMAF_858_P_9521','TMAF_858_P_9522')))
        AND product_id = '0M300ETNJA170G1M' ) t1
JOIN
(SELECT guid,user_type
 FROM beacon_olap.dws_yyb_dau_di
 WHERE ds BETWEEN 20241201 AND 20241207
   AND user_type IN ('new','old')
 GROUP BY guid,user_type)t2 ON t1.guid = t2.guid
   GROUP BY 1,2)t
order by 1
    """
    
    # 调用模型查询接口示例
    body = {
        "bizId": "yyb_bi",
        "plugin_model_conf": {
            "special_model_info": {
                "sql": sql,
                "sqlTpl": sql,
                "dynamicSqls": [],
                "sqlSyntaxCheck": True,
                "variables": []
            },
            "common_model_info": {
                "data_source_id": 1235751,
                "data_source_ids": [],
                "result_desc": []
            }
        },
        "cacheable": False,
        "period": None,
        "periodDate": None,
        "queryType": None,
        "version": 1,
        "model_source": 1,
        "canBeaconTurbo": True,
        "variableInfo": {
            "timerange.start": "20241201",
            "timerange.end": "20241207",
            "qua_val": "'TMAF_858_P_9521','TMAF_858_P_9522'",
            "user_type_val": "'remain','silent_back'"
        },
        "model_type": "sql",
        "is_edit": True
    }

    response = client.post_model_query(body=body)
    print("post_model_query状态码:", response.status_code)
    print("post_model_query响应内容:", response.text)
