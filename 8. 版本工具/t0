import hashlib
import time
import requests
from typing import List, Dict
from datetime import datetime, timedelta

from common.constant.beacon_sql_constants import BeaconSQLConstants



# 参考文档：https://doc.beacon.woa.com/page/openapi?app=%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2&interface=getCardAnalysisResultUsingGET
# API申请指南：https://iwiki.woa.com/p/4007758100
# Datatalk如何通过Openapi 传自定义SQL查询数据(不依赖图卡)：https://doc.weixin.qq.com/doc/w3_ABsATwaJACkCNIhF0r9d8TBS724Wr?scode=AJEAIQdfAAo7Yqud2EAUkA7gZiAPw


class BeaconAPIClient:
    def __init__(self, application_id: int = 2235, secret_key: str = "B40FF43E210D1B6363D3CA37DA2770B4"):
        self.application_id = application_id
        self.secret_key = secret_key
        self.session = requests.Session()
        self.sign_version = 'v1'
        self.base_url = "https://api.beacon.woa.com/openapi"

    def _get_sign(self):
        """
        生成签名和时间戳
        签名算法: sha256(sha256(secretKey + '-' + timestamp) + '-' + appId)
        timestamp为13位毫秒时间戳
        """
        timestamp = str(int(time.time() * 1000))
        key_hl_1 = hashlib.sha256()
        key_hl_1.update(f"{self.secret_key}-{timestamp}".encode('utf-8'))
        secret_key_sha256 = key_hl_1.hexdigest()

        key_hl_2 = hashlib.sha256()
        key_hl_2.update(f"{secret_key_sha256}-{self.application_id}".encode('utf-8'))
        sign = key_hl_2.hexdigest()

        return timestamp, sign

    def _get_headers(self, content_type="application/x-www-form-urlencoded"):
        timestamp, sign = self._get_sign()
        headers = {
            "bc-api-app-id": str(self.application_id),
            "bc-api-sign-version": self.sign_version,
            "bc-api-timestamp": timestamp,
            "bc-api-sign": sign,
            "Content-Type": content_type
        }
        return headers

    def post_model_query(self, sql, data_source_id):
        """
        自定义sql查询数据接口

        :param sql: SQL语句
        :param data_source_id: 数据源ID
        :return: requests.Response对象
        """

        body = {
            "bizId": "yyb_bi",
            "plugin_model_conf": {
                "special_model_info": {
                    "sql": sql,
                    "sqlTpl": sql,
                    "sqlSyntaxCheck": True,
                },
                "common_model_info": {
                    "data_source_id": data_source_id, 
                }
            },
            "cacheable": False,
            "version": 1,
            "model_source": 1,
            "canBeaconTurbo": True,
            "model_type": "sql",
            "is_edit": True
        }
        url = f"{self.base_url}/datatalk/analysis/model?bizId=yyb_bi"
        headers = self._get_headers(content_type="application/json")

        response = self.session.post(url, headers=headers, json=body)
        return response

    def query_online_user_count(self, start_date: str = None, end_date: str = None, qua_list: List[str] = None) -> Dict[str, Dict[str, int]]:
        """
        查询联网用户数，表：dws_ydc_networking_guid_di，数据源ID：1235751

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {qua: user_count}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_online_user_count_sql(
            start_date_formatted, end_date_formatted, qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('ds')
            qua = obj.get('qua')
            user_count = obj.get('count(DISTINCT guid)', 0)

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            result[formatted_date][qua] = user_count

        return result

    def get_user_count_by_date_qua(self, data: Dict[str, Dict[str, int]], date: str, qua: str) -> int:
        """
        获取指定日期和QUA的联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称
        :return: 联网用户数，如果不存在则返回0
        """
        return data.get(date, {}).get(qua, 0)

    def get_user_count_by_date(self, data: Dict[str, Dict[str, int]], date: str) -> Dict[str, int]:
        """
        获取指定日期的所有QUA联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有QUA的用户数字典，格式为 {qua: user_count}
        """
        return data.get(date, {})

    def get_user_count_by_qua(self, data: Dict[str, Dict[str, int]], qua: str) -> Dict[str, int]:
        """
        获取指定QUA在所有日期的联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的用户数字典，格式为 {date: user_count}
        """
        result = {}
        for date, qua_data in data.items():
            if qua in qua_data:
                result[date] = qua_data[qua]
        return result

    def query_launch_speed_analytics(self,
                                   qua_list: List[str],
                                   start_date: str = "2024-11-24",
                                   end_date: str = "2025-12-29") -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        查询启动速度分析数据，支持外部自定义QUA、时间参数

        :param qua_list: QUA列表
        :param start_date: 开始日期，格式：YYYY-MM-DD，默认：2024-11-24
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认：2025-12-29
        :return: 返回字典，格式为 {qua: {user_type: {launch_type: average_value}}}
                user_type包括: 新用户, 老用户
                launch_type包括: 常规热启动, 常规冷启动, 常规外call热启动, 常规外call冷启动
        """
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDDHH
        start_date_formatted = start_date.replace('-', '') + '00'
        end_date_formatted = end_date.replace('-', '') + '23'

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询 - 基于用户提供的参考SQL，但不按日期分组以获取整体平均值
        sql = BeaconSQLConstants.get_launch_speed_analytics_sql(
            start_date_formatted, end_date_formatted, qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        # 定义目标启动类型
        # 新用户（不带套壳）- 只关注常规启动类型
        new_user_target_types = {'常规热启动', '常规冷启动', '常规外call热启动', '常规外call冷启动'}
        # 老用户（带套壳）- 只关注套壳常规启动类型
        old_user_target_types = {'套壳常规热启动', '套壳常规冷启动', '套壳常规外call热启动', '套壳常规外call冷启动'}

        for obj in objects:
            qua = obj.get('qua')
            startup_type = obj.get('desc_str')
            avg_value = obj.get('平均值', 0)

            # 判断是否为目标启动类型
            if startup_type in new_user_target_types:
                user_type = '新用户'
                launch_type = startup_type
            elif startup_type in old_user_target_types:
                user_type = '老用户'
                # 去掉"套壳"前缀
                launch_type = startup_type[2:]  # 去掉前两个字符"套壳"
            else:
                continue  # 跳过非目标启动类型

            # 构建嵌套字典
            if qua not in result:
                result[qua] = {}
            if user_type not in result[qua]:
                result[qua][user_type] = {}

            # 存储平均值，保留2位小数
            result[qua][user_type][launch_type] = round(avg_value, 2) if avg_value else 0

        return result

    def get_new_user_launch_speed(self, data: Dict[str, Dict[str, Dict[str, float]]], qua: str = None) -> Dict[str, float]:
        """
        获取新用户启动速度数据

        :param data: 通过query_launch_speed_analytics获取的数据
        :param qua: 指定QUA，如果为None则返回所有QUA的聚合数据
        :return: 返回字典，格式为 {launch_type: average_value}
        """
        if qua:
            return data.get(qua, {}).get('新用户', {})

        # 聚合所有QUA的新用户数据
        aggregated = {}
        for qua_data in data.values():
            new_user_data = qua_data.get('新用户', {})
            for launch_type, avg_value in new_user_data.items():
                if launch_type not in aggregated:
                    aggregated[launch_type] = []
                if avg_value > 0:  # 只考虑有效值
                    aggregated[launch_type].append(avg_value)

        # 计算平均值
        result = {}
        for launch_type, values in aggregated.items():
            if values:
                result[launch_type] = round(sum(values) / len(values), 2)
            else:
                result[launch_type] = 0

        return result

    def get_old_user_launch_speed(self, data: Dict[str, Dict[str, Dict[str, float]]], qua: str = None) -> Dict[str, float]:
        """
        获取老用户启动速度数据

        :param data: 通过query_launch_speed_analytics获取的数据
        :param qua: 指定QUA，如果为None则返回所有QUA的聚合数据
        :return: 返回字典，格式为 {launch_type: average_value}
        """
        if qua:
            return data.get(qua, {}).get('老用户', {})

        # 聚合所有QUA的老用户数据
        aggregated = {}
        for qua_data in data.values():
            old_user_data = qua_data.get('老用户', {})
            for launch_type, avg_value in old_user_data.items():
                if launch_type not in aggregated:
                    aggregated[launch_type] = []
                if avg_value > 0:  # 只考虑有效值
                    aggregated[launch_type].append(avg_value)

        # 计算平均值
        result = {}
        for launch_type, values in aggregated.items():
            if values:
                result[launch_type] = round(sum(values) / len(values), 2)
            else:
                result[launch_type] = 0

        return result

    def query_network_coverage_data(self, date: str = None) -> int:
        """
        查询指定日期的联网用户总数，用于计算联网覆盖率

        :param date: 日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :return: 联网用户总数
        """
        # 设置默认日期为当前时间的前一天
        if date is None:
            date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        date_formatted = date.replace('-', '')

        # 构建SQL查询 - 查询总的联网用户数（不按QUA分组）
        sql = BeaconSQLConstants.get_network_coverage_data_sql(date_formatted)

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据
        objects = response_data.get('data', {}).get('objects', [])

        if not objects:
            return 0

        # 获取总联网用户数
        total_users = objects[0].get('total_networking_users', 0)
        return total_users

    def calculate_network_coverage_rate(self,
                                      online_user_data: Dict[str, Dict[str, int]],
                                      date: str,
                                      total_networking_users: int = None) -> Dict[str, float]:
        """
        计算联网覆盖率：版本联网数/联网用户总数

        :param online_user_data: 通过query_online_user_count获取的数据
        :param date: 日期，格式：YYYY/M/D（与online_user_data中的日期格式一致）
        :param total_networking_users: 联网用户总数，如果为None则自动查询
        :return: 返回字典，格式为 {qua: coverage_rate}
        """
        # 获取指定日期的QUA用户数据
        date_data = self.get_user_count_by_date(online_user_data, date)

        if not date_data:
            return {}

        # 如果没有提供总联网用户数，则自动查询
        if total_networking_users is None:
            # 将日期格式从YYYY/M/D转换为YYYY-MM-DD
            date_parts = date.split('/')
            year = date_parts[0]
            month = date_parts[1].zfill(2)  # 补零
            day = date_parts[2].zfill(2)    # 补零
            formatted_date = f"{year}-{month}-{day}"

            total_networking_users = self.query_network_coverage_data(formatted_date)

        if total_networking_users == 0:
            return {}

        # 计算每个QUA的联网覆盖率
        coverage_rates = {}
        for qua, version_networking_count in date_data.items():
            coverage_rate = round(version_networking_count / total_networking_users, 4)
            coverage_rates[qua] = coverage_rate

        return coverage_rates

    def get_network_coverage_rate_by_qua(self,
                                       coverage_data: Dict[str, float],
                                       qua: str) -> float:
        """
        获取指定QUA的联网覆盖率（便捷函数）

        :param coverage_data: 通过calculate_network_coverage_rate获取的数据
        :param qua: QUA名称
        :return: 联网覆盖率，如果不存在则返回0
        """
        return coverage_data.get(qua, 0)

    def query_external_call_download_data(self,
                                        start_date: str = None,
                                        end_date: str = None,
                                        qua_list: List[str] = None) -> Dict[str, Dict[str, any]]:
        """
        查询外call下载数据，包括开始下载率和成功下载率

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {qua: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD和YYYYMMDDHH
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')
        start_date_hour_formatted = start_date.replace('-', '') + '00'
        end_date_hour_formatted = end_date.replace('-', '') + '23'

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_external_call_download_rate_sql(
            start_date_formatted, end_date_formatted,
            start_date_hour_formatted, end_date_hour_formatted,
            qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('日期')
            qua = obj.get('版本')

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if qua not in result[formatted_date]:
                result[formatted_date][qua] = {}

            # 存储所有指标数据
            result[formatted_date][qua] = {
                '外call承接户数': obj.get('外call承接户数', 0),
                '外call曝光户数': obj.get('外call曝光户数', 0),
                '外call卡曝光率': obj.get('外call卡曝光率', '0%'),
                '外call承接曝光pv': obj.get('外call承接曝光pv', 0),
                '外call曝光pv': obj.get('外call曝光pv', 0),
                '开始下载户数': obj.get('开始下载户数', 0),
                '外call开始下载率': obj.get('外call开始下载率', '0%'),
                '成功下载户数': obj.get('成功下载户数', 0),
                '外call成功下载率': obj.get('外call成功下载率', '0%'),
                '开始下载量': obj.get('开始下载量', 0),
                '成功下载量': obj.get('成功下载量', 0),
                '失败下载量': obj.get('失败下载量', 0),
                '暂停下载量': obj.get('暂停下载量', 0),
                '失败下载户数': obj.get('失败下载户数', 0),
                '暂停下载户数': obj.get('暂停下载户数', 0)
            }

        return result

    def get_external_call_start_download_rate(self,
                                             data: Dict[str, Dict[str, Dict[str, any]]],
                                             date: str,
                                             qua: str = None) -> Dict[str, str]:
        """
        获取外call开始下载率（便捷函数）

        :param data: 通过query_external_call_download_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的开始下载率
        :return: 如果指定QUA，返回该QUA的开始下载率字符串；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('外call开始下载率', '0%')

        # 返回该日期所有QUA的开始下载率
        result = {}
        for qua_name, qua_data in date_data.items():
            result[qua_name] = qua_data.get('外call开始下载率', '0%')

        return result

    def get_external_call_success_download_rate(self,
                                               data: Dict[str, Dict[str, Dict[str, any]]],
                                               date: str,
                                               qua: str = None) -> Dict[str, str]:
        """
        获取外call成功下载率（便捷函数）

        :param data: 通过query_external_call_download_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的成功下载率
        :return: 如果指定QUA，返回该QUA的成功下载率字符串；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('外call成功下载率', '0%')

        # 返回该日期所有QUA的成功下载率
        result = {}
        for qua_name, qua_data in date_data.items():
            result[qua_name] = qua_data.get('外call成功下载率', '0%')

        return result

    def get_external_call_download_metrics_by_qua(self,
                                                 data: Dict[str, Dict[str, Dict[str, any]]],
                                                 qua: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定QUA在所有日期的外call下载指标（便捷函数）

        :param data: 通过query_external_call_download_data获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的下载指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if qua in date_data:
                result[date] = date_data[qua]
        return result

    def query_cloud_gaming_plugin_launch_success_rate(self,
                                                     start_date: str = None,
                                                     end_date: str = None,
                                                     app_version_list: List[str] = None) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        查询云游插件拉起成功率数据

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param app_version_list: app_version列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {app_version: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认app_version列表
        if app_version_list is None:
            app_version_list = []

        # 如果app_version列表为空，直接返回空结果
        if not app_version_list:
            return {}

        # 将日期格式转换为所需格式
        # start_date_hour_formatted: YYYYMMDDHH
        start_date_hour_formatted = start_date.replace('-', '') + '00'

        # end_date_hour_formatted_plus_one: 结束日期+1天的00时
        end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')
        end_date_plus_one = (end_date_dt + timedelta(days=1)).strftime('%Y%m%d') + '01'

        # start_date_time: YYYY-MM-DD HH:MM:SS
        start_date_time = start_date + ' 00:00:00'

        # end_date_time_plus_one: 结束日期+1天的00:00:00
        end_date_time_plus_one = (end_date_dt + timedelta(days=1)).strftime('%Y-%m-%d') + ' 00:00:00'

        # 构建app_version条件
        app_version_conditions = "', '".join(app_version_list)
        app_version_conditions = f"'{app_version_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_cloud_gaming_plugin_launch_success_rate_sql(
            start_date_hour_formatted, end_date_plus_one,
            start_date_time, end_date_time_plus_one,
            app_version_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            date = obj.get('dim_0')  # 日期格式：YYYY-MM-DD
            app_version = obj.get('dim_1')

            # 获取各项指标
            tamst_users = obj.get('tamst跳转用户数', 0)  # tamst跳转用户数
            open_activity_users = obj.get('openActivity用户数', 0)  # openActivity用户数
            open_activity_start_users = obj.get('openActivity-实际启动用户数', 0)  # openActivity-实际启动用户数
            click_to_launch_rate = obj.get('端内插件点击到拉起率', 0)  # 端内插件点击到拉起率
            click_to_execution_rate = obj.get('端内插件点击到实际执行率', 0)  # 端内插件点击到实际执行率（云游插件拉起成功率）

            # 构建嵌套字典
            if date not in result:
                result[date] = {}

            if app_version not in result[date]:
                result[date][app_version] = {}

            # 存储所有指标数据
            result[date][app_version] = {
                'tamst跳转用户数': tamst_users,
                'openActivity用户数': open_activity_users,
                'openActivity实际启动用户数': open_activity_start_users,
                '端内插件点击到拉起率': round(click_to_launch_rate, 4) if click_to_launch_rate else 0,
                '端内插件点击到实际执行率': round(click_to_execution_rate, 4) if click_to_execution_rate else 0,
                '云游插件拉起成功率': round(click_to_execution_rate, 4) if click_to_execution_rate else 0  # 别名
            }

        return result

    def get_cloud_gaming_plugin_launch_success_rate(self,
                                                   data: Dict[str, Dict[str, Dict[str, float]]],
                                                   date: str,
                                                   app_version: str = None) -> float:
        """
        获取云游插件拉起成功率（便捷函数）

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :param app_version: app_version名称，如果为None则返回该日期所有版本的平均成功率
        :return: 云游插件拉起成功率（0-1之间的小数）
        """
        date_data = data.get(date, {})

        if app_version:
            app_data = date_data.get(app_version, {})
            return app_data.get('云游插件拉起成功率', 0)

        # 返回该日期所有版本的平均成功率
        if not date_data:
            return 0

        rates = [app_data.get('云游插件拉起成功率', 0) for app_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 4)

    def get_cloud_gaming_plugin_click_to_launch_rate(self,
                                                   data: Dict[str, Dict[str, Dict[str, float]]],
                                                   date: str,
                                                   app_version: str = None) -> float:
        """
        获取端内插件点击到拉起率（便捷函数）

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :param app_version: app_version名称，如果为None则返回该日期所有版本的平均拉起率
        :return: 端内插件点击到拉起率（0-1之间的小数）
        """
        date_data = data.get(date, {})

        if app_version:
            app_data = date_data.get(app_version, {})
            return app_data.get('端内插件点击到拉起率', 0)

        # 返回该日期所有版本的平均拉起率
        if not date_data:
            return 0

        rates = [app_data.get('端内插件点击到拉起率', 0) for app_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 4)

    def get_cloud_gaming_plugin_metrics_by_app_version(self,
                                                     data: Dict[str, Dict[str, Dict[str, float]]],
                                                     app_version: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定app_version在所有日期的云游插件指标（便捷函数）

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param app_version: app_version名称
        :return: 该app_version在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if app_version in date_data:
                result[date] = date_data[app_version]
        return result

    def get_cloud_gaming_plugin_metrics_by_date(self,
                                              data: Dict[str, Dict[str, Dict[str, float]]],
                                              date: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定日期的所有app_version云游插件指标（便捷函数）

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :return: 该日期下所有app_version的指标字典，格式为 {app_version: {metric_name: value}}
        """
        return data.get(date, {})

    def query_popup_success_rate(self,
                                start_date: str = None,
                                end_date: str = None,
                                qua_list: List[str] = None,
                                user_type_list: List[str] = None) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        查询弹窗成功率数据

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :param user_type_list: 用户类型列表，默认为['1', '2']（新用户和老用户）
        :return: 返回嵌套字典，格式为 {date: {qua: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 设置默认用户类型列表
        if user_type_list is None:
            user_type_list = ['remain', 'silent_back']  

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建用户类型条件
        user_type_conditions = "', '".join(user_type_list)
        user_type_conditions = f"'{user_type_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_popup_success_rate_sql(
            start_date_formatted, end_date_formatted,
            qua_conditions, user_type_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('时间')
            qua = obj.get('版本号')
            pop_times = obj.get('弹窗人数', 0)
            expose_times = obj.get('曝光人数', 0)
            success_rate = obj.get('弹窗成功率(%)', 0)

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if qua not in result[formatted_date]:
                result[formatted_date][qua] = {}

            # 存储所有指标数据
            result[formatted_date][qua] = {
                '弹窗人数': pop_times,
                '曝光人数': expose_times,
                '弹窗成功率(%)': success_rate
            }

        return result

    def get_popup_success_rate(self,
                             data: Dict[str, Dict[str, Dict[str, float]]],
                             date: str,
                             qua: str = None) -> float:
        """
        获取弹窗成功率（便捷函数）

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的平均成功率
        :return: 弹窗成功率（百分比数值）
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('弹窗成功率(%)', 0)

        # 返回该日期所有QUA的平均成功率
        if not date_data:
            return 0

        rates = [qua_data.get('弹窗成功率(%)', 0) for qua_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 2)

    def get_popup_metrics_by_qua(self,
                               data: Dict[str, Dict[str, Dict[str, float]]],
                               qua: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定QUA在所有日期的弹窗指标（便捷函数）

        :param data: 通过query_popup_success_rate获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if qua in date_data:
                result[date] = date_data[qua]
        return result

    def get_popup_metrics_by_date(self,
                                data: Dict[str, Dict[str, Dict[str, float]]],
                                date: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定日期的所有QUA弹窗指标（便捷函数）

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有QUA的指标字典，格式为 {qua: {metric_name: value}}
        """
        return data.get(date, {})

    def get_popup_exposure_rate(self,
                              data: Dict[str, Dict[str, Dict[str, float]]],
                              date: str,
                              qua: str = None) -> Dict[str, float]:
        """
        获取弹窗曝光率（便捷函数）- 曝光人数/弹窗人数

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的曝光率
        :return: 如果指定QUA，返回该QUA的曝光率；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            pop_times = qua_data.get('弹窗人数', 0)
            expose_times = qua_data.get('曝光人数', 0)
            if pop_times > 0:
                return round((expose_times / pop_times) * 100, 2)
            return 0

        # 返回该日期所有QUA的曝光率
        result = {}
        for qua_name, qua_data in date_data.items():
            pop_times = qua_data.get('弹窗人数', 0)
            expose_times = qua_data.get('曝光人数', 0)
            if pop_times > 0:
                result[qua_name] = round((expose_times / pop_times) * 100, 2)
            else:
                result[qua_name] = 0

        return result


if __name__ == "__main__":
    client = BeaconAPIClient()
    # 测试联网用户数查询功能
    # try:
    #     # 查询2025年5月30日到6月1日的联网用户数
    #     qua_list = [
    #         'TMAF_899_P_7921', 'TMAF_899_P_7851', 'TMAF_899_P_7852',
    #         'TMAF_899_P_7853', 'TMAF_899_P_7924', 'TMAF_899_P_7855',
    #         'TMAF_899_P_7856', 'TMAF_898_P_7857'
    #     ]

    #     # 获取联网用户数数据（嵌套字典格式）
    #     print("=== 联网用户数查询结果 ===")
    #     data = client.query_online_user_count(
    #         start_date="2025-05-30",
    #         end_date="2025-06-01",
    #         qua_list=qua_list
    #     )

    #     print("查询结果数据结构:")
    #     for date in sorted(data.keys(), reverse=True):
    #         print(f"日期: {date}")
    #         for qua, count in data[date].items():
    #             print(f"  QUA: {qua}, 联网用户数: {count}")
    #         print()

    #     # 便捷查询功能
    #     print("=== 便捷查询功能 ===")

    #     # 查询特定日期和QUA的用户数
    #     specific_count = client.get_user_count_by_date_qua(
    #         data, "2025/5/30", "TMAF_899_P_7924"
    #     )
    #     print(f"2025/5/30 TMAF_899_P_7924 的联网用户数: {specific_count}")

    #     # 查询特定日期的所有QUA用户数
    #     date_data = client.get_user_count_by_date(data, "2025/5/30")
    #     print(f"2025/5/30 所有QUA的联网用户数: {date_data}")

    #     # 查询特定QUA在所有日期的用户数
    #     qua_data = client.get_user_count_by_qua(data, "TMAF_899_P_7924")
    #     print(f"TMAF_899_P_7924 在所有日期的联网用户数: {qua_data}")

    # except Exception as e:
    #     print(f"查询失败: {e}")

    # # ========== 启动速度分析功能使用示例 ==========
    # print("=" * 60)
    # print("启动速度分析功能使用示例")
    # print("=" * 60)

    # try:
    #     # a. 输入QUA list和时间范围
    #     print("=== a. 查询指定QUA列表和时间范围的启动速度数据 ===")
    #     qua_list = ['TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550', 'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552']
    #     start_date = "2025-06-01"
    #     end_date = "2025-06-08"

    #     print(f"QUA列表: {qua_list}")
    #     print(f"时间范围: {start_date} ~ {end_date}")

    #     # 查询启动速度数据
    #     launch_speed_data = client.query_launch_speed_analytics(
    #         qua_list=qua_list,
    #         start_date=start_date,
    #         end_date=end_date
    #     )

    #     print(f"查询完成，获得 {len(launch_speed_data)} 个QUA的数据")

    #     # b. 利用a查到的数据，使用便捷函数，根据指定的qua得到新用户和老用户的启动速度
    #     print("\n=== b. 使用便捷函数获取指定QUA的新用户和老用户启动速度 ===")

    #     # 指定要查询的QUA
    #     target_qua = 'TMAF_899_P_8547'
    #     print(f"目标QUA: {target_qua}")

    #     # 便捷取数 - 新用户启动速度
    #     new_user_speed = client.get_new_user_launch_speed(launch_speed_data, target_qua)

    #     # 便捷取数 - 老用户启动速度
    #     old_user_speed = client.get_old_user_launch_speed(launch_speed_data, target_qua)

    #     print(f"\n{target_qua} 新用户启动速度:")
    #     print(f"  常规热启动: {new_user_speed.get('常规热启动', 0)}ms")
    #     print(f"  常规冷启动: {new_user_speed.get('常规冷启动', 0)}ms")
    #     print(f"  常规外call热启动: {new_user_speed.get('常规外call热启动', 0)}ms")
    #     print(f"  常规外call冷启动: {new_user_speed.get('常规外call冷启动', 0)}ms")

    #     print(f"\n{target_qua} 老用户启动速度:")
    #     print(f"  常规热启动: {old_user_speed.get('常规热启动', 0)}ms")
    #     print(f"  常规冷启动: {old_user_speed.get('常规冷启动', 0)}ms")
    #     print(f"  常规外call热启动: {old_user_speed.get('常规外call热启动', 0)}ms")
    #     print(f"  常规外call冷启动: {old_user_speed.get('常规外call冷启动', 0)}ms")

    # except Exception as e:
    #     print(f"查询失败: {e}")


    # # ========== 联网覆盖率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("联网覆盖率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询联网用户数据
    #     print("=== 1. 查询联网用户数据 ===")
    #     test_date = "2025-06-08"
    #     test_qua_list = [
    #         'TMAF_899_P_7921', 'TMAF_899_P_7851', 'TMAF_899_P_7852',
    #         'TMAF_899_P_7853', 'TMAF_899_P_7924'
    #     ]

    #     # 获取联网用户数据
    #     online_user_data = client.query_online_user_count(
    #         start_date=test_date,
    #         end_date=test_date,
    #         qua_list=test_qua_list
    #     )

    #     # 格式化日期为YYYY/M/D格式
    #     date_parts = test_date.split('-')
    #     formatted_date = f"{date_parts[0]}/{int(date_parts[1])}/{int(date_parts[2])}"

    #     print(f"查询日期: {test_date}")
    #     print(f"查询QUA列表: {test_qua_list}")
    #     print(f"查询结果: {online_user_data.get(formatted_date, {})}")

    #     # 2. 查询总联网用户数
    #     print("\n=== 2. 查询总联网用户数 ===")
    #     total_users = client.query_network_coverage_data(test_date)
    #     print(f"总联网用户数: {total_users}")

    #     # 3. 计算联网覆盖率
    #     print("\n=== 3. 计算联网覆盖率 ===")
    #     coverage_rates = client.calculate_network_coverage_rate(
    #         online_user_data=online_user_data,
    #         date=formatted_date,
    #         total_networking_users=total_users
    #     )

    #     print("联网覆盖率结果:")
    #     for qua, rate in coverage_rates.items():
    #         print(f"  QUA: {qua}, 覆盖率: {rate:.4f} ({rate*100:.2f}%)")

    #     # 4. 使用便捷函数获取特定QUA的覆盖率
    #     print("\n=== 4. 使用便捷函数获取特定QUA的覆盖率 ===")
    #     target_qua = 'TMAF_899_P_7924'
    #     target_rate = client.get_network_coverage_rate_by_qua(coverage_rates, target_qua)
    #     print(f"{target_qua} 的联网覆盖率: {target_rate:.4f} ({target_rate*100:.2f}%)")

    # except Exception as e:
    #     print(f"联网覆盖率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("联网覆盖率示例结束")
    # print("=" * 60)

    # # ========== 外call下载率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("外call下载率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询外call下载数据
    #     print("=== 1. 查询外call下载数据 ===")
    #     test_start_date = "2025-06-06"
    #     test_end_date = "2025-06-08"
    #     test_qua_list = [
    #         'TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550',
    #         'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552'
    #     ]

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询QUA列表: {test_qua_list}")

    #     # 获取外call下载数据
    #     download_data = client.query_external_call_download_data(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         qua_list=test_qua_list
    #     )

    #     print(f"查询完成，获得 {len(download_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(download_data.keys()):
    #         print(f"\n日期: {date}")
    #         for qua in list(download_data[date].keys())[:2]:  # 只显示前2个QUA
    #             qua_data = download_data[date][qua]
    #             print(f"  QUA: {qua}")
    #             print(f"    外call开始下载率: {qua_data.get('外call开始下载率', '0%')}")
    #             print(f"    外call成功下载率: {qua_data.get('外call成功下载率', '0%')}")
    #             print(f"    外call曝光户数: {qua_data.get('外call曝光户数', 0)}")
    #             print(f"    开始下载户数: {qua_data.get('开始下载户数', 0)}")
    #             print(f"    成功下载户数: {qua_data.get('成功下载户数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的开始下载率
    #     target_date = "2025/6/6"
    #     if target_date in download_data:
    #         start_rates = client.get_external_call_start_download_rate(download_data, target_date)
    #         print(f"\n{target_date} 所有QUA的外call开始下载率:")
    #         for qua, rate in start_rates.items():
    #             print(f"  {qua}: {rate}")

    #         # 获取特定日期的成功下载率
    #         success_rates = client.get_external_call_success_download_rate(download_data, target_date)
    #         print(f"\n{target_date} 所有QUA的外call成功下载率:")
    #         for qua, rate in success_rates.items():
    #             print(f"  {qua}: {rate}")

    #         # 获取特定QUA的开始下载率
    #         target_qua = 'TMAF_899_P_8547'
    #         if target_qua in download_data.get(target_date, {}):
    #             qua_start_rate = client.get_external_call_start_download_rate(download_data, target_date, target_qua)
    #             qua_success_rate = client.get_external_call_success_download_rate(download_data, target_date, target_qua)
    #             print(f"\n{target_date} {target_qua} 的下载率:")
    #             print(f"  外call开始下载率: {qua_start_rate}")
    #             print(f"  外call成功下载率: {qua_success_rate}")

    #     # 3. 获取特定QUA在所有日期的指标
    #     print("\n=== 3. 获取特定QUA在所有日期的指标 ===")
    #     target_qua = 'TMAF_899_P_8547'
    #     qua_metrics = client.get_external_call_download_metrics_by_qua(download_data, target_qua)
    #     print(f"\n{target_qua} 在所有日期的下载指标:")
    #     for date, metrics in qua_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    外call开始下载率: {metrics.get('外call开始下载率', '0%')}")
    #         print(f"    外call成功下载率: {metrics.get('外call成功下载率', '0%')}")

    # except Exception as e:
    #     print(f"外call下载率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("外call下载率示例结束")
    # print("=" * 60)

    # # ========== 云游插件拉起成功率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("云游插件拉起成功率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询云游插件拉起成功率数据
    #     print("=== 1. 查询云游插件拉起成功率数据 ===")
    #     test_start_date = "2025-06-06"
    #     test_end_date = "2025-06-08"
    #     test_app_version_list = [
    #         '8.9.9_8994130_8549', '8.9.9_8994130_8551', '8.9.9_8994130_8552',
    #         '8.9.9_8994130_8547', '8.9.9_8994130_8548', '8.9.9_8994130_8550'
    #     ]

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询app_version列表: {test_app_version_list}")

    #     # 获取云游插件拉起成功率数据
    #     plugin_data = client.query_cloud_gaming_plugin_launch_success_rate(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         app_version_list=test_app_version_list
    #     )

    #     print(f"查询完成，获得 {len(plugin_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(plugin_data.keys()):
    #         print(f"\n日期: {date}")
    #         for app_version in list(plugin_data[date].keys())[:2]:  # 只显示前2个版本
    #             app_data = plugin_data[date][app_version]
    #             print(f"  app_version: {app_version}")
    #             print(f"    云游插件拉起成功率: {app_data.get('云游插件拉起成功率', 0):.4f} ({app_data.get('云游插件拉起成功率', 0)*100:.2f}%)")
    #             print(f"    端内插件点击到拉起率: {app_data.get('端内插件点击到拉起率', 0):.4f} ({app_data.get('端内插件点击到拉起率', 0)*100:.2f}%)")
    #             print(f"    tamst跳转用户数: {app_data.get('tamst跳转用户数', 0)}")
    #             print(f"    openActivity用户数: {app_data.get('openActivity用户数', 0)}")
    #             print(f"    openActivity实际启动用户数: {app_data.get('openActivity实际启动用户数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的云游插件拉起成功率
    #     target_date = "2025-06-06"
    #     if target_date in plugin_data:
    #         # 获取该日期所有版本的平均成功率
    #         avg_success_rate = client.get_cloud_gaming_plugin_launch_success_rate(plugin_data, target_date)
    #         print(f"\n{target_date} 所有版本的平均云游插件拉起成功率: {avg_success_rate:.4f} ({avg_success_rate*100:.2f}%)")

    #         # 获取特定版本的成功率
    #         target_app_version = '8.9.9_8994130_8547'
    #         if target_app_version in plugin_data.get(target_date, {}):
    #             version_success_rate = client.get_cloud_gaming_plugin_launch_success_rate(plugin_data, target_date, target_app_version)
    #             version_launch_rate = client.get_cloud_gaming_plugin_click_to_launch_rate(plugin_data, target_date, target_app_version)
    #             print(f"\n{target_date} {target_app_version} 的指标:")
    #             print(f"  云游插件拉起成功率: {version_success_rate:.4f} ({version_success_rate*100:.2f}%)")
    #             print(f"  端内插件点击到拉起率: {version_launch_rate:.4f} ({version_launch_rate*100:.2f}%)")

    #     # 3. 获取特定版本在所有日期的指标
    #     print("\n=== 3. 获取特定版本在所有日期的指标 ===")
    #     target_app_version = '8.9.9_8994130_8547'
    #     version_metrics = client.get_cloud_gaming_plugin_metrics_by_app_version(plugin_data, target_app_version)
    #     print(f"\n{target_app_version} 在所有日期的指标:")
    #     for date, metrics in version_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    云游插件拉起成功率: {metrics.get('云游插件拉起成功率', 0):.4f} ({metrics.get('云游插件拉起成功率', 0)*100:.2f}%)")
    #         print(f"    端内插件点击到拉起率: {metrics.get('端内插件点击到拉起率', 0):.4f} ({metrics.get('端内插件点击到拉起率', 0)*100:.2f}%)")

    #     # 4. 获取特定日期的所有版本指标
    #     print("\n=== 4. 获取特定日期的所有版本指标 ===")
    #     target_date = "2025-06-06"
    #     date_metrics = client.get_cloud_gaming_plugin_metrics_by_date(plugin_data, target_date)
    #     print(f"\n{target_date} 所有版本的云游插件拉起成功率:")
    #     for app_version, metrics in date_metrics.items():
    #         success_rate = metrics.get('云游插件拉起成功率', 0)
    #         print(f"  {app_version}: {success_rate:.4f} ({success_rate*100:.2f}%)")

    # except Exception as e:
    #     print(f"云游插件拉起成功率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("云游插件拉起成功率示例结束")
    # print("=" * 60)

    # ========== 弹窗成功率功能使用示例 ==========
    print("\n" + "=" * 60)
    print("弹窗成功率功能使用示例")
    print("=" * 60)

    try:
        # 1. 查询弹窗成功率数据
        print("=== 1. 查询弹窗成功率数据 ===")
        test_start_date = "2025-06-04"
        test_end_date = "2025-06-10"
        # test_qua_list = [
        #     'TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550',
        #     'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552'
        # ]
        test_qua_list = ['TMAF_842_F_6869']
        test_user_type_list = ['remain', 'silent_back']  

        print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
        print(f"查询QUA列表: {test_qua_list}")
        print(f"查询用户类型: {test_user_type_list}")

        # 获取弹窗成功率数据
        popup_data = client.query_popup_success_rate(
            start_date=test_start_date,
            end_date=test_end_date,
            qua_list=test_qua_list,
            user_type_list=test_user_type_list
        )

        print(f"查询完成，获得 {len(popup_data)} 个日期的数据")

        # 显示部分数据结构
        for date in sorted(popup_data.keys()):
            print(f"\n日期: {date}")
            for qua in list(popup_data[date].keys())[:2]:  # 只显示前2个QUA
                qua_data = popup_data[date][qua]
                print(f"  QUA: {qua}")
                print(f"    弹窗成功率: {qua_data.get('弹窗成功率(%)', 0):.2f}%")
                print(f"    弹窗人数: {qua_data.get('弹窗人数', 0)}")
                print(f"    曝光人数: {qua_data.get('曝光人数', 0)}")

        # 2. 使用便捷函数获取特定指标
        print("\n=== 2. 使用便捷函数获取特定指标 ===")

        # 获取特定日期的弹窗成功率
        target_date = "2025/6/6"
        if target_date in popup_data:
            # 获取该日期所有QUA的平均成功率
            avg_success_rate = client.get_popup_success_rate(popup_data, target_date)
            print(f"\n{target_date} 所有QUA的平均弹窗成功率: {avg_success_rate:.2f}%")

            # 获取特定QUA的成功率
            target_qua = 'TMAF_899_P_8547'
            if target_qua in popup_data.get(target_date, {}):
                qua_success_rate = client.get_popup_success_rate(popup_data, target_date, target_qua)
                qua_exposure_rate = client.get_popup_exposure_rate(popup_data, target_date, target_qua)
                print(f"\n{target_date} {target_qua} 的指标:")
                print(f"  弹窗成功率: {qua_success_rate:.2f}%")
                print(f"  弹窗曝光率: {qua_exposure_rate:.2f}%")

            # 获取该日期所有QUA的曝光率
            all_exposure_rates = client.get_popup_exposure_rate(popup_data, target_date)
            print(f"\n{target_date} 所有QUA的弹窗曝光率:")
            for qua, rate in all_exposure_rates.items():
                print(f"  {qua}: {rate:.2f}%")

        # 3. 获取特定QUA在所有日期的指标
        print("\n=== 3. 获取特定QUA在所有日期的指标 ===")
        target_qua = 'TMAF_899_P_8547'
        qua_metrics = client.get_popup_metrics_by_qua(popup_data, target_qua)
        print(f"\n{target_qua} 在所有日期的弹窗指标:")
        for date, metrics in qua_metrics.items():
            print(f"  {date}:")
            print(f"    弹窗成功率: {metrics.get('弹窗成功率(%)', 0):.2f}%")
            print(f"    弹窗人数: {metrics.get('弹窗人数', 0)}")
            print(f"    曝光人数: {metrics.get('曝光人数', 0)}")

        # 4. 获取特定日期的所有QUA指标
        print("\n=== 4. 获取特定日期的所有QUA指标 ===")
        target_date = "2025/6/6"
        date_metrics = client.get_popup_metrics_by_date(popup_data, target_date)
        print(f"\n{target_date} 所有QUA的弹窗成功率:")
        for qua, metrics in date_metrics.items():
            success_rate = metrics.get('弹窗成功率(%)', 0)
            print(f"  {qua}: {success_rate:.2f}%")

    except Exception as e:
        print(f"弹窗成功率查询失败: {e}")

    print("\n" + "=" * 60)
    print("弹窗成功率示例结束")
    print("=" * 60)


    # 原始SQL查询示例（保留作为参考）
    # sql = """
    # select
    #     ds, qua, count(distinct guid)
    # from
    #     beacon_olap.dws_ydc_networking_guid_di
    # where
    #     ds >= 20250530
    #     and ds <= 20250601
    #     and qua in ('TMAF_899_P_7921','TMAF_899_P_7851','TMAF_899_P_7852', 'TMAF_899_P_7853', 'TMAF_899_P_7924', 'TMAF_899_P_7855', 'TMAF_899_P_7856', 'TMAF_898_P_7857')
    # group by ds, qua
    # order by qua desc
    #     """
    # response = client.post_model_query(sql=sql, data_source_id=1235751)
    # print("post_model_query状态码:", response.status_code)
    # print("post_model_query响应内容:", response.text)
