新添加一个能力，支持外部自定义qua、时间（可以不传，默认时间为20241124 ~ 20251229，查询出来的数据都是一样的），查询 指定时间内的 新用户 启动速度（常规热启动，常规冷启动，常规外call热启动，常规外call冷启动 对应的 平均值） 与 指定时间内的 老用户 启动速度（套壳常规热启动，套壳常规冷启动，套壳常规外call热启动，套壳常规外call冷启动 对应的 平均值）。同时，外部可以根据包名便捷的查询 指定时间内的 新用户的启动速度 和 老用户的启动速度。注意 每个包 的 各个启动类型，就对应一个 平均值，不需要按天输出。
data_source_id=1235751
sql语句参考如下，注意 sql中的平均值就是给定时间范围的平均值了。
SELECT  qua,launch_type,start_type,run_type,crab_shell_type,
  case  
    when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=1 then '应用首次断层冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=3 then '当前版本首次断层冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=1 then '应用首次外call冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=3 then '当前版本首次外call冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=1 and run_type=2 then '常规冷启动'
    when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=2 then '常规外call冷启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=1 and run_type=2 then '常规热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=2 and run_type=1 then '首次断层热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=1 then '首次外call热启动'
    when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=2 then '常规外call热启动'
    
    when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=1 then '套壳应用首次断层冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=3 then '套壳当前版本首次断层冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=1 then '套壳应用首次外call冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=3 then '套壳当前版本首次外call冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=1 and run_type=2 then '套壳常规冷启动'
    when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=2 then '套壳常规外call冷启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=1 and run_type=2 then '套壳常规热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=2 and run_type=1 then '套壳首次断层热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=1 then '套壳首次外call热启动'
    when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=2 then '套壳常规外call热启动'
  else '其他' end as desc_str ,COUNT( *) as 次数,
AVG( tag_duration) as  平均值,
ApproxPercentile( tag_duration, 0.5) as  50分位,
ApproxPercentile( tag_duration, 0.8) as  80分位,
ApproxPercentile( tag_duration, 0.9) as  90分位  
FROM  
  [1055717].[launch_speed_event]  
WHERE  
  ds  >= 2024112400  
  AND  ds  <= 2025122923   
  AND tag = "Draw_End"
  AND  tagger_id  = 1  
  AND  qua  in ('TMAF_899_P_8547','TMAF_899_P_8548','TMAF_899_P_8550','TMAF_899_P_8549','TMAF_899_P_8551','TMAF_899_P_8552')
  AND content_type not LIKE '%ANCHOR_GAME_TAB%'
  AND content_type not LIKE '%SPLASH_IMAGE%'
  AND content_type not LIKE '%MAIN_SPLASH_VIEW%'
  group by qua,launch_type, start_type, run_type, crab_shell_type
  order by desc_str asc, qua asc



