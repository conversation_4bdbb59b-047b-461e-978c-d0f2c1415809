select imp_date,
  yyb_version,
  scene,
  coalesce(max(avg_exp_cnt),0) as avg_exp_cnt,
  coalesce(max(avg_click_cnt),0) as avg_click_cnt,
  coalesce(max(avg_dload_cnt),0) as avg_dload_cnt,
  coalesce(max(avg_install_cnt),0) as avg_install_cnt
from (
    select imp_date,
      yyb_version,
      scene,
      sum(
        case
          when event_code = 'AppExposure' then num
        end
      )  avg_exp_cnt,
      sum(
        case
          when event_code = 'AppClick' then num
        end
      )  avg_click_cnt,
      sum(
        case
          when event_code = 'AppSuccDownload' then num
        end
      )  avg_dload_cnt,
      sum(
        case
          when event_code = 'AppSuccInstall' then num
        end
      )  avg_install_cnt
    from (
        select imp_date,
          guid,
          case
            when #is_scene# = 1 then scene else 'all' end as scene,
          event_code,
          case
            when 'all' in (#version#) then 'all'
            else yyb_version
          end as yyb_version,
          count(1) as num
        from pcg_yyb_adbilling.dwd_yyb_business_event_log_di
        where imp_date >= #imp_date.start#
          and imp_date <= #imp_date.end#
          and is_yyb = 1
          and event_code in (
            'AppExposure',
            'AppClick',
            'AppSuccDownload',
            'AppSuccInstall'
          )
          and adposid in (
            18,
            34,
            1,
            1139,
            1162,
            13,
            1021,
            1160,
            174,
            1072,
            1165,
            799,
            346
          )
        group by imp_date,
          guid,
          scene,
          event_code,
          yyb_version
    ) a
    group by imp_date,
      scene,
      yyb_version
) t
where yyb_version in (#version#)
group by imp_date,
  scene,
  yyb_version
order by imp_date desc