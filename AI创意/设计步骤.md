（1） 设计Prompt，可以更具个人需求，灵活设计。如：
你是一个专业旅行博主，请以小红书专业旅行攻略博主的口吻，根据以下结构化信息，结合地理人文背景知识，输出具有叙事逻辑的深度攻略，生成一份专业级旅行攻略。

1. **基础信息**
- 出发地：[深圳]
- 目的地：[澳门]
- 旅行时长：[一天]
- 时间：[5月1日]
- 同行人数及类型：[朋友两人行]
- 预算区间：[经济型]
- 核心需求：[文化探索，摄影]

2. **目的地亮点速览**
- 🌟至少3个最具代表性的景观/文化符号
- 🏯当地特色建筑风格解析
- 🍜不可错过的标志性美食

3. **行程规划框架**
⏳建议季节与最佳时段
🗺️地理区域划分（如城市/自然景区分布）
🚶♂️每日节奏建议（文化沉浸/体力分配）

4. **交通指南**
🚌市内交通系统图解（地铁/公交/共享单车）
✈️机场到核心区域的接驳方案
🚗自驾注意事项（限速/停车/特色公路）

5. **住宿精选**
🏨不同预算段推荐（豪华型/设计型/民宿）
🛏️地理位置优势分析（靠近景点/夜生活区）
💡住宿贴士（插座类型/网络状况/宠物政策）

6. **深度体验清单**
🎭本地人常去的隐蔽景点
📸摄影爱好者必拍机位
🎭非遗体验工作坊推荐

7. **实用锦囊包**
🍂季节性穿着建议
💰消费水平参考（餐饮/门票/购物）
🚫文化禁忌警示
📱必备APP推荐（翻译/导航/本地服务）

8. **请避免：**
× 网红打卡点堆砌
× 模糊的时间表述
× 脱离预算的推荐
× 缺乏动线规划的景点罗列

（2）将Prompt输入ds，并开启联网搜索。
（3）获得生成的旅游攻略