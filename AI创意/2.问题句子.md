
思考过程：
嗯，用户让我设计一个Prompt，让AI生成文艺句子。首先，我需要理解用户的需求。文艺句子通常指的是富有诗意、情感丰富、用词考究的句子，可能用于文学作品、社交媒体文案或者个人创作灵感。用户可能希望这些句子能够引发共鸣，或者具有独特的比喻和意象。

接下来，我得考虑用户可能的深层需求。他们可能不只是想要一些华丽的辞藻，而是希望句子有深度和情感，能够传达特定的情绪或画面感。比如，用户可能想用这些句子在文章中增强感染力，或者在社交平台上吸引关注。因此，Prompt需要引导AI在结构、修辞和主题上有所侧重。

然后，我需要分析如何构造有效的Prompt。通常，好的Prompt需要明确任务、设定风格、提供例子或结构，并可能包括限制条件。比如，指定使用比喻、拟人等修辞手法，或者限定主题如自然、时间、孤独等。同时，可能需要避免过于抽象或晦涩的表达，确保句子既有文艺感又不失可读性。

还要考虑用户可能没有明确提到的方面。比如，句子的长度、是否需要押韵、是否要有特定的情感基调（如忧伤、温暖、希望等）。这些细节会影响生成结果的质量，所以在Prompt中最好明确这些要素，或者提供一定的灵活性让AI发挥。

另外，测试Prompt的效果也很重要。可能需要生成几个例子，看看是否符合预期。如果结果不够理想，可能需要调整Prompt中的关键词或结构，比如增加更多具体的修辞手法，或者细化主题范围。

最后，总结出一个结构清晰、要素齐全的Prompt，确保AI能够准确理解并生成符合要求的文艺句子。可能包括角色设定、核心指令、具体要求、示例和限制条件，这样用户使用时可以直接复制并获得满意的结果。

回答：
以下是为生成文艺句子设计的结构化Prompt模板，包含多维度引导和示例：

【角色设定】
你是一位拥有诗人气质的文字艺术家，擅长用细腻的意象和诗性语言捕捉瞬间的情感流动。

【核心指令】
请创作一组（3-5句）具有文学美感的原创句子，要求：

【具体要求】
1. 意象组合：
- 采用非常规物象搭配（如「时间在玻璃上结霜」）
- 融入自然元素隐喻（暮色/潮汐/植物等）
- 创造通感体验（颜色有温度，声音具形态）

2. 语言特质：
√ 克制而精准的陌生化表达
√ 保留适当的语法留白
√ 动词使用突破常规搭配
√ 每句控制在18-24字之间

3. 情感维度：
■ 呈现存在主义的孤独感
■ 暗含对流逝的温柔抵抗
■ 保持朦胧的多重解读空间

【示例参考】
"邮筒吞下最后一道暮色，铁锈在信封背面开花"
"候鸟把天空缝合成一件旧毛衣，线头垂落成雨"
"沉默在茶杯里膨胀，长出珊瑚状的阴影"

【限制条件】
× 避免直接抒情词汇
× 禁用成语俗谚
× 排除科技相关意象

请以分行短诗形式呈现，每句之间保留恰当的呼吸感。