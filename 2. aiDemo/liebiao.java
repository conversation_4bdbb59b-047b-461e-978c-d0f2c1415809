public static Spannable parseMarkdown(String markdownText) {
        SpannableStringBuilder spannable = new SpannableStringBuilder();
        int length = markdownText.length();
        int i = 0;

        while (i < length) {
            char currentChar = markdownText.charAt(i);

            if (currentChar == '#' && (i == 0 || markdownText.charAt(i - 1) == '\n')) {
                // Handle headers
                int headerLevel = 0;
                while (i < length && markdownText.charAt(i) == '#') {
                    headerLevel++;
                    i++;
                }
                if (i < length && markdownText.charAt(i) == ' ') {
                    int start = i + 1;
                    int end = markdownText.indexOf('\n', start);
                    if (end == -1) {
                        end = length;
                    }
                    // Append the header text without the leading #
                    spannable.append(markdownText, start, end);
                    float relativeSize = getHeaderRelativeSize(headerLevel);
                    spannable.setSpan(new RelativeSizeSpan(relativeSize), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    spannable.setSpan(new StyleSpan(Typeface.BOLD), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end;
                } else {
                    spannable.append(currentChar);
                }
            } else if (currentChar == '*' && i + 1 < length && markdownText.charAt(i + 1) == '*') {
                // Handle bold text
                int start = i + 2;
                int end = markdownText.indexOf("**", start);
                if (end != -1) {
                    spannable.append(markdownText, i, start - 2);
                    spannable.append(markdownText, start, end);
                    spannable.setSpan(new StyleSpan(Typeface.BOLD), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end + 2;
                } else {
                    spannable.append(currentChar);
                    i++;
                }
            } else if (currentChar == '*' && i + 1 < length && markdownText.charAt(i + 1) != '*') {
                // Handle italic text
                int start = i + 1;
                int end = markdownText.indexOf("*", start);
                if (end != -1) {
                    spannable.append(markdownText, i, start - 1);
                    spannable.append(markdownText, start, end);
                    spannable.setSpan(new StyleSpan(Typeface.ITALIC), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end + 1;
                } else {
                    spannable.append(currentChar);
                    i++;
                }
            } else if (currentChar == '-' && (i == 0 || markdownText.charAt(i - 1) == '\n')) {
                // Handle list items
                int start = i + 1;
                while (start < length && markdownText.charAt(start) == ' ') {
                    start++;
                }
                int end = markdownText.indexOf('\n', start);
                if (end == -1) {
                    end = length;
                }
                // Append the list item text without the leading -
                spannable.append(markdownText, start, end);
                spannable.setSpan(new BulletSpan(20), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                i = end;
            } else {
                spannable.append(currentChar);
                i++;
            }
        }

        return spannable;
    }







    import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.BulletSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;
import android.graphics.Typeface;

public class MarkdownParser {

    public static Spannable parseMarkdown(String markdownText) {
        SpannableStringBuilder spannable = new SpannableStringBuilder();
        int length = markdownText.length();
        int i = 0;

        while (i < length) {
            char currentChar = markdownText.charAt(i);

            if (currentChar == '#' && (i == 0 || markdownText.charAt(i - 1) == '\n')) {
                // Handle headers
                int headerLevel = 0;
                while (i < length && markdownText.charAt(i) == '#') {
                    headerLevel++;
                    i++;
                }
                if (i < length && markdownText.charAt(i) == ' ') {
                    int start = i + 1;
                    int end = markdownText.indexOf('\n', start);
                    if (end == -1) {
                        end = length;
                    }
                    // Append the header text without the leading #
                    spannable.append(markdownText, start, end);
                    float relativeSize = getHeaderRelativeSize(headerLevel);
                    spannable.setSpan(new RelativeSizeSpan(relativeSize), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    spannable.setSpan(new StyleSpan(Typeface.BOLD), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end;
                } else {
                    spannable.append(currentChar);
                }
            } else if (currentChar == '*' && i + 1 < length && markdownText.charAt(i + 1) == '*') {
                // Handle bold text
                int start = i + 2;
                int end = markdownText.indexOf("**", start);
                if (end != -1) {
                    spannable.append(markdownText, i, start - 2);
                    spannable.append(markdownText, start, end);
                    spannable.setSpan(new StyleSpan(Typeface.BOLD), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end + 2;
                } else {
                    spannable.append(currentChar);
                    i++;
                }
            } else if (currentChar == '*' && i + 1 < length && markdownText.charAt(i + 1) != '*') {
                // Handle italic text
                int start = i + 1;
                int end = markdownText.indexOf("*", start);
                if (end != -1) {
                    spannable.append(markdownText, i, start - 1);
                    spannable.append(markdownText, start, end);
                    spannable.setSpan(new StyleSpan(Typeface.ITALIC), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    i = end + 1;
                } else {
                    spannable.append(currentChar);
                    i++;
                }
            } else if (currentChar == '-' && (i == 0 || markdownText.charAt(i - 1) == '\n')) {
                // Handle list items
                int start = i + 1;
                while (start < length && markdownText.charAt(start) == ' ') {
                    start++;
                }
                int end = markdownText.indexOf('\n', start);
                if (end == -1) {
                    end = length;
                }
                // Append the list item text without the leading -
                spannable.append(markdownText, start, end);
                spannable.setSpan(new BulletSpan(20), spannable.length() - (end - start), spannable.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                i = end;
            } else {
                spannable.append(currentChar);
                i++;
            }
        }

        return spannable;
    }

    private static float getHeaderRelativeSize(int headerLevel) {
        switch (headerLevel) {
            case 1:
                return 2.0f; // H1
            case 2:
                return 1.5f; // H2
            case 3:
                return 1.2f; // H3
            default:
                return 1.0f; // Other headers
        }
    }
