公用模型 api-token：6288e078-9160-44f4-8397-6182af963c80
公用模型 api-token（0213）：00ac8819-7488-4487-bfbd-17f4d760aed8
混元API token：ag0Qtd0aX3S7Ge0Qetnb68YFBgkRFhvp

- 模型训练文档：https://iwiki.woa.com/p/4012013838
- 模型产出分析：https://iwiki.woa.com/p/4009141354



- 无法找到域名
  - 打开ioa的远程访问

- 未找到 model_detail_output 
  - 请求超时，建议开启流式回答

- 请求时间过长
  - 可能是接入层负载较高导致的，混元研发排查一次有所改善

- 使用 自己部署的 精调模型
  - 先部署精调模型，然后关联服务组，在agent中选择即可。
  - 部署时提示，超过3小时且调用次数低于5次、运行时长超过 1 天 将被回收
    - 咨询不同的2000，可以 变更 - 扩容 - 实例+1 来增加示例重新使用 
    - https://iwiki.woa.com/p/4009352581?from=iWiki_search

- agent加入精调模型，调试发现，精调模型输出异常
  - 排查中

- prompt
  - 模型：目前看7B模型有点呆，换13B 效果会好一点




用kotlin写一个通用方法。接受markdown 字符串，如果 遇到 书名号被加粗，需要在用空格隔开

例子1: 这是 空格 **《书名》** 空格 例子。
例子2: **《书名》** 空格 例子2来啦。


fun formatMarkdown(input: String): String {
    // 使用正则表达式匹配加粗的书名号
    val regex = Regex("\\*\\*(《[^》]+》)\\*\\*")
    
    // 使用replace函数替换匹配的内容
    return input.replace(regex) { matchResult ->
        // 在书名号前后添加空格
        " ${matchResult.groupValues[1]} "
    }
}