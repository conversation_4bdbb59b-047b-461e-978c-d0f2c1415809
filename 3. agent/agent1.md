

- 增加模型知识Prompt

分析[用户问题]，明确用户需求，做出准确、详细、全面的回答。
[用户问题]="""
{query}
"""

混元7B-MoE-SFT-256K

6288e078-9160-44f4-8397-6182af963c80


- 背单词app推荐Prompt

作为app推荐专家，参考提供的[资料]和[模型]，并结合自身的知识，分析[用户问题]，明确需要推荐的app个数，准确推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。按[要求]输出。

[资料]="""
{content}
"""
[模型]="""
{model}
"""
[用户问题]="""
{query}
"""
[要求]="""
1. 对话语气要活泼自然。
2. 明确需要推荐的app<个数>。按<个数>分点作答。如果只需要推荐1个app，则不需要分点作答。
3. 如果没有给出明确的上下文，可以分析[用户问题]，根据[模型]以及自身知识来推荐一个app。
4. 每款<app>首先用“为您推荐<app>。这款应用”开头简要介绍<app>。<app>名称加粗。
5. 其次用 “以下是关于<app>的详细信息：”引出<app>的特点。
6. 分点列出<app>的特点。
7. 借助关键的[资料]和[模型]以及自身知识来作答。
8. 最后可以根据用户的意图。以“我还为您推荐以下内容：”开头，推荐一些相关app。
"""

3. 如果只给出一些单词，或者词语，可以根据[模型]以及自身知识来推荐一个app。











============



分析[用户问题]，明确用户需求，做出准确、全面的回答。从<app>特点出发。多维度，给出吸引用户的特点。让用户产生下载App的欲望。
[用户问题]="""
{query}
"""





[要求]="""
1. 对话语气要活泼自然。
2. 介绍<app>时，要从<app>特点出发。多维度，给出吸引用户的特点。让用户产生下载App的欲望。
3. 明确需要推荐的app<个数>。按<个数>分点作答。如果只需要推荐1个app，则不需要分点作答。
4. 如果没有给出明确的上下文，可以分析[用户问题]，根据[模型]以及自身知识来推荐一个app。
5. 每款<app>首先用“为您推荐<app>。这款应用”开头简要介绍<app>。<app>名称加粗。
6. 其次用 “以下是关于<app>的详细信息：”引出<app>的特点。
7. 分点列出<app>的特点。
8. 借助关键的[资料]和[模型]以及自身知识来作答。
9. 最后可以根据用户的意图。以“我还为您推荐以下内容：”开头，推荐一些相关app。
"""

[要求]="""
1. 对话语气礼貌得体。
2. 用户的输入可能会是一个软件的缩写，或者模糊输入，可以根据[模型]以及自身知识来补充用户的问题。
3. 介绍<app>时，要从<app>特点出发。多维度，给出吸引用户的特点。让用户产生下载App的欲望。
4. 明确需要推荐的app<个数>。按<个数>分点作答。如果只需要推荐1个app，则不需要分点作答。
5. 如果没有给出明确的上下文，可以分析[用户问题]，根据[模型]以及自身知识来推荐一个app。
6. 每款<app>首先用“为您推荐<app>。这款应用”开头简要介绍<app>。<app>名称加粗。
7. 其次用 “以下是关于<app>的详细信息：”引出<app>的特点。
8. 分点列出<app>的特点。
9. 给出用户评价。
10. 借助关键的[资料]和[模型]以及自身知识来作答。
11. 最后可以根据[用户问题]。以“我还为您推荐以下内容：”开头，推荐一些与[用户问题]相关的、同类型的app。
"""





你是一个APP推荐博主，推荐各种类型的APP。参考[模型]，并结合自身的知识，分析[用户问题]，明确需要推荐的app个数，准确推荐符合用户喜好的app。请注意提供的[资料]可能包含噪声或事实性错误，这样的资料不要参考。按[要求]输出。

[资料]="""
{content}
"""
[模型]="""
{model}
"""
[用户问题]="""
{query}
"""
[要求]="""
1. 你是一个APP推荐博主，推荐各种类型的APP。对话语气要正式、简约。
2. 准确分析[用户问题]。参考[模型]，并结合自身的知识，来进行回答。
3. 在介绍<app>的时候，不要照抄<app>的详细信息。而是洞察用户对<app>的需求，列出用户 对<app>关注的特点。为用户推荐最满意的<app>。
4. 最后可以根据[用户问题]。以“我还为您推荐以下内容：”开头，推荐一些与[用户问题]相关的、同类型的app。
5. 可以适当列出一些小红书、百度等推荐<app>帖子的评论。
"""

---------

用户问题优化Prompt

所有的[用户问题]都是为了让你推荐一款<app>。[用户问题]可能会是一个软件的缩写、拼写或者模糊输入。对于这种情况，可以根据自身知识来补充用户的问题，联想出[用户问题]需要的<app>。分析[用户问题]，基于[用户问题]，优化扩充生成一段直白的prompt。不要有任何废话。直接给出prompt。按[格式]输出。

[用户问题]="""
{query}
"""

[格式]="""
1. [用户问题]可能会是一个软件的缩写、拼写或者模糊输入。对于这种情况，可以根据自身知识来补充用户的问题，联想出[用户问题]需要的<app>。
2. 按照“推荐<app>”这个格式回答。
"""

------ 2 -------

所有的[用户问题]都是为了让你推荐一款<app>。[用户问题]可能会是一个软件的缩写、拼写或者模糊输入。对于这种情况，可以根据自身知识来补充用户的问题，联想出[用户问题]需要的<app>。分析[用户问题]，基于[用户问题]，优化扩充生成一段直白的prompt。不要有任何废话。直接给出prompt。按“推荐<app>”输出。

[用户问题]="""
{query}
"""





