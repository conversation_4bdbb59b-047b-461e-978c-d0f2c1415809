{"problem_cnt": "30", "field": "即时通讯"}
{"problem_cnt": "30", "field": "陌生人社交"}
{"problem_cnt": "30", "field": "兴趣社区"}
{"problem_cnt": "30", "field": "职场社交"}
{"problem_cnt": "30", "field": "短视频社交"}
{"problem_cnt": "30", "field": "婚恋交友"}
{"problem_cnt": "30", "field": "匿名社交"}
{"problem_cnt": "30", "field": "校园社交"}
{"problem_cnt": "30", "field": "语音社交"}
{"problem_cnt": "30", "field": "游戏社交"}
{"problem_cnt": "30", "field": "同城社交"}
{"problem_cnt": "30", "field": "家族/熟人社交"}
{"problem_cnt": "20", "field": "虚拟社交（元宇宙概念）"}
{"problem_cnt": "30", "field": "知识问答社区"}
{"problem_cnt": "20", "field": "LBS社交（基于位置服务）"}
{"problem_cnt": "10", "field": "AI社交"}
{"problem_cnt": "10", "field": "轻量化社交"}
{"problem_cnt": "30", "field": "综合电商平台"}
{"problem_cnt": "30", "field": "垂直电商（特定品类）"}
{"problem_cnt": "30", "field": "跨境电商"}
{"problem_cnt": "30", "field": "二手交易"}
{"problem_cnt": "30", "field": "本地生活服务"}
{"problem_cnt": "30", "field": "社区团购"}
{"problem_cnt": "30", "field": "直播电商"}
{"problem_cnt": "30", "field": "会员制电商"}
{"problem_cnt": "30", "field": "生鲜即时配送"}
{"problem_cnt": "30", "field": "同城即时零售"}
{"problem_cnt": "30", "field": "优惠比价与导购"}
{"problem_cnt": "30", "field": "本地家政服务"}
{"problem_cnt": "30", "field": "旅游与票务"}
{"problem_cnt": "30", "field": "奢侈品电商"}
{"problem_cnt": "30", "field": "本地生活聚合平台"}
{"problem_cnt": "10", "field": "即时零售"}
{"problem_cnt": "10", "field": "AI电商"}
{"problem_cnt": "10", "field": "绿色电商"}
{"problem_cnt": "40", "field": "短视频平台"}
{"problem_cnt": "40", "field": "长视频平台（影视综艺）"}
{"problem_cnt": "30", "field": "音乐与音频"}
{"problem_cnt": "30", "field": "在线阅读"}
{"problem_cnt": "30", "field": "直播娱乐"}
{"problem_cnt": "60", "field": "游戏娱乐"}
{"problem_cnt": "30", "field": "播客与声音社区"}
{"problem_cnt": "30", "field": "动漫与二次元"}
{"problem_cnt": "30", "field": "追星与粉丝社区"}
{"problem_cnt": "30", "field": "搞笑与泛娱乐内容"}
{"problem_cnt": "30", "field": "虚拟娱乐与元宇宙"}
{"problem_cnt": "30", "field": "知识付费与轻学习"}
{"problem_cnt": "30", "field": "本地娱乐（演出/展览）"}
{"problem_cnt": "30", "field": "同人创作与IP衍生"}
{"problem_cnt": "30", "field": "UGC内容工具"}
{"problem_cnt": "20", "field": "AI生成内容"}
{"problem_cnt": "10", "field": "互动剧/游戏化内容"}
{"problem_cnt": "10", "field": "虚拟现实娱乐"}
{"problem_cnt": "50", "field": "效率工具（待办/日历/提醒）"}
{"problem_cnt": "30", "field": "文件管理与云存储"}
{"problem_cnt": "40", "field": "笔记与知识管理"}
{"problem_cnt": "30", "field": "时间管理（专注/番茄钟）"}
{"problem_cnt": "30", "field": "翻译与语言工具"}
{"problem_cnt": "30", "field": "扫描与OCR工具"}
{"problem_cnt": "30", "field": "密码与隐私管理"}
{"problem_cnt": "30", "field": "剪贴板增强工具"}
{"problem_cnt": "30", "field": "自动化工具（RPA）"}
{"problem_cnt": "30", "field": "设备清理与优化"}
{"problem_cnt": "30", "field": "多设备协同工具"}
{"problem_cnt": "30", "field": "学习与生产力工具"}
{"problem_cnt": "30", "field": "财务与记账工具"}
{"problem_cnt": "30", "field": "思维导图与图表工具"}
{"problem_cnt": "30", "field": "系统工具（高级功能）"}
{"problem_cnt": "10", "field": "AI效率工具"}
{"problem_cnt": "10", "field": "跨平台协作"}
{"problem_cnt": "10", "field": "隐私增强"}
{"problem_cnt": "30", "field": "移动支付与电子钱包"}
{"problem_cnt": "30", "field": "网上银行与直销银行"}
{"problem_cnt": "30", "field": "投资理财（基金/股票/黄金）"}
{"problem_cnt": "30", "field": "股票交易与行情工具"}
{"problem_cnt": "30", "field": "保险科技（投保/理赔/比价）"}
{"problem_cnt": "30", "field": "借贷与消费金融"}
{"problem_cnt": "30", "field": "信用卡管理"}
{"problem_cnt": "30", "field": "记账与个人资产管理"}
{"problem_cnt": "30", "field": "跨境支付与汇款"}
{"problem_cnt": "30", "field": "金融资讯与教育"}
{"problem_cnt": "30", "field": "数字资产与区块链"}
{"problem_cnt": "30", "field": "金融安全与反欺诈"}
{"problem_cnt": "10", "field": "数字人民币普及"}
{"problem_cnt": "10", "field": "AI投顾工具"}
{"problem_cnt": "10", "field": "绿色金融"}
{"problem_cnt": "30", "field": "导航工具类"}
{"problem_cnt": "30", "field": "公共交通查询类"}
{"problem_cnt": "30", "field": "网约车服务类"}
{"problem_cnt": "30", "field": "共享出行类"}
{"problem_cnt": "30", "field": "长途出行规划类"}
{"problem_cnt": "30", "field": "实时路况工具类"}
{"problem_cnt": "30", "field": "电动车专项导航"}
{"problem_cnt": "30", "field": "停车辅助工具"}
{"problem_cnt": "30", "field": "AR实景导航"}
{"problem_cnt": "30", "field": "无障碍出行类"}
{"problem_cnt": "30", "field": "违章查询处理类"}
{"problem_cnt": "30", "field": "旅行轨迹记录类"}
{"problem_cnt": "30", "field": "境外出行辅助类"}
{"problem_cnt": "30", "field": "物流导航专用类"}
{"problem_cnt": "30", "field": "应急避险导航类"}
{"problem_cnt": "30", "field": "K12学科辅导（含政策合规类）"}
{"problem_cnt": "30", "field": "语言学习"}
{"problem_cnt": "30", "field": "高等教育与考研"}
{"problem_cnt": "30", "field": "职业教育与技能提升"}
{"problem_cnt": "30", "field": "兴趣启蒙与素质教育"}
{"problem_cnt": "30", "field": "考试与考证工具"}
{"problem_cnt": "30", "field": "教育信息化工具"}
{"problem_cnt": "30", "field": "早教与亲子陪伴"}
{"problem_cnt": "30", "field": "编程与科技教育"}
{"problem_cnt": "30", "field": "教育资讯与社区"}
{"problem_cnt": "30", "field": "教育硬件配套App"}
{"problem_cnt": "30", "field": "教育公益与普惠平台"}
{"problem_cnt": "10", "field": "AI个性化学习"}
{"problem_cnt": "10", "field": "虚拟现实教育"}
{"problem_cnt": "30", "field": "在线问诊类"}
{"problem_cnt": "30", "field": "健康管理类"}
{"problem_cnt": "30", "field": "运动健身类"}
{"problem_cnt": "30", "field": "心理健康类"}
{"problem_cnt": "30", "field": "医药服务类"}
{"problem_cnt": "10", "field": "医疗辅助工具"}
{"problem_cnt": "10", "field": "康复护理类"}
{"problem_cnt": "10", "field": "中医养生类"}
{"problem_cnt": "10", "field": "紧急救援类"}
{"problem_cnt": "10", "field": "医疗信息服务"}
{"problem_cnt": "30", "field": "相机与专业拍摄工具"}
{"problem_cnt": "30", "field": "全能修图与滤镜"}
{"problem_cnt": "20", "field": "创意设计与平面创作"}
{"problem_cnt": "20", "field": "艺术效果与AI生成"}
{"problem_cnt": "30", "field": "视频编辑与动态特效"}
{"problem_cnt": "30", "field": "专业后期与调色"}
{"problem_cnt": "30", "field": "特效相机与AR玩法"}
{"problem_cnt": "30", "field": "拼图与排版工具"}
{"problem_cnt": "30", "field": "素材资源与版权设计"}
{"problem_cnt": "30", "field": "摄影社区与作品分享"}
{"problem_cnt": "30", "field": "3D建模与视觉创意"}
{"problem_cnt": "30", "field": "摄影辅助工具"}
{"problem_cnt": "10", "field": "AI摄影辅助"}
{"problem_cnt": "10", "field": "国风创意工具"}
{"problem_cnt": "30", "field": "手游分发平台类"}
{"problem_cnt": "30", "field": "游戏直播类"}
{"problem_cnt": "20", "field": "电竞赛事服务类"}
{"problem_cnt": "30", "field": "游戏辅助工具类"}
{"problem_cnt": "30", "field": "游戏社交类"}
{"problem_cnt": "30", "field": "云游戏/模拟器类"}
{"problem_cnt": "30", "field": "游戏攻略社区类"}
{"problem_cnt": "30", "field": "游戏账号交易类"}
{"problem_cnt": "30", "field": "游戏创作工具类"}
{"problem_cnt": "10", "field": "云电竞基础设施类"}
{"problem_cnt": "10", "field": "防沉迷管理类"}
{"problem_cnt": "20", "field": "游戏福利聚合类"}
{"problem_cnt": "10", "field": "游戏引擎开发类"}
{"problem_cnt": "10", "field": "电竞硬件联动类"}
{"problem_cnt": "10", "field": "游戏同人文化类"}
{"problem_cnt": "30", "field": "热门游戏"}
{"problem_cnt": "30", "field": "新游"}
{"problem_cnt": "30", "field": "角色扮演游戏"}
{"problem_cnt": "30", "field": "策略类游戏"}
{"problem_cnt": "30", "field": "MOBA（多人在线战术竞技）游戏"}
{"problem_cnt": "30", "field": "休闲益智类游戏"}
{"problem_cnt": "30", "field": "模拟经营类游戏"}
{"problem_cnt": "30", "field": "射击类游戏"}
{"problem_cnt": "30", "field": "卡牌类游戏"}
{"problem_cnt": "30", "field": "奇幻/魔幻题材游戏"}
{"problem_cnt": "30", "field": "科幻/未来题材游戏"}
{"problem_cnt": "30", "field": "历史/国风题材游戏"}
{"problem_cnt": "30", "field": "二次元/动漫IP游戏"}
{"problem_cnt": "30", "field": "现实/生活游戏"}
{"problem_cnt": "10", "field": "免费+内购（F2P）游戏"}
{"problem_cnt": "10", "field": "买断制游戏"}
{"problem_cnt": "10", "field": "广告变现游戏"}
{"problem_cnt": "10", "field": "订阅制游戏"}
{"problem_cnt": "30", "field": "硬核玩家游戏"}
{"problem_cnt": "30", "field": "女性向游戏"}
{"problem_cnt": "30", "field": "儿童向游戏"}
{"problem_cnt": "30", "field": "单机游戏"}
{"problem_cnt": "10", "field": "弱联网游戏"}
{"problem_cnt": "10", "field": "轻策略游戏"}
{"problem_cnt": "30", "field": "双人游戏"}
{"problem_cnt": "30", "field": "多人游戏"}
{"problem_cnt": "20", "field": "综合政务服务平台"}
{"problem_cnt": "20", "field": "公安与司法服务"}
{"problem_cnt": "20", "field": "税务服务"}
{"problem_cnt": "20", "field": "社保与公积金"}
{"problem_cnt": "20", "field": "公共缴费与生活服务"}
{"problem_cnt": "30", "field": "交通出行服务"}
{"problem_cnt": "20", "field": "医疗健康服务"}
{"problem_cnt": "10", "field": "企业服务与营商"}
{"problem_cnt": "20", "field": "公共安全与应急"}
{"problem_cnt": "15", "field": "法律与权益保障"}
{"problem_cnt": "30", "field": "数据开放与智慧城市"}
{"problem_cnt": "20", "field": "乡村振兴与基层服务"}
{"problem_cnt": "10", "field": "数字身份普及"}
{"problem_cnt": "5", "field": "全屋智能控制中枢类"}
{"problem_cnt": "5", "field": "可穿戴设备管理类"}
{"problem_cnt": "5", "field": "AR/VR设备"}
{"problem_cnt": "5", "field": "智能家电控制类"}
{"problem_cnt": "5", "field": "IoT开发平台类"}
{"problem_cnt": "5", "field": "协议转换工具类"}
{"problem_cnt": "5", "field": "能源管理系统类"}
{"problem_cnt": "5", "field": "安防监控生态类"}
{"problem_cnt": "5", "field": "工业物联网类"}
{"problem_cnt": "5", "field": "车家互联类"}
{"problem_cnt": "5", "field": "元设备管理类"}
{"problem_cnt": "5", "field": "配件生态管理类"}
{"problem_cnt": "5", "field": "二手硬件流通类"}
{"problem_cnt": "5", "field": "固件升级服务类"}
{"problem_cnt": "5", "field": "声学设备生态类"}
{"problem_cnt": "5", "field": "隐私安全管理类"}
{"problem_cnt": "10", "field": "医疗与健康工具"}
{"problem_cnt": "10", "field": "农业与农村服务工具"}
{"problem_cnt": "10", "field": "建筑与工程工具"}
{"problem_cnt": "10", "field": "法律与合规工具"}
{"problem_cnt": "10", "field": "物流与供应链工具"}
{"problem_cnt": "10", "field": "餐饮与零售工具"}
{"problem_cnt": "10", "field": "制造业与工业工具"}
{"problem_cnt": "10", "field": "设计师与创意工具"}
{"problem_cnt": "10", "field": "音乐与演出工具"}
{"problem_cnt": "10", "field": "科研与学术工具"}
{"problem_cnt": "10", "field": "宗教与文化工具"}
{"problem_cnt": "10", "field": "环保与能源工具"}
{"problem_cnt": "5", "field": "AI垂直化渗透"}
{"problem_cnt": "5", "field": "绿色科技工具"}
{"problem_cnt": "30", "field": "传统文化传播类"}
{"problem_cnt": "30", "field": "知识普惠类"}
{"problem_cnt": "10", "field": "公益参与类"}
{"problem_cnt": "15", "field": "文化遗产保护类"}
{"problem_cnt": "10", "field": "公共文化服务类"}
{"problem_cnt": "10", "field": "艺术普及类"}
{"problem_cnt": "10", "field": "公益科技类"}
{"problem_cnt": "10", "field": "文化IP开发类"}
{"problem_cnt": "10", "field": "特殊群体关怀类"}
{"problem_cnt": "10", "field": "公益监督类"}
{"problem_cnt": "10", "field": "国际文化交流类"}
{"problem_cnt": "10", "field": "公益创新实验类"}
{"problem_cnt": "15", "field": "生成式AI工具"}
{"problem_cnt": "10", "field": "AR/VR沉浸体验"}
{"problem_cnt": "10", "field": "区块链与Web3工具"}
{"problem_cnt": "5", "field": "脑机接口与生物传感"}
{"problem_cnt": "5", "field": "量子计算科普与模拟"}
{"problem_cnt": "10", "field": "无人驾驶与车联网"}
{"problem_cnt": "10", "field": "机器人交互与开发"}
{"problem_cnt": "10", "field": "生物科技与基因探索"}
{"problem_cnt": "10", "field": "智能穿戴与仿生设备"}
{"problem_cnt": "10", "field": "空间计算与全息交互"}
{"problem_cnt": "10", "field": "开源硬件与极客开发"}
{"problem_cnt": "10", "field": "科技资讯与未来社区"}
{"problem_cnt": "10", "field": "综合新闻客户端"}
{"problem_cnt": "20", "field": "垂直领域资讯"}
{"problem_cnt": "10", "field": "新闻聚合与个性化推荐"}
{"problem_cnt": "10", "field": "地方新闻与民生资讯"}
{"problem_cnt": "15", "field": "短视频新闻平台"}
{"problem_cnt": "20", "field": "政府与官方发布平台"}
{"problem_cnt": "20", "field": "深度报道与杂志类"}
{"problem_cnt": "20", "field": "兴趣社区化资讯"}
{"problem_cnt": "20", "field": "有声新闻与播客"}
{"problem_cnt": "10", "field": "辟谣与事实核查平台"}
{"problem_cnt": "5", "field": "AI新闻助手"}
{"problem_cnt": "5", "field": "新闻游戏化"}
{"problem_cnt": "10", "field": "购车决策支持类"}
{"problem_cnt": "20", "field": "汽车交易平台类"}
{"problem_cnt": "10", "field": "用车服务类"}
{"problem_cnt": "20", "field": "养护维修类"}
{"problem_cnt": "10", "field": "车联网服务类"}
{"problem_cnt": "10", "field": "新能源专项服务类"}
{"problem_cnt": "10", "field": "汽车金融类"}
{"problem_cnt": "20", "field": "汽车文化类"}
{"problem_cnt": "15", "field": "汽车安全救援类"}
{"problem_cnt": "10", "field": "汽车配件类"}
{"problem_cnt": "20", "field": "商用车服务类"}
{"problem_cnt": "10", "field": "汽车政策服务类"}
{"problem_cnt": "30", "field": "婚恋社交类"}
{"problem_cnt": "10", "field": "婚礼筹备类"}
{"problem_cnt": "15", "field": "家庭健康管理类"}
{"problem_cnt": "15", "field": "育儿支持类"}
{"problem_cnt": "15", "field": "家政服务类"}
{"problem_cnt": "15", "field": "家庭财务管理类"}
{"problem_cnt": "5", "field": "法律公证类"}
{"problem_cnt": "10", "field": "情感咨询类"}
{"problem_cnt": "10", "field": "家庭教育类"}
{"problem_cnt": "20", "field": "生活采购类"}
{"problem_cnt": "5", "field": "家庭安全类"}
{"problem_cnt": "5", "field": "婚姻登记服务类"}
{"problem_cnt": "10", "field": "家庭活动策划类"}
{"problem_cnt": "10", "field": "婚庆用品类"}
{"problem_cnt": "5", "field": "特殊家庭形态类"}
{"problem_cnt": "8", "field": "综合天气预报"}
{"problem_cnt": "8", "field": "灾害预警与应急服务"}
{"problem_cnt": "8", "field": "空气质量监测"}
{"problem_cnt": "8", "field": "农业气象与专业服务"}
{"problem_cnt": "10", "field": "天文与星空观测工具"}
{"problem_cnt": "10", "field": "生活场景化天气服务"}
{"problem_cnt": "15", "field": "桌面插件与美化工具"}
{"problem_cnt": "8", "field": "全球旅行天气规划"}
{"problem_cnt": "8", "field": "气象数据可视化工具"}
{"problem_cnt": "8", "field": "智能硬件联动工具"}
{"problem_cnt": "3", "field": "碳中和工具"}
{"problem_cnt": "30", "field": "通勤"}
{"problem_cnt": "15", "field": "户外探险"}
{"problem_cnt": "15", "field": "农业生产"}
{"problem_cnt": "15", "field": "摄影爱好者"}
{"problem_cnt": "15", "field": "新婚夫妇"}
{"problem_cnt": "10", "field": "多孩家庭"}
{"problem_cnt": "6", "field": "银发婚恋"}
{"problem_cnt": "10", "field": "新能源车主"}
{"problem_cnt": "10", "field": "玩车青年"}
{"problem_cnt": "30", "field": "实用主义"}
{"problem_cnt": "8", "field": "普通读者"}
{"problem_cnt": "15", "field": "行业人士"}
{"problem_cnt": "30", "field": "学生群体"}
{"problem_cnt": "30", "field": "中老年用户"}
{"problem_cnt": "15", "field": "文化传承"}
{"problem_cnt": "10", "field": "银龄关怀"}
{"problem_cnt": "5", "field": "青年公益"}
{"problem_cnt": "5", "field": "餐饮店主"}
{"problem_cnt": "10", "field": "极客开发者"}
{"problem_cnt": "10", "field": "电竞观众"}
{"problem_cnt": "10", "field": "游戏开发者"}
{"problem_cnt": "30", "field": "日常记录"}
{"problem_cnt": "30", "field": "专业摄影"}
{"problem_cnt": "30", "field": "内容创作"}
{"problem_cnt": "10", "field": "艺术实验"}
{"problem_cnt": "10", "field": "慢性病患者"}
{"problem_cnt": "15", "field": "综合健康"}
{"problem_cnt": "30", "field": "学生党"}
{"problem_cnt": "30", "field": "职场人"}
{"problem_cnt": "10", "field": "家长群体"}
{"problem_cnt": "10", "field": "教师用户"}
{"problem_cnt": "30", "field": "小白用户"}
{"problem_cnt": "30", "field": "碎片化时间"}
{"problem_cnt": "30", "field": "沉浸式体验"}
{"problem_cnt": "10", "field": "兴趣圈层"}
{"problem_cnt": "15", "field": "价格敏感型用户"}
{"problem_cnt": "15", "field": "品质导向型用户"}
{"problem_cnt": "50", "field": "小众宝藏"}
{"problem_cnt": "50", "field": "热门爆款"}
{"problem_cnt": "50", "field": "新上架"}
{"problem_cnt": "15", "field": "垂直领域神级App"}
{"problem_cnt": "5", "field": "数字身份认证类"}
{"problem_cnt": "8", "field": "虚拟形象创建类"}
{"problem_cnt": "6", "field": "合规数字藏品类"}
{"problem_cnt": "10", "field": "数字资产管理类"}
{"problem_cnt": "10", "field": "元宇宙入口类"}
{"problem_cnt": "10", "field": "数字版权保护类"}
{"problem_cnt": "10", "field": "虚拟空间搭建类"}
{"problem_cnt": "10", "field": "合规数字资产交易类"}
{"problem_cnt": "10", "field": "数字遗产管理类"}
{"problem_cnt": "10", "field": "区块链工具类"}
{"problem_cnt": "15", "field": "创作者"}
{"problem_cnt": "10", "field": "宗教文化传播与经典学习"}
{"problem_cnt": "10", "field": "冥想与心灵修养工具"}
{"problem_cnt": "10", "field": "宗教场所服务与活动"}
{"problem_cnt": "10", "field": "哲学与国学教育"}
{"problem_cnt": "10", "field": "宗教音乐与艺术"}
{"problem_cnt": "10", "field": "民间信仰与习俗工具"}
{"problem_cnt": "5", "field": "数字宗教文化体验"}
{"problem_cnt": "10", "field": "文化研究者"}
{"problem_cnt": "10", "field": "冥想爱好者"}
{"problem_cnt": "10", "field": "民俗兴趣者"}
{"problem_cnt": "15", "field": "汉服与国风文化"}
{"problem_cnt": "15", "field": "谷圈（周边收藏）"}
{"problem_cnt": "15", "field": "密室逃脱与剧本杀"}
{"problem_cnt": "10", "field": "娃圈（BJD/棉花娃娃）"}
{"problem_cnt": "10", "field": "同人创作与二创"}
{"problem_cnt": "10", "field": "赛博朋克与科技亚文化"}
{"problem_cnt": "10", "field": "语C（语言cosplay）"}
{"problem_cnt": "15", "field": "玄学与神秘学研究"}
{"problem_cnt": "10", "field": "军迷与历史重演"}
{"problem_cnt": "10", "field": "地下音乐场景"}
{"problem_cnt": "15", "field": "神秘学实践工具"}
{"problem_cnt": "10", "field": "老式科技复兴"}
{"problem_cnt": "10", "field": "特殊动物交流"}
{"problem_cnt": "10", "field": "手工与微缩艺术"}
{"problem_cnt": "10", "field": "模拟飞行与铁道迷"}
{"problem_cnt": "5", "field": "国潮亚文化"}
{"problem_cnt": "5", "field": "虚拟偶像共创"}
{"problem_cnt": "5", "field": "元宇宙兴趣社区"}
{"problem_cnt": "6", "field": "文化考据党"}
{"problem_cnt": "5", "field": "硬核技术派"}
{"problem_cnt": "4", "field": "次元穿梭者"}
{"problem_cnt": "10", "field": "复古爱好者"}
{"problem_cnt": "10", "field": "企业级协同办公套件"}
{"problem_cnt": "10", "field": "国际支付与汇率工具"}
{"problem_cnt": "10", "field": "全球通讯与社交适配"}
{"problem_cnt": "8", "field": "跨境电商与物流跟踪"}
{"problem_cnt": "10", "field": "国际导航与出行服务"}
{"problem_cnt": "25", "field": "多语言学习与翻译"}
{"problem_cnt": "15", "field": "全球流媒体内容适配"}
{"problem_cnt": "8", "field": "跨文化社区平台"}
{"problem_cnt": "10", "field": "全球新闻与资讯聚合"}
{"problem_cnt": "10", "field": "国际教育服务工具"}
{"problem_cnt": "6", "field": "跨境医疗与药品服务"}
{"problem_cnt": "20", "field": "国际旅行"}
{"problem_cnt": "30", "field": "旅行必备"}
{"problem_cnt": "30", "field": "装机必备"}
{"problem_cnt": "30", "field": "女生必备"}
{"problem_cnt": "30", "field": "男生必备"}
{"problem_cnt": "30", "field": "留学生必备"}
{"problem_cnt": "5", "field": "无障碍辅助工具"}
{"problem_cnt": "5", "field": "隐私与反追踪工具"}
{"problem_cnt": "5", "field": "极客与开发者工具"}
{"problem_cnt": "7", "field": "极端环境作业工具"}
{"problem_cnt": "8", "field": "特殊文化场景工具"}
{"problem_cnt": "10", "field": "特殊人群服务工具"}
{"problem_cnt": "4", "field": "虚拟身份管理工具"}
{"problem_cnt": "10", "field": "特殊交互方式工具"}
{"problem_cnt": "30", "field": "残障人士"}
{"problem_cnt": "30", "field": "隐私敏感者"}
{"problem_cnt": "50", "field": "治愈系"}
{"problem_cnt": "50", "field": "极简系"}
{"problem_cnt": "30", "field": "脑洞系"}
{"problem_cnt": "30", "field": "赛博朋克系"}
{"problem_cnt": "40", "field": "复古系"}
{"problem_cnt": "30", "field": "二次元系"}
{"problem_cnt": "30", "field": "佛系"}
{"problem_cnt": "50", "field": "学霸系"}
{"problem_cnt": "40", "field": "仙女系"}
{"problem_cnt": "30", "field": "仙男系"}
{"problem_cnt": "40", "field": "摸鱼系"}
{"problem_cnt": "20", "field": "极客系"}
{"problem_cnt": "30", "field": "国风系"}
{"problem_cnt": "40", "field": "暗黑系"}
{"problem_cnt": "50", "field": "文艺系"}
{"problem_cnt": "30", "field": "社恐友好"}
{"problem_cnt": "30", "field": "反焦虑系"}
{"problem_cnt": "40", "field": "土味系"}
{"problem_cnt": "30", "field": "未来系"}
{"problem_cnt": "40", "field": "经典"}
{"problem_cnt": "40", "field": "高分好评"}
{"problem_cnt": "30", "field": "低分逆袭"}
{"problem_cnt": "30", "field": "小众高分工具"}