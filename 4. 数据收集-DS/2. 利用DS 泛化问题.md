

现在需要根据 下面的模版，来构造一些关于app推荐的问题。这几个变量可以填什么？{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}。举出100个例子。这100个例子要求多样不重复，其中{{problem_cnt}}和{{word_cnt}} 固定，都是30个，按照格式：{"problem_cnt":30, "field":"健康类", "field_example":"睡眠监测", "word_cnt":30, "seed_problem":"求推荐一款精准监测睡眠周期且支持数据导出的健康类App？最好有深度分析报告，界面简洁无广告"} 输出



假设你是一个app推荐达人，推荐各种类型的中国安卓平台app，下面让我们一步一步地按照[步骤]进行，并且按照[格式]给出回答。
[步骤]=
仿照[示例]，生成{{problem_cnt}}个类似的[问题]，要求: 1、生成{{field}}领域的数据，例如{{field_exmple}}。2、注重生成的多样性，尽可能生成有区别的数据。3、随机给生成的[问题]添加一些要求。4、不少于{{word_cnt}}字。
[示例]={{seed_problem}}。

{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}




你是一个问题生成专家，能够推荐各种类型的中国安卓平台app。现在可以根据特定领域，给出一些app类别，如小众宝藏，热门，上新


### 泛化问题构造 

- 模版

假设你是一个问题生成专家，能够围绕“推荐各种类型的中国安卓平台app”生成问题。下面让我们一步一步地按照[步骤]进行，给出回答。
[步骤]=
仿照[示例]，必须生成{{problem_cnt}}个类似的[问题]，要求: 1、基于{{field}}领域的数据，生成多个子领域的数据，例如{{field_exmple}}。2、注重生成的多样性，模仿人类的自然问答语言，生成多种形式的提问方式。3、随机给生成的[问题]添加一些要求。4、问题要符合实际，得考虑是否存在这样的app。5、不少于{{word_cnt}}字。
[示例]={{seed_problem}}。

{{problem_cnt}}, {{field}}, {{field_exmple}}, {{word_cnt}}, {{seed_problem}}


建议根据用户画像（年龄/地域/需求）进行精准推荐，同时关注新兴领域如AIGC应用、适老化改造App、无障碍应用等特殊类别的发展。


#### 模版

##### 模版1

假设你是一个问题生成专家，能够围绕“推荐各种类型的中国安卓平台app”生成问题。下面让我们一步一步地按照[步骤]进行，给出回答。
[步骤]=
必须生成{{problem_cnt}}个[问题]，要求: 1、基于{{field}}领域生成问题。2、注重生成的多样性，模仿人类的自然问答语言，生成多种形式的提问方式。3、随机给生成的[问题]添加一些要求，如根据用户画像（年龄/地域/需求等）进行构造问题。4、问题要符合实际，得考虑是否存在这样的app。5、不少于{{word_cnt}}字。

{{problem_cnt}}, {{field}}, {{word_cnt}}

###### ds模版

你是一个问题生成专家，能够围绕“推荐各种类型的中国安卓平台app”生成问题。下面让我们一步一步地按照[要求]进行，给出回答。
[要求]=
1. 必须生成{{problem_cnt}}个[问题]
2. 基于{{field}}领域生成问题。
3. 注重生成的多样性，模仿人类的自然问答语言，生成多种形式的提问方式。
4. 随机给生成的[问题]添加一些要求，如根据用户画像（年龄/地域/需求/职业等）进行构造问题。
5. 问题要符合实际，得考虑是否存在这样的app。
6. 回答不要有其他废话和符号，每个[问题]输出格式为：{"subject": "[问题]"}


###### 抓取模版

你是一个问题生成专家，能够围绕“推荐各种类型的APP”生成问题。下面让我们一步一步地按照[要求]进行，给出回答。
[要求]=
1. 必须生成{{problem_cnt}}个[问题]
2. 基于{{field}}领域生成问题。
3. 注重生成的多样性，模仿人类的自然问答语言，生成多种形式的提问方式。
4. 随机给生成的[问题]添加一些要求，如根据用户画像（年龄/地域/需求/职业等）进行构造问题。
5. 问题要符合实际，得考虑是否存在这样的app。
6. 回答不要有其他废话和符号，每个[问题]输出格式为：{"subject": "[问题]"}
 
### 问答对构造


#### 模版

你是一个APP推荐博主，推荐各种类型的APP。结合自身的知识，根据[用户问题]，准确推荐符合用户喜好的APP。根据[要求]，按照[格式]输出。

[用户问题]="""
{{subject}}
"""

[要求]="""
1. 你是一个APP推荐博主，推荐各种类型的APP。对话语气要正式、简约，不要有任何废话。
2. 准确分析[用户问题]。结合自身的知识来进行回答。
3. 请确保遵从[用户问题]，不要随意发挥。
4. 建议根据用户画像（年龄/地域/需求等等）进行精准推荐。
5. 优先推荐中国安卓平台的APP。不要自己创造APP。
6. 准确分析[资料]，如果[资料]不符合[用户问题]的需求，请不要参考。
7. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
8. 严谨按照markdown格式输出。
"""
[格式]="""
1. 如果推荐出多个APP，需要总结推荐几个APP，并且排序标序。如果只推荐1个APP，不需要标序。
2. 介绍。标题为APP名称。用一句话来简要介绍APP。需标注APP综合评价和下载量。
3. 介绍特点。标题为特点。在介绍APP特点的时候，不要照抄APP的详细信息。要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。让用户产生下载APP的欲望。
4. 介绍适合人群以及适合的原因。
5. 缺点或者需要注意的地方。
6. 介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。
7. 接着，如果有推荐搭配，可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
8. 回答的最后，根据[用户问题]。以“我还为您推荐以下内容：”开头，推荐符合[用户问题]的APP。
9. 具体参考[例子]
"""

[例子]="""
为你推荐以下两款app：
### 1. 元气骑士
**元气骑士**是一款支持本地联机的Roguelike射击手游。综合评价4.9分。下载安装量100w次。

**亮点**：

1. **同屏联机**：无需网络，通过热点/WIFI直连即可双人同屏
2. **百种组合**：200+武器与技能随机组合，每局都是新体验
3. **像素萌系**：搞怪角色设计搭配复古街机操作手感

**适合人群**：

1. 喜欢探索、解迷的玩家。
2. 对美术风格有偏好者。

**缺点**：
1. 

**用户评论**：

- 用户1：“和弟弟用一部手机就能联机，暑假杀时间神器”
- 用户2：“随机性超强，双人配合通关超有成就感”
- 用户3：“买断制无内购，良心国产独立游戏”

### 2. 欢乐斗地主
**欢乐斗地主**是一款腾讯出品的经典双人卡牌对战游戏。综合评分4.5分。下载安装量5000w。

**亮点**：

**即时匹配**：支持与亲友或陌生人快速组局，无延迟实时对战。
**方言配音**：提供川普、东北话等特色方言语音包，增加趣味性。
**赛事系统**：每日开放全国联赛，可与好友组队冲击排名。

**适合人群**：

1. 喜欢休闲益智的玩家。

**需注意**：
1. 勿上瘾

**用户评论**：

- 用户1：“和老婆每晚必开黑斗地主，方言语音互相嘲讽特别欢乐”
- 用户2：“3分钟一局的节奏超适合等人时玩，飞机模式也能玩”
- 用户3：“全国联赛模式让我们同事间有了新的团建方式”

**搭配推荐**：
- 情侣可选择《喵斯快跑》+《同桌大作战》培养默契
- 好友聚会首选《人类一败涂地》+《元气骑士》制造欢乐
- 家庭场景适合《欢乐斗地主》+《疯狂兔子：奇遇派对》三代同堂娱乐。

我还为您推荐以下内容：

**喵斯快跑**：音游+跑酷的双人同屏竞技，支持情侣手柄操作
**同桌大作战**：30+款迷你游戏合集，专为双人面对面PK设计
**人类一败涂地**：物理引擎沙雕游戏，支持双人合作闯关
"""



#### 模版1

你是一个APP推荐博主，推荐各种类型的APP。结合自身的知识，根据[用户问题]，准确推荐符合用户喜好的APP。根据[要求]，按照[格式]输出。

[用户问题]="""
{{subject}}
"""

[要求]="""
1. 你是一个APP推荐博主，推荐各种类型的APP。对话语气要正式、简约，不要有任何废话。
2. 准确分析[用户问题]。结合自身的知识来进行回答。
3. 请确保遵从[用户问题]，不要随意发挥。
4. 建议根据用户画像（年龄/地域/需求等等）进行精准推荐。
5. 优先推荐中国安卓平台的APP。不要自己创造APP。
6. 准确分析[资料]，如果[资料]不符合[用户问题]的需求，请不要参考。
7. APP的名称加粗显示。如果APP有中文名和英文名，优先展示中文名。
8. 严谨按照markdown格式输出。
"""
[格式]="""
1. 如果推荐出多个APP，需要总结推荐几个APP，并且排序标序。如果只推荐1个APP，不需要标序。
2. 介绍。标题为APP名称。用一句话来简要介绍APP。
3. 介绍特点。标题为特点。在介绍APP特点的时候，不要照抄APP的详细信息。要洞察用户对APP的需求，列出用户对APP关注的APP特点（至多3个，分点作答）。让用户产生下载APP的欲望。
4. 介绍适合人群以及适合的原因。
5. 缺点或者需要注意的地方。
6. 介绍APP需要列出一些真实的网络平台、社交平台等平台的用户评论，需要真实、客观、锐评，让用户更好的对APP进行判断。
7. 接着，如果有推荐搭配，可将2-5款APP组合推荐，根据不同场景需求构建差异化搭配方案。典型搭配类型包括不仅限于：1）功能互补型组合，通过APP间的协同效应提升使用效能；2）场景化组合（如工作/学习/出行场景套件），满足特定情境的复合需求；3）技术尝鲜型组合，集成具备创新交互或前沿技术的应用；4）用户画像驱动推荐，基于使用习惯匹配个性化应用矩阵。每个推荐组合需标注核心协同逻辑，例如："效率工具包（A+B）——A应用提供任务管理框架，B应用实现自动化执行，形成PDCA完整闭环"。
8. 回答的最后，根据[用户问题]。以“我还为您推荐以下内容：”开头，推荐符合[用户问题]的APP。
9. 具体参考[例子]
"""

[例子]="""
为你推荐以下两款app：
### 1. 元气骑士
**元气骑士**是一款支持本地联机的Roguelike射击手游。

**亮点**：

1. **同屏联机**：无需网络，通过热点/WIFI直连即可双人同屏
2. **百种组合**：200+武器与技能随机组合，每局都是新体验
3. **像素萌系**：搞怪角色设计搭配复古街机操作手感

**适合人群**：

1. 喜欢探索、解迷的玩家。
2. 对美术风格有偏好者。

**缺点**：
1. 

**用户评论**：

- 用户1：“和弟弟用一部手机就能联机，暑假杀时间神器”
- 用户2：“随机性超强，双人配合通关超有成就感”
- 用户3：“买断制无内购，良心国产独立游戏”

### 2. 欢乐斗地主
**欢乐斗地主**是一款腾讯出品的经典双人卡牌对战游戏。

**亮点**：

**即时匹配**：支持与亲友或陌生人快速组局，无延迟实时对战。
**方言配音**：提供川普、东北话等特色方言语音包，增加趣味性。
**赛事系统**：每日开放全国联赛，可与好友组队冲击排名。

**适合人群**：

1. 喜欢休闲益智的玩家。

**需注意**：
1. 勿上瘾

**用户评论**：

- 用户1：“和老婆每晚必开黑斗地主，方言语音互相嘲讽特别欢乐”
- 用户2：“3分钟一局的节奏超适合等人时玩，飞机模式也能玩”
- 用户3：“全国联赛模式让我们同事间有了新的团建方式”

**搭配推荐**：
- 情侣可选择《喵斯快跑》+《同桌大作战》培养默契
- 好友聚会首选《人类一败涂地》+《元气骑士》制造欢乐
- 家庭场景适合《欢乐斗地主》+《疯狂兔子：奇遇派对》三代同堂娱乐。

我还为您推荐以下内容：

**喵斯快跑**：音游+跑酷的双人同屏竞技，支持情侣手柄操作
**同桌大作战**：30+款迷你游戏合集，专为双人面对面PK设计
**人类一败涂地**：物理引擎沙雕游戏，支持双人合作闯关
"""






{"problem_cnt":30, "field":"健康类", "field_example":"心率监测", "word_cnt":30, "seed_problem":"需要一款能实时监测静息心率且自动生成趋势图的健康App？要求支持异常提醒功能，操作界面直观易用"}
{"problem_cnt":30, "field":"教育类", "field_example":"幼儿英语启蒙", "word_cnt":30, "seed_problem":"求推荐适合3-6岁儿童的互动式英语学习App？需要包含发音纠正功能，最好有原创动画IP角色陪伴"}
{"problem_cnt":30, "field":"工具类", "field_example":"WiFi测速", "word_cnt":30, "seed_problem":"找一款能检测多设备WiFi信号强度并生成热力图的工具App？要求支持5G频段分析，可导出PDF报告"}
{"problem_cnt":30, "field":"旅行类", "field_example":"地铁导航", "word_cnt":30, "seed_problem":"需要覆盖全球200+城市的地铁线路离线导航App？要求实时显示末班车时间，支持无障碍路线规划"}
{"problem_cnt":30, "field":"摄影类", "field_example":"星空拍摄", "word_cnt":30, "seed_problem":"求推荐专业级星空摄影辅助App？需要具备银河定位功能和长曝光计时器，适配安卓RAW格式处理"}
{"problem_cnt":30, "field":"金融类", "field_example":"股票预警", "word_cnt":30, "seed_problem":"找一款支持自定义涨跌幅/成交量异动推送的炒股App？要求数据实时性强，界面无财经直播等冗余信息"}
{"problem_cnt":30, "field":"运动类", "field_example":"羽毛球训练", "word_cnt":30, "seed_problem":"需要记录羽毛球挥拍速度、击球点分布的专业运动App？最好有AI动作纠正建议，支持多设备同步"}
{"problem_cnt":30, "field":"阅读类", "field_example":"古籍阅读", "word_cnt":30, "seed_problem":"求推荐包含四库全书等古籍原文的阅读App？要求支持竖排繁体显示，具备生僻字注音功能"}
{"problem_cnt":30, "field":"音乐类", "field_example":"分轨录音", "word_cnt":30, "seed_problem":"找一款支持多音轨录音和简易混音的音乐制作App？需要自带降噪功能，适配外接USB声卡"}
{"problem_cnt":30, "field":"美食类", "field_example":"热量计算", "word_cnt":30, "seed_problem":"需要能扫码识别食品营养成分并计算餐食总热量的App？要求数据库包含各地特色菜肴，支持自定义代谢率"}
{"problem_cnt":30, "field":"社交类", "field_example":"兴趣匹配", "word_cnt":30, "seed_problem":"求推荐基于电影/书籍偏好进行好友匹配的社交App？要求匿名机制完善，拒绝位置信息获取"}
{"problem_cnt":30, "field":"医疗类", "field_example":"用药提醒", "word_cnt":30, "seed_problem":"找一款可识别药品相互作用并提供用药时间提醒的医疗App？需要OCR识别处方功能，符合HIPAA认证"}
{"problem_cnt":30, "field":"园艺类", "field_example":"多肉养护", "word_cnt":30, "seed_problem":"需要针对多肉植物的个性化养护指南App？要求具备浇水/光照周期提醒，支持病害AI识别"}
{"problem_cnt":30, "field":"天文类", "field_example":"星图识别", "word_cnt":30, "seed_problem":"求推荐实时AR星座定位App？需要包含深空天体观测指南，适配天文望远镜蓝牙控制"}
{"problem_cnt":30, "field":"艺术类", "field_example":"水彩教程", "word_cnt":30, "seed_problem":"找一款从调色到技法完整的水彩绘画教学App？要求支持画笔压感调节，提供名师课程订阅"}
{"problem_cnt":30, "field":"宠物类", "field_example":"狗狗训练", "word_cnt":30, "seed_problem":"需要包含50+种犬类专项训练视频的宠物App？要求记录训练进度，具备吠叫识别纠正功能"}
{"problem_cnt":30, "field":"效率类", "field_example":"番茄工作法", "word_cnt":30, "seed_problem":"求推荐高度自定义的番茄钟效率App？需要统计各项目时间分配，支持智能打断恢复机制"}
{"problem_cnt":30, "field":"购物类", "field_example":"历史比价", "word_cnt":30, "seed_problem":"找一款监控商品180天价格波动并提供降价提醒的购物App？要求支持全网比价，有伪促销识别功能"}
{"problem_cnt":30, "field":"AR类", "field_example":"家具预览", "word_cnt":30, "seed_problem":"需要AR测量房间尺寸并预览家具摆放效果的App？要求厘米级精度，支持导出3D模型文件"}
{"problem_cnt":30, "field":"语言类", "field_example":"方言学习", "word_cnt":30, "seed_problem":"求收录各地方言发音对比的语言学习App？要求具备跟读评分系统，支持生成方言分布地图"}
{"problem_cnt":30, "field":"游戏类", "field_example":"复古模拟器", "word_cnt":30, "seed_problem":"找一款整合PSP/GBA等多平台游戏模拟器的App？要求支持外接手柄，具备云存档和金手指功能"}
{"problem_cnt":30, "field":"新闻类", "field_example":"信源追溯", "word_cnt":30, "seed_problem":"需要标注新闻原始信源并提供可信度评分的阅读App？要求事实核查功能完善，界面零广告推送"}
{"problem_cnt":30, "field":"冥想类", "field_example":"脑波监测", "word_cnt":30, "seed_problem":"求推荐配合穿戴设备的冥想辅助App？需要实时显示脑波状态，定制专属声光引导方案"}
{"problem_cnt":30, "field":"母婴类", "field_example":"哺乳记录", "word_cnt":30, "seed_problem":"找一款详细记录婴儿哺乳/排便/睡眠周期的App？要求生成成长曲线图，支持多设备共享数据"}
{"problem_cnt":30, "field":"维修类", "field_example":"电路检测", "word_cnt":30, "seed_problem":"需要包含家电维修电路图查询的辅助App？要求AR指导拆机步骤，提供常见故障排除方案"}
{"problem_cnt":30, "field":"军事类", "field_example":"装备识别", "word_cnt":30, "seed_problem":"求推荐军用飞机/舰船型号识别的百科App？需要3D模型旋转查看，更新最新军事动态资讯"}
{"problem_cnt":30, "field":"手工类", "field_example":"皮革制作", "word_cnt":30, "seed_problem":"找一款指导皮具打版/缝制/保养的手工App？要求含工具选购指南，支持AR查看针脚细节演示"}
{"problem_cnt":30, "field":"生产力类", "field_example":"思维导图", "word_cnt":30, "seed_problem":"求支持无限画布且能导出Markdown格式的思维导图App？需协同编辑功能，适配Surface Pen压感笔"}
{"problem_cnt":30, "field":"安全类", "field_example":"隐私检测", "word_cnt":30, "seed_problem":"需要扫描App后台权限滥用情况的工具？要求识别隐藏摄像头调用，提供风险等级评估报告"}
{"problem_cnt":30, "field":"导航类", "field_example":"徒步路线", "word_cnt":30, "seed_problem":"找一款标注野外水源/露营地点的离线导航App？需地形剖面图功能，支持GPX轨迹导入导出"}
{"problem_cnt":30, "field":"美容类", "field_example":"护肤计划", "word_cnt":30, "seed_problem":"求根据肤质测试定制早晚护肤流程的App？需要成分冲突检查功能，支持扫描化妆品条形码分析"}
{"problem_cnt":30, "field":"汽车类", "field_example":"油耗统计", "word_cnt":30, "seed_problem":"需要记录每次加油数据并计算真实油耗的App？要求生成驾驶习惯报告，适配OBDII蓝牙设备"}
{"problem_cnt":30, "field":"法律类", "field_example":"合同审查", "word_cnt":30, "seed_problem":"找一款识别劳动合同陷阱条款的法律助手App？需实时更新劳动法案例库，支持风险条款高亮"}
{"problem_cnt":30, "field":"天气类", "field_example":"气象雷达", "word_cnt":30, "seed_problem":"求专业级气象雷达动态图查看App？要求显示未来2小时降水概率，支持风暴路径预测"}
{"problem_cnt":30, "field":"社交类", "field_example":"语聊房", "word_cnt":30, "seed_problem":"需要创建加密语音聊天室的社交App？要求变声器功能多样，支持实时歌词弹幕互动"}
{"problem_cnt":30, "field":"教育类", "field_example":"编程教学", "word_cnt":30, "seed_problem":"找一款通过游戏化关卡学习Python的App？需内置代码检查器，适配Chromebook低配置设备"}
{"problem_cnt":30, "field":"医疗类", "field_example":"症状自查", "word_cnt":30, "seed_problem":"求推荐基于循证医学的症状分析App？要求区分成人/儿童病例，提供急诊分级建议"}
{"problem_cnt":30, "field":"艺术类", "field_example":"书法练习", "word_cnt":30, "seed_problem":"需要颜体/柳体等多种字帖的书法临摹App？要求笔锋轨迹评分，适配数位板压感检测"}
{"problem_cnt":30, "field":"游戏类", "field_example":"MOD管理", "word_cnt":30, "seed_problem":"找一款整合《我的世界》MOD资源并自动配置的App？要求冲突检测功能，支持光影包一键安装"}
{"problem_cnt":30, "field":"效率类", "field_example":"邮件整理", "word_cnt":30, "seed_problem":"求自动分类工作邮件并提取待办事项的App？需支持Outlook/Gmail多账户，生成周报模板"}
{"problem_cnt":30, "field":"音乐类", "field_example":"乐理训练", "word_cnt":30, "seed_problem":"需要包含视唱练耳/和弦听辨的音乐教育App？要求渐进难度设置，适配MIDI键盘实时反馈"}
{"problem_cnt":30, "field":"摄影类", "field_example":"延时摄影", "word_cnt":30, "seed_problem":"找一款计算天体运动轨迹的延时摄影规划App？需月相/银河位置模拟，支持蓝牙快门控制"}
{"problem_cnt":30, "field":"旅行类", "field_example":"签证指南", "word_cnt":30, "seed_problem":"求更新各国签证材料清单的旅行App？要求预约提醒功能，整合领保紧急联系方式"}
{"problem_cnt":30, "field":"金融类", "field_example":"REITs投资", "word_cnt":30, "seed_problem":"需要分析房地产信托基金历史分红的理财App？要求股息再投资模拟器，关联宏观经济指标"}
{"problem_cnt":30, "field":"工具类", "field_example":"单位换算", "word_cnt":30, "seed_problem":"找一款支持古代计量单位转换的工具App？如斛/石等粮食单位，含历史汇率数据查询"}
{"problem_cnt":30, "field":"天文类", "field_example":"日食预测", "word_cnt":30, "seed_problem":"求推荐精准计算本地日食时间的App？需AR模拟观测效果，提醒最佳拍摄参数设置"}
{"problem_cnt":30, "field":"宠物类", "field_example":"猫语翻译", "word_cnt":30, "seed_problem":"需要分析猫咪不同叫声含义的App？要求关联肢体语言数据库，生成情绪解读报告"}
{"problem_cnt":30, "field":"阅读类", "field_example":"论文检索", "word_cnt":30, "seed_problem":"找一款整合SCI-Hub资源的学术文献阅读App？需引文关系图谱功能，支持BibTeX导出"}
{"problem_cnt":30, "field":"智能家居", "field_example":"设备联动", "word_cnt":30, "seed_problem":"求推荐能统一控制米家/涂鸦/Alexa不同品牌设备的App？要求创建跨平台自动化场景，支持耗材剩余量预警"}
{"problem_cnt":30, "field":"健身类", "field_example":"HIIT训练", "word_cnt":30, "seed_problem":"需要包含40种以上HIIT动作库的健身App？要求自动编排训练计划，具备心率区间达标提醒功能"}
{"problem_cnt":30, "field":"办公类", "field_example":"PDF编辑", "word_cnt":30, "seed_problem":"找一款实现PDF批量加密/水印/压缩的办公App？需OCR识别扫描件，支持电子签名认证"}
{"problem_cnt":30, "field":"母婴类", "field_example":"辅食记录", "word_cnt":30, "seed_problem":"求记录宝宝每餐辅食成分及过敏反应的App？要求营养分析可视化，提供月龄适配食谱推荐"}
{"problem_cnt":30, "field":"VR类", "field_example":"虚拟旅游", "word_cnt":30, "seed_problem":"需要4K画质的全球博物馆VR导览App？要求支持多人语音互动，可保存全景截图分享"}
{"problem_cnt":30, "field":"植物类", "field_example":"兰花养殖", "word_cnt":30, "seed_problem":"找一款专业指导兰花分株/换盆/催花的App？需环境温湿度监测，提供稀有品种图鉴"}
{"problem_cnt":30, "field":"电竞类", "field_example":"战绩分析", "word_cnt":30, "seed_problem":"求分析《英雄联盟》对局数据并生成改进建议的App？需装备路线优化，支持OB视角复盘"}
{"problem_cnt":30, "field":"应急类", "field_example":"急救指导", "word_cnt":30, "seed_problem":"需要含心肺复苏AR指引的急救教学App？要求离线可用，适配不同体型患者操作标准"}
{"problem_cnt":30, "field":"收藏类", "field_example":"钱币鉴定", "word_cnt":30, "seed_problem":"找一款鉴别各国钱币版别及市场估值的App？需瑕疵评级系统，关联拍卖行实时成交数据"}
{"problem_cnt":30, "field":"助眠类", "field_example":"白噪音", "word_cnt":30, "seed_problem":"求混合自然环境声与人声ASMR的助眠App？要求3D音效追踪，智能跳过雷同片段"}
{"problem_cnt":30, "field":"穿搭类", "field_example":"风格测试", "word_cnt":30, "seed_problem":"需要AI分析身形比例推荐穿搭方案的App？要求虚拟试衣间功能，关联平价品牌购买链接"}
{"problem_cnt":30, "field":"区块链类", "field_example":"NFT管理", "word_cnt":30, "seed_problem":"找一款多链NFT资产展示与交易监控的App？需Gas费预测功能，支持冷钱包离线签名"}
{"problem_cnt":30, "field":"研学类", "field_example":"化学实验", "word_cnt":30, "seed_problem":"求模拟危险化学实验的AR教学App？要求物质属性实时查询，实验步骤安全评估"}
{"problem_cnt":30, "field":"棋牌类", "field_example":"围棋AI", "word_cnt":30, "seed_problem":"需要分析围棋棋局并提供AI胜率评估的App？要求支持导入SGF格式，显示变化图推荐"}
{"problem_cnt":30, "field":"编程类", "field_example":"API调试", "word_cnt":30, "seed_problem":"找一款可视化编排API调用流程的App？需自动生成Swagger文档，支持OAuth2.0授权"}
{"problem_cnt":30, "field":"钓鱼类", "field_example":"潮汐预报", "word_cnt":30, "seed_problem":"求整合潮汐/水温/月相的钓鱼助手App？需记录钓点鱼种分布，支持声呐设备连接"}
{"problem_cnt":30, "field":"租赁类", "field_example":"设备出租", "word_cnt":30, "seed_problem":"需要管理摄影器材租赁状态的App？要求生成电子合同，具备逾期GPS定位功能"}
{"problem_cnt":30, "field":"玄学类", "field_example":"占星排盘", "word_cnt":30, "seed_problem":"找一款支持中西方多种占卜术的App？需生成命盘解读报告，保存历史预测准确率统计"}
{"problem_cnt":30, "field":"直播类", "field_example":"推流设置", "word_cnt":30, "seed_problem":"求多平台同时直播且带虚拟背景的App？需实时弹幕互动，支持直播数据对比分析"}
{"problem_cnt":30, "field":"模型类", "field_example":"3D打印", "word_cnt":30, "seed_problem":"需要检测3D模型打印可行性的App？要求显示支撑结构建议，计算耗材用量与成本"}
{"problem_cnt":30, "field":"密码类", "field_example":"双重验证", "word_cnt":30, "seed_problem":"找一款替代Google Authenticator的2FA工具？需加密云备份，支持Watch版同步"}
{"problem_cnt":30, "field":"招聘类", "field_example":"简历优化", "word_cnt":30, "seed_problem":"求AI分析招聘JD并匹配简历关键词的App？需竞争力评分，隐藏公司信息防追踪"}
{"problem_cnt":30, "field":"减压类", "field_example":"指尖陀螺", "word_cnt":30, "seed_problem":"需要模拟多种解压玩具触感的App？要求3D Touch振动反馈，统计使用时长报告"}
{"problem_cnt":30, "field":"航空类", "field_example":"航班追踪", "word_cnt":30, "seed_problem":"找一款显示航空管制实时动态的飞行地图App？需机型数据库查询，播报机场流控信息"}
{"problem_cnt":30, "field":"考古类", "field_example":"文物识别", "word_cnt":30, "seed_problem":"求识别青铜器纹饰年代特征的App？需三维碎片拼接功能，关联考古遗址数据库"}
{"problem_cnt":30, "field":"酒类", "field_example":"红酒品鉴", "word_cnt":30, "seed_problem":"需要分析红酒产区/年份/评分数据库的App？要求食物搭配建议，扫码识别酒庄信息"}
{"problem_cnt":30, "field":"测评类", "field_example":"手机跑分", "word_cnt":30, "seed_problem":"需要对比不同机型游戏帧率稳定性的测评App？要求展示散热温度曲线，支持自定义测试场景组合"}
{"problem_cnt":30, "field":"极客类", "field_example":"Root工具", "word_cnt":30, "seed_problem":"找一款安全解除安卓Bootloader锁定的工具？需刷机包校验功能，提供变砖紧急恢复方案"}
{"problem_cnt":30, "field":"传统文化", "field_example":"节气养生", "word_cnt":30, "seed_problem":"求根据二十四节气推荐食疗方案的App？需结合用户体质辨识，提供药膳制作视频指导"}
{"problem_cnt":30, "field":"潜水类", "field_example":"气瓶管理", "word_cnt":30, "seed_problem":"需要记录潜水日志并计算氮氧饱和度的App？要求整合潮汐数据，支持蓝牙潜水电脑表连接"}
{"problem_cnt":30, "field":"声音类", "field_example":"环境降噪", "word_cnt":30, "seed_problem":"找一款实时分离人声与背景音的录音App？需支持会议纪要转写，导出独立音轨文件"}
{"problem_cnt":30, "field":"写作类", "field_example":"小说大纲", "word_cnt":30, "seed_problem":"求推荐故事线索可视化的写作辅助App？需角色关系图谱功能，支持多版本剧情对比"}
{"problem_cnt":30, "field":"仓储类", "field_example":"库存盘点", "word_cnt":30, "seed_problem":"需要扫描商品条码管理仓库进销存的App？要求生成补货预警，适配手持扫码枪设备"}
{"problem_cnt":30, "field":"直播类", "field_example":"虚拟主播", "word_cnt":30, "seed_problem":"找一款创建3D虚拟形象进行直播的App？需面部捕捉精度高，支持实时礼物特效互动"}
{"problem_cnt":30, "field":"魔术类", "field_example":"手法教学", "word_cnt":30, "seed_problem":"求包含硬币/扑克进阶技巧的魔术训练App？要求慢动作分解教学，拍摄角度可360度旋转"}
{"problem_cnt":30, "field":"家政类", "field_example":"保洁排班", "word_cnt":30, "seed_problem":"需要统筹多个家庭保洁需求的日程App？要求工时智能分配，支持服务评价体系管理"}
{"problem_cnt":30, "field":"灯光类", "field_example":"智能调色", "word_cnt":30, "seed_problem":"找一款根据音乐节奏同步智能灯光的App？需预设灯光秀场景，支持DMX512协议控制"}
{"problem_cnt":30, "field":"咖啡类", "field_example":"手冲计时", "word_cnt":30, "seed_problem":"求推荐精确控制手冲咖啡参数的App？需记录不同豆种冲煮方案，生成风味雷达图"}
{"problem_cnt":30, "field":"房车类", "field_example":"营地导航", "word_cnt":30, "seed_problem":"需要标注房车充电桩/排污站的导航App？要求显示营地空位实时情况，支持离线地图下载"}
{"problem_cnt":30, "field":"占卜类", "field_example":"塔罗牌阵", "word_cnt":30, "seed_problem":"找一款保存每日占卜记录并分析准确率的App？需牌意深度学习推荐，支持自定义牌阵设计"}
{"problem_cnt":30, "field":"无人机类", "field_example":"航线规划", "word_cnt":30, "seed_problem":"求专业级无人机三维航线规划App？需自动避障模拟，导出KML格式飞行方案"}
{"problem_cnt":30, "field":"剧本杀类", "field_example":"线索管理", "word_cnt":30, "seed_problem":"需要组织线上剧本杀并分配角色线索的App？要求计时器功能，支持语音变装特效"}
{"problem_cnt":30, "field":"制图类", "field_example":"等高线绘制", "word_cnt":30, "seed_problem":"找一款导入GPS数据生成地形图的App？需高程数据标注，支持DWG格式导出"}
{"problem_cnt":30, "field":"反诈类", "field_example":"短信过滤", "word_cnt":30, "seed_problem":"求识别诈骗短信并模拟应对话术的App？需伪装号码标记，自动生成报警材料"}
{"problem_cnt":30, "field":"展览类", "field_example":"AR导览", "word_cnt":30, "seed_problem":"需要美术馆展品AR增强现实讲解的App？要求多语言切换，保存用户偏好作品集"}
{"problem_cnt":30, "field":"投屏类", "field_example":"多屏互动", "word_cnt":30, "seed_problem":"找一款同时投射到4台设备的办公投屏App？需激光笔标记功能，支持无线传输4K画质"}
{"problem_cnt":30, "field":"冷知识类", "field_example":"奇闻百科", "word_cnt":30, "seed_problem":"求每日推送颠覆常识的冷知识App？需文献溯源功能，支持知识卡片社交分享"}
{"problem_cnt":30, "field":"声音类", "field_example":"声纹分析", "word_cnt":30, "seed_problem":"需要检测声音疲劳度的护嗓助手App？要求发声姿势评估，定制声带保养计划"}
{"problem_cnt":30, "field":"灾害类", "field_example":"地震预警", "word_cnt":30, "seed_problem":"找一款接入官方地震预警系统的App？需逃生路线规划，紧急联系人自动拨号功能"}
{"problem_cnt":30, "field":"广告类", "field_example":"素材制作", "word_cnt":30, "seed_problem":"求快速生成信息流广告模板的App？需A/B测试数据跟踪，适配各大媒体平台尺寸"}
{"problem_cnt":30, "field":"极限运动", "field_example":"翼装飞行", "word_cnt":30, "seed_problem":"求记录翼装飞行三维轨迹与风速关联数据的App？需降落点预测功能，支持GoPro视频数据同步分析"}
{"problem_cnt":30, "field":"植物识别", "field_example":"苔藓分类", "word_cnt":30, "seed_problem":"找一款识别300+苔藓品种的植物学App？需显微照片比对功能，标注濒危物种保护等级"}
{"problem_cnt":30, "field":"家庭安全", "field_example":"门窗感应", "word_cnt":30, "seed_problem":"需要监控门窗开合状态并生成安防报告的App？要求伪装布防模式，支持多品牌传感器接入"}
{"problem_cnt":30, "field":"声乐训练", "field_example":"头声开发", "word_cnt":30, "seed_problem":"求分析发声共鸣腔体分布的专业声乐App？需声谱可视化，制定每日练声计划"}
{"problem_cnt":30, "field":"自行车类", "field_example":"变速调试", "word_cnt":30, "seed_problem":"找一款通过听变速噪音诊断传动故障的App？需提供调试教学AR指引，计算齿比推荐"}
{"problem_cnt":30, "field":"天文摄影", "field_example":"赤道仪控制", "word_cnt":30, "seed_problem":"需要控制星野赤道仪自动跟星的摄影App？要求解析FITS文件，标注拍摄天体参数"}
{"problem_cnt":30, "field":"电竞类", "field_example":"外设宏设置", "word_cnt":30, "seed_problem":"求创建鼠标/键盘组合宏命令的App？需宏指令云端共享，支持压枪灵敏度微调"}
{"problem_cnt":30, "field":"家庭预算", "field_example":"水电预测", "word_cnt":30, "seed_problem":"找一款根据历史数据预测水电支出的App？需异常用量警报，生成节能改造建议"}
{"problem_cnt":30, "field":"语言交换", "field_example":"语音日记", "word_cnt":30, "seed_problem":"需要自动纠正外语口语日记发音的App？要求AI语法润色，生成学习进度热力图"}
{"problem_cnt":30, "field":"滑雪类", "field_example":"滑行轨迹", "word_cnt":30, "seed_problem":"求记录滑雪速度/坡度/腾空时长的App？需雪场地图导航，支持与专业运动员数据对比"}