### 如何判断 ai回答 是否正确

- 先从现有的应用库里搜索，看看有没有能够推荐的。

### 数据问题

- 数据 需要 不断更新？
  - 用户点赞 可以保存为数据对



### 需要什么数据
- 新游的数据  -- 外挂
- 热门app的数据 -- 
- 搜索框 的问题
- 评论数据
  - 评论是否真实
  - q：评论多样化
    - 模型根据评论进行提问。
  - a：评论
- 数据构造，目前看deepSeek的回答还不错
  - 根据固定模版生成数据。
  - 如果用户的问题是围绕搜索app的呢？


- 索引不能一成不变，因为软件口碑在不断更新
  - 那索引需要什么数据呢？
    - 运营配的数据
    - 其他的没了 因为训练的话都是旧数据


！！
- 需要的是一个软件库。可以让ds去软件库搜索，再结合网络上的评价来推荐

- 推荐语气自定义：博主、简约
- app皮肤自定义
- 个人空间：
  - 分享
    - app玩法攻略
    - 推荐
    - 避雷


- 索引：适合固定q&a，可以不断更新的 -- 运营配置
  - q：可以利用模型，针对一个value 生成不同的q
  - a：配置的value
  


- “试试这么搜” -- 帮助用户发现app（可以小红书看看 博主都怎么分类推荐的。 或者让大模型模仿小红书博主分类推荐）
  - 用户也不知道自己要搜什么。（搜索框里展示、近期热门应用排行）
    - 小众且热门
    - 治愈系
    - 画风 
      - 可爱系
      - 国风系
    - ip
      - 三海经
      - 宝可梦
    - 功能性
      - 旅行必备
  - 根据不同类别列出软件
    - 实时热门
    - 小众热门
    - 热门工具
    - 不同应用组合拳
- 现在yyb的推荐都不想下载


### 推荐内容
- 推荐的形式，不一定只推荐单一的软件，可以搭配推荐，使用效果更好。
  - 对于金融投资的小白用户：支付宝（综合理财）+鲨鱼记账（消费管控）。 


### 提问方式
- 根据不同领域
- 上新时间
- 热门
