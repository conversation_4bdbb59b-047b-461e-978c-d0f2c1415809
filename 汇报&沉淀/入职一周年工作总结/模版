【模板】年度工作总结
姓名： 
所在团队：XXX组


现在 我需要写一个年度工作总结。按照下面的模版和我的相关经历，帮我生成一个高质量的年度工作总结。不要夸大其词，胡编乱造。

以下是模版。

一、个人经历
1、职业发展经历：简要介绍在腾讯近一年的经历
2、介绍当前工作内容和职责：介绍一下所负责的业务，工作内容及你的角色是什么。（当前工作，版本工具，开发自动化拉取版本需求列表。）
二、重点工作思路、策略（日志定位Agent）
该部分内容是整个陈述中最重要的部分，展示1-2个核心项目：
1、请将重点放在业务价值/产出/技术成长与个人思考方面。陈述的是能力而仅非业绩！
2、请说明个人角色，介绍个人在项目中的价值，切勿将其他人的成果据为己有
3、项目呈现要结构清晰、重点突出，可参考STAR原则
●工作/项目的背景是什么，有什么冲突/问题，需解决什么问题或达成什么目标
●基于项目目标分析采取哪些方式，明确任务和个人角色
●过程中的挑战和难度在哪里，你如何思考的，过程中你做过哪些重要的决策或抉择
●工作/项目的成果如何？
●有哪些方法论/工具的应用或突破，以及工作沉淀和经验总结分享
三、影响力
影响力： KM 发表的链接、分享举证，团队内CR影响力等。（进行过一次AI专题的模型精调分享）

四、专业能力自评
介绍个人在技术/业务领域的最突出的几项专长，以及还有哪些待提升的能力项和相应的提升计划。


以下是我入职一年来的的工作内容：


① 车载需求和内容化专项
丽晨作为毕业生入职后，参与了车载项目的需求开发，回归平台后负责AIGC场景的功能迭代，工作认真负责，能够独立思考并解决需求实现过程中遇到的难题，不管是团队内还是团队外，善于沟通协作；
（1-1）参与车载项目，承担应用列表页、应用详情页、语音搜索交互的开发，过程中开发效率和质量都较好。
（1-2）改造AI实验室生成记录页，增加生成结果提醒功能，带动AI实验室留存提升、分享次数提升。
（1-3）优化AI实验室写真链路的跳转锚定流程，AI实验室的整体功能周留存率从10.22%提升至25.35%；人均保存次数从8.4次提升至11.50次；人均分享次数从2.20次提升至3.09次。


一、内容化场景建设
1. 从0到1搭建新游日历板块，以聚合用户在宝内“找新游”的核心场景。主要包括app信息卡、活动卡、话题卡和操作按钮卡片的开发。新游日历一期以金刚位为入口上线后游戏用户中心页预约率3.26%，较单款游戏预约活动页预约率+36.31%。
2. 完成打榜列表页支持直接评分的开发。支持用户直接在列表页点击进行评分，动画呼出评分组件，并联动话题详情页的评分状态。该功能全量后，在没有影响原详情页打分数据持续上涨的基础上，每日额外带来了评分人数增长15%(均值)、21%（峰值），评分次数增长17%(均值)、46%(峰值）。
二、AIGC需求的开发
1. 改造AI实验室生成记录页，增加生成结果提醒功能。将生成记录页改造为瀑布流布局，并支持单张生成记录的展示，以适配新游滤镜。实现用户进入AI实验室时，如有未查看的生成记录则显示红点提醒。通过游戏化玩法促进新游戏发行，同时带动AI实验室留存提升、分享次数提升。
2. 完成AI实验室生成结果提醒功能的开发。在首页底部tab新增红点和气泡，引导用户找到AI实验室的入口，并在AI实验室入口处给出状态提示标签，从而提高功能留存和参与率。
3. 优化AI实验室写真链路的跳转锚定。支持用户扫码跳转至对应二级卡片风格的详情页，从而可以一键生成对应的分享风格，提升用户的基础体验，促进AI实验室的参与率和保存分享率。
经过上述AIGC相关需求的开发，AI实验室的整体功能周留存率从10.22%提升至25.35%；人均保存次数从8.4次提升至11.50次；人均分享次数从2.20次提升至3.09次。


② 一元购组件
完成活动页一元购组件的开发，通过翻译前端代码逻辑完成。该组件已全量发布。


③ 日志定位Agent
1. 完成具备多项能力的日志定位工具
- 支持通过日志下载链接自动下载解压日志。
- 实现日志内容结构化转JSON，提升AI模型对日志的理解和处理能力。
- 搭建daemon进程日志分析链路，支持被动和主动多场景日志分析。
- 支持日志归一化去重、多日志文件综合分析及正则表达式过滤，提升分析效率和准确度。

2. 完成日志定位agent与aisee平台及企业微信机器人的深度集成
- 实现aisee平台一键日志分析功能，提升日志分析的便捷性和自动化水平。
- 企业微信机器人支持解析Bugly日志链接，支持解析用户输入从而自动拉取iwiki知识库进行日志分析，增强交互体验。
- 搭建企业微信机器人用户反馈评价机制和错误抛出机制，促进工具持续优化、提升用户体验。
- 通过集成，协助研发团队定位问题，提高效率，减少人工排查成本。

3. 构建动态更新的知识库共建体系，支持多场景日志分析
- 预设并完善21个日志分析场景，包括下载、安装、活动页、自动下载、垃圾清理、弹窗等关键业务场景。
- 实现场景Prompt沉淀至iwiki文档，支持实时更新和动态拉取，保障知识库的持续扩充和迭代。

4. 推动日志定位agent广泛应用，提升日志定位效率
- 4月3日~6月16日，日志分析工具在aisee平台累计调用超过500次；5月23日~6月16日，企业微信机器人调用238次，用户好评率持续提升（35次5星好评）。
- 多轮测评覆盖安装日志、下载日志、活动页场景等，平均评分从2.08提升至2.89（满分3分），显著提升分析准确率。
- 持续优化代码结构和交互体验，支持多场景自定义参数配置，提升工具的灵活性和易用性。

④ 版本工具（该需求当前持续开发中）
1. 支持动态配置版本信息与数据拉取
- 实现灰度实验版本信息的动态配置管理，支持自动拉取包括QUA信息、RQD信息、实验时间等关键参数，确保版本信息实时更新且准确可靠。
- 自动捞取并汇总Crash率、ANR率、启动速度等核心性能指标，以及联网用户数、云游拉起成功率、弹窗成功率等行为指标。
- 集成安装下载及广告相关数据的自动采集，覆盖业务关键环节，保障数据的完整性与全面性。

2. 结构化分析报告自动生成与AI辅助分析
- 实现异常数据的自动计算与识别，支持异常告警功能。
- 引入AI辅助异常数据分析模块，辅助定位潜在问题。

3. 实验数据的美观呈现与易用性提升
- 设计并实现数据可视化模块，采用表格形式展示关键指标，突出核心内容，提升报告的直观性和易读性。
- 通过自动化采集与分析流程，显著缩短灰度实验数据分析周期，提升整体工作效率。
- 集成企业微信机器人，提供便捷的访问入口，增强用户操作体验  



以下是我的专业能力自评

① 日志定位Agent

1. 注重代码结构设计与可维护性
   - 在项目开发过程中，注重代码的模块化设计和清晰的架构规划，确保代码具备良好的可维护性和可扩展性。  

2. 第三方平台API的使用与系统集成能力
   - 深度集成日志定位agent与aisee平台及企业微信机器人，实现一键日志分析、自动拉取知识库等功能，体现了对第三方平台API的熟练掌握和高效集成能力。  

3. 团队协作与沟通能力
   - 在项目开发过程中，持续与leader保持沟通，及时发现并纠正问题，确保项目方向和目标的准确性。
   - 搭建并推动日志知识库共建体系，预设并完善多场景日志分析方案，同时积极宣传和讲解知识库共建方法，促进团队成员的理解和参与。  
   - 配合团队调试和使用工具，及时收集反馈并推动持续优化，体现了良好的团队协作精神和有效的沟通能力，保障项目顺利推进和落地。


② 版本工具

1. 较强的调研能力
   - 调研多个平台的API接口，理解不同数据源的结构和调用方式，设计合理的的版本工具方案。
   - 设计数据可视化模块，通过表格等直观形式展示关键指标，提升了灰度实验数据的易读性和用户体验。

2. 系统集成与自动化能力
   - 集成企业微信机器人，提供便捷访问入口，增强了系统的实用性和交互性。

3. 代码架构设计能力
   - 合理划分模块、设计清晰接口，提升了代码的可维护性和扩展性。


③ 自述

我注重代码结构设计与可维护性，采用模块化和清晰的架构规划，确保代码具备良好的扩展性和后续维护便利性。项目中，高效集成了第三方平台API，成功实现了与aisee平台及企业微信机器人的对接，支持一键日志分析和实时拉取知识库能力，体现了较强的系统集成能力。通过搭建日志知识库共建体系，预设多场景日志分析方案，并向团队成员宣讲相关使用方法。同时，配合团队调试和使用工具，收集反馈并推动持续优化，体现了良好的沟通能力和团队合作精神。

在“版本工具”项目中，深入分析多个平台API接口，理解不同数据源的结构和调用方式，设计出合理的版本工具方案。针对灰度实验数据，我设计了数据可视化模块，通过表格等直观形式提升了数据的易读性和用户体验。项目中，同样注重系统集成与自动化，集成企业微信机器人，提供便捷的访问入口，增强了系统的实用性和交互性。

总体来看，我在项目开发中注重团队协作，具备良好的代码设计习惯，能够有效推动项目的顺利实施和持续优化。未来我将继续努力提升专业技能和协作能力，争取在后续工作中取得更好的成果。



技术专长
代码架构设计：注重模块化和清晰接口设计，提升代码的可维护性和扩展性。
系统集成能力：使用第三方平台API，实现日志定位Agent与aisee平台及企业微信机器人的深度集成。
团队协作与沟通：积极与团队成员沟通，推动项目顺利推进和持续优化。
待提升能力及计划
深化AI模型应用能力：计划通过系统学习和实践，提升在AI辅助分析领域的技术深度。
数据可视化设计：进一步提升数据展示的美观性和交互性，增强用户体验。
项目管理能力：加强项目整体规划和风险控制能力，提升跨团队协作效率。


