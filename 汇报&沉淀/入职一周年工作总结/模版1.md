年度工作总结

一、个人经历  
1. 职业发展经历  
作为2023年毕业生，我于去年加入腾讯，先后参与车载项目和内容化专项的需求开发，随后回归平台，负责AIGC场景的功能迭代及版本工具的持续开发。在这一年中，我逐步熟悉了腾讯的业务流程和技术体系，积累了较为扎实的项目开发和系统集成经验。  

2. 当前工作内容和职责  
目前，我主要负责版本工具的设计与开发，致力于实现版本信息的动态配置与数据自动拉取，提升灰度实验数据的分析效率和准确性。同时，我承担日志定位Agent的开发与维护，推动日志分析工具与aisee平台及企业微信机器人的深度集成，提升日志定位的自动化和智能化水平。我的角色涵盖需求分析、方案设计、核心代码开发及团队协作，确保项目按计划高质量交付。  

二、重点工作思路与策略——日志定位Agent项目  
1. 背景与目标  
在日常研发过程中，日志分析是定位问题的关键手段。在aisee平台上，许多用户频繁咨询同一业务相关的问题，如发货失败、安装失败、无法下载等。针对这些高频问题，可以沉淀业务问题分析的知识库，借助大语言模型（LLM）对日志进行智能分析，辅助快速定位问题。项目目标是开发一款支持多场景日志分析的定位工具，提升团队的问题排查效率，降低人工成本。同时，支持实时更新和扩充业务知识库，丰富LLM的分析场景，进一步增强工具的智能化水平。



在日常研发过程中，日志分析是定位问题的重要手段。在aisee平台中，可以看到许多用户会频繁的问某些同一个业务的问题，如为什么发货失败，安装失败，不能下载等。对于这些频繁出现的问题，我们可以沉淀业务分析问题的知识库，让LLM分析日志，辅助问题定位。项目目标是开发一个能够多场景分析的日志定位工具，提升团队的问题排查效率，降低人工成本。此外，可以试试更新扩充业务知识，增加LLM的分析场景。


但传统日志处理效率低、准确率有限，且缺乏统一的知识库支持。



可能认识的人业务历史悠久，逻辑复杂，且经历过多次逻辑迭代与交接，后台与客户端限制过滤逻辑多。在测试过程中出现UI不展示问题时，客户端作为首要一环需要先行进行问题排查，较为耗费精力。

客户端日志一般以Key:value的形式记录信息，日志中的英文名称及缩略写法对于非当前业务的客户端开发同学（测试、产品、后台）来说理解成本很高。这就造成了遇到问题首先需要客户端看日志确定问题边界，再进一步分析排查的低效工作流。

以可能认识的人场景为例，测试中90%的问题可以归结为，开关错误  /  数据错误  /  客户端数据处于有效期内未拉取新数据 这三类，且这些问题都有清晰明确的日志。如果AI大模型可以把难懂的日志转化为具体业务描述，则测试或后台同学遇到问题时，可以首先通过AI机器人界定问题边界，从而提升问题定位及解决效率。



在尝试让大模型协助分析日志的过程中，我们发现下面两个问题

1. 大模型看不懂日志内容，就如同非当前业务同学单看日志文件看不懂。
2. 日志规模很大，过多的内容输入，会让大模型产生幻觉。
   

在尝试让大模型辅助日志分析的过程中，我们发现了以下两个主要问题：

大模型难以理解日志内容，类似于非当前业务同学单独查看日志文件时难以把握关键信息。
日志数据量庞大，过多的输入信息容易导致大模型产生“幻觉”或误判，影响分析准确性。



      1）Prompt越高效（越复杂），大模型效果越好，但对使用者要求也越高，易用性越差
      2）AI大模型上下文限制，导致如果输入内容过多，最终效果大幅度下降（表现为幻觉增加）
      3）Prompt对大模型输出的影响巨大，Prompt调优测试验证耗费大量时间。


3. 任务与个人角色  
作为项目的核心开发者，我负责整体架构设计、关键功能实现及第三方平台集成，推动日志分析链路的搭建和知识库体系的构建。  

1. 挑战与思考  
项目面临日志格式多样、分析场景复杂、集成需求多样等挑战。针对这些问题，我采用模块化设计，确保代码结构清晰且易于扩展；通过日志结构化转JSON，提升AI模型对日志的理解能力；设计多场景Prompt沉淀机制，实现知识库动态更新，保障分析方案的持续有效。集成aisee平台和企业微信机器人时，注重接口稳定性和用户交互体验，搭建反馈评价机制，推动工具迭代优化。  

1. 成果  
日志定位Agent支持自动下载解压日志、结构化解析、多日志综合分析及正则过滤，极大提升了日志处理效率。工具在aisee平台累计调用超500次，企业微信机器人调用238次，用户好评率持续提升。多轮测评显示分析准确率显著提高，平均评分从2.08提升至2.89（满分3分）。知识库共建体系覆盖21个关键业务场景，保障了多场景日志分析的广泛适用性。  

1. 方法论与经验总结  
项目中，我深刻体会到模块化设计和清晰接口的重要性，确保代码具备良好的可维护性和扩展性。通过持续与团队沟通，及时收集反馈，推动工具优化，体现了有效的团队协作和沟通能力。知识库动态更新机制的搭建，为后续日志分析提供了稳定的技术支撑和经验沉淀。  

三、影响力  
今年我在团队内开展过一次关于AI模型精调的专题分享，介绍了日志结构化及智能分析的思路和实践，获得同事积极反馈。此外，日志定位Agent项目的代码评审（CR）中，我多次提出优化建议，促进代码质量提升。通过知识库共建和工具推广，增强了团队整体的日志分析能力和问题定位效率。  

四、专业能力自评  
1. 技术专长  
- 代码架构设计：注重模块化和清晰接口设计，提升代码的可维护性和扩展性。  
- 系统集成能力：熟练使用第三方平台API，实现日志定位Agent与aisee平台及企业微信机器人的深度集成。  
- 自动化与智能化：推动日志分析自动化，结合AI辅助分析提升问题定位准确率。  
- 团队协作与沟通：积极与团队成员和领导沟通，推动项目顺利推进和持续优化。  

2. 待提升能力及计划  
- 深化AI模型应用能力：计划通过系统学习和实践，提升在AI辅助分析领域的技术深度。  
- 数据可视化设计：进一步提升数据展示的美观性和交互性，增强用户体验。  
- 项目管理能力：加强项目整体规划和风险控制能力，提升跨团队协作效率。  

总结  
过去一年，我在腾讯的工作经历让我积累了丰富的项目开发和系统集成经验，尤其是在日志定位Agent项目中，锻炼了技术设计和团队协作能力。未来，我将持续提升专业技能，深化业务理解，争取为团队和公司创造更大价值。