# 背    景     

计费业务交付越来越快、越来越复杂，老旧的人工编写用例效率难以支撑
在腾讯庞大的数字生态中， 米大师（Midas）如同隐形的血脉网络，承载着公司游戏点券道具的充值购买、视频号电商、小游戏小程序支付、腾讯云计费以及广告计费结算等公司所有核心业务的资金流转命脉 。从《王者荣耀》皮肤的秒级到账，到视频号创作者的分账结算，再到云广告的CPM精准计费——每一次交易的背后，都是米大师对复杂业务规则、资金安全与敏捷迭代的极致平衡。考虑到：

1.  计费系统历经多年演进，沉淀大量复杂逻辑与特殊场景（如金额精度截断、小币种汇率波动），但历史漏测经验未被系统化复用，如何将测试经验沉淀辅助测试同学的设计；

2.  计费版本迭代提速，测试同学需掌握全业务流程，新人学习曲线陡峭，如何加速测试同学产出用例的效率；

以上，伴随业务的迅猛扩张与持续进，米大师正面临测试场景设计和自动化用例产出的精准性挑战与效率瓶颈。此刻，AI驱动的智能测试能力，成为破局关键：通过AI理解复杂业务规则、自动生成高覆盖率用例，实现测试质效的飞跃式提升。 

# 现状痛点及问题


高频迭代下如何提效

月均500+ 迭代版本汹涌而至，测试深陷压力：

▪️ 新旧兼顾：既要敏捷验证新逻辑，又要死守历史功能防线

▪️ 分身乏术：现网故障随时截流测试资源

▪️ 时间塌方：人力在版本洪流中逐渐透支

历史包袱中的覆盖盲区

计费系统经年积累的复杂特殊逻辑（如资金精度截断、小币种流水校验），在用例设计时面临双重困境：

▪️ 隐性知识难挖掘：特殊逻辑散落文档/故障报告，未转化为可复用资产

▪️ 防御经验未传承：同类漏测反复发生

脚本编写的「组装式地狱」

单场景脚本需穿越 10+步骤关卡



新人面临的绝望循环

▪️ 协议迷失：不知「扣费金额」字段定义藏身哪份文档？

▪️ 检查项漏网：到账流水查了，但外部系统请求忘校验？

▪️ 复用无门：相似功能无法快速移植

▪️ 协议繁杂：脚本编写难以快速定位对应协议参数

# 解决方案

## 结合AI新思路
现阶段，计费业务版本发布管理基本在智研平台中进行：

✅测试堂用例库管理（存储历史十万多条用例资产）

✅测试堂自动任务管理（存量执行记录）

✅计费 DevOps 版本发布流程管理

✨了解到智研测试堂测试设计及AI能力现已通过测试设计与协同Oteam内部开源， 为业务提供了更灵活对接方式（感兴趣可点击了解： https://iwiki.woa.com/p/4013299840 ）

Oteam组件提供从人工设计到智能辅助的测试用例编写流程，可通过可视化脑图进行节点，标记如「场景」、「模块」、「测试点」、「用例」等结构化拆解能力，加持 AI辅助生成能力（需求生成模块、场景/模块生成测试点、测试点生成用例、用例异化），以此来快速生成测试用例。





计费业务通过对接Oteam组件，在生成时通过 api 对接专属计费智能 Agent，替代通用 AI 模型完成业务用例的更加精准生成，减免测试同学反复陷入在大量自动化用例代码编写过程中的重复实践消耗。整体流程思路如下图所示：



AI生成用例设计的整体思路逻辑如图所示：



1.  输入：从TAPD需求出发，通过需求澄清与iWiki连接解析 获取结构化需求；

2.  生成引擎：

     智能切割 ：AI生成测试模块（如支付/音频控制） → 过滤重复场景；

     原子化拆解：将模块转为测试场景 → 生成原子测试点 → 过滤无效测试点；

3.  知识库支撑：依据知识库内容召回相似用例， 知识图谱校验业务规则冲突，历史用例库 补全边缘场景；（后续即将接入）

4.  输出 ：组合测试点生成完整用例

## 提效实践路径
1）版本提测流程引入测试用例设计
经团队共识，我们将在版本发布流程中新增『需求测试设计』关键环节，确保所有需求上线前必经测试用例设计与自动化脚本配套，保障交付质量。

早前由于用例资产管理方式未统一，用例文件格式不一，线上线下均有维护，现基于智研-计费 DevOps - 版本管理模块，打通在提测版本后与测试设计工具的关联，方便测试同学线上以统一的数字化管理方式，将需求和场景用例设计融入版本发布流程中来。

可见，版本下发时，可选择具体的版本，进入详情后一键启动设计用例，即可通过测试设计工具完成场景用例设计



当然对于一个版本下的多需求，也在此路径上可以闭环。管理发版需求时默认关联本次提测版本的所有需求，集中在一个测试设计中进行全版本用例的编写



2）AI助力功能用例设计
在从需求到功能用例设计产出的方式上，我们实现了从原来依赖测试同学经验编写到引入AI自动生成助手协助。为了生成的准确率更高，特别细化了两种读取需求内容的方式，目前支持填写需求摘要或者iwiki读取方式

（1）需求摘要模式

适合场景：测试人员自己归纳需求改动，归纳细化后的需求放在摘要中；

用例场景设计会优先根据摘要内容进行设计，选择需要的用例同步到脑图；





（2）读取iwiki模式

适合场景：开发同学将改动点写在iwiki中，tapd中贴入iwiki链接，测试设计读取iwiki内容作为补充辅助信息，进行测试设计





涉及多种类型测试场景应用：

支持功能测试用例生成，以下为示例



支持接口测试用例生成，通过判断上一节点的关键字‘接口用例’，会调用不同的生成助手，以下为示例：



3）AI助力生成自动化用例
再上一步AI生成功能用例之后，结果经测试人员校准后，可直接借助AI自动化用例助手转化为自动化用例的场景描述。

开发同学只需通过插件获取标准化用例模板，即可一键生成可执行的自动化测试代码，实现从版本提测到用例落地的全流程自动化闭环。

如图所示，在IDE编辑器中，直接获取指定项目的制定测试设计，再获取测试设计下用例详情，通过AI助手自动生成自动化用例后归档 Git 仓库中。



# 取得成果



GoodCase
以下为实践探索中，生成效果较好的实例

优质的需求内容建议包含：

1.  需求涉及改动说明清晰，包括影响的业务范围，逻辑改动点，配置改动点等（tapd直接说明或者提供iwiki链接都可以）

2.  协议说明详细，输入包括（参数名，是否必填，长度，范围等），输出包括（参数名，类型，是否必填等），错误码等

case1：根据需求生成测试点（包括功能测试，配置测试，协议测试），生成用例采纳率：24/25

【需求描述内容】



【实际生成效果】



case2：根据接口协议文档生成接口用例（覆盖接口参数校验，逻辑，后端异常返回等），补充了后端系统返回不同状态的场景覆盖，生成用例采纳率 23/23

【需求内容-协议文档】



【实际生成效果】



## 落地进展
功能上线后，目前落地进展情况：

1.  落地试点覆盖计费17个项目，项目覆盖占比达 50%；扩大应用中

2.  基于版本需求，生成测试用例1500+个，用例生成准确率达 80%以上

3.  生成自动化用例脚本2000+个，自动化建设效率提升 50%以上

# 后续规划


AI 生成文本用例和自动化用例的探索仍旧道阻且长，目前已经有一定的提效成果，计费测试团队将会不断优化和调整，寻找更多的机会与可能进一步发展：

1.  通过梳理计费不同项目的知识库，提升用例场景设计的准确性和完整性

2.  在前端测试设计和自动化上和测试设计与协同Oteam成员进行交流和探索，希望在web场景下能够将需求，测试设计和前端自动化用例进行工程化打通

3.  根据不同的项目的特性进行有针对性的设计


# 感谢 I'm deeply grateful

在项目推进过程中，得到了许多协作团队的鼎力支持与合作。

再次，特别感谢 congzeng 在业务实践流程梳理、计费用例生成 agent 研发等方面的全力投入；感谢 Oteam团队 fulinzhang、allenche、feekiayan等同学在技术对接方案上的配合与支持。

希望后续继续在 AI 智能用例生成的道路上大家继续协同互助，一起乘风破浪！