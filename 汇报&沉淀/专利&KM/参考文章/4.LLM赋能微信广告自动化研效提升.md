# 一、背景
随着广告系统的升级、广告位持续扩展、广告形态以及迭代模式调整，微信广告自动化系统也在持续不断迭代、优化和完善。

当前微信广告自动化执行交付的流程如下图：




                         图1： 微信广告自动化执行交付流程

由图可见，广告自动化执行流程一般可以抽象为三个步骤： 查找广告，形态分类，广告元素识别与校验；测试结束后会生成测试报告并且需要人工确认badcase情况。

在该流程实施中，存在以下痛点：:

● 痛点1：广告元素分类与定位受广告形态迭代影响，自动化用例维护成本高，且无法复用新形态
       由于广告UI多变且迭代快，自动化传统元素定位方式受控件树、UI展示等因素影响，导致用例维护难；且难以直接复用于新形态，用例迭代成本高。

● 痛点2：广告查找稳定性受到多因素影响，缺少广告形态识别分类能力
       准确识别广告形态分类（如朋友圈三图、视频号信息流等）对保障广告正确展示和用户体验至关重要，当前自动化测试依赖DOM分析和Yolo AI模型存在局限，前者难以识别形态分类和布局问题，后者训练成本高且迭代不便。

● 痛点3：自动化测试报告跟进门槛高，延长交付周期
       自动化测试报错类型繁多，定位问题门槛高；目前测试报告跟进主要依赖外包，复杂难题还需导师协助，拉长自动化落地、交付的周期。



# 二、应对措施：
目标：利用大型语言模型的文本生成和图文理解能力，在广告元素识别、广告形态分类、自动化报错诊断等场景提供技术支持，扩展测试覆盖面并提升研发效率。 具体包括：

● 应对措施一：基于LLM精调的广告元素定位模型与实践

● 应对措施二：基于LLM精调的广告形态分类模型与实践

● 应对措施三：基于混元OpenAPI的自动化报告报错诊断+修复建议

## 应对措施一：基于LLM精调的广告元素定位模型与实践
为解决痛点1 ，广告UI的多变性和迭代周期短，用例编写和维护成本增加等问题，计划利用LLM多模态精调能力，训练广告元素定位模型，识别所有广告交互元素，泛化至新广告形态，并通过LLM实现自动化流程决策与断言。

1.  方案：
1)  提示词工程：使用“格式化文本”，“身份假定”、“背景描述”、“分步提示”、“输出格式限定”、“给出示例”等技巧，并将广告UI元素定义为9大类（标题、描述、素材等）分别概述

2)  训练：采用FULL Finetune+生成式训练；首轮训练共计1000+样本，详细标注常见广告元素；

3)  产物

a)  V1模型：经过第一轮多次训练，初步得到稳定性的最初版本模型

b)  V2模型：经过训练参数调整、样本集扩充（1500+）、提示词优化等措施，得到优化后的第二个版本

2.  模型测试：
对V1模型和V2模型进行了多图测试，验证方案可行性以及模型识别效果。得到以下结论：

1)  元素类别/文案识别准确性

a)  V1模型、V2模型文案识别准确性≥95%

b)  V1模型、V2模型元素类别准确性≥93%

结论：结合自动化系统OCR、文案相似度等能力，基本满足自动化需求。

2)  元素定位准确性（训练集内广告形态）

a)  定义：模型识别区域中心点在广告元素区域内视为准确

b)  测试范围：所有训练集内的广告形态及元素

c)  数据结论：V1模型元素定位准确率达到83%，V2模型元素定位准确率达到91%，相较于V1提升8%

d)  异常case：相较于V2，V1模型部分case存在标注漂移的情况



图2： 模型元素识别准确性测试——异常case示意

e)  训练tips：适当降低batch_size以增加迭代次数、多轮训练、增加训练集样本量、以及均衡广告形态元素规模，均有利于提升元素识别准确性

结论：对训练集内广告形态元素的识别，经过优化后的V2模型满足自动化使用标准，为降低用例难度成本提供了良好的解决方案。

3)  广告形态泛化识别能力

a)  除了对训练集内广告元素识别准确，我们希望模型同样可以对衍生样式、实验样式等新广告形态&元素（不在训练集内），有一定的泛化识别效果。经过多图测试，得到以下测试结论：

b)  目标：模型对新广告形态&元素（训练集外）具备泛化识别效果。

c)  测试范围：非训练集中的广告形态&元素

d)  数据：V1模型元素位置识别成功率达到63%，V2模型元素位置识别成功率达到72%，相较于V1提升9%； 整体成功率仍有提升空间，例如以下case：V1模型有部分元素识别存在漂移情况，V2效果较好






图3： 模型广告形态识别泛化能力测试——case示意

结论：V2模型在泛化识别上优于V1模型，但仍有提升空间；证实优化后的模型具备新广告形态泛化识别能力，为一份用例适配多种广告形态、减少用例迭代成本提供了良好的解决方案。

3.  总结：
1)  广告元素定位模型，显著提升了元素识别的准确率和泛化能力，降低了用例编写和维护的成本，提高了自动化执行交付效率。

2)  同时证明，该方案具有泛化识别能力，可扩展客户端自动化应用到部分新需求的场景



## 应对措施二：基于LLM精调的广告形态分类模型与实践
针对痛点2，计划基于多模态LLM进行精调，让模型了学习理解广告形态业务，能够实现广告形态的精准识别和分类，并且降低训练成本。

1.  方案：
1)  方案一：直接采用预训练模型识别广告形态，但缺乏业务理解，识别效果不佳。

2)  方案二：尝试基于LLM文生文模型精调，结合OCR技术，经过测试发现，广告形态识别准确率仍低，模型难以通过图片的OCR信息来进行图片分类特征的学习。

3)  方案三：采用基于LLM的多模态预训练模型精调，首先判断图片中是否存在广告，若存在则进行分类，此为当前采用方案。

2.  实现：
1)  训练准备

a)  资源：完成精调空间创建、应用组申请、测试训练资源申请、存储资源申请。

b)  数据：覆盖8个广告位，19个形态，一类负样本（不含广告），共计约4000个样本。

2)  训练

a)  提示词：“身份假定”、“背景描述”、“分步提示”、“输出格式限定”、“给出示例”等技巧。

b)  配置：基于图生文(多模态) - 混元7B-MoE-图生文-1792-240702-4k的基座模型；采用FULL Finetune精调方式。

3)  效果展示

a)  可以看出，精调后的模型可以成功做出广告位、广告形态的识别，符合预期



图4： 广告形态分类模型效果示意

3.  总结：
广告形态分类模型：实现了对广告形态的准确识别，不但补充了识别广告整体形态的能力空白，而且有助于提升自动化执行稳定性。



## 应对措施三：基于混元OpenAPI的自动化报告报错诊断+修复建议
针对痛点3，利用LLM的语言生成和上下文理解能力，提供智能化的测试报告报错诊断与修复建议，提升测试用例编写及问题定位效率，优化需求交付与值班效率。

1.方案：
1)  阶段1：基于LLM能力，分析自动化运行报错日志，给出报错的原因分析以及修复&优化的建议；

2)  阶段2（当前阶段）：使用Prompt Engineering技术，通过不断调整提示词（system + 上下文 + question），充分发挥LLM的语言生成能力和上下文理解能力，优化报错诊断+修复建议的效果

3)  阶段3（未来计划）：若需进一步提高LLM性能，将对其进行精调并结合RAG技术，检索业务常见报错信息以提高准确性

2.  实现：
1)  框架图



图5： LLM报错诊断框架图

1)  阶段1——实现：

a)  设计提示词以引导LLM分析报错日志。构造提示词示例如下：

[{"role": "user", "content": f"请问以下代码报错的原因是什么，以及有什么解决报错的思路？ \r\n {key_log}"]
b)  基于混元OpenAPI，开发接口链路并封装调用方法，并集成到微信广告自动化平台报告页面，辅助值班同学排查问题：



c)  效果

可以看到在【自动化元素定位失败】报错场景，AI的诊断结果效果较好，能够从自动化测试&用例编写角度触发，判断用例执行失败原因：

【报错信息】
raise RuntimeError(u"无法找到 \"{}\" 这个元素".format(text))
RuntimeError: 无法找到 "插屏" 这个元素
【AI诊断&修复建议】
从错误信息来看，报错的原因是在代码的第683行：`raise RuntimeError(u"无法找到 \"{}\" 这个元素".format(text))`。具体的错误信息为：`RuntimeError: 无法找到 "插屏" 这个元素`。
这意味着在执行自动化测试时，系统无法在界面上找到文本为 "插屏" 的元素。
为了解决这个问题，你可以尝试以下几种方法：
1. **检查元素是否存在**：确保界面上确实存在文本为 "插屏" 的元素。有时候，页面加载时间较长，元素可能尚未加载完成。你可以尝试增加等待时间，确保元素已经加载到界面上。
2. **使用更精确的查找方法**：检查你的代码中使用的查找元素的方法是否足够精确。有时候，使用模糊匹配可能会导致找不到正确的元素。你可以尝试使用更精确的查找方法，如通过元素的ID、类名或XPath等属性来查找。
3. **处理动态变化的界面**：如果界面上元素的文本是动态变化的，你可能需要使用其他方法来定位元素，而不是仅仅依赖文本内容。例如，你可以尝试通过元素的相对位置或其他属性来定位它。
4. **异常处理**：为了使你的代码更加健壮，可以考虑在查找元素时添加异常处理机制。当找不到元素时，可以捕获异常并采取相应的措施，而不是直接抛出错误。这样可以使你的自动化测试更加稳定。
但在【业务逻辑异常】报错场景，落地效果较差，Case如下：用例报错：点击广告文案，跳转小游戏落地页，由于落地页加载异常，用例报错




LLM分析结果，回答比较通用化，没有从业务层面给出更准确的意见：



如何优化上述场景？ ——通过上下问内容补充+提示词工程，优化诊断效果

2)  阶段2——优化

通过引入system角色、上下文补充和分布式提问方式优化提示词，构造提示词示例如下：

[{"role": "system", "content": "你是一个软件开发工程师，擅长开发客户端自动化框架以及系统"},
{"role": "user", "content": 
"1. 你好，我开发了一个客户端自动化系统，应用于微信广告业务中，通过编写自动化测试用例，测试各类广告形态跳转是否正常；
2. 当用例执行报错时，需要你帮忙定位自动化代码报错的原因；你可以根据报错函数或方法的名称推断函数功能，进而协助你给出结论；
3. 如果遇到了self.ad_error('xxxx')方法报错，请理解ad_error函数的英文字符串参数内容含义，并以此做出推断来给出结论； 
4. 如果没有ad_error相关报错，可以忽略这一点。"},
{"role": "assistant", "content": "好的"},
{"role": "user", "content": f"请问以下代码报错的原因是什么，并且给出解决报错的思路？ \r\n {key_log}"}]
优化后，在上述业务逻辑异常的报错场景中，能准确给出报错原因和修复建议：



结论：相对比，优化后AI分析结果，会尝试理解业务代码功能，并且结合广告业务特性给出更准确的解决思路

3.  总结：
基于LLM的自动化报告报错诊断与修复建议，可以提供有意义的诊断和修复建议，通过优化提示词，进一步提升了诊断的准确性和实用性，对于跟进人排查case问题有较大帮助。



# 三、业务落地：LLM模型能力接入样式优选自动化测试
1.  业务背景：
样式优选需求旨在解决广告主对播放形态的不了解问题，由播放端决定最佳形态或组件样式，特点是形态多样且迭代迅速。为迅速响应上线需求，我们已实现“样式优选自动化”。然而频繁的形态变化导致用例维护成本高，影响交付效率。

2.  落地方案：
将V2元素检测模型应用于样式优选自动化测试，以提升测试速度、扩大覆盖面并提高交付效率。接入方案图如下：



图6： 样式优选业务落地架构图

3.  实例展示：
针对朋友圈样式优选case，在自动化测试过程中，先依赖LLM元素检测模型识别所有广告元素信息，之后再进一步驱动自动化执行后续校验流程。

以下测试报告可以看出，该优选形态虽不在模型训练集的范畴中，也能输出相对准确的识别结果，满足自动化使用标准。



图7： 样式优选业务落地测试报告

自动化报告AI诊断：






# 四、项目收益：
1.  降低自动化用例迭代成本：面对广告样式优选的频繁调整，我们的自动化用例设计灵活，大多数情况下无需额外编写或修改代码，即可无缝适应新需求，大幅降低了迭代成本。

2.  提速自动化执行交付：通过采用LLM识别元素驱动自动化执行，提高了自动化执行速度；LLM用于报错诊断，协助值班人员提高了问题定位效率，加速报告交付。

3.  自动化能力补齐：经过精细调整的模型具备了深入理解广告形态的能力，有效弥补了自动化测试在业务理解方面的不足，显著提高了测试的全面性和准确性。

4.  自动化场景衍生：LLM驱动自动化可以成功落地形态衍生的自动化需求，也为后续LLM驱动自动化覆盖更多新形态场景提供可行性验证。



总体来看，微信广告自动化基于LLM的应用实践，在提升测试效率、降低维护成本、增强问题定位能力等方面取得成效；后续可通过不断优化和调整，仍有进一步提升发展的空间。



# 五、感谢
以上工作成果离不开团队的共同努力，感谢fiifnli，iriszhu，gastinxie等同学的帮助，也感谢运营产品研发中心milochen的支持和指导；同时感谢商数xpzhang团队txtingwang同学的帮助和建议。