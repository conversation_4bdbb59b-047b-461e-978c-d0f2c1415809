# 背景
当前，国内外都涌现出了众多AI代码助手，如Github copilot，Google Bard，Amazon CodeWhisperer，Baidu Comate，阿里的通义灵码，华为的CodeArts Snap等等。工蜂copilot（腾讯云AI代码助手）作为公司自研AI代码助手，CSIG研效工作组一直在积极推广落地。医疗健康事业部研发一中心也顺应潮流、拥抱时代，希望借力提升整体研效水平。在实际工作中，面对缺乏有效数据度量指标，尤其是推广初期的补全效果磨合等挑战。除了被动等待引擎优化效果，我们也站在业务的视角积极思考如何更加有效地推进与磨合。

# 面临挑战
## 挑战一：推广初期引擎效果欠佳，团队接受门槛较高
医疗研发一中心作为首批在内部推广工蜂copilot的业务团队，自2024.03月起便开始尝试将工蜂copilot应用在研发流程的各个场景。经过一番尝试，发现当时工蜂copilot的代码推荐补全在实际应用场景下还有较大提升空间，比如补全代码不完整，补全代码跨逻辑块，向后关联不足导致推荐与下文重复的代码，代码生硬照搬等。这些问题在一定程度上影响了开发体验。如果一味硬推，可能会适得其反，降低开发效率，同时也有可能会引发团队的排斥心理，在这种情况下，我们需要思考如何积极主动做出改变，降低团队的心理门槛，从而有序推进其在研发中心的整体使用情况？
 

图1 项目初期，引擎在代码推荐补全方面存在的几个典型BadCase

## 挑战二：缺乏数据度量指标进行有效地决策
在初期阶段，全面推广工蜂copilot主要依靠业务leader协助推进。然而，由于缺乏一套有效的数据度量指标，难以及时跟进各业务的推广进展。此外，中心的业务很多，不同业务的迭代频率，工作性质本身存在差异，不能用单一的数据指标来一概而论。工蜂copilot推广提效是一个长期且复杂的过程，既要关注短期的数据波动，也要关注长期的变化趋势。同时这也是一个偏管理类的工作，需要有效地进行决策并推进任务的执行。因此我们应该如何建立多维度的数据度量指标，以便根据不同业务特点来分析各项数据指标，有效地决策并推进工蜂copilot的快速落地？

## 挑战三：帮助团队充分发挥工蜂Copilot的潜力
工蜂copilot推广提效的目标包含两个方面，一是让更多人开始使用，二是关注如何让团队更高效地利用这一工具。因此，在确保广泛使用的同时，还需重点关注如何帮助团队充分发挥工蜂copilot的潜力，实现更高效地使用。

# 应对措施
## 应对措施1：分阶段推广，先局部推广，建立反馈通路，再全面推广
在推广初期，由于工蜂copilot的效果尚不理想，我们采取了分阶段推广的策略，先局部推广，再全面推广。首先找各个业务leader举荐使用意愿高的同学组建内部推广小组，成员来自不同的业务，包括前端、后台和算法。推广小组成员负责记录各自业务中的goodcase和badcase，记录的goodcase和badcase部分典型示例如表1和表2所示。
表1 典型的Goodcase示例

场景	代码目标	补全情况	代码示例
前端公共组件库	写出vueId的正则表达式，然后替换掉匹配到的字符串	copilot根据变量命名推断出想要的正则表达式，正则表达式的内容也完全正确，并且vue-id这个正则表达式是基于IDE打开的其他文件的上下文推理出来的（实现了跨文件上下文推理）	
前端公共组件库	写出当前vue项目的入口文件的路径	能够正确推理出当前入口文件的路径在哪个位置	
健康卡后台	打印错误日志并抛出异常	根据上下文能推断正确的错误处理并返回错误	
健康卡后台	用Go实现发送企业微信消息的函数	根据请求内容和要求，给出完整的处理函数，符合预期	
影像云前端	生成不同类型的按钮配置属性	自动关联上下文的不同类型，根据类型生成了相同结构，配置属性的text属性会基于变量名进行推断	
影像云前端	根据不同值展示不同的组件	根据代码文件中已有的组件代码和变量，自动推断出其他正确的组件代码和与之关联的变量	
表2 典型的Badcase示例

场景	Badcase类型	case描述	代码示例
业务代码实现	推荐不完整	在推理过程中，推断我可能使用if操作，但是生成的代码不完整，并且实际我并不是想要if判断操作	
前端组件库开发	多了反括号	按tab，括号越来越多，最后得到的东西就比较凌乱	
业务代码实现	过多推荐	此处应该属于正常换行，但是推荐一个return的内容，但是return后面不是本身就不能跟return 吗	
业务代码实现	推荐与上文重复的代码	推荐的代码与已有代码重复	
业务代码实现	推荐代码有语法错误	预期是catch()而不是catch(}	
业务代码实现	推荐代码跨函数块	拆分函数本身体验不好，最好不要有这种拆分的情况出现	
然后建立与工蜂copilot团队的反馈通路，通过双周会的形式进行交流和反馈，5-6月份共反馈了44个goodcase和170个badcase，其中badacase涵盖25种类别，并持续跟进每个badcase的处理进展，我们的积极反馈也得到工蜂copilot团队的点赞。

图2 反馈给工蜂copilot的有效badcase（涵盖25种类别）


通过局部推广的这个过程，工蜂copilot的效果在持续提升，中心的代码补全生成率、采纳率、周活跃率等各项指标在稳步提升，如图3和图4所示，大家的接受意愿也在逐步提升。

图3 医疗研发一中心代码补全生成率和采纳率的变化趋势

图4 医疗研发一中心周活跃率的变化趋势

## 应对措施2：建立多维度数据度量指标进行差异化分析，数据驱动决策
在中心全面推广工蜂copilot后，借助工蜂copilot现成提供的各项统计数据指标，如代码补全生成率，代码补全采纳率，补全次数，活跃总天数等（代码补全生成率和代码补全采纳率在一定程度上能反映工蜂copilot推荐补全的效果，补全次数、活跃总天数在一定程度上能反映大家使用工蜂copilot的活跃程度），分别按小组、业务维度做更进一步的统计分析，如某小组的平均代码补全生成率，某业务的人均补全次数，进而更为全面地度量和分析各小组、各业务推广的进展。通过长期统计各个小组和业务各项指标的变化趋势，结合业务的差异化，观察短期内的数据波动，洞察问题并采取相应的解决策略。举个例子，如图5所示，A业务的平均代码补全生成率和采纳率在7/1号有显著的提升，但近三周出现连续下滑。如图6所示，A业务的周人均补全次数同样在7/1号有显著的提升，但近几周也出现连续的下滑，这些数据反映出A业务还没有真正用起来，还需要重点关注跟进A业务的推广工作。同时也要观察业务各项指标长期的变化趋势，举个例子，如图7和图8所示，虽然B业务的平均补全生成率、采纳率和周人均补全次数在7月中上旬的有出现显著下降，但近几周已经出现回升，从长期的趋势变化看，各项指标是稳步上升的，这些数据反映出B业务的推广情况是比较乐观的。

图5 业务A平均代码补全生成率和采纳率的变化趋势

图6 业务A周人均代码补全次数的变化趋势

图7 业务B平均代码补全生成率和采纳率的变化趋势

图8 业务B人均代码补全次数的变化趋势
工蜂copilot推广提效是涉及整个中心的公共事务，属于偏管理类的工作。定期向上汇报，将各小组各业务拉通对比，并保持信息透明，是在全面推广工蜂Copilot阶段行之有效的策略。
汇报形式：
每周整理工蜂copilot推进简报，向研发中心负责人和各业务leader汇报进展

汇报内容
中心整体的各项指标的情况
• 在BG的综合排名情况
• 代码补全生成率的变化趋势
• 代码补全采纳率的变化趋势
• 周活跃率的变化趋势
集团各小组各项指标的情况
• 代码补全生成率的变化趋势
• 代码补全采纳率的变化趋势
• 补全次数的变化趋势
• 各小组各项指标的对比分析和结论
云智各业务各项指标的情况
• 代码补全生成率的变化趋势
• 代码补全采纳率的变化趋势
• 补全次数的变化趋势
• 各业务各项指标的对比分析和结论
具体做法
问题
接下来的计划
通过建立多维度数据度量指标，精细化数据运营，差异化分析，定期向上汇报，有效地决策，推动了工蜂copilot在各小组和业务的快速落地。
## 应对措施3： 激励机制与经验分享
通过工蜂copilot的使用数据统计分析，发现代码补全采纳率与使用的熟练度呈正相关，使用愈发熟练，更易于找到适合自己的姿势使用工蜂copilot，从而最大化提升自己的编码效率。通过收集各个业务的最佳实践，我们总结了一些致使工蜂copilot低效的做法和为工蜂copilot提效的做法。
致使工蜂copilot低效的做法 ：
• 函数、变量命名不规范
• 代码缺少注释，注释不清晰
• 代码实现逻辑复杂
• 大块重复代码复制黏贴
• 问chatgpt直接复制代码

为工蜂copilot提效的做法 ：
• 函数、变量命名符合良好实践
• 注释先行，清晰的注释
• 复杂的任务拆解成简单的小任务
• 使用/explain，@workspace等内置指令提升回答效果
• 当相关的代码跨文件时，IDE打开相关代码文件
• 任务式场景采用内敛对话
为了鼓励大家积极使用工蜂copilot，医疗研发一中心每个月综合考虑补全次数、采纳补全次数、活跃总天数、当月代码提交情况以及badcase反馈情况等多项指标，评选当月工蜂copilot的优秀标兵，给予奖励并分享个人使用心得和经验，让大家借鉴和学习。以下是医疗研发一中心以往评选的工蜂copilot优秀标兵以及他们分享的个人使用心得和经验。
4月份的工蜂copilot优秀标兵
tituzhu：极大减少编写重复性胶水代码的时间，开发者也有了更多的时间去思考更深层次的代码问题
geraldchen：减少大量的变量拼写，代码互转能力强，比如json快速转go struct
sophiaswu：在使用形式上基本上不打破原有的编码习惯，有感受到推荐方面做了一定的平衡和优化
5月份的工蜂copilot优秀标兵
jardgechen：通过准确的注释推荐出正确的代码段；调用函数时可以快速按照参数定义进行补全
fayangdai：日常的curd和配置相关代码能准确无误推荐，阅读他人代码时，能准确推断代码含义，提升在已有功能迭代的效率
arthurxing：免去了很多体力活，对于规范变量名起到积极的作用；跨文件上下文记录并推理和内联对话的功能挺有用
6月份的工蜂copilot优秀标兵
hamilhong：与GPT相比，Copilot无需提供过多的开发目标描述，仅需通过简单的代码提示和注释，长期使用后，在代码补全时机的把握上变得更加准确
yeonzhou：工蜂copliot免费，安全，vue的sfc语法轻松转tsx语法，降低编程语言学习门槛，边写边查边问，快速熟悉一门新的技术框架
kangjia：sql，json语言间能很快进行转换；代码安全扫描时，能很快的给出相关建议，方便处理各类安全问题
为了让大家更好地使用工蜂copilot，医疗研发一中心积极组织和参与中心级和BG级关于工蜂copilot应用实践的分享，4-7月份工蜂copilot相关的中心级分享有4次，BG级分享1次，并且积极参与工蜂copilot组织举办的直播《鹅厂程序员教你“摸鱼”小白也能用的AI代码助手》。
 
图9 工蜂copilot相关的中心级分享


图10 工蜂copilot相关的BG级分享
# 取得的结果
截至8月9日，医疗研发一中心T族总人数126人，代码补全生成率31.16%，代码补全采纳率28.21%，周活跃率89.68%，工蜂copilot各项指标的综合分数是74.99分，在CSIG所有中心的排名是1/85。

图11 医疗研发一中心工蜂copilot综合分数排名
基于医疗研发一中心对工蜂copilot的使用情况，我们将用户按使用的活跃程度分成三组：A组（最活跃）、B组（中低活跃）、C组（低活跃/不使用）。我们统计了从2月份到6月份，这三组用户的人均交付需求数、人均编码行数，分析使用工蜂copilot后对于效率的影响。结果显示，在2024年上半年，医疗研发一中心活跃用户的人均需求交付数量和人均编码行数有较为明显地提升。

图12 工蜂copilot的使用活跃程度对人均交付需求数的影响

图13 工蜂copilot的使用活跃程度对人均编码行数的影响
我们还分别统计了去年6月份（未使用）和今年6月份（已使用）在人均交付需求个数、人均编码行数的变化，分析使用前后的效率差异。结果表明，以6月份为例，深度使用工蜂copilot的用户相比未使用同期，人均交付需求个数和人均编码行数分别提升18.18%，41.34%。

图14 工蜂copilot使用前后对人均交付需求数的影响

图15 工蜂copilot使用前后对人均编码行数的影响
此外，医疗研发一中心有两位云智同学荣获云智2024H1“工蜂copilot优秀标兵”。在工蜂copilot第一期有奖反馈活动中，医疗研发一中心有3人获得工蜂copilot最有价值奖，7人获得工蜂copilot有效反馈奖，1人获得工蜂copilot反馈达人奖。

# 经验总结
分阶段推广策略：在推广过程中，为避免团队排斥心理，可先在小范围内进行试点推广，记录使用情况，并与开发团队建立反馈机制，之后再逐步扩大推广范围。这有助于发现问题并及时优化工具，逐步提升团队的接受意愿。

多维度数据度量指标：建立一套多维度数据度量指标，包括代码补全生成率、代码补全采纳率等，以便根据不同业务特点进行分析和决策。通过定期统计、分析和汇报这些指标，可以更好地了解各小组和业务的推广进展，从而制定针对性的解决策略。

激励机制与经验分享：为了鼓励团队成员积极使用工蜂copilot，可以设置激励机制，评选优秀使用者并组织分享活动。这种做法不仅有助于提升团队的使用意愿，还能帮助成员们互相学习、借鉴，从而更好地发挥工蜂copilot的潜力。
这些策略和方法不仅对于工蜂copilot的推广具有借鉴意义，也为其他团队推广类似工具提供了有益的经验。

# 特别鸣谢
在此特别感谢医疗研发一中心所有同学和工蜂copilot团队的努力和支持！

