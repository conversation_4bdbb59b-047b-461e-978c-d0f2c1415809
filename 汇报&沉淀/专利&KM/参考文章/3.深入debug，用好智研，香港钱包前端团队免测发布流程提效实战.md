# 一、背景
近期在团队的发布群中，看到发布要罗列的条目越来越多。有同学甚至做了一个表单页面，专门用于辅助填写发布内容，做标准化。（发布案例参考下图）



乍一看，你可能会觉得，这个发布好规范，材料列得非常详细，还有很多监控截图，很规范，执行的不错。

但是我总感觉有点不对劲，直觉上就是太复杂，要当心大家有抵触心理，于是仔细复盘一下现有的发布流程，发现了不少问题。

其实发布这块的流程，简单说就是做好自测验证和监控。当前我们都有了，但运行着运行着，我们不断优化，流程就越来越重了，大家一开始执行这个流程，肯定是纠结挣扎的，但是久了，也就“轻车熟路”了，知道如何应对了，就会达到一种相对平衡与默契的状态，“表面”上执行的挺好。

为了执行流程，凑一些差不多的测试用例；为了执行流程，建一个差不多的测试计划；为了执行流程，发一堆差不多的监控截图。大家忙着往前冲，差不多的事情干的多了，也就逐渐麻木了。

好在，大家意识都建立了，习惯养成了，这个流程了已经跑了一段时间，只是现在需要做一次debug，需要把所有的模糊地带清晰化，更需要化繁为简。

# 二、深入debug，找到问题
前面提到，发布这块的流程，就是做好自测验证和监控，所以，我们从这2块来做debug。

这里有几个背景信息：

1、  团队负责的业务模块多，业务拆的比较细：我们是业务是香港钱包，主要分为：1、钱包基础功能；2、各类支付应用（大陆钱包9宫格有的业务，我们大部分都有，还都是我们一个team维护的）；3、运营活动三大板块。由于历史原因，我们的业务拆的非常细，有80+项目仓库，每次发布只发一个仓库。所以，每次的发布，都是针对其中某个项目进行自测验证和监控。



2、  测试用例不完善，P0用例多：测试团队经历了一波调整，很多新人，而且人手不太足，我们业务场景多，也很复杂。以前的用例很多没更新，不同的测试人员写的也五花八门。后面新团队有花力气去整这块的流程，采用测试堂管理用例，重新整理用例，但是因为同时很多需求测试工作，整理用例的过程比较缓慢，导致很多业务的用例，不完整。另外，已经整理过的用例，里面很多都是打了P0用例标签（站在测试同学的角度，是合理的）。



## 问题概述
我们已有流程看起来还是比较规范的，已经采用智研的「测试堂」管理起测试用例及测试计划，采用智研的「日志汇」及自带的grafana构建了业务的监控视图。（这里点赞智研，日志汇做的太棒了，一个高度可定制化的ELK平台，配套了kibana和grafana，还提供公有云上报接口。FIT私有化的FIT智研，也无缝对接了原有磐石日志和告警系统，实在是好用！）

日常开发中，前端免测需求的量级是非常大的，为了保证质量，我们提出了标准的免测需求研发流程（如下图）。



其中，在发布阶段，保证质量最关键的2个环节是：

自测流程：执行【内部发布】测试计划
监控流程：查看监控视图
有三个地方是需要优化的：

问题一：P0用例不全，P0用例太多

问题二：执行测试计划成本很高

比如有些场景依赖特定的账号或者状态，构造数据比较复杂，难以验证

问题三：监控视图太多，看哪些视图不明确

所以，要求团队的每一次发布，都执行【内部发布】测试计划，不是一件简单的事情。监控视图太多，查看起来效率低，执行的效果难以保证。

接下来具体阐述分析细节（赶时间的客官，看完这个小节，可以直接跳过下面2个小节，直接去到“解法：引入前端P0用例来解决问题”）

## 自测流程问题分析
### 现有流程
现有流程中，要求大家在发布的时候，要勾选业务的P0用例及需求用例，执行测试计划，带着测试计划的报告，来申请发布。

### 现有流程问题分析
#### 要不要验证P0用例？
那问题来了，所有发布，包括轻量的发布，要不要走P0用例验证？我改一个图片，也需要建立测试用例，测试计划？

我的回答是：要，因为有几次案例，只是修改一个小小的地方，但引发了线上问题。

案例一：跨境游，修改领券组件tab的样式，导致核心功能领券失败

案例二：零钱首页，解决大字体跳动问题，导致页面白屏

案例三：绑卡优化，多删除了一个router，导致信用卡绑卡2FA页面缺失，核心绑卡流程失败

案例四：之前汇款出现一个小的改动，没有验证到安卓，导致安卓渠道曲线掉底，造成五级事故

结论：以史为鉴，无论需求大小，都需要验证P0用例，才算稳妥。

#### 验证P0用例有哪些挑战？
但是，日常发布有很多小改动，都要验证P0用例，成本高，会和效率产生冲突，产生较大心智负担。大家执行过程中，时常会有以下疑问：



问题场景

问题分析

1

（讨价还价）这个需求实在是比较简单，就改了个文案，申请不提供测试计划可以吗

执行P0用例验证流程繁琐，工作量大，有心智负担

2

这个需求很紧急，没时间搞测试计划了，我这次先放过吧？或者简单搞几个用例先凑合一下？

执行验证流程繁琐，工作量大，有心智负担

3

测试同学还没完善测试用例，我这次就临时搞几个需求相关的用例，搞完整的用例太多了，可以吗？

已有测试用例不完善

4

leader：测试同学没整全测试计划，那你们就自己梳理核心的一些测试用例呗

组员（不满）：为什么好多事情都要前端做，团队分工不应该分清楚边界吗？该测试做的，应该推测试同学做。

leader尝试找测试负责人解决问题，测试负责人诉苦，实在是抽不出时间整理，都在忙业务需求呢，要不再等等？

已有测试用例不完善，推动前端同学整理用例不乐意

5

测试有沉淀完整用例，但测试同学理的用例，一大堆用例都打了P0标签，太多了，完全没法执行啊？我要从里面挑几个出来关联的，另外，每次这么挑选，真是烦躁

已有测试用例，P0用例太多，验证工作量太大

6

有很多用例，构造场景数据好麻烦啊，我能否先放过一些用例，上线后再观察好吗？或者能否mock数据？但是mock数据，每次都要重新构造数据好麻烦

有些业务的场景构造难，Mock数据不方便

所以，在当前自测流程中，执行测试计划，提供测试报告，是一件不那么容易的事情。进一步分析问题的根源，在于，P0用例是一个模糊地带：

1、  P0用例可能都没有，测试同学没法及时给到：目前测试人员不足，且在忙于测试业务需求，但钱包业务场景非常多，目前智研上维护的用例并不完整，测试同学梳理周期会非常长。但我们的业务开发和发布验证，等不了。

2、  即使测试梳理完整了用例，P0用例又太多：站在测试的角度其实是合理的，站在前端角度又太多，都验证不合适。比如支付中心，手续费，消费券，优惠券，就很多用例，且都是P0的，前端发布改一个文案，一张图片，都要验证所有这些P0用例岂不是要痛苦死？

更别提有些场景数据不好构造了。

所以验证P0用例，是很难实施下去的，那实际操作中，大家就会见招拆招，差不多就行了。比如遇到大家反馈这个业务还没有测试用例，又比较紧急，leader会跟他说，你就写几条需求用例和大概的几个主流程用例就好了，等以后测试梳理了用例再说。结果，大部分发布的测试计划，都是自己简单临时搞几条。（这就是前面提到的，相对平衡与默契的状态，“表面”上执行的挺好。）

## 监控流程问题分析
### 现有流程
现有流程中，要求大家在发布的时候观察视图（业务核心主流程监控+需求特性监控），并截图到发布群。

### 现有流程问题分析
发布是否有问题，关键点在于识别出该业务的核心场景，做好相应的监控。比如我们钱包基础业务中的零钱&充值业务的发布，具体就要关注：

1、零钱和各充值页面正常展示

2、  银行卡充值，信用卡充值，线下充值，都有成功量级



但是实际情况下，我们做的业务监控视图，视图太多了，多就是好吗？肯定不是的，多可能会让人迷惑！多就意味着没有重点。

（下图是我们一个业务的通用监控视图，只截图了一部分，视图实在太多了...）







比如我做了零钱&充值业务的一个小迭代需求，在发布阶段，观测零钱充值监控，如果我是认真的查看监控，且我是一个非常懂这个业务的同学，那么：

我需要知道那3个页面视图是零钱首页页面访问、零钱记录页面访问、零钱明细页面访问。
 银行卡充值，我需要知道那个页面视图是银行卡充值页面访问，那个接口视图是银行卡充值成功。
 信用卡充值，我需要知道那个页面视图是信用卡充值页面访问，那个接口视图是信用卡充值触发3DS，那个接口视图是信用卡充值成功。
 线下充值，我需要知道那个页面视图是线下充值页面访问，那个接口视图是轮循充值接口，那个接口视图是线下充值成功。
所以，我需要找到6个页面视图，5个接口视图，但是该业务的视图，有很多很多，在众多视图之中，去找到这11个视图，是需要非常熟悉业务，且带着认真负责心态的同学，才能执行到位。

如果，我不是负责这个业务的同学，我只是临时过来帮忙迭代，在发布的时候，我怎么知道要看这11个视图？

如果我的改动很小，我可能只是大概看了一下整体视图，就报告验证成功，谁又能知道我偷懒了（反正大部分情况下，并没有出问题）？

所以，这里的核心问题是：视图很多，但没有进行归类，并没有专门针对发布的业务主流程监控视图，如何执行好监控也是模糊地带。

### 对视图进行归类会面临什么挑战
如果去做归类，怎么样做归类好呢？

没有明确标准，大家的作出的业务视图会五花八本。
没有明确标准，有些同学可能会做的过于细，导致监控视图膨胀，最后又太多了。
说到底，这里就是缺乏实施标准。

# 三、解法：引入前端P0用例来解决问题
针对前面提到自测流程问题，P0用例是一个模糊地带，现有测试同学维护的用例，是一个很大很全的超集，目前也不是很全，如果要等他们补充完整，我们又等不起。

那么，我们能不能自己维护一套前端的用例？只出最核心的几个用例，让绝大多数用户，使用业务不会遇到明显问题就行了。

## 前端P0用例概念定义
经过和团队达成一致协议，我们提出前端P0用例概念，基于前端的特性，前端负责业务的同学，识别出哪些是业务的关键核心路径和场景，筛选完整版用例“P0中的P0”，或全新创建。这些能保证业务正常运行最简的用例集合（能保证业务绝大部分场景正常，没有明显报错），叫做前端P0用例。兼顾效率和执行成本，减少执行自测流程带来的心智负担，前端P0用例要少，不超过10个，尽量最好在5个以内。

前端发布，需验证前端P0用例+需求特性用例。

在原则上，和团队约定，如果出现线上bug，在前端P0用例中的问题没验证好，开发人员需要担责主要责任。在前端P0用例之外的问题，leader承担主要责任。

备注：仅供参考，可能并不适合其他业务。我们是项目拆的比较细，一次发布只涉及单个业务，所以能做到10个以内的核心用例。有些团队可能由专门的测试团队，管理用例或进行发布的验证；有些团队是大仓，业务庞大复杂，核心用例肯定做不到这么少。

## 前端P0用例串联Mock用例和监控视图
构造业务便捷的Mock验证标准：前端很多发布是不涉及后端的，可以只关注前端界面上的变化，所以完全可以采用Mock数据来验证变更，来提升前端P0用例验证效率。决定将前端P0用例的Mock数据，统一管理到git仓库中，便于运行mock-server，提升验证P0用例的效率。

在监控方面，基于创建的前端P0用例，建立前端P0用例监控视图，一一对应起来。发布环节，只需要对这些视图进行截图发布到群里，就可以证明该次改动没有引发线上问题。

## 建立规范，明确执行标准
### 1）建设《前端P0用例编写规范》：
前端P0用例编写规范： https://iwiki.woa.com/p/4010794637

前端P0用例，存放如下：



前端P0用例核心关注点包括2大块

1、首页展示（最基本的用例，为什么不包含其他页面，因为其他页面一般都在核心关键流程中，做为中间节点，所以多写了不仅麻烦，还重复）

2、核心关键流程（关键流程中，涉及一系列操作，中间包含页面/组件的曝光、按钮的点击，关键接口的调用）





示例

用例名称

xxx功能

零钱首页功能

零钱明细记录和详情功能

门店现金增值功能

步骤

{点击｜输入｜选择} xxx {按钮｜链接}

点击“门店现金增值”按钮

选择银行卡，输入增值金额0.01，并点击“下一步”按钮，输入密码

预期结果

xxxx{正常 | 成功 | 正确}

{正常 | 成功 ｜正确}xxxx

零钱首页展示正常

弹出提现说明dialog成功，限额文案展示正确

### 2）根据前端P0用例，建设监控视图的规范：
前端P0用例监控视图规范 https://iwiki.woa.com/p/4007822745

所以我们针对首页展示、核心关键流程这两块来做视图（其实视图的推导，还是挺复杂的）

用例类型

视图可采用的数据源

说明

首页展示正常

（首页展示不白屏，各种按钮点击正常）

1、按钮的点击上报

推荐：某个按钮有点击上报了，就证明这个页面正常渲染，不会白屏。

2、页面上某个动作触发的接口上报

推荐：某个动作触发的接口调用有了，就证明这个页面正常渲染，不会白屏。

3、唯一的后续页面触发的接口上报

可临时使用，但不推荐：当前页面没有点击上报，也没有触发接口调用。但是如果后续流程中，当前页面是后续页面唯一的前序页面，可以用后续页面的按钮点击上报或页面上某个动作触发的接口调用，来当作前序页面没有白屏的证明。

不推荐的原因，是前序页面不是一个稳定的，可能后续有迭代，会产生新增的前序页面。所以只提供临时使用。

4、组件的曝光事件

不推荐：不好证明页面是否白屏

核心关键流程正常

（关键流程，用户能正常走完，关注最终成功的上报）

1、视图关键接口的调用成功

推荐：比如fps转账结果轮询成功调用。

2、成功页面的曝光

推荐：比如银行卡充值成功页面曝光，可以证明银行卡充值流程正常。

### 3）根据用例和视图，建立前端发布规范
前端发布流程规范 https://iwiki.woa.com/p/4007822742

包含「发布申请周知」和「发布监控周知」，如下：



### 4）建立前端P0用例Mock规范
前端P0用例Mock规范 https://iwiki.woa.com/p/4012190677

以项目为维度存放mock数据, 项目成员共同维护。

而且项目的新加入成员也可以通过mock数据更好的了解熟悉具体的业务逻辑&异常流程



新建文件夹用于存放 mock 数据配置.

案例如下：



## 如何有效推行起来
在需求的方案设计阶段，需评估，完善业务前端P0用例及监控视图的时间，在新的需求中，一起做了，工作排期上给予支持。所有的P0用例和监控视图，需要leader验收。



# 四、取得成果
1、所有前端日志迁移到日志汇，在日志汇grafana平台上，建立了前端监控通用模版，并快速建立了15+业务视图

2、  建立了前端P0用例标准，并在测试堂建立了15+业务的前端P0用例



3、根据测试堂的业务前端P0用例，在日志汇grafana平台中建立了15+业务的监控视图



建立了通用的视图模版，加快业务视图的接入



根据前端P0用例建立了业务的前端P0监控视图，用于日常发布检查



4、日常团队发布需要执行测试计划和前端监控观察





5、我们通过再规范了前端mock规范，将前端P0用例对应的实现了快速验证





目前，全流程非常明确，且流程进行了最大限度的精简，团队容易遵守并执行（再也没有讨价还价了），提升了发布的效率，更提升了质量。

对于该流程的执行，经由leader审核验收并记录，各个项目逐步有序覆盖中，目前多个活跃项目已经完成。



# 五、效果分析
比较遗憾，并没有相关的数据进行佐证，只能通过理论和体感上，给到效率提升的结论。

质量提升的结论，需要挖掘版本回退率数据和bug转需求的比例数据，由于推行时间不长，相关统计数据波动不明显，也不好统计。

这些也暴露了，整个研发流程上，是缺乏一些数据统计的手段，还需努力。

我这里罗列一下所有优化措施，汇总起来：



从理论上分析，效率肯定是提升的，质量也是会提升的。

后面从EPLUS同事了解到，tapd数据，智研的数据，都可以在EPLUS上对应的宽表中拿到（如下图），比如可以将智研的测试计划关联到TAPD，然后在EPLUS上拿到测试计划创建、执行完成时间，从而得到自测验证环节更准确的数据。接下来我们会利用这个能力，去观测相关数据。





# 六、还能优化更多吗？
其实前端P0用例是比较固定的，现在是人工执行，那将来，是不是可以把这些P0用例，转化为自动化执行？采用前端UI自动化测试，可以进一步提效。

另外，这里的前端P0用例，其实就是业务的核心场景用例，分工上也可以让测试同学来负责。只是当前在一个团队中闭环执行起来，比较容易。长期来看，还是应该转交给测试同学负责。

# 七、经验总结
其实发布流程只是研发流程中的一个部分而已，要做好也并不容易，需要花很长一段精力去打磨完善。举一反三，在整个研发流程中，还有很多部分需要不断优化。比如我们团队前期有推行公共组件（库）建设流程；前端项目文档，方案设计流程；前后端联调流程优化等等。其实中间走了一些弯路，那些强加给团队不合理的枷锁，最终都会丢进垃圾桶。一个团队的流程规范建设，应该是持续的debug的过程，去除流程中的一切模糊地带，尽最大可能简化流程，让流程规范清晰可执行、容易执行。