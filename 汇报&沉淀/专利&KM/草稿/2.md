

## 基于动态模板与大模型协同的应用宝提效助手

### 一、背景：效率瓶颈催生工具革新
在应用宝业务的快速迭代中，研发团队长期面临以下核心痛点：  
**版本管理场景**：  
- 手动捞取版本需求需跨工蜂、Tapd等多平台操作，单次操作耗时超过1小时  
- 灰度报告制作需人工查询灯塔、Bugly等系统，涉及12类指标（如Crash率、ANR率）的跨平台复制粘贴，易出现数据错位  
- 版本计划与覆盖率信息分散在iwiki、Tedis等平台，缺乏统一查询入口  

**问题定位场景**：  
- 单日需处理数千条用户反馈，日志文件体积常达GB级别  
- 业务日志格式差异显著（如广告模块采用JSON埋点，下载模块使用特定错误码），人工解析耗时且易遗漏关键信息  

### 二、技术方案设计
#### 2.1 版本工具技术实现
**1. 自动化需求收集引擎**  
- 通过工蜂OpenAPI获取MR列表（API：`/api/v3/projects/{project_id}/merge_requests`）  
- 采用正则表达式解析Commit Message，提取需求ID（Pattern：`feature/\d{6}`）  
- 基于Tapd REST接口（`/story/get_story_by_id`）拉取需求详情，关联开发负责人、测试用例等字段  
- 结果持久化至iwiki表格，通过`iwiki-python-sdk`实现数据自动更新  

**2. 智能报告生成器**  
- 构建Jinja2模板引擎，定义三类报告模板：  
  ```python
  class ReportTemplate:
      CRASH_ANALYSIS = """
      ## Crash报告 
      - 时间段: {{ start_time }}至{{ end_time }}
      - 影响用户: {{ affect_users | default(0) }}
      {% for stack in crash_stacks %}
      ### {{ stack.exception_type }}
      发生次数: {{ stack.count }}
      首现版本: {{ stack.first_version }}
      {% endfor %}
      """
  ```
- 数据层集成：  
  - 从Bugly批量拉取Crash堆栈（`/crash/external/data`）  
  - 通过灯塔OpenAPI获取DAU、留存率（`/dataapi/v1/query`）  
- 通过企业微信机器人推送Markdown报告（使用`requests.post`调用webhook）  

**3. 版本信息聚合查询**  
- 构建统一GraphQL网关，整合多源数据：  
  ```graphql
  query VersionInfo($version: String!) {
    tediSchedule: tedi_get(version: $version) {
      mergeDeadline
      grayStages
    }
    buglyCoverage: bugly_query(version: $version) {
      install_count
      active_rate 
    }
  }
  ```

#### 2.2 日志智能分析系统
**1. 日志清洗引擎**  
- 采用分层处理架构：  
  ![日志清洗架构](https://via.placeholder.com/600x200?text=Log+Process+Flow)  
- 关键清洗规则示例：  
  ```yaml
  rules:
    - action: RETAIN
      condition: 
        tag: "Download"
        contains: "status=FAIL"
    - action: TRUNCATE
      condition:
        tag: "Network"
        max_length: 200
    - action: DEDUP
      scope: GLOBAL
      keys: ["error_code"]
  ```

**2. 动态知识库构建**  
- 在iwiki搭建错误码知识库，支持版本化管理：  
  ```markdown
  >>> ERR_DOWNLOAD_1003
  触发场景: 用户带宽不足引发的下载中断  
  建议排查: 
    1. 检查CDN节点状态 
    2. 验证用户地域网络策略
  ```  
- 知识加载器通过`iwiki.get_page_content(page_id)`实时获取最新规则  

**3. 大模型推理流水线**  
- Prompt构建算法：  
  ```python
  def build_prompt(cleaned_logs, knowledge):
      context = f"已知业务知识：\n{knowledge}\n"
      context += f"清洗后日志：\n{cleaned_logs[:2000]}"
      return {
          "system": "你是一名资深技术专家，请结合业务知识和日志分析问题原因",
          "user": context
      }
  ```
- 采用LangChain框架实现分析链：  
  ```python
  chain = (
      load_cleaned_logs 
      | fetch_relevant_knowledge 
      | construct_prompt
      | llm_analytics
  )
  ```

### 三、落地成效与接入方式
**版本管理场景**  
- 需求收集自动化覆盖率提升至90%，手动操作时间从60+分钟降至5分钟  
- 灰度报告生成耗时缩短80%，通过企微机器人实现10秒内报告触达  

**日志分析场景**  
- 典型问题排查链路：  
  1. 用户在Aisee平台提交日志文件（支持100MB以内ZIP包上传）  
  2. 系统自动触发清洗规则，过滤后日志体积缩减至原始5%  
  3. 关联知识库中12个错误码解释文档  
  4. 输出带置信度评分的根因分析（例："83%概率为CDN节点异常"）  

**接入指引**  
- 企微机器人指令集：  
  ```
  /版本报告 8.7.2       # 生成指定版本分析
  /日志分析 @文件      # 上传日志后触发分析
  ```  
- 详细操作文档：<https://iwiki.woa.com/p/4014613423>

### 四、未来演进方向
- 增加版本风险预测模块，基于历史CRash数据训练LSTM预警模型  
- 探索日志知识库自动化更新机制，利用大模型提取日志中的新模式  
- 优化清洗规则配置界面，支持产品运营同学自主定义关注指标  

通过动态模板与大模型的深度协同，该方案为应用宝团队构建了标准化、智能化的效率提升平台。目前已在版本发布、故障排查等核心场景验证技术价值，未来将持续扩展应用边界。