
版本工具背景：

1. 手动拉取版本需求列表，耗时，需要流转多个页面进行信息捞取，并复制粘贴到excel表
2. 人工制作 版本灰度实验数据分析报告，耗时，易错，在查询crash率和anr率的时候，需要切换多个窗口进行输入查询等待...在灯塔查询数据的时候，需要修改多个sql语句，查询数据。同属数据行数多，数据量大，容易眼花且容易复制粘贴错误。我在曾手动捞取版本灰度数据进行测试，完整捞取一次数据至少需要一小时的时间。
3. 版本信息分散在不同的页面上，没有一个整合的查看节点，同时，不止研发同学需要查看版本信息，产品、运营同学也会需要查看。将版本信息进行整合，自助 查询版本信息，便于查看。


版本工具，具有的能力：
- 自动化收集 版本需求列表，需求列表内容包含需求名，需求类型，需求单，工蜂MR链接，负责产品，终端开发，是否预埋，测试关注重点，开关说明，特性实验链接或特性实验报告
    - 实现方案，通过工蜂接口，获取工蜂MR信息，解析MR信息，获取需求信息
    - 通过tapd接口，获取需求单信息
    - 通过iwiki接口，沉淀需求信息，便于查看
- 自动化收集 版本灰度实验数据并生成报告，包括qua信息，rqd信息，实验时间，crash率，anr率，启动速度，联网用户，云游拉起成功率，弹窗成功率，下载相关数据，广告相关数据
    - 实现方案，搭建模版，通过接口拉取数据，填充到模版中，生成报告
    - 通过企业微信机器人，实现一键调用，查看报告
    - 通过iwiki接口，沉淀报告，便于查看
    - 通过灯塔接口，获取版本灰度实验数据，如启动数据，联网用户数，下载和广告相关数据等
    - 通过bugly接口，获取crash率，anr率
- 自助 查询版本信息，如版本覆盖率，当前版本计划等
    - 实现方案，整合 iwiki、tedi、bugly 接口，获取版本信息
    - 通过 tedi接口，获取当前的版本所处节点、当前版本计划（如，什么时候截止合流，什么时候灰度，什么时候发布）
    - 通过bugly 接口，获取版本覆盖率
    - iwiki预埋版本日历，通过iwiki接口，查询未来版本的计划。
    - 通过企业微信机器人，实现一键调用，查看版本信息


AI日志定位系统 背景：

在日常研发过程中，日志分析是定位问题的关键手段。在aisee平台上（用户反馈处理平台），许多用户频繁反馈与同一业务相关的问题，如发货失败、安装失败、无法下载等。针对这些高频问题，可以构建业务问题分析知识库，借助大语言模型（LLM）强大的自然语言理解和智能推理能力，实现对日志的智能分析，辅助快速定位问题。基于动态模版与大模型协同的日志定位系统支持多场景应用，旨在提升团队的问题排查效率，降低人工成本。同时，该系统具备实时更新和扩充业务知识库的能力，丰富LLM的分析场景，进一步提升整体智能化水平。

二、挑战与优化思路
1.遇到的挑战
在尝试让大模型辅助日志分析的过程中，我们发现了以下两个主要问题：
（1）日志数据量庞大，过多的输入信息容易导致大模型产生“幻觉”或误判，影响分析准确性。
（2）大模型难以理解日志内容，类似于非当前业务同学单独查看日志文件时难以把握关键信息。

2.优化思路
（1）日志内容庞大 —> 日志清洗，过滤出关键日志
	海量的日志信息虽然蕴含丰富的业务和运行状态细节，但也给大模型的日志分析带来了严峻挑战。过多的输入信息不仅增加了模型处理的负担，更容易导致模型出现“幻觉”现象，即基于不相关或噪声数据生成错误的推断，进而引发误判。为了有效降低噪声干扰，提高分析的准确性，需要对日志进行结构化和预处理，过滤出关键日志。
如图所示，首先将日志格式化为四个部分：时间、级别、tag和日志内容，便于后续对日志信息的处理。随后，根据具体业务场景的需求，对日志进行清洗，主要包括以下几种操作：
●保留包含指定内容的日志行。支持根据指定的 tag 和日志内容筛选日志行。例如，若业务场景只关注包含“orderStatus=3”的日志行，可以设置仅保留这些日志。
●删除包含指定内容的日志行。支持根据指定的 tag 和日志内容删除日志行。例如，若业务场景不关注包含“type=Plugin”的日志行，可以将其删除，减少无关信息干扰。
●对过长的日志内容进行截取。支持对指定 tag 和日志内容的日志行，在指定位置进行截取。例如，当后台回包数据量较大时，可截取日志中的关键信息，避免过长的内容导致大模型忽略重要细节。
●对指定内容进行去重处理。支持对指定 tag 和日志内容的日志行进行全局去重，或仅保留相邻重复日志的第一条。部分业务场景关注日志时序，适合保留顺序；不关注时序的场景则可进行全局去重，减少冗余。
●将指定内容转换为 JSON 格式。支持对指定 tag 和日志内容的日志行进行 JSON 转换，使日志内容结构化，便于模型理解和分析。

（2）模型难以理解日志 —> 构建日志分析知识库
在实际应用中，大模型在理解日志内容时面临诸多挑战，类似于非当前业务的同事单独查看日志文件时难以迅速把握关键信息的困境。日志内容因业务场景的差异而千变万化，不同业务模块往往定义了各自独特的日志格式、错误码含义以及业务命名。这些差异导致日志信息高度依赖具体的业务流程和背景知识，形成了显著的业务知识壁垒。这使得大模型在没有充分业务背景知识的情况下，难以准确解析和定位日志中的关键信息。
	为了解决这一难题，我们借助iwiki平台构建动态知识库，如图所示。iwiki平台支持知识库的版本迭代与实时更新，能够将最新日志解析规则等内容及时同步到知识库中。通过持续维护和完善知识库，不仅降低了业务知识壁垒，还为大模型提供了丰富且准确的业务背景知识，提升其对日志内容的理解能力。
知识库模板可参考：https://iwiki.woa.com/p/4014601292  。其中，我们使用符号“>>>”来分隔标题与内容，便于快速提取对应信息。
    对于包含大量错误码的业务场景，可以将错误码及其含义存储在映射表中。当日志中出现对应错误码时，只需在映射表中查询并返回其含义，避免将大量错误码直接填充到prompt中，如下图所示。

三、整体流程
	根据上述优化思路，整体实现流程如图所示。为提升用户体验和问题定位效率，我们将日志分析工具集成至Aisee平台及企业微信机器人，提供便捷高效的日志分析服务。用户可通过不同入口发起日志分析请求，系统会根据用户意图，自动获取相关业务知识，对原始日志数据进行清洗与结构化处理。随后，系统将清洗后的日志数据和业务背景知识进行整合，动态生成高质量的Prompt，作为输入指令引导大语言模型进行深度理解与推理。大模型结合业务知识和日志内容，输出日志分析结果，辅助快速定位问题。


四、使用日志分析系统
1、Aisee平台使用日志分析系统
●支持一键分析日志 以及 指定场景分析日志。


●分析结果：


2、企微机器人：应用宝终端AI助手 
●机器人使用教程：https://iwiki.woa.com/p/4014613423 
使用流程：输入相应参数后，机器人完成分析并返回包含日志分析结果的 Markdown 文件，打开即可查看日志分析内容。


●返回文件内容（日志分析结果）


五、总结
基于动态模版与大模型协同的日志定位系统构建了涵盖动态知识库构建、日志清洗、Prompt封装及大模型推理的完整闭环流程，有效提升了问题定位的效率。通过集成于Aisee平台和企业微信机器人，该系统为用户和研发团队提供了便捷高效的日志分析入口，支持多场景应用，满足了多样化的业务需求。基于现有方案，大家可以结合实际业务，进一步拓展功能与应用场景，挖掘更多创新玩法。展望未来，随着业务知识库的持续丰富和大模型能力的不断提升，日志分析系统将进一步增强分析日志的能力，助力实现更精准高效的问题定位。


