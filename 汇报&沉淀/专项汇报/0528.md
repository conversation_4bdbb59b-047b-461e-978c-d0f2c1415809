根据使用完善一些能力

【进展】
1. 优化日志分析工具。支持归一化去重。增加分析全部日志文件的选项
2. 增加埋点。包含调用次数，评价反馈。
3. 优化企微机器人。完善错误反馈机制。支持字段代码块输入，减少iwiki编辑出现的格式问题。利用临时目录，自动清理下载的日志，节省存储空间。
4. 配合aisee联调。支持实时拉取iwiki配置分析日志。
5. 协助场景Prompt补充。目前云游、视频、会员、



# 5.21
1. crash场景的增加
2. 调研灯塔上报
3. 发布版本新包，配合联调
4. 归一化去重

# 5.22
1. 上报
2. crash场景的测评
3. 联调
4. 增加video错误码
5. tag支持正则


# 5.23
iwiki格式
如果与正则匹配冲突，需要转义字符
如 ()  -> \(\)

弹窗场景 需要过滤全部的日志文件
- todo 脚本预处理 输出弹窗次数

注意：日志格式化，是把前面的去掉了，只留下 VideoViewComponent了

模型过载反馈，待解决
1. 根据使用过程中，常见的问题，完善错误反馈机制，（输入不是英文，{}冲突）
遇到格式规范检查，
   - {{}}
   - 中英文书写
2. 配合aisee
   - 上报
   - 联调
3. 增加分析全部日志文件的选项

# 5.26
1. daemon场景bug修复
2. 配合场景完善
3. 兼容iwiki书写错误：支持代码块输入
4. iwiki输入格式异常、格式不对 抛出提示

# 5.27
临时文件机制，解压的日志都删掉

# 5.28
增加网络错误场景
