活动页-过滤日志为空，重新匹配
处理过长日志，将日志行无用的部分截除，如发货失败请求的后半部分

将日志过滤、日志选取的逻辑提出，更通用

优化日志过滤逻辑，小于多少行回溯

设计baseAgent、basePrompt供其他场景使用

优化整体代码逻辑，


完善活动页场景（发货失败、领取失败、点击没响应）的知识，并进行了测评和优化。
优化整体代码结构。提取日志文件选取与日志过滤逻辑，以支持 日志不足 回溯。开放可选参数，供其场景进行自定义日志分析。
完善企微机器人交互。支持输入Prompt分析日志。支持保存Prompt，版本迭代更改Prompt。支持用户反馈评价。

1. 完善活动页场景Agent。针对发货失败、领取失败和点击无响应等异常场景，补充完善相关知识库，完成相关测评并进行优化。
2. 优化整体代码结构。重构日志文件选取与日志过滤逻辑，支持“日志不足”情况下的回溯分析。同时开放可选参数，方便不同场景自定义日志分析。
3. 升级企微机器人交互功能。新增支持输入Prompt进行日志分析，支持Prompt的保存与版本迭代管理，并增加用户反馈评价机制。





