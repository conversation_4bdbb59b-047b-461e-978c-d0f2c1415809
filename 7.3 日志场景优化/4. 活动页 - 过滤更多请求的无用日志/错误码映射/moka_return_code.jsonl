{"id": 127, "query": "code-99000", "value": "code=-99000 表示 接口超时"}
{"id": 128, "query": "code=-99001", "value": "code=-99001 表示 不支持 SSR 环境"}
{"id": 129, "query": "code=-99002", "value": "code=-99002 表示 非应用宝环境访问"}
{"id": 130, "query": "code=-99003", "value": "code=-99003 表示 应用宝版本过低"}
{"id": 131, "query": "code=-99004", "value": "code=-99004 表示 网络异常"}
{"id": 132, "query": "code=-99999", "value": "code=-99999 表示 未知错误"}
{"id": 133, "query": "code=100005", "value": "code=100005 表示 无效的 taskID 参数"}
{"id": 134, "query": "code=403", "value": "code=403 表示 鉴权失败"}
{"id": 135, "query": "code=0", "value": "code=0 表示 成功"}
{"id": 136, "query": "code=10011", "value": "code=10011 表示 非活动时间"}
{"id": 137, "query": "code=9901002", "value": "code=9901002 表示 登录态失效"}
{"id": 138, "query": "code=0", "value": "code=0 表示 成功"}
{"id": 139, "query": "code=-99006", "value": "code=-99006 表示 账号存在风险且验证码校验失败"}
{"id": 140, "query": "code=-99007", "value": "code=-99007 表示 账号存在风险取消图形验证码"}
{"id": 141, "query": "code=1106004", "value": "code=1106004 表示 【中台抽奖】抽奖已经结束"}
{"id": 142, "query": "code=10106004", "value": "code=10106004 表示 【中台抽奖】抽奖已经结束"}
{"id": 143, "query": "code=1106003", "value": "code=1106003 表示 【中台抽奖】抽奖请求参数有误"}
{"id": 144, "query": "code=10106003", "value": "code=10106003 表示 【中台抽奖】抽奖请求参数有误"}
{"id": 145, "query": "code=1106001", "value": "code=1106001 表示 【中台抽奖】抽奖还未开始"}
{"id": 146, "query": "code=10106001", "value": "code=10106001 表示 【中台抽奖】抽奖还未开始"}
{"id": 147, "query": "code=14103001", "value": "code=14103001 表示 【中台多物品领取】领取时间还未开始"}
{"id": 148, "query": "code=0", "value": "code=0 表示 终端通道调用成功"}
{"id": 149, "query": "code=1001002", "value": "code=1001002 表示 Exec异常-redis 操作失败"}
{"id": 150, "query": "code=1001003", "value": "code=1001003 表示 Exec异常-配置初始化失败"}
{"id": 151, "query": "code=1001004", "value": "code=1001004 表示 Exec异常-无极数据类型断言错误"}
{"id": 152, "query": "code=1001005", "value": "code=1001005 表示 Exec异常-未实现该查询参数"}
{"id": 153, "query": "code=1001006", "value": "code=1001006 表示 Exec异常-未知的组件类型"}
{"id": 154, "query": "code=1001007", "value": "code=1001007 表示 Exec异常-未知的寻址方式"}
{"id": 155, "query": "code=1001008", "value": "code=1001008 表示 Exec异常-反序列化失败"}
{"id": 156, "query": "code=1001009", "value": "code=1001009 表示 Exec异常-序列化失败"}
{"id": 157, "query": "code=1001010", "value": "code=1001010 表示 Exec异常-类型转换失败"}
{"id": 158, "query": "code=1001011", "value": "code=1001011 表示 Exec异常-一般情况下不可达的逻辑分支抛出的错误"}
{"id": 159, "query": "code=1002001", "value": "code=1002001 表示 Exec异常-非法实例id"}
{"id": 160, "query": "code=1002002", "value": "code=1002002 表示 Exec异常-非法组件实例id"}
{"id": 161, "query": "code=1002003", "value": "code=1002003 表示 Exec异常-非法方法名"}
{"id": 162, "query": "code=1002004", "value": "code=1002004 表示 Exec异常-反序列化Instance失败"}
{"id": 163, "query": "code=1002006", "value": "code=1002006 表示 Exec异常-访问UnionPlus失败"}
{"id": 164, "query": "code=1002007", "value": "code=1002007 表示 Exec异常-Qualifier表达式参数个数不匹配"}
{"id": 165, "query": "code=1002008", "value": "code=1002008 表示 Exec异常-Qualifier表达式参数名不匹配"}
{"id": 166, "query": "code=1002009", "value": "code=1002009 表示 Exec异常-活动实例已被删除"}
{"id": 167, "query": "code=1002010", "value": "code=1002010 表示 Exec异常-无效的图id"}
{"id": 168, "query": "code=1002011", "value": "code=1002011 表示 Exec异常-安全加固的接口没配条件"}
{"id": 169, "query": "code=1002012", "value": "code=1002012 表示 判断为黑产用户"}
{"id": 170, "query": "code=1004001", "value": "code=1004001 表示 Exec异常-未知的被调服务协议"}
{"id": 171, "query": "code=1004002", "value": "code=1004002 表示 Exec异常-未知的接口类型"}
{"id": 172, "query": "code=1004003", "value": "code=1004003 表示 Exec异常-反序列化定制化adapter的失败"}
{"id": 173, "query": "code=1004004", "value": "code=1004004 表示 Exec异常-序列化定制化adapter的失败"}
{"id": 174, "query": "code=1002010", "value": "code=1002010 表示 ExecGraph异常-无效的图id"}
{"id": 175, "query": "code=1002001", "value": "code=1002001 表示 ExecGraph异常-非法实例id"}
{"id": 176, "query": "code=1002004", "value": "code=1002004 表示 ExecGraph异常-反序列化Instance失败"}
{"id": 177, "query": "code=1002009", "value": "code=1002009 表示 ExecGraph异常-活动实例已被删除"}
{"id": 178, "query": "code=9901002", "value": "code=9901002 表示 ExecGraph异常-未登录"}
{"id": 179, "query": "code=1001008", "value": "code=1001008 表示 ExecGraph异常-反序列化失败"}
{"id": 180, "query": "code=1006001", "value": "code=1006001 表示 ExecGraph异常-dsl脚本返回异常"}
{"id": 181, "query": "code=1006002", "value": "code=1006002 表示 ExecGraph异常-初始化GEngine失败"}
{"id": 182, "query": "code=1001010", "value": "code=1001010 表示 ExecGraph异常-类型转换失败"}
{"id": 183, "query": "code=1002001", "value": "code=1002001 表示 GetActivityConfig异常-非法实例id"}
{"id": 184, "query": "code=1002004", "value": "code=1002004 表示 GetActivityConfig异常-反序列化Instance失败"}
{"id": 185, "query": "code=1002009", "value": "code=1002009 表示 GetActivityConfig异常-活动实例已被删除"}
{"id": 186, "query": "code=9901003", "value": "code=9901003 表示 GetActivityConfig异常-类型转化失败"}
{"id": 187, "query": "code=9101001", "value": "code=9101001 表示 查询签到列表-配置初始化失败"}
{"id": 188, "query": "code=9101001", "value": "code=9101001 表示 签到-配置初始化失败"}
{"id": 189, "query": "code=9101001", "value": "code=9101001 表示 满签-配置初始化失败"}
{"id": 190, "query": "code=9101001", "value": "code=9101001 表示 补签-配置初始化失败"}
{"id": 191, "query": "code=9101002", "value": "code=9101002 表示 查询签到列表-请求参数错误"}
{"id": 192, "query": "code=9101002", "value": "code=9101002 表示 签到-请求参数错误"}
{"id": 193, "query": "code=9101002", "value": "code=9101002 表示 满签-请求参数错误"}
{"id": 194, "query": "code=9101002", "value": "code=9101002 表示 补签-请求参数错误"}
{"id": 195, "query": "code=9101003", "value": "code=9101003 表示 签到-重入性校验，请求已被执行"}
{"id": 196, "query": "code=9101003", "value": "code=9101003 表示 满签-重入性校验，请求已被执行"}
{"id": 197, "query": "code=9101003", "value": "code=9101003 表示 补签-重入性校验，请求已被执行"}
{"id": 198, "query": "code=9101004", "value": "code=9101004 表示 签到-悬挂校验，二阶段已执行"}
{"id": 199, "query": "code=9101004", "value": "code=9101004 表示 满签-悬挂校验，二阶段已执行"}
{"id": 200, "query": "code=9101004", "value": "code=9101004 表示 补签-悬挂校验，二阶段已执行"}
{"id": 201, "query": "code=9102001", "value": "code=9102001 表示 签到-数据解析失败"}
{"id": 202, "query": "code=9102001", "value": "code=9102001 表示 满签-数据解析失败"}
{"id": 203, "query": "code=9102001", "value": "code=9102001 表示 补签-数据解析失败"}
{"id": 204, "query": "code=9102003", "value": "code=9102003 表示 签到-签到实例不存在"}
{"id": 205, "query": "code=9102003", "value": "code=9102003 表示 满签-签到实例不存在"}
{"id": 206, "query": "code=9102003", "value": "code=9102003 表示 补签-签到实例不存在"}
{"id": 207, "query": "code=9103001", "value": "code=9103001 表示 签到-签到未开始"}
{"id": 208, "query": "code=9103001", "value": "code=9103001 表示 满签-签到未开始"}
{"id": 209, "query": "code=9103001", "value": "code=9103001 表示 补签-签到未开始"}
{"id": 210, "query": "code=9103002", "value": "code=9103002 表示 签到-签到已结束"}
{"id": 211, "query": "code=9103002", "value": "code=9103002 表示 满签-签到已结束"}
{"id": 212, "query": "code=9103002", "value": "code=9103002 表示 补签-签到已结束"}
{"id": 213, "query": "code=9103003", "value": "code=9103003 表示 签到-签到已暂停"}
{"id": 214, "query": "code=9103003", "value": "code=9103003 表示 满签-签到已暂停"}
{"id": 215, "query": "code=9103003", "value": "code=9103003 表示 补签-签到已暂停"}
{"id": 216, "query": "code=9103004", "value": "code=9103004 表示 签到-已经签到过"}
{"id": 217, "query": "code=9103004", "value": "code=9103004 表示 满签-已经签到过"}
{"id": 218, "query": "code=9103004", "value": "code=9103004 表示 补签-已经签到过"}
{"id": 219, "query": "code=9103005", "value": "code=9103005 表示 签到-未满签"}
{"id": 220, "query": "code=9103005", "value": "code=9103005 表示 满签-未满签"}
{"id": 221, "query": "code=9103005", "value": "code=9103005 表示 补签-未满签"}
{"id": 222, "query": "code=9104001", "value": "code=9104001 表示 签到-物品服务请求失败"}
{"id": 223, "query": "code=9104001", "value": "code=9104001 表示 满签-物品服务请求失败"}
{"id": 224, "query": "code=9104001", "value": "code=9104001 表示 补签-物品服务请求失败"}
{"id": 225, "query": "code=6803001", "value": "code=6803001 表示 签到-奖励已发完"}
{"id": 226, "query": "code=6803001", "value": "code=6803001 表示 满签-奖励已发完"}
{"id": 227, "query": "code=6803001", "value": "code=6803001 表示 补签-奖励已发完"}
{"id": 228, "query": "code=6803002", "value": "code=6803002 表示 签到-奖励已领取"}
{"id": 229, "query": "code=6803002", "value": "code=6803002 表示 满签-奖励已领取"}
{"id": 230, "query": "code=6803002", "value": "code=6803002 表示 补签-奖励已领取"}
{"id": 231, "query": "code=10106002", "value": "code=10106002 表示 抽奖流程错误"}
{"id": 232, "query": "code=1106002", "value": "code=1106002 表示 抽奖流程错误"}
{"id": 233, "query": "code=999", "value": "code=999 表示 接口异常"}
{"id": 234, "query": "code=10101003", "value": "code=10101003 表示 拉取用户登录态信息失败"}
{"id": 235, "query": "code=9901002", "value": "code=9901002 表示 登录态过期"}
{"id": 236, "query": "code=1401004", "value": "code=1401004 表示 请求已执行过"}
{"id": 237, "query": "code=1402007", "value": "code=1402007 表示 积分不足"}
{"id": 238, "query": "code=10106003", "value": "code=10106003 表示 抽奖参数异常"}
{"id": 239, "query": "code=10106001", "value": "code=10106001 表示 抽奖未开始"}
{"id": 240, "query": "code=10106004", "value": "code=10106004 表示 抽奖已结束"}
{"id": 241, "query": "code=10106005", "value": "code=10106005 表示 连抽模式下需要重新抽奖"}
{"id": 242, "query": "code=10102005", "value": "code=10102005 表示 库存不足"}
{"id": 243, "query": "code=10102004", "value": "code=10102004 表示 数据不一致"}
{"id": 244, "query": "code=10102003", "value": "code=10102003 表示 请求超限"}
{"id": 245, "query": "code=10102002", "value": "code=10102002 表示 数据解析失败"}
{"id": 246, "query": "code=10102001", "value": "code=10102001 表示 请求参数错误"}
{"id": 247, "query": "code=10101001", "value": "code=10101001 表示 配置初始化失败"}
{"id": 248, "query": "code=10101002", "value": "code=10101002 表示 Redis 操作失败"}
{"id": 249, "query": "code=10101004", "value": "code=10101004 表示 JSON序列化失败"}
{"id": 250, "query": "code=10101005", "value": "code=10101005 表示 JSON反序列化失败"}
{"id": 251, "query": "code=10101006", "value": "code=10101006 表示 拿延时队列锁失败"}
{"id": 252, "query": "code=10101007", "value": "code=10101007 表示 无效的订单id"}
{"id": 253, "query": "code=10603001", "value": "code=10603001 表示 领取未开始"}
{"id": 254, "query": "code=10603002", "value": "code=10603002 表示 领取已结束"}
{"id": 255, "query": "code=6803001", "value": "code=6803001 表示 领取-奖励已发完"}
{"id": 256, "query": "code=6803002", "value": "code=6803002 表示 领取-已经领取过奖励"}
{"id": 257, "query": "code=10602003", "value": "code=10602003 表示 领取-区服信息有误"}
{"id": 258, "query": "code=10601001", "value": "code=10601001 表示 领取-配置初始化失败"}
{"id": 259, "query": "code=10601002", "value": "code=10601002 表示 请求参数错误"}
{"id": 260, "query": "code=10601003", "value": "code=10601003 表示 TCC重入性校验"}
{"id": 261, "query": "code=10601004", "value": "code=10601004 表示 TCC悬挂错误码"}
{"id": 262, "query": "code=10601005", "value": "code=10601005 表示 待提交用户记录不存在"}
{"id": 263, "query": "code=10602001", "value": "code=10602001 表示 数据解析失败"}
{"id": 264, "query": "code=10602002", "value": "code=10602002 表示 领取实例不存在"}
{"id": 265, "query": "code=10602004", "value": "code=10602004 表示 领礼包缺区服信息"}
{"id": 266, "query": "code=10602005", "value": "code=10602005 表示 领q币缺qq号"}
{"id": 267, "query": "code=14101001", "value": "code=14101001 表示 配置初始化失败"}
{"id": 268, "query": "code=14101002", "value": "code=14101002 表示 请求参数错误"}
{"id": 269, "query": "code=14101003", "value": "code=14101003 表示 TCC重入性校验"}
{"id": 270, "query": "code=14101004", "value": "code=14101004 表示 TCC悬挂错误码"}
{"id": 271, "query": "code=14101005", "value": "code=14101005 表示 待提交用户记录不存在"}
{"id": 272, "query": "code=14102001", "value": "code=14102001 表示 数据解析失败"}
{"id": 273, "query": "code=14102002", "value": "code=14102002 表示 领取实例不存在"}
{"id": 274, "query": "code=14102004", "value": "code=14102004 表示 领礼包缺区服信息"}
{"id": 275, "query": "code=14102005", "value": "code=14102005 表示 领q币缺qq号"}
{"id": 276, "query": "code=14103001", "value": "code=14103001 表示 领取未开始"}
{"id": 277, "query": "code=14103002", "value": "code=14103002 表示 领取已结束"}
{"id": 278, "query": "code=11101001", "value": "code=11101001 表示 榜单数据源获取失败"}
{"id": 279, "query": "code=11101003", "value": "code=11101003 表示 用户未参与"}
{"id": 280, "query": "code=11101004", "value": "code=11101004 表示 榜单数据源反序列化失败"}
{"id": 281, "query": "code=11101005", "value": "code=11101005 表示 获取榜单列表长度失败"}
{"id": 282, "query": "code=11101006", "value": "code=11101006 表示 访问IEG接口获取用户游戏数据失败"}
{"id": 283, "query": "code=11101007", "value": "code=11101007 表示 榜单未开始"}
{"id": 284, "query": "code=11101008", "value": "code=11101008 表示 榜单已结束"}
{"id": 285, "query": "code=11101009", "value": "code=11101009 表示 用户参与的区服不在该榜单限定参与的区服内"}
{"id": 286, "query": "code=11101010", "value": "code=11101010 表示 参与榜单参数不合法"}
{"id": 287, "query": "code=0", "value": "code=0 表示 普通 HTTP 请求成功"}
{"id": 288, "query": "code=-1", "value": "code=-1 表示 预约失败"}
{"id": 289, "query": "code=-1", "value": "code=-1 表示 引导关注公众号失败（获取二维码场景号）"}
{"id": 290, "query": "code=200028", "value": "code=200028 表示 组队-标题或描述不可用"}
{"id": 291, "query": "code=200016", "value": "code=200016 表示 组队-不允许退队"}
{"id": 292, "query": "code=0", "value": "code=0 表示 获取活动信息成功"}
{"id": 293, "query": "code=0", "value": "code=0 表示 查询领取信息成功"}
{"id": 294, "query": "code=0", "value": "code=0 表示 获取领取信息成功"}
{"id": 295, "query": "code=0", "value": "code=0 表示 助力成功"}
{"id": 296, "query": "code=0", "value": "code=0 表示 加载助力奖励列表成功"}
{"id": 297, "query": "code=0", "value": "code=0 表示 中台助力详情获取成功"}
{"id": 298, "query": "code=200007", "value": "code=200007 表示 助力活动未开始"}
{"id": 299, "query": "code=200005", "value": "code=200005 表示 助力活动已结束"}
{"id": 300, "query": "code=0", "value": "code=0 表示 发起助力成功"}
{"id": 301, "query": "code=0", "value": "code=0 表示 中台助力领取成功"}
{"id": 302, "query": "code=200002", "value": "code=200002 表示 助力-中台助力达最大次数"}
{"id": 303, "query": "code=101", "value": "code=101 表示 系统错误"}
{"id": 304, "query": "code=101", "value": "code=101 表示 系统错误"}
{"id": 305, "query": "code=100001", "value": "code=100001 表示 系统错误"}
{"id": 306, "query": "code=100001", "value": "code=100001 表示 系统错误"}
{"id": 307, "query": "code=100014", "value": "code=100014 表示 系统异常"}
{"id": 308, "query": "code=100014", "value": "code=100014 表示 系统错误"}
{"id": 309, "query": "code=100012", "value": "code=100012 表示 系统错误"}
{"id": 310, "query": "code=100012", "value": "code=100012 表示 系统错误"}
{"id": 311, "query": "code=111", "value": "code=111 表示 系统错误"}
{"id": 312, "query": "code=111", "value": "code=111 表示 系统错误"}
{"id": 313, "query": "code=141", "value": "code=141 表示 系统错误"}
{"id": 314, "query": "code=141", "value": "code=141 表示 系统错误"}
{"id": 315, "query": "code=100014", "value": "code=100014 表示 系统错误"}
{"id": 316, "query": "code=200022", "value": "code=200022 表示 领取奖励-领取失败（一般都是没有库存）"}
{"id": 318, "query": "code=-99000", "value": "code=-99000 表示 接口超时"}
{"id": 319, "query": "code=200000", "value": "code=200000 表示 发起助力-用户接口请求频率过高"}
{"id": 320, "query": "code=200000", "value": "code=200000 表示 助力-用户接口请求频率过高"}
{"id": 321, "query": "code=141", "value": "code=141 表示 未知服务异常"}
{"id": 322, "query": "code=141", "value": "code=141 表示 141"}
{"id": 323, "query": "code=-1", "value": "code=-1 表示 无数据返回"}
{"id": 324, "query": "code=-1", "value": "code=-1 表示 无数据返回"}
{"id": 325, "query": "code=200004", "value": "code=200004 表示 助力-被助力达最大次数"}
{"id": 326, "query": "code=51", "value": "code=51 表示 参数错误"}
{"id": 327, "query": "code=51", "value": "code=51 表示 参数错误"}
{"id": 328, "query": "code=21", "value": "code=21 表示 tcp client transport ReadFrame"}
{"id": 329, "query": "code=21", "value": "code=21 表示 tcp client transport ReadFrame"}
{"id": 330, "query": "code=9901001", "value": "code=9901001 表示 access to Redis failed"}
{"id": 331, "query": "code=9901001", "value": "code=9901001 表示 access to Redis failed"}
{"id": 332, "query": "code=200003", "value": "code=200003 表示 助力-已助力过"}
{"id": 333, "query": "code=0", "value": "code=0 表示 调用成功"}
{"id": 334, "query": "code=200008", "value": "code=200008 表示 队伍名称审核不通过"}
{"id": 335, "query": "code=11002", "value": "code=11002 表示 赛事排行榜-重复报名"}
{"id": 336, "query": "code=200028", "value": "code=200028 表示 达到个人限量"}
{"id": 337, "query": "code=200029", "value": "code=200029 表示 已达总限量"}
{"id": 338, "query": "code=200003", "value": "code=200003 表示 创建队伍时间已截止"}
{"id": 339, "query": "code=10602006", "value": "code=10602006 表示 QB单Q限制1"}
{"id": 340, "query": "code=200040", "value": "code=200040 表示 QB单Q被限制"}
{"id": 341, "query": "code=200002", "value": "code=200002 表示 用户助力受限"}
{"id": 342, "query": "code=400010", "value": "code=400010 表示 注册条件不满足"}
{"id": 343, "query": "code=400002", "value": "code=400002 表示 登录类型异常"}
{"id": 344, "query": "code=400003", "value": "code=400003 表示 登录appid异常"}
{"id": 345, "query": "code=400004", "value": "code=400004 表示 获取会员信息失败"}
{"id": 346, "query": "code=400005", "value": "code=400005 表示 获取授权失败"}
{"id": 347, "query": "code=400007", "value": "code=400007 表示 用户未授权"}
{"id": 348, "query": "code=400008", "value": "code=400008 表示 用户未激活"}
{"id": 349, "query": "code=400009", "value": "code=400009 表示 获取用户注册信息失败"}
{"id": 350, "query": "code=500002", "value": "code=500002 表示 助力周期异常"}
{"id": 351, "query": "code=500023", "value": "code=500023 表示 获取用户信息失败"}
{"id": 352, "query": "code=300000", "value": "code=300000 表示 操作过于频繁"}
{"id": 353, "query": "code=300001", "value": "code=300001 表示 在线发货失败"}
{"id": 354, "query": "code=300002", "value": "code=300002 表示 在线发货物品已超限"}
{"id": 355, "query": "code=300003", "value": "code=300003 表示 获取领取下载注册积分记录失败"}
{"id": 356, "query": "code=300003", "value": "code=300003 表示 获取领取下载注册积分记录失败"}
{"id": 357, "query": "code=300004", "value": "code=300004 表示 获取领取下载注册积分记录失败"}
{"id": 358, "query": "code=300006", "value": "code=300006 表示 获取客态绑定的主态信息失败"}
{"id": 359, "query": "code=300006", "value": "code=300006 表示 获取客态绑定的主态信息失败"}
{"id": 360, "query": "code=300008", "value": "code=300008 表示 获取领取下载注册积分记录反序列化失败"}
{"id": 361, "query": "code=300008", "value": "code=300008 表示 获取领取下载注册积分记录反序列化失败"}
{"id": 362, "query": "code=300011", "value": "code=300011 表示 获取物品信息失败"}
{"id": 363, "query": "code=300012", "value": "code=300012 表示 写限量失败"}
{"id": 364, "query": "code=200028", "value": "code=200028 表示 总限量超限"}
{"id": 365, "query": "code=200029", "value": "code=200029 表示 个人限量超限"}
{"id": 366, "query": "code=300009", "value": "code=300009 表示 用户已获取过领取下载注册积分"}
{"id": 367, "query": "code=100000", "value": "code=100000 表示 系统错误"}
{"id": 368, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 369, "query": "code=100002", "value": "code=100002 表示 登录类型异常"}
{"id": 370, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 371, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 372, "query": "code=100006", "value": "code=100006 表示 助力记录序列化失败"}
{"id": 373, "query": "code=100007", "value": "code=100007 表示 助力消息序列化失败"}
{"id": 374, "query": "code=100008", "value": "code=100008 表示 助力消息生产失败"}
{"id": 375, "query": "code=100009", "value": "code=100009 表示 获取用户是否助力失败"}
{"id": 376, "query": "code=100010", "value": "code=100010 表示 获取被助力次数失败"}
{"id": 377, "query": "code=100011", "value": "code=100011 表示 获取助力流水失败"}
{"id": 378, "query": "code=100012", "value": "code=100012 表示 获取我的分享码失败"}
{"id": 379, "query": "code=100013", "value": "code=100013 表示 根据shareKey获取分享信息失败"}
{"id": 380, "query": "code=100014", "value": "code=100014 表示 根据openid获取分享信息失败"}
{"id": 381, "query": "code=100015", "value": "code=100015 表示 获取昵称和头像失败"}
{"id": 382, "query": "code=100016", "value": "code=100016 表示 分层领取消息序列化失败"}
{"id": 383, "query": "code=100017", "value": "code=100017 表示 分层领取消息生产失败"}
{"id": 384, "query": "code=100018", "value": "code=100018 表示 任务池反序列化失败"}
{"id": 385, "query": "code=100021", "value": "code=100021 表示 保存分享信息失败"}
{"id": 386, "query": "code=100022", "value": "code=100022 表示 分层奖励记录反序列化失败"}
{"id": 387, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 388, "query": "code=200000", "value": "code=200000 表示 进入时-操作过于频繁"}
{"id": 389, "query": "code=200001", "value": "code=200001 表示 进入时-助力分享无效"}
{"id": 390, "query": "code=200005", "value": "code=200005 表示 进入时-助力活动已结束"}
{"id": 391, "query": "code=200006", "value": "code=200006 表示 进入时-助力活动已下线"}
{"id": 392, "query": "code=200007", "value": "code=200007 表示 进入时-助力活动未开始"}
{"id": 393, "query": "code=200008", "value": "code=200008 表示 助力周期不合法"}
{"id": 394, "query": "code=200009", "value": "code=200009 表示 被助力周期不合法"}
{"id": 395, "query": "code=200010", "value": "code=200010 表示 不能为自己助力"}
{"id": 396, "query": "code=200013", "value": "code=200013 表示 TCC事务悬挂"}
{"id": 397, "query": "code=200014", "value": "code=200014 表示 重入限制"}
{"id": 398, "query": "code=200015", "value": "code=200015 表示 事务分支ID不存在"}
{"id": 399, "query": "code=200016", "value": "code=200016 表示 分层奖励物品不存在"}
{"id": 400, "query": "code=200017", "value": "code=200017 表示 分层奖励配置不匹配"}
{"id": 401, "query": "code=200018", "value": "code=200018 表示 分层奖励记录不存在"}
{"id": 402, "query": "code=200019", "value": "code=200019 表示 空回滚"}
{"id": 403, "query": "code=200022", "value": "code=200022 表示 执行try失败"}
{"id": 404, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 405, "query": "code=100000", "value": "code=100000 表示 系统错误"}
{"id": 406, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 407, "query": "code=100002", "value": "code=100002 表示 登录类型异常"}
{"id": 408, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 409, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 410, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 411, "query": "code=100006", "value": "code=100006 表示 助力记录序列化失败"}
{"id": 412, "query": "code=100007", "value": "code=100007 表示 助力消息序列化失败"}
{"id": 413, "query": "code=100008", "value": "code=100008 表示 助力消息生产失败"}
{"id": 414, "query": "code=100009", "value": "code=100009 表示 获取用户是否助力失败"}
{"id": 415, "query": "code=100010", "value": "code=100010 表示 获取被助力次数失败"}
{"id": 416, "query": "code=100011", "value": "code=100011 表示 获取助力流水失败"}
{"id": 417, "query": "code=100012", "value": "code=100012 表示 获取我的分享码失败"}
{"id": 418, "query": "code=100013", "value": "code=100013 表示 根据shareKey获取分享信息失败"}
{"id": 419, "query": "code=100014", "value": "code=100014 表示 根据openid获取分享信息失败"}
{"id": 420, "query": "code=100015", "value": "code=100015 表示 获取昵称和头像失败"}
{"id": 421, "query": "code=100016", "value": "code=100016 表示 分层领取消息序列化失败"}
{"id": 422, "query": "code=100017", "value": "code=100017 表示 分层领取消息生产失败"}
{"id": 423, "query": "code=100018", "value": "code=100018 表示 任务池反序列化失败"}
{"id": 424, "query": "code=100019", "value": "code=100019 表示 发起助力消息序列化失败"}
{"id": 425, "query": "code=100020", "value": "code=100020 表示 发起助力消息生产失败"}
{"id": 426, "query": "code=100021", "value": "code=100021 表示 保存分享信息失败"}
{"id": 427, "query": "code=100022", "value": "code=100022 表示 分层奖励记录反序列化失败"}
{"id": 428, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 429, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 430, "query": "code=200001", "value": "code=200001 表示 发起助力-助力分享无效"}
{"id": 431, "query": "code=200005", "value": "code=200005 表示 发起助力-助力活动已结束"}
{"id": 432, "query": "code=200006", "value": "code=200006 表示 发起助力-助力活动已下线"}
{"id": 433, "query": "code=200007", "value": "code=200007 表示 发起助力-助力活动未开始"}
{"id": 434, "query": "code=200008", "value": "code=200008 表示 助力周期不合法"}
{"id": 435, "query": "code=200009", "value": "code=200009 表示 被助力周期不合法"}
{"id": 436, "query": "code=200013", "value": "code=200013 表示 TCC事务悬挂"}
{"id": 437, "query": "code=200014", "value": "code=200014 表示 重入限制"}
{"id": 438, "query": "code=200015", "value": "code=200015 表示 事务分支ID不存在"}
{"id": 439, "query": "code=200016", "value": "code=200016 表示 分层奖励物品不存在"}
{"id": 440, "query": "code=200017", "value": "code=200017 表示 分层奖励配置不匹配"}
{"id": 441, "query": "code=200018", "value": "code=200018 表示 分层奖励记录不存在"}
{"id": 442, "query": "code=200019", "value": "code=200019 表示 空回滚"}
{"id": 443, "query": "code=200022", "value": "code=200022 表示 执行try失败"}
{"id": 444, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 445, "query": "code=100000", "value": "code=100000 表示 系统错误"}
{"id": 446, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 447, "query": "code=100002", "value": "code=100002 表示 登录类型异常"}
{"id": 448, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 449, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 450, "query": "code=100005", "value": "code=100005 表示 助力-助力失败"}
{"id": 451, "query": "code=100006", "value": "code=100006 表示 助力记录序列化失败"}
{"id": 452, "query": "code=100007", "value": "code=100007 表示 助力消息序列化失败"}
{"id": 453, "query": "code=100008", "value": "code=100008 表示 助力消息生产失败"}
{"id": 454, "query": "code=100009", "value": "code=100009 表示 获取用户是否助力失败"}
{"id": 455, "query": "code=100010", "value": "code=100010 表示 获取被助力次数失败"}
{"id": 456, "query": "code=100011", "value": "code=100011 表示 获取助力流水失败"}
{"id": 457, "query": "code=100012", "value": "code=100012 表示 获取我的分享码失败"}
{"id": 458, "query": "code=100013", "value": "code=100013 表示 根据shareKey获取分享信息失败"}
{"id": 459, "query": "code=100014", "value": "code=100014 表示 根据openid获取分享信息失败"}
{"id": 460, "query": "code=100015", "value": "code=100015 表示 获取昵称和头像失败"}
{"id": 461, "query": "code=100016", "value": "code=100016 表示 分层领取消息序列化失败"}
{"id": 462, "query": "code=100017", "value": "code=100017 表示 分层领取消息生产失败"}
{"id": 463, "query": "code=100018", "value": "code=100018 表示 任务池反序列化失败"}
{"id": 464, "query": "code=100019", "value": "code=100019 表示 发起助力消息序列化失败"}
{"id": 465, "query": "code=100020", "value": "code=100020 表示 发起助力消息生产失败"}
{"id": 466, "query": "code=100021", "value": "code=100021 表示 保存分享信息失败"}
{"id": 467, "query": "code=100022", "value": "code=100022 表示 分层奖励记录反序列化失败"}
{"id": 468, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 469, "query": "code=200001", "value": "code=200001 表示 助力分享无效"}
{"id": 470, "query": "code=200005", "value": "code=200005 表示 助力-助力活动已结束"}
{"id": 471, "query": "code=200006", "value": "code=200006 表示 助力-助力活动已下线"}
{"id": 472, "query": "code=200007", "value": "code=200007 表示 助力-助力活动未开始"}
{"id": 473, "query": "code=200008", "value": "code=200008 表示 助力周期不合法"}
{"id": 474, "query": "code=200009", "value": "code=200009 表示 被助力周期不合法"}
{"id": 475, "query": "code=200010", "value": "code=200010 表示 助力-不能为自己助力"}
{"id": 476, "query": "code=200011", "value": "code=200011 表示 分层助力领取缺少区服信息"}
{"id": 477, "query": "code=200012", "value": "code=200012 表示 分层助力领取缺少QQ"}
{"id": 478, "query": "code=200013", "value": "code=200013 表示 TCC事务悬挂"}
{"id": 479, "query": "code=200014", "value": "code=200014 表示 重入限制"}
{"id": 480, "query": "code=200015", "value": "code=200015 表示 事务分支ID不存在"}
{"id": 481, "query": "code=200016", "value": "code=200016 表示 分层奖励物品不存在"}
{"id": 482, "query": "code=200017", "value": "code=200017 表示 分层奖励配置不匹配"}
{"id": 483, "query": "code=200018", "value": "code=200018 表示 分层奖励记录不存在"}
{"id": 484, "query": "code=200019", "value": "code=200019 表示 空回滚"}
{"id": 485, "query": "code=200022", "value": "code=200022 表示 执行try失败"}
{"id": 486, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 487, "query": "code=100000", "value": "code=100000 表示 系统错误"}
{"id": 488, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 489, "query": "code=100002", "value": "code=100002 表示 登录类型异常"}
{"id": 490, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 491, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 492, "query": "code=100006", "value": "code=100006 表示 助力记录序列化失败"}
{"id": 493, "query": "code=100007", "value": "code=100007 表示 助力消息序列化失败"}
{"id": 494, "query": "code=100009", "value": "code=100009 表示 获取用户是否助力失败"}
{"id": 495, "query": "code=100010", "value": "code=100010 表示 获取被助力次数失败"}
{"id": 496, "query": "code=100011", "value": "code=100011 表示 获取助力流水失败"}
{"id": 497, "query": "code=100012", "value": "code=100012 表示 获取我的分享码失败"}
{"id": 498, "query": "code=100015", "value": "code=100015 表示 获取昵称和头像失败"}
{"id": 499, "query": "code=100016", "value": "code=100016 表示 分层领取消息序列化失败"}
{"id": 500, "query": "code=100017", "value": "code=100017 表示 分层领取消息生产失败"}
{"id": 501, "query": "code=100018", "value": "code=100018 表示 任务池反序列化失败"}
{"id": 502, "query": "code=100022", "value": "code=100022 表示 分层奖励记录反序列化失败"}
{"id": 503, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 504, "query": "code=200000", "value": "code=200000 表示 操作过于频繁"}
{"id": 505, "query": "code=200001", "value": "code=200001 表示 助力分享无效"}
{"id": 506, "query": "code=200006", "value": "code=200006 表示 助力活动已下线"}
{"id": 507, "query": "code=200008", "value": "code=200008 表示 助力周期不合法"}
{"id": 508, "query": "code=200009", "value": "code=200009 表示 被助力周期不合法"}
{"id": 509, "query": "code=200010", "value": "code=200010 表示 不能为自己助力"}
{"id": 510, "query": "code=200011", "value": "code=200011 表示 分层助力领取缺少区服信息"}
{"id": 511, "query": "code=200012", "value": "code=200012 表示 分层助力领取缺少QQ"}
{"id": 512, "query": "code=200013", "value": "code=200013 表示 TCC事务悬挂"}
{"id": 513, "query": "code=200014", "value": "code=200014 表示 重入限制"}
{"id": 514, "query": "code=200015", "value": "code=200015 表示 事务分支ID不存在"}
{"id": 515, "query": "code=200016", "value": "code=200016 表示 分层奖励物品不存在"}
{"id": 516, "query": "code=200017", "value": "code=200017 表示 分层奖励配置不匹配"}
{"id": 517, "query": "code=200018", "value": "code=200018 表示 分层奖励记录不存在"}
{"id": 518, "query": "code=200019", "value": "code=200019 表示 空回滚"}
{"id": 519, "query": "code=200020", "value": "code=200020 表示 人数不够不可领取"}
{"id": 520, "query": "code=200021", "value": "code=200021 表示 对应奖励已领取"}
{"id": 521, "query": "code=200022", "value": "code=200022 表示 执行try失败"}
{"id": 522, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 523, "query": "code=100000", "value": "code=100000 表示 系统错误"}
{"id": 524, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 525, "query": "code=100002", "value": "code=100002 表示 登录类型异常"}
{"id": 526, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 527, "query": "code=100004", "value": "code=100004 表示 获取redis数据失败"}
{"id": 528, "query": "code=100006", "value": "code=100006 表示 助力记录序列化失败"}
{"id": 529, "query": "code=100007", "value": "code=100007 表示 助力消息序列化失败"}
{"id": 530, "query": "code=100009", "value": "code=100009 表示 获取用户是否助力失败"}
{"id": 531, "query": "code=100010", "value": "code=100010 表示 获取被助力次数失败"}
{"id": 532, "query": "code=100011", "value": "code=100011 表示 获取助力流水失败"}
{"id": 533, "query": "code=100012", "value": "code=100012 表示 获取我的分享码失败"}
{"id": 534, "query": "code=100013", "value": "code=100013 表示 根据shareKey获取分享信息失败"}
{"id": 535, "query": "code=100014", "value": "code=100014 表示 根据openid获取分享信息失败"}
{"id": 536, "query": "code=100015", "value": "code=100015 表示 获取昵称和头像失败"}
{"id": 537, "query": "code=100016", "value": "code=100016 表示 分层领取消息序列化失败"}
{"id": 538, "query": "code=100017", "value": "code=100017 表示 分层领取消息生产失败"}
{"id": 539, "query": "code=100018", "value": "code=100018 表示 任务池反序列化失败"}
{"id": 540, "query": "code=100019", "value": "code=100019 表示 发起助力消息序列化失败"}
{"id": 541, "query": "code=100020", "value": "code=100020 表示 发起助力消息生产失败"}
{"id": 542, "query": "code=100022", "value": "code=100022 表示 分层奖励记录反序列化失败"}
{"id": 543, "query": "code=100023", "value": "code=100023 表示 获取分层奖励领取记录失败"}
{"id": 544, "query": "code=200000", "value": "code=200000 表示 领取奖励-操作过于频繁"}
{"id": 545, "query": "code=200001", "value": "code=200001 表示 领取奖励-助力分享无效"}
{"id": 546, "query": "code=200005", "value": "code=200005 表示 领取奖励-助力活动已结束"}
{"id": 547, "query": "code=200006", "value": "code=200006 表示 领取奖励-助力活动已下线"}
{"id": 548, "query": "code=200007", "value": "code=200007 表示 领取奖励-助力活动未开始"}
{"id": 549, "query": "code=200008", "value": "code=200008 表示 助力周期不合法"}
{"id": 550, "query": "code=200009", "value": "code=200009 表示 被助力周期不合法"}
{"id": 551, "query": "code=200010", "value": "code=200010 表示 领取奖励-不能为自己助力"}
{"id": 552, "query": "code=200011", "value": "code=200011 表示 分层助力领取缺少区服信息"}
{"id": 553, "query": "code=200012", "value": "code=200012 表示 分层助力领取缺少QQ"}
{"id": 554, "query": "code=200013", "value": "code=200013 表示 TCC事务悬挂"}
{"id": 555, "query": "code=200014", "value": "code=200014 表示 重入限制"}
{"id": 556, "query": "code=200015", "value": "code=200015 表示 事务分支ID不存在"}
{"id": 557, "query": "code=200016", "value": "code=200016 表示 分层奖励物品不存在"}
{"id": 558, "query": "code=200017", "value": "code=200017 表示 分层奖励配置不匹配"}
{"id": 559, "query": "code=200018", "value": "code=200018 表示 分层奖励记录不存在"}
{"id": 560, "query": "code=200019", "value": "code=200019 表示 空回滚"}
{"id": 561, "query": "code=200020", "value": "code=200020 表示 人数不够不可领取"}
{"id": 562, "query": "code=200021", "value": "code=200021 表示 领取奖励-对应奖励已领取"}
{"id": 563, "query": "code=200022", "value": "code=200022 表示 执行try失败"}
{"id": 564, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 565, "query": "code=100001", "value": "code=100001 表示 创建-获取组队活动配置失败"}
{"id": 566, "query": "code=100002", "value": "code=100002 表示 创建-获取组队队长白名单失败"}
{"id": 567, "query": "code=100003", "value": "code=100003 表示 创建-登录类型错误"}
{"id": 568, "query": "code=100004", "value": "code=100004 表示 创建-登录appID错误"}
{"id": 569, "query": "code=100005", "value": "code=100005 表示 创建-获取用户信息异常"}
{"id": 570, "query": "code=100006", "value": "code=100006 表示 创建-redis锁异常"}
{"id": 571, "query": "code=100007", "value": "code=100007 表示 创建-平台类型错误"}
{"id": 572, "query": "code=100008", "value": "code=100008 表示 创建-未知平台类型"}
{"id": 573, "query": "code=100009", "value": "code=100009 表示 创建-保存创建组队异常"}
{"id": 574, "query": "code=100010", "value": "code=100010 表示 创建-AES解密异常"}
{"id": 575, "query": "code=100011", "value": "code=100011 表示 创建-生产创建组队消息异常"}
{"id": 576, "query": "code=100012", "value": "code=100012 表示 创建-生产加入组队消息异常"}
{"id": 577, "query": "code=100015", "value": "code=100015 表示 创建-生产审核组队消息异常"}
{"id": 578, "query": "code=100016", "value": "code=100016 表示 创建-AES加密异常"}
{"id": 579, "query": "code=200001", "value": "code=200001 表示 创建-活动未开始"}
{"id": 580, "query": "code=200002", "value": "code=200002 表示 创建-活动结束"}
{"id": 581, "query": "code=200003", "value": "code=200003 表示 创建-创建活动结束"}
{"id": 582, "query": "code=200004", "value": "code=200004 表示 创建-活动下线"}
{"id": 583, "query": "code=200005", "value": "code=200005 表示 创建-测试未在白名单"}
{"id": 584, "query": "code=200006", "value": "code=200006 表示 创建-不在组队创建队长白名单"}
{"id": 585, "query": "code=200007", "value": "code=200007 表示 创建-查询被拒绝的队伍名异常"}
{"id": 586, "query": "code=200008", "value": "code=200008 表示 创建-队伍名已被拒绝"}
{"id": 587, "query": "code=200009", "value": "code=200009 表示 创建-获取组队成员异常"}
{"id": 588, "query": "code=200010", "value": "code=200010 表示 创建-获取组队信息异常"}
{"id": 589, "query": "code=200011", "value": "code=200011 表示 创建-获取组队信息为空"}
{"id": 590, "query": "code=200013", "value": "code=200013 表示 创建-未知队伍key格式"}
{"id": 591, "query": "code=200014", "value": "code=200014 表示 创建-不同活动id"}
{"id": 592, "query": "code=200015", "value": "code=200015 表示 创建-未通过队伍审核"}
{"id": 593, "query": "code=200020", "value": "code=200020 表示 创建-保存退出队伍异常"}
{"id": 594, "query": "code=200024", "value": "code=200024 表示 创建-保存踢出队伍异常"}
{"id": 595, "query": "code=200026", "value": "code=200026 表示 创建-保存组队信息异常"}
{"id": 596, "query": "code=200027", "value": "code=200027 表示 创建-获取组队积分异常"}
{"id": 597, "query": "code=200028", "value": "code=200028 表示 创建-机审被驳回"}
{"id": 598, "query": "code=200029", "value": "code=200029 表示 创建-已经退出队伍"}
{"id": 599, "query": "code=200030", "value": "code=200030 表示 创建-机审异常"}
{"id": 600, "query": "code=100001", "value": "code=100001 表示 加入-获取组队活动配置失败"}
{"id": 601, "query": "code=100002", "value": "code=100002 表示 加入-获取组队队长白名单失败"}
{"id": 602, "query": "code=100003", "value": "code=100003 表示 加入-登录类型错误"}
{"id": 603, "query": "code=100004", "value": "code=100004 表示 加入-登录appID错误"}
{"id": 604, "query": "code=100005", "value": "code=100005 表示 加入-获取用户信息异常"}
{"id": 605, "query": "code=100006", "value": "code=100006 表示 加入-redis锁异常"}
{"id": 606, "query": "code=100007", "value": "code=100007 表示 加入-平台类型错误"}
{"id": 607, "query": "code=100008", "value": "code=100008 表示 加入-未知平台类型"}
{"id": 608, "query": "code=100010", "value": "code=100010 表示 加入-AES解密异常"}
{"id": 609, "query": "code=100012", "value": "code=100012 表示 加入-生产加入组队消息异常"}
{"id": 610, "query": "code=100016", "value": "code=100016 表示 加入-AES加密异常"}
{"id": 611, "query": "code=200001", "value": "code=200001 表示 加入-活动未开始"}
{"id": 612, "query": "code=200002", "value": "code=200002 表示 加入-活动结束"}
{"id": 613, "query": "code=200004", "value": "code=200004 表示 加入-活动下线"}
{"id": 614, "query": "code=200009", "value": "code=200009 表示 加入-获取组队成员异常"}
{"id": 615, "query": "code=200010", "value": "code=200010 表示 加入-获取组队信息异常"}
{"id": 616, "query": "code=200011", "value": "code=200011 表示 加入-获取组队信息为空"}
{"id": 617, "query": "code=200012", "value": "code=200012 表示 加入-用户已加入队伍"}
{"id": 618, "query": "code=200013", "value": "code=200013 表示 加入-未知队伍key格式"}
{"id": 619, "query": "code=200014", "value": "code=200014 表示 加入-不同活动id"}
{"id": 620, "query": "code=200015", "value": "code=200015 表示 加入-未通过队伍审核"}
{"id": 621, "query": "code=200018", "value": "code=200018 表示 加入-找不到该队员"}
{"id": 622, "query": "code=200019", "value": "code=200019 表示 加入-保存加入队伍成员异常"}
{"id": 623, "query": "code=200021", "value": "code=200021 表示 加入-队伍满员"}
{"id": 624, "query": "code=200025", "value": "code=200025 表示 加入-获取所有组队成员异常"}
{"id": 625, "query": "code=200026", "value": "code=200026 表示 加入-保存组队信息异常"}
{"id": 626, "query": "code=200027", "value": "code=200027 表示 加入-获取组队积分异常"}
{"id": 627, "query": "code=200028", "value": "code=200028 表示 加入-机审被驳回"}
{"id": 628, "query": "code=200030", "value": "code=200030 表示 加入-机审异常"}
{"id": 629, "query": "code=100001", "value": "code=100001 表示 踢人-获取组队活动配置失败"}
{"id": 630, "query": "code=100002", "value": "code=100002 表示 踢人-获取组队队长白名单失败"}
{"id": 631, "query": "code=100003", "value": "code=100003 表示 踢人-登录类型错误"}
{"id": 632, "query": "code=100004", "value": "code=100004 表示 踢人-登录appID错误"}
{"id": 633, "query": "code=100005", "value": "code=100005 表示 踢人-获取用户信息异常"}
{"id": 634, "query": "code=100006", "value": "code=100006 表示 踢人-redis锁异常"}
{"id": 635, "query": "code=100007", "value": "code=100007 表示 踢人-平台类型错误"}
{"id": 636, "query": "code=100008", "value": "code=100008 表示 踢人-未知平台类型"}
{"id": 637, "query": "code=100010", "value": "code=100010 表示 踢人-AES解密异常"}
{"id": 638, "query": "code=100014", "value": "code=100014 表示 踢人-生产踢出组队消息异常"}
{"id": 639, "query": "code=100016", "value": "code=100016 表示 踢人-AES加密异常"}
{"id": 640, "query": "code=200001", "value": "code=200001 表示 踢人-活动未开始"}
{"id": 641, "query": "code=200002", "value": "code=200002 表示 踢人-活动结束"}
{"id": 642, "query": "code=200003", "value": "code=200003 表示 踢人-创建活动结束"}
{"id": 643, "query": "code=200004", "value": "code=200004 表示 踢人-活动下线"}
{"id": 644, "query": "code=200005", "value": "code=200005 表示 踢人-测试未在白名单"}
{"id": 645, "query": "code=200009", "value": "code=200009 表示 踢人-获取组队成员异常"}
{"id": 646, "query": "code=200010", "value": "code=200010 表示 踢人-获取组队信息异常"}
{"id": 647, "query": "code=200011", "value": "code=200011 表示 踢人-获取组队信息为空"}
{"id": 648, "query": "code=200013", "value": "code=200013 表示 踢人-未知队伍key格式"}
{"id": 649, "query": "code=200014", "value": "code=200014 表示 踢人-不同活动id"}
{"id": 650, "query": "code=200016", "value": "code=200016 表示 踢人-活动不允许退出"}
{"id": 651, "query": "code=200017", "value": "code=200017 表示 踢人-队长不允许退出"}
{"id": 652, "query": "code=200018", "value": "code=200018 表示 踢人-找不到该队员"}
{"id": 653, "query": "code=200020", "value": "code=200020 表示 踢人-保存退出队伍异常"}
{"id": 654, "query": "code=200022", "value": "code=200022 表示 踢人-退出队伍次数限制"}
{"id": 655, "query": "code=200023", "value": "code=200023 表示 踢人-活动不允许踢人"}
{"id": 656, "query": "code=200024", "value": "code=200024 表示 踢人-保存踢出队伍异常"}
{"id": 657, "query": "code=200025", "value": "code=200025 表示 踢人-获取所有组队成员异常"}
{"id": 658, "query": "code=200026", "value": "code=200026 表示 踢人-保存组队信息异常"}
{"id": 659, "query": "code=200027", "value": "code=200027 表示 踢人-获取组队积分异常"}
{"id": 660, "query": "code=200028", "value": "code=200028 表示 踢人-机审被驳回"}
{"id": 661, "query": "code=200029", "value": "code=200029 表示 踢人-已经退出队伍"}
{"id": 662, "query": "code=200030", "value": "code=200030 表示 踢人-机审异常"}
{"id": 663, "query": "code=100001", "value": "code=100001 表示 退出-获取组队活动配置失败"}
{"id": 664, "query": "code=100002", "value": "code=100002 表示 退出-获取组队队长白名单失败"}
{"id": 665, "query": "code=100003", "value": "code=100003 表示 退出-登录类型错误"}
{"id": 666, "query": "code=100004", "value": "code=100004 表示 退出-登录appID错误"}
{"id": 667, "query": "code=100005", "value": "code=100005 表示 退出-获取用户信息异常"}
{"id": 668, "query": "code=100006", "value": "code=100006 表示 退出-redis锁异常"}
{"id": 669, "query": "code=100007", "value": "code=100007 表示 退出-平台类型错误"}
{"id": 670, "query": "code=100008", "value": "code=100008 表示 退出-未知平台类型"}
{"id": 671, "query": "code=100010", "value": "code=100010 表示 退出-AES解密异常"}
{"id": 672, "query": "code=100013", "value": "code=100013 表示 退出-生产退出组队消息异常"}
{"id": 673, "query": "code=100016", "value": "code=100016 表示 退出-AES加密异常"}
{"id": 674, "query": "code=200001", "value": "code=200001 表示 退出-活动未开始"}
{"id": 675, "query": "code=200002", "value": "code=200002 表示 退出-活动结束"}
{"id": 676, "query": "code=200003", "value": "code=200003 表示 退出-创建活动结束"}
{"id": 677, "query": "code=200004", "value": "code=200004 表示 退出-活动下线"}
{"id": 678, "query": "code=200005", "value": "code=200005 表示 退出-测试未在白名单"}
{"id": 679, "query": "code=200009", "value": "code=200009 表示 退出-获取组队成员异常"}
{"id": 680, "query": "code=200010", "value": "code=200010 表示 退出-获取组队信息异常"}
{"id": 681, "query": "code=200011", "value": "code=200011 表示 退出-获取组队信息为空"}
{"id": 682, "query": "code=200013", "value": "code=200013 表示 退出-未知队伍key格式"}
{"id": 683, "query": "code=200014", "value": "code=200014 表示 退出-不同活动id"}
{"id": 684, "query": "code=200016", "value": "code=200016 表示 退出-活动不允许退出"}
{"id": 685, "query": "code=200017", "value": "code=200017 表示 退出-队长不允许退出"}
{"id": 686, "query": "code=200018", "value": "code=200018 表示 退出-找不到该队员"}
{"id": 687, "query": "code=200020", "value": "code=200020 表示 退出-保存退出队伍异常"}
{"id": 688, "query": "code=200021", "value": "code=200021 表示 退出-队伍满员"}
{"id": 689, "query": "code=200022", "value": "code=200022 表示 退出-退出队伍次数限制"}
{"id": 690, "query": "code=200025", "value": "code=200025 表示 退出-获取所有组队成员异常"}
{"id": 691, "query": "code=200027", "value": "code=200027 表示 退出-获取组队积分异常"}
{"id": 692, "query": "code=200028", "value": "code=200028 表示 退出-机审被驳回"}
{"id": 693, "query": "code=200029", "value": "code=200029 表示 退出-已经退出队伍"}
{"id": 694, "query": "code=200030", "value": "code=200030 表示 退出-机审异常"}
{"id": 695, "query": "code=100001", "value": "code=100001 表示 读-获取组队活动配置失败"}
{"id": 696, "query": "code=100003", "value": "code=100003 表示 读-登录类型错误"}
{"id": 697, "query": "code=100004", "value": "code=100004 表示 读-登录appID错误"}
{"id": 698, "query": "code=100005", "value": "code=100005 表示 读-获取用户信息异常"}
{"id": 699, "query": "code=100006", "value": "code=100006 表示 读-redis锁异常"}
{"id": 700, "query": "code=100007", "value": "code=100007 表示 读-平台类型错误"}
{"id": 701, "query": "code=100008", "value": "code=100008 表示 读-未知平台类型"}
{"id": 702, "query": "code=100010", "value": "code=100010 表示 读-AES解密异常"}
{"id": 703, "query": "code=100016", "value": "code=100016 表示 读-AES加密异常"}
{"id": 704, "query": "code=200001", "value": "code=200001 表示 读-活动未开始"}
{"id": 705, "query": "code=200002", "value": "code=200002 表示 读-活动结束"}
{"id": 706, "query": "code=200004", "value": "code=200004 表示 读-活动下线"}
{"id": 707, "query": "code=200009", "value": "code=200009 表示 读-获取组队成员异常"}
{"id": 708, "query": "code=200010", "value": "code=200010 表示 读-获取组队信息异常"}
{"id": 709, "query": "code=200011", "value": "code=200011 表示 读-获取组队信息为空"}
{"id": 710, "query": "code=200013", "value": "code=200013 表示 读-未知队伍key格式"}
{"id": 711, "query": "code=200014", "value": "code=200014 表示 读-不同活动id"}
{"id": 712, "query": "code=200015", "value": "code=200015 表示 读-未通过队伍审核"}
{"id": 713, "query": "code=200018", "value": "code=200018 表示 读-找不到该队员"}
{"id": 714, "query": "code=200021", "value": "code=200021 表示 读-队伍满员"}
{"id": 715, "query": "code=200025", "value": "code=200025 表示 读-获取所有组队成员异常"}
{"id": 716, "query": "code=200027", "value": "code=200027 表示 读-获取组队积分异常"}
{"id": 717, "query": "code=200028", "value": "code=200028 表示 读-机审被驳回"}
{"id": 718, "query": "code=200029", "value": "code=200029 表示 读-已经退出队伍"}
{"id": 719, "query": "code=200030", "value": "code=200030 表示 读-机审异常"}
{"id": 720, "query": "code=10601001", "value": "code=10601001 表示 配置初始化失败"}
{"id": 721, "query": "code=10501001", "value": "code=10501001 表示 配置初始化失败"}
{"id": 722, "query": "code=10501002", "value": "code=10501002 表示 JSON序列化失败"}
{"id": 723, "query": "code=10501003", "value": "code=10501003 表示 JSON反序列化失败"}
{"id": 724, "query": "code=10501004", "value": "code=10501004 表示 类型断言失败"}
{"id": 725, "query": "code=10501005", "value": "code=10501005 表示 实例ID生成失败"}
{"id": 726, "query": "code=10501006", "value": "code=10501006 表示 字符串转换失败"}
{"id": 727, "query": "code=10502001", "value": "code=10502001 表示 实例已存在"}
{"id": 728, "query": "code=10502002", "value": "code=10502002 表示 实例不存在"}
{"id": 729, "query": "code=10502003", "value": "code=10502003 表示 实例已删除"}
{"id": 730, "query": "code=10502004", "value": "code=10502004 表示 状态转换无效"}
{"id": 731, "query": "code=10502005", "value": "code=10502005 表示 无效的领取时间"}
{"id": 732, "query": "code=10502006", "value": "code=10502006 表示 无效的字段变更"}
{"id": 733, "query": "code=10502007", "value": "code=10502007 表示 无效的字段值"}
{"id": 734, "query": "code=10502008", "value": "code=10502008 表示 字段为空"}
{"id": 735, "query": "code=10502009", "value": "code=10502009 表示 属性场景不匹配"}
{"id": 736, "query": "code=10502010", "value": "code=10502010 表示 用户加载中"}
{"id": 737, "query": "code=10601001", "value": "code=10601001 表示 配置初始化失败"}
{"id": 738, "query": "code=10601002", "value": "code=10601002 表示 请求参数错误"}
{"id": 739, "query": "code=10601003", "value": "code=10601003 表示 TCC重入性校验"}
{"id": 740, "query": "code=10601004", "value": "code=10601004 表示 TCC悬挂错误码"}
{"id": 741, "query": "code=10601005", "value": "code=10601005 表示 待提交用户记录不存在"}
{"id": 742, "query": "code=10602001", "value": "code=10602001 表示 数据解析失败"}
{"id": 743, "query": "code=10602002", "value": "code=10602002 表示 领取实例不存在"}
{"id": 744, "query": "code=10604001", "value": "code=10604001 表示 获取礼包类型错误"}
{"id": 745, "query": "code=10501001", "value": "code=10501001 表示 配置初始化失败"}
{"id": 746, "query": "code=10501002", "value": "code=10501002 表示 JSON序列化失败"}
{"id": 747, "query": "code=10501003", "value": "code=10501003 表示 JSON反序列化失败"}
{"id": 748, "query": "code=10501004", "value": "code=10501004 表示 类型断言失败"}
{"id": 749, "query": "code=10501005", "value": "code=10501005 表示 实例ID生成失败"}
{"id": 750, "query": "code=10501006", "value": "code=10501006 表示 字符串转换失败"}
{"id": 751, "query": "code=10502001", "value": "code=10502001 表示 实例已存在"}
{"id": 752, "query": "code=10502002", "value": "code=10502002 表示 实例不存在"}
{"id": 753, "query": "code=10502003", "value": "code=10502003 表示 实例已删除"}
{"id": 754, "query": "code=10502004", "value": "code=10502004 表示 状态转换无效"}
{"id": 755, "query": "code=10502005", "value": "code=10502005 表示 无效的领取时间"}
{"id": 756, "query": "code=10502006", "value": "code=10502006 表示 无效的字段变更"}
{"id": 757, "query": "code=10502007", "value": "code=10502007 表示 无效的字段值"}
{"id": 758, "query": "code=10502008", "value": "code=10502008 表示 字段为空"}
{"id": 759, "query": "code=10502009", "value": "code=10502009 表示 属性场景不匹配"}
{"id": 760, "query": "code=10502010", "value": "code=10502010 表示 用户加载中"}
{"id": 761, "query": "code=10604001", "value": "code=10604001 表示 获取礼包类型错误"}
{"id": 762, "query": "code=14001001", "value": "code=14001001 表示 配置初始化失败"}
{"id": 763, "query": "code=14001002", "value": "code=14001002 表示 JSON序列化失败"}
{"id": 764, "query": "code=14001003", "value": "code=14001003 表示 JSON反序列化失败"}
{"id": 765, "query": "code=14001004", "value": "code=14001004 表示 类型断言失败"}
{"id": 766, "query": "code=14001005", "value": "code=14001005 表示 实例ID生成失败"}
{"id": 767, "query": "code=14001006", "value": "code=14001006 表示 字符串转换失败"}
{"id": 768, "query": "code=14002001", "value": "code=14002001 表示 实例已存在"}
{"id": 769, "query": "code=14002002", "value": "code=14002002 表示 实例不存在"}
{"id": 770, "query": "code=14002003", "value": "code=14002003 表示 实例已删除"}
{"id": 771, "query": "code=14002004", "value": "code=14002004 表示 状态转换无效"}
{"id": 772, "query": "code=14002005", "value": "code=14002005 表示 无效的领取时间"}
{"id": 773, "query": "code=14002006", "value": "code=14002006 表示 无效的字段变更"}
{"id": 774, "query": "code=14002007", "value": "code=14002007 表示 无效的字段值"}
{"id": 775, "query": "code=14002008", "value": "code=14002008 表示 字段为空"}
{"id": 776, "query": "code=14002009", "value": "code=14002009 表示 property scene mismatch"}
{"id": 777, "query": "code=14002010", "value": "code=14002010 表示 property type mismatch"}
{"id": 778, "query": "code=14002011", "value": "code=14002011 表示 property game type mismatch"}
{"id": 779, "query": "code=14002012", "value": "code=14002012 表示 property address mismatch"}
{"id": 780, "query": "code=14002013", "value": "code=14002013 表示 obtain users loading"}
{"id": 781, "query": "code=14101001", "value": "code=14101001 表示 配置初始化失败"}
{"id": 782, "query": "code=14101002", "value": "code=14101002 表示 请求参数错误"}
{"id": 783, "query": "code=14101003", "value": "code=14101003 表示 TCC重入性校验"}
{"id": 784, "query": "code=14101004", "value": "code=14101004 表示 TCC悬挂错误码"}
{"id": 785, "query": "code=14101005", "value": "code=14101005 表示 待提交用户记录不存在"}
{"id": 786, "query": "code=14102001", "value": "code=14102001 表示 数据解析失败"}
{"id": 787, "query": "code=14102002", "value": "code=14102002 表示 领取实例不存在"}
{"id": 788, "query": "code=14104001", "value": "code=14104001 表示 获取礼包类型错误"}
{"id": 789, "query": "code=14001001", "value": "code=14001001 表示 配置初始化失败"}
{"id": 790, "query": "code=14001002", "value": "code=14001002 表示 JSON序列化失败"}
{"id": 791, "query": "code=14001003", "value": "code=14001003 表示 JSON反序列化失败"}
{"id": 792, "query": "code=14001004", "value": "code=14001004 表示 类型断言失败"}
{"id": 793, "query": "code=14001005", "value": "code=14001005 表示 实例ID生成失败"}
{"id": 794, "query": "code=14001006", "value": "code=14001006 表示 字符串转换失败"}
{"id": 795, "query": "code=14002001", "value": "code=14002001 表示 实例已存在"}
{"id": 796, "query": "code=14002002", "value": "code=14002002 表示 实例不存在"}
{"id": 797, "query": "code=14002003", "value": "code=14002003 表示 实例已删除"}
{"id": 798, "query": "code=14002004", "value": "code=14002004 表示 状态转换无效"}
{"id": 799, "query": "code=14002005", "value": "code=14002005 表示 无效的领取时间"}
{"id": 800, "query": "code=14002006", "value": "code=14002006 表示 无效的字段变更"}
{"id": 801, "query": "code=14002007", "value": "code=14002007 表示 无效的字段值"}
{"id": 802, "query": "code=14002008", "value": "code=14002008 表示 字段为空"}
{"id": 803, "query": "code=14002009", "value": "code=14002009 表示 property scene mismatch"}
{"id": 804, "query": "code=14002010", "value": "code=14002010 表示 property type mismatch"}
{"id": 805, "query": "code=14002011", "value": "code=14002011 表示 property game type mismatch"}
{"id": 806, "query": "code=14002012", "value": "code=14002012 表示 property address mismatch"}
{"id": 807, "query": "code=14002013", "value": "code=14002013 表示 obtain users loading"}
{"id": 808, "query": "code=14104001", "value": "code=14104001 表示 获取礼包类型错误"}
{"id": 809, "query": "code=10001001", "value": "code=10001001 表示 配置初始化失败"}
{"id": 810, "query": "code=10001002", "value": "code=10001002 表示 JSON序列化失败"}
{"id": 811, "query": "code=10001003", "value": "code=10001003 表示 JSON反序列化失败"}
{"id": 812, "query": "code=10001004", "value": "code=10001004 表示 类型断言失败"}
{"id": 813, "query": "code=10001005", "value": "code=10001005 表示 实例ID生成失败"}
{"id": 814, "query": "code=10001006", "value": "code=10001006 表示 字符串转换失败"}
{"id": 815, "query": "code=10001007", "value": "code=10001007 表示 部分发布成功"}
{"id": 816, "query": "code=10001008", "value": "code=10001008 表示 部分删除成功"}
{"id": 817, "query": "code=10001009", "value": "code=10001009 表示 数据不一致"}
{"id": 818, "query": "code=10001010", "value": "code=10001010 表示 未知模式类型"}
{"id": 819, "query": "code=10002001", "value": "code=10002001 表示 访问MDB失败"}
{"id": 820, "query": "code=10002002", "value": "code=10002002 表示 访问Redis失败"}
{"id": 821, "query": "code=10002003", "value": "code=10002003 表示 访问UnionPlus失败"}
{"id": 822, "query": "code=10003001", "value": "code=10003001 表示 实例已存在"}
{"id": 823, "query": "code=10003002", "value": "code=10003002 表示 实例不存在"}
{"id": 824, "query": "code=10003003", "value": "code=10003003 表示 实例已删除"}
{"id": 825, "query": "code=10003004", "value": "code=10003004 表示 property ID is not existed; please check whether the property is released"}
{"id": 826, "query": "code=10003005", "value": "code=10003005 表示 one property can only be used by one lottery; please create a new property in the property system"}
{"id": 827, "query": "code=10003006", "value": "code=10003006 表示 property ID is not allowed to update by this instance ID"}
{"id": 828, "query": "code=10003007", "value": "code=10003007 表示 无效的状态转换"}
{"id": 829, "query": "code=10003008", "value": "code=10003008 表示 无效的抽奖时间"}
{"id": 830, "query": "code=10003009", "value": "code=10003009 表示 字段为空"}
{"id": 831, "query": "code=10003010", "value": "code=10003010 表示 无效的开始时间"}
{"id": 832, "query": "code=10003011", "value": "code=10003011 表示 无效的页面大小"}
{"id": 833, "query": "code=10003012", "value": "code=10003012 表示 无效的属性场景"}
{"id": 834, "query": "code=10004001", "value": "code=10004001 表示 访问Ets失败"}
{"id": 835, "query": "code=10004002", "value": "code=10004002 表示 访问Magic失败"}
{"id": 836, "query": "code=10101001", "value": "code=10101001 表示 配置初始化失败"}
{"id": 837, "query": "code=10101002", "value": "code=10101002 表示 Redis操作失败"}
{"id": 838, "query": "code=10101003", "value": "code=10101003 表示 获取用户登录信息失败"}
{"id": 839, "query": "code=10101004", "value": "code=10101004 表示 JSON序列化失败"}
{"id": 840, "query": "code=10101005", "value": "code=10101005 表示 JSON反序列化失败"}
{"id": 841, "query": "code=10101007", "value": "code=10101007 表示 无效的订单ID"}
{"id": 842, "query": "code=10102001", "value": "code=10102001 表示 请求参数错误"}
{"id": 843, "query": "code=10102002", "value": "code=10102002 表示 数据解析失败"}
{"id": 844, "query": "code=10102003", "value": "code=10102003 表示 请求超限"}
{"id": 845, "query": "code=10102004", "value": "code=10102004 表示 数据不一致"}
{"id": 846, "query": "code=10103001", "value": "code=10103001 表示 用户已参与"}
{"id": 847, "query": "code=10104001", "value": "code=10104001 表示 获取管理数据失败"}
{"id": 848, "query": "code=10105001", "value": "code=10105001 表示 获取在线存储数据失败"}
{"id": 849, "query": "code=10106003", "value": "code=10106003 表示 请求参数错误"}
{"id": 850, "query": "code=10107002", "value": "code=10107002 表示 背包设置账号为空"}
{"id": 851, "query": "code=10107003", "value": "code=10107003 表示 登录类型与账号类型不匹配"}
{"id": 852, "query": "code=10107008", "value": "code=10107008 表示 用户中奖记录为空"}
{"id": 853, "query": "code=10001001", "value": "code=10001001 表示 配置初始化失败"}
{"id": 854, "query": "code=10001002", "value": "code=10001002 表示 JSON序列化失败"}
{"id": 855, "query": "code=10001003", "value": "code=10001003 表示 JSON反序列化失败"}
{"id": 856, "query": "code=10001004", "value": "code=10001004 表示 类型断言失败"}
{"id": 857, "query": "code=10001005", "value": "code=10001005 表示 实例ID生成失败"}
{"id": 858, "query": "code=10001006", "value": "code=10001006 表示 字符串转换失败"}
{"id": 859, "query": "code=10001007", "value": "code=10001007 表示 部分发布成功"}
{"id": 860, "query": "code=10001008", "value": "code=10001008 表示 部分删除成功"}
{"id": 861, "query": "code=10001009", "value": "code=10001009 表示 数据不一致"}
{"id": 862, "query": "code=10001010", "value": "code=10001010 表示 未知模式类型"}
{"id": 863, "query": "code=10002001", "value": "code=10002001 表示 访问MDB失败"}
{"id": 864, "query": "code=10002002", "value": "code=10002002 表示 访问Redis失败"}
{"id": 865, "query": "code=10002003", "value": "code=10002003 表示 访问UnionPlus失败"}
{"id": 866, "query": "code=10003001", "value": "code=10003001 表示 实例已存在"}
{"id": 867, "query": "code=10003002", "value": "code=10003002 表示 实例不存在"}
{"id": 868, "query": "code=10003003", "value": "code=10003003 表示 实例已删除"}
{"id": 869, "query": "code=10003004", "value": "code=10003004 表示 property ID is not existed; please check whether the property is released"}
{"id": 870, "query": "code=10003005", "value": "code=10003005 表示 one property can only be used by one lottery; please create a new property in the property system"}
{"id": 871, "query": "code=10003006", "value": "code=10003006 表示 property ID is not allowed to update by this instance ID"}
{"id": 872, "query": "code=10003007", "value": "code=10003007 表示 无效的状态转换"}
{"id": 873, "query": "code=10003008", "value": "code=10003008 表示 无效的抽奖时间"}
{"id": 874, "query": "code=10003009", "value": "code=10003009 表示 字段为空"}
{"id": 875, "query": "code=10003010", "value": "code=10003010 表示 无效的开始时间"}
{"id": 876, "query": "code=10003011", "value": "code=10003011 表示 无效的页面大小"}
{"id": 877, "query": "code=10003012", "value": "code=10003012 表示 无效的属性场景"}
{"id": 878, "query": "code=10004001", "value": "code=10004001 表示 访问Ets失败"}
{"id": 879, "query": "code=10004002", "value": "code=10004002 表示 访问Magic失败"}
{"id": 880, "query": "code=10101001", "value": "code=10101001 表示 配置初始化失败"}
{"id": 881, "query": "code=10101002", "value": "code=10101002 表示 Redis操作失败"}
{"id": 882, "query": "code=10101004", "value": "code=10101004 表示 JSON序列化失败"}
{"id": 883, "query": "code=10101005", "value": "code=10101005 表示 JSON反序列化失败"}
{"id": 884, "query": "code=10101006", "value": "code=10101006 表示 获取队列锁失败"}
{"id": 885, "query": "code=10101007", "value": "code=10101007 表示 无效的订单ID"}
{"id": 886, "query": "code=10102001", "value": "code=10102001 表示 请求参数错误"}
{"id": 887, "query": "code=10103001", "value": "code=10103001 表示 用户已参与"}
{"id": 888, "query": "code=10104001", "value": "code=10104001 表示 获取管理数据失败"}
{"id": 889, "query": "code=10105001", "value": "code=10105001 表示 获取在线存储数据失败"}
{"id": 890, "query": "code=10105002", "value": "code=10105002 表示 生成用户ID失败"}
{"id": 891, "query": "code=10107001", "value": "code=10107001 表示 用户已抽齐所有奖品"}
{"id": 892, "query": "code=10107002", "value": "code=10107002 表示 背包设置账号为空"}
{"id": 893, "query": "code=10107003", "value": "code=10107003 表示 登录类型与账号类型不匹配"}
{"id": 894, "query": "code=10107004", "value": "code=10107004 表示 选择的物品ID无效"}
{"id": 895, "query": "code=10107005", "value": "code=10107005 表示 选择的物品订单ID无效"}
{"id": 896, "query": "code=10107006", "value": "code=10107006 表示 选择的物品总数无效"}
{"id": 897, "query": "code=10107007", "value": "code=10107007 表示 选择的单类型物品数无效"}
{"id": 898, "query": "code=10107008", "value": "code=10107008 表示 用户中奖记录为空"}
{"id": 899, "query": "code=10107009", "value": "code=10107009 表示 设置用户地址信息失败"}
{"id": 900, "query": "code=10107010", "value": "code=10107010 表示 选择的物品类型不匹配"}
{"id": 901, "query": "code=1", "value": "code=1 表示 请求参数有错误"}
{"id": 902, "query": "code=1", "value": "code=1 表示 请求参数有错误"}
{"id": 903, "query": "code=1", "value": "code=1 表示 请求参数有错误"}
{"id": 904, "query": "code=12", "value": "code=12 表示 cmd 有误"}
{"id": 905, "query": "code=12", "value": "code=12 表示 cmd 有误"}
{"id": 906, "query": "code=12", "value": "code=12 表示 cmd 有误"}
{"id": 907, "query": "code=-5", "value": "code=-5 表示 客户配置错误"}
{"id": 908, "query": "code=-5", "value": "code=-5 表示 客户配置错误"}
{"id": 909, "query": "code=-5", "value": "code=-5 表示 客户配置错误"}
{"id": 910, "query": "code=-6", "value": "code=-6 表示 后端服务异常"}
{"id": 911, "query": "code=-6", "value": "code=-6 表示 后端服务异常"}
{"id": 912, "query": "code=-6", "value": "code=-6 表示 后端服务异常"}
{"id": 913, "query": "code=-7", "value": "code=-7 表示 签名信息错误"}
{"id": 914, "query": "code=-7", "value": "code=-7 表示 签名信息错误"}
{"id": 915, "query": "code=-7", "value": "code=-7 表示 签名信息错误"}
{"id": 916, "query": "code=-8", "value": "code=-8 表示 请求过期"}
{"id": 917, "query": "code=-8", "value": "code=-8 表示 请求过期"}
{"id": 918, "query": "code=-8", "value": "code=-8 表示 请求过期"}
{"id": 919, "query": "code=-9", "value": "code=-9 表示 请求重放"}
{"id": 920, "query": "code=-9", "value": "code=-9 表示 请求重放"}
{"id": 921, "query": "code=-9", "value": "code=-9 表示 请求重放"}
{"id": 922, "query": "code=-1", "value": "code=-1 表示 频控限制"}
{"id": 923, "query": "code=200023", "value": "code=200023 表示 获取用户信息失败"}
{"id": 924, "query": "code=100003", "value": "code=100003 表示 登录appid异常"}
{"id": 925, "query": "code=200000", "value": "code=200000 表示 操作过于频繁"}
{"id": 926, "query": "code=100001", "value": "code=100001 表示 获取wuji助力配置失败"}
{"id": 927, "query": "code=500023", "value": "code=500023 表示 获取登录信息失败"}
{"id": 928, "query": "code=200000", "value": "code=200000 表示 操作过于频繁"}
{"id": 929, "query": "code=200001", "value": "code=200001 表示 分享key无效"}
{"id": 930, "query": "code=200010", "value": "code=200010 表示 不能为自己助力"}
{"id": 931, "query": "code=200007", "value": "code=200007 表示 助力活动还未开始"}
{"id": 932, "query": "code=200005", "value": "code=200005 表示 助力活动已结束"}
{"id": 933, "query": "code=200026", "value": "code=200026 表示 发起助力时间已截止"}
{"id": 934, "query": "code=200002", "value": "code=200002 表示 助力已达到最大次数"}
{"id": 935, "query": "code=200030", "value": "code=200030 表示 数值助力总限量被限制"}
{"id": 936, "query": "code=200031", "value": "code=200031 表示 数值子助力被限制"}
{"id": 937, "query": "code=200032", "value": "code=200032 表示 次数子助力被限制"}
{"id": 938, "query": "code=200004", "value": "code=200004 表示 ta被助力已达到最大次数"}
{"id": 939, "query": "code=200033", "value": "code=200033 表示 数值被助力总限量被限制"}
{"id": 940, "query": "code=200034", "value": "code=200034 表示 数值子被助力被限制"}
{"id": 941, "query": "code=200035", "value": "code=200035 表示 次数子被助力被限制"}
{"id": 942, "query": "code=200036", "value": "code=200036 表示 双向助力被限制"}
{"id": 943, "query": "code=200027", "value": "code=200027 表示 助力已结束"}
{"id": 944, "query": "code=500003", "value": "code=500003 表示 活动ID不存在或者活动无效"}
{"id": 945, "query": "code=400003", "value": "code=400003 表示 获取登录信息失败"}
{"id": 946, "query": "code=200012", "value": "code=200012 表示 创建失败，您已加入小队"}
{"id": 947, "query": "code=0", "value": "code=0 表示 中台其他成功"}
{"id": 948, "query": "code=0", "value": "code=0 表示 接口正常"}
{"id": 949, "query": "code=9901002", "value": "code=9901002 表示 获取用户信息失败"}
{"id": 950, "query": "code=16001003", "value": "code=16001003 表示 调用福利宝券服务出错"}
{"id": 951, "query": "code=16001003", "value": "code=16001003 表示 调用福利宝券服务出错"}
{"id": 952, "query": "code=200041", "value": "code=200041 表示 活动不匹配"}
{"id": 953, "query": "code=200041", "value": "code=200041 表示 活动不匹配"}
{"id": 954, "query": "code=200041", "value": "code=200041 表示 活动不匹配"}
{"id": 955, "query": "code=200031", "value": "code=200031 表示 活动不匹配"}
{"id": 956, "query": "code=200031", "value": "code=200031 表示 活动不匹配"}
{"id": 957, "query": "code=200031", "value": "code=200031 表示 活动不匹配"}
{"id": 958, "query": "code=200031", "value": "code=200031 表示 活动不匹配"}
{"id": 959, "query": "code=1", "value": "code=1 表示 系统繁忙"}
{"id": 960, "query": "code=2", "value": "code=2 表示 参数错误"}
{"id": 961, "query": "code=1", "value": "code=1 表示 系统繁忙"}
{"id": 962, "query": "code=2", "value": "code=2 表示 参数错误"}
{"id": 963, "query": "code=3", "value": "code=3 表示 未注册或未通关"}
{"id": 964, "query": "code=4", "value": "code=4 表示 非应用宝注册"}
{"id": 965, "query": "code=5", "value": "code=5 表示 宝券不足"}
{"id": 966, "query": "code=6", "value": "code=6 表示 重复领取"}
{"id": 967, "query": "code=7", "value": "code=7 表示 非新用户"}
{"id": 968, "query": "code=13", "value": "code=13 表示 未登录"}
{"id": 969, "query": "code=14", "value": "code=14 表示 未抽中"}
{"id": 970, "query": "code=200008", "value": "code=200008 表示 当前用户已拥有卡片"}
{"id": 971, "query": "code=200010", "value": "code=200010 表示 卡片已被领取"}
{"id": 972, "query": "code=200003", "value": "code=200003 表示 未登录"}
{"id": 973, "query": "code=200007", "value": "code=200007 表示 卡片数量不足，无法赠送"}
{"id": 974, "query": "code=200008", "value": "code=200008 表示 对方已经拥有这张卡片"}
{"id": 975, "query": "code=200003", "value": "code=200003 表示 未登录"}
{"id": 976, "query": "code=3", "value": "code=3 表示 在应用宝下载注册游戏并通过新手关才能领取哦"}
{"id": 977, "query": "code=6", "value": "code=6 表示 你已经领取过宝券啦，不可重复领取哦"}
{"id": 978, "query": "code=2", "value": "code=2 表示 参数错误"}
{"id": 979, "query": "code=4", "value": "code=4 表示 非应用宝注册，应用宝下载安装注册才能领取哦"}
{"id": 980, "query": "code=5", "value": "code=5 表示 宝券已经抢光啦"}
{"id": 981, "query": "code=7", "value": "code=7 表示 需要新注册用户才可以领取哦"}
{"id": 982, "query": "code=0", "value": "code=0 表示 领取成功，游戏内支付可抵扣"}
{"id": 983, "query": "code=0", "value": "code=0 表示 请求成功"}
{"id": 984, "query": "code=200033", "value": "code=200033 表示 组队-队伍不存在"}
{"id": 985, "query": "code=200007", "value": "code=200007 表示 对方已无多余卡片啦，再找找看吧～"}
{"id": 986, "query": "code=1002014", "value": "code=1002014 表示 登录态不合法"}
{"id": 987, "query": "code=1002015", "value": "code=1002015 表示 请在应用宝内访问活动页面"}
{"id": 988, "query": "code=1002016", "value": "code=1002016 表示 请在PC应用宝内登录访问活动页面"}
{"id": 989, "query": "code=1002017", "value": "code=1002017 表示 请通过QQ或微信访问活动页面"}
{"id": 990, "query": "code=6803002", "value": "code=6803002 表示 个人限量超限"}
{"id": 991, "query": "code=6803001", "value": "code=6803001 表示 总限量超限"}
{"id": 992, "query": "code=600012", "value": "code=600012 表示 联运游戏注册渠道未匹配"}
{"id": 993, "query": "code=600013", "value": "code=600013 表示 联运游戏注册时间未匹配"}
{"id": 994, "query": "code=600011", "value": "code=600011 表示 微信 openid 转换失败"}
{"id": 995, "query": "code=600010", "value": "code=600010 表示 QQopenid 转换失败"}
{"id": 996, "query": "code=1002020", "value": "code=1002020 表示 风控结果不置信"}
{"id": 997, "query": "code=1002018", "value": "code=1002018 表示 只允许移商接入层 & Native 接入层访问"}
{"id": 998, "query": "code=1002019", "value": "code=1002019 表示 只允许Native 接入层访问"}
{"id": 999, "query": "code=1002021", "value": "code=1002021 表示 请在应用宝内访问活动页面"}
{"id": 1000, "query": "code=1002022", "value": "code=1002022 表示 请在应用宝内访问活动页面"}
{"id": 1001, "query": "code=400012", "value": "code=400012 表示 不满足游戏回归用户条件"}
{"id": 1002, "query": "code=400013", "value": "code=400013 表示 仅游戏回归用户和新注册用户可领取"}
{"id": 1003, "query": "code=300022", "value": "code=300022 表示 任务中心的UDF条件失败"}
