[I][2025-04-17 +80 12:45:16.679][20137, 1411][kuikly_thread-5][market][ActExecRequester][][|12:45.15.660|[KLog][ActExecRequester]:innerRequest start, req: ActExecReq(componentInfo=BackendComponentInfo(componentID=moka-ui-obtain_dffbb467, componentType=yyb-obtain, modID=10002), invocation={name=/trpc.component_plat.obtain.Obtain/GetObtainInfo, data={obtain_iid=iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95}}, qualifier_params=[], timeout=30000, reuse=false, reuseUniqueKey=, isTest=true, componentType=yyb-obtain, componentID=moka-ui-obtain_dffbb467)
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][ReturnCodeConfigManager][][|12:45.15.675|[KLog][ReturnCodeConfigManager]:loadConfig mergedReturnCodeConfig: MergedReturnCodeConfig(interfacesByID={108=ServiceInterface(id=108, keyword=4032, type=YYBClient), 109=ServiceInterface(id=109, keyword=get_comeback_tasks, type=Access), 110=ServiceInterface(id=110, keyword=/public/object, type=Common), 111=ServiceInterface(id=111, keyword=trpc.game_community.iwan_boost.IwanBoost/getBoostDetail, type=Iwan), 112=ServiceInterface(id=112, keyword=/trpc.component_plat.obtain.Obtain/DoObtain, type=ActExec), 113=ServiceInterface(id=113, keyword=/trpc.component_plat.lottery.LotteryService/DoLottery, type=ActExec), 114=ServiceInterface(id=114, keyword=/trpc.component_plat.multi_obtain.MultiObtain/DoObtain, type=ActExec), 115=ServiceInterface(id=115, keyword=/trpc.component_plat.multi_obtain.MultiObtain/GetObtainInfo, type=ActExec), 116=ServiceInterface(id=116, keyword=/trpc.activity.boost_server.BoostServer/GetBoostDetail, type=ActExec), 117=ServiceInterface(id=117, keyword=/trpc.activity.boost_server.BoostServer/LaunchBoost, type=ActExec), 118=ServiceInterface(id=118, keyword=ExecGraph, type=ActOther), 119=ServiceInterface(id=119, keyword=GetActivityConfig, type=ActOther), 120=ServiceInterface(id=120, keyword=/trpc.component_plat.sign_in.SignIn/GetSignInInfo, type=ActExec), 121=ServiceInterface(id=121, keyword=/trpc.component_plat.sign_in.SignIn/DoSignIn, type=ActExec), 122=ServiceInterface(id=122, keyword=/trpc.component_plat.sign_in.SignIn/CompleteSignIn, type=ActExec), 123=ServiceInterface(id=123, keyword=/trpc.component_plat.sign_in.SignIn/CompensateSignIn, type=ActExec), 124=ServiceInterface(id=124, keyword=/trpc.component_plat.top_list.TopList/GetTopListInfo, type=ActExec), 125=ServiceInterface(id=125, keyword=/trpc.component_plat.top_list.TopList/Participate, type=ActExec), 126=ServiceInterface(id=126, keyword=/trpc.yybgame.newgame_center.NewGameCenter/BookGameForHttp, type=Common), 127=ServiceInterface(id=127, keyword=/api/getQrCodeScene, type=Common), 128=ServiceInterface(id=128, keyword=/trpc.activity.team_server.TeamServer/CreateTeam, type=ActExec), 129=ServiceInterface(id=129, keyword=/trpc.activity.team_server.TeamServer/JoinTeam, type=ActExec), 130=ServiceInterface(id=130, keyword=/trpc.activity.team_server.TeamServer/KickOutTeam, type=ActExec), 131=ServiceInterface(id=131, keyword=/trpc.activity.team_server.TeamServer/ExitTeam, type=ActExec), 132=ServiceInterface(id=132, keyword=/trpc.component_plat.obtain.Obtain/GetObtainInfo, type=ActExec), 133=ServiceInterface(id=133, keyword=/trpc.activity.boost_server.BoostServer/DoBoost, type=ActExec), 134=ServiceInterface(id=134, keyword=/trpc.activity.boost_server.BoostServer/GetLayeredAwardInfos, type=ActExec), 135=ServiceInterface(id=135, keyword=/trpc.activity.boost_server.BoostServer/ReceiveLayeredAward, type=ActExec), 136=ServiceInterface(id=136, keyword=/trpc.activity.match_rank_server.MatchRankServer/Signup, type=ActExec), 138=ServiceInterface(id=138, keyword=/trpc.activity.fission_point.FissionPoint/SendDownloadRegisterPoints, type=ActExec), 139=ServiceInterface(id=139, keyword=/trpc.activity.fission_point.FissionPoint/GetDownloadRegisterStatus, type=ActExec), 140=ServiceInterface(id=140, keyword=/trpc.activity.team_server.TeamServer/GetTeamInfo, type=ActExec), 141=ServiceInterface(id=141, keyword=/trpc.activity.team_server.TeamServer/GetTeamConfig, type=ActExec), 142=ServiceInterface(id=142, keyword=/trpc.activity.team_server.TeamServer/GetTeamRank, type=ActExec), 144=ServiceInterface(id=144, keyword=/trpc.component_plat.yybpoint.YybPoint/Query, type=ActExecForward), 145=ServiceInterface(id=145, keyword=trpc.iwan.team_server.TeamServer/getTeamPoint, type=Iwan), 146=ServiceInterface(id=146, keyword=apigateway/point/query, type=Iwan), 148=ServiceInterface(id=148, keyword=/trpc.activity.match_rank_server.MatchRankServer/GetMatch, type=ActExec), 149=ServiceInterface(id=149, keyword=/trpc.activity.match_rank_server.MatchRankServer/GetSeason, type=ActExec), 150=ServiceInterface(id=150, keyword=/trpc.component_plat.biz_custom_coupon.BizCustomCoupon/GetCouponInfo, type=ActExecForward), 151=ServiceInterface(id=151, keyword=/trpc.component_plat.biz_custom_coupon.BizCustomCoupon/ObtainCoupon, type=ActExecForward), 152=ServiceInterface(id=152, keyword=/trpc.component_plat.lottery.LotteryService/GetLotteryInfo, type=ActExec), 153=ServiceInterface(id=153, keyword=/trpc.activity.boost_server.BoostServer/LaunchValueBoost, type=ActExec), 155=ServiceInterface(id=155, keyword=/trpc.activity.fission_point.FissionPoint/RelationBoost, type=ActExec), 156=ServiceInterface(id=156, keyword=/trpc.activity.fission_point.FissionPoint/GetUserPoints, type=ActExec), 157=ServiceInterface(id=157, keyword=/trpc.activity.fission_point.FissionPoint/GetActivityInfo, type=ActExec), 158=ServiceInterface(id=158, keyword=/trpc.component_plat.property.Property/GetUserPropertyList, type=ActExecForward), 159=ServiceInterface(id=159, keyword=trpc.iwan.mission_system_server.MissionSystemSvr/getUserAssets, type=Iwan), 160=ServiceInterface(id=160, keyword=/trpc.component_plat.lottery.LotteryService/GetOrderDetail, type=ActExecForward), 161=ServiceInterface(id=161, keyword=/trpc.component_plat.lottery.LotteryService/RegisterUserAddrInfo, type=ActExecForward), 162=ServiceInterface(id=162, keyword=/trpc.component_plat.lottery.LotteryService/BindUserAdditionalInfo, type=ActExecForward), 164=ServiceInterface(id=164, keyword=/trpc.component_plat.biz_custom_coupon.BizCustomCoupon/GetCouponInfoV2, type=ActExecForward), 165=ServiceInterface(id=165, keyword=/trpc.component_plat.biz_custom_coupon.BizCustomCoupon/ObtainCouponPackV2, type=ActExecForward), 166=ServiceInterface(id=166, keyword=/trpc.mobileassist.pngbasicabilitytooltrpcgoserver.basic_ability_tool/GetAppInfo, type=ActExec), 167=ServiceInterface(id=167, keyword=/trpc.activity.card_collection.CardCollection/SendCardReceive, type=ActExec), 168=ServiceInterface(id=168, keyword=/trpc.activity.card_collection.CardCollection/AskCardSend, type=ActExec), 169=ServiceInterface(id=169, keyword=/trpc.task_center.task_center_game_task.GameTaskCenter/GetTaskList, type=Default), 170=ServiceInterface(id=170, keyword=/trpc.task_center.task_center_game_task.GameTaskCenter/TaskFinish, type=Default)}, codeRulesByID={108=ReturnCodeRule(id=108, ruleFunc=function fun(resp){var _resp$body$errCode,_resp$body;return(_resp$body$errCode=(_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.errCode)!==null&&_resp$body$errCode!==void 0?_resp$body$errCode:0};return fun(resp);, priority=0), 109=ReturnCodeRule(id=109, ruleFunc=function fun(resp){var _resp$body$data$ret,_resp$body;return(_resp$body$data$ret=(_resp$body=resp.body)===null||_resp$body===void 0||(_resp$body=_resp$body.data)===null||_resp$body===void 0?void 0:_resp$body.ret)!==null&&_resp$body$data$ret!==void 0?_resp$body$data$ret:0};return fun(resp);, priority=20), 110=ReturnCodeRule(id=110, ruleFunc=function fun(resp){var _resp$statusCode;var statusCode=(_resp$statusCode=resp===null||resp===void 0?void 0:resp.statusCode)!==null&&_resp$statusCode!==void 0?_resp$statusCode:-1;return statusCode==200?0:statusCode};return fun(resp);, priority=5), 111=ReturnCodeRule(id=111, ruleFunc=function fun(resp){var _resp$headers;return(_resp$headers=resp.headers)===null||_resp$headers===void 0?void 0:_resp$headers["ual-access-ret"]};return fun(resp);, priority=10), 112=ReturnCodeRule(id=112, ruleFunc=function fun(resp){var _resp$body;return(_resp$body=resp.body)===null||_resp$body===void 0||(_resp$body=_resp$body.error_info)===null||_resp$body===void 0?void 0:_resp$body.code};return fun(resp);, priority=10), 113=ReturnCodeRule(id=113, ruleFunc=function fun(resp){var _resp$headers;return((_resp$headers=resp.headers)===null||_resp$headers===void 0?void 0:_resp$headers["trpc-func-ret"])||0};return fun(resp);, priority=10), 114=ReturnCodeRule(id=114, ruleFunc=function fun(resp){var _resp$body;var code=(_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.sdkErrCode;return code};return fun(resp);, priority=30), 115=ReturnCodeRule(id=115, ruleFunc=function fun(resp){var _resp$body;return((_resp$body=resp.body)===null||_resp$body===void 0||(_resp$body=_resp$body.data)===null||_resp$body===void 0?void 0:_resp$body.ret)==0?0:-1};return fun(resp);, priority=6), 116=ReturnCodeRule(id=116, ruleFunc=function fun(resp){var _resp$headers$trpcFu,_resp$headers,_resp$body$ret,_resp$body;var headerRet=(_resp$headers$trpcFu=(_resp$headers=resp.headers)===null||_resp$headers===void 0?void 0:_resp$headers["trpc-func-ret"])!==null&&_resp$headers$trpcFu!==void 0?_resp$headers$trpcFu:0;var bodyRet=(_resp$body$ret=(_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.ret)!==null&&_resp$body$ret!==void 0?_resp$body$ret:-1;return headerRet||bodyRet};return fun(resp);, priority=0), 117=ReturnCodeRule(id=117, ruleFunc=function fun(resp){var _resp$headers;return(_resp$headers=resp.headers)===null||_resp$headers===void 0?void 0:_resp$headers["trpc-ret"]};return fun(resp);, priority=5), 118=ReturnCodeRule(id=118, ruleFunc=function fun(resp){var checkIsEmpty=function checkIsEmpty(val){return val===undefined||val===null};return checkIsEmpty(resp.headers)&&checkIsEmpty(resp.body)?-1:0};return fun(resp);, priority=2), 119=ReturnCodeRule(id=119, ruleFunc=function fun(resp){var _resp$body;return((_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.ret)==0?0:-1};return fun(resp);, priority=0), 120=ReturnCodeRule(id=120, ruleFunc=function fun(resp){var _resp$body;var code=(_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.code;return code};return fun(resp);, priority=10), 121=ReturnCodeRule(id=121, ruleFunc=function fun(resp){var _resp$body$ret,_resp$body;return(_resp$body$ret=(_resp$body=resp.body)===null||_resp$body===void 0?void 0:_resp$body.ret)!==null&&_resp$body$ret!==void 0?_resp$body$ret:0};return fun(resp);, priority=10)}, returnCodeConfigs=ReturnCodeConfigs(common=[ReturnCodeConfigItem(id=127, interfaceID=0, interfaceType=YYBClient, code=-99000, codeRuleID=108, defaultTip=超时了，请重试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=128, interfaceID=0, interfaceType=YYBClient, code=-99001, codeRuleID=108, defaultTip=SSR 环境不支持, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=129, interfaceID=0, interfaceType=YYBClient, code=-99002, codeRuleID=108, defaultTip=请前往应用宝参加活动哦～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=130, interfaceID=0, interfaceType=YYBClient, code=-99003, codeRuleID=108, defaultTip=请先升级应用宝版本哦～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=131, interfaceID=0, interfaceType=YYBClient, code=-99004, codeRuleID=108, defaultTip=网络异常，请稍后重试, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=132, interfaceID=0, interfaceType=YYBClient, code=-99999, codeRuleID=108, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=133, interfaceID=108, interfaceType=Default, code=100005, codeRuleID=109, defaultTip=奖励不存在，请稍后再试, needAlarm=NoNeedAlarm, priority=20), ReturnCodeConfigItem(id=134, interfaceID=109, interfaceType=Default, code=403, codeRuleID=110, defaultTip=请重新登录, needAlarm=NoNeedAlarm, priority=15), ReturnCodeConfigItem(id=135, interfaceID=109, interfaceType=Default, code=0, codeRuleID=111, defaultTip=获取成功～, needAlarm=NoNeedAlarm, priority=15), ReturnCodeConfigItem(id=136, interfaceID=111, interfaceType=Default, code=10011, codeRuleID=112, defaultTip=非活动时间内, needAlarm=NoNeedAlarm, priority=100), ReturnCodeConfigItem(id=137, interfaceID=0, interfaceType=ActExec, code=9901002, codeRuleID=113, defaultTip=请重新登录, needAlarm=NoNeedAlarm, priority=30), ReturnCodeConfigItem(id=138, interfaceID=112, interfaceType=Default, code=0, codeRuleID=109, defaultTip=领取成功～, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=139, interfaceID=0, interfaceType=ActExec, code=-99006, codeRuleID=114, defaultTip=您的账号存在风险，暂不支持该活动, needAlarm=NoNeedAlarm, priority=20), ReturnCodeConfigItem(id=140, interfaceID=0, interfaceType=ActExec, code=-99007, codeRuleID=114, defaultTip=验证失败, needAlarm=NoNeedAlarm, priority=20), ReturnCodeConfigItem(id=141, interfaceID=113, interfaceType=ActExec, code=1106004, codeRuleID=113, defaultTip=抽奖已经结束, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=142, interfaceID=113, interfaceType=ActExec, code=10106004, codeRuleID=113, defaultTip=抽奖已经结束, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=143, interfaceID=113, interfaceType=ActExec, code=1106003, codeRuleID=113, defaultTip=抽奖请求参数有误, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=144, interfaceID=113, interfaceType=ActExec, code=10106003, codeRuleID=113, defaultTip=抽奖请求参数有误, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=145, interfaceID=113, interfaceType=ActExec, code=1106001, codeRuleID=113, defaultTip=抽奖还未开始, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=146, interfaceID=113, interfaceType=ActExec, code=10106001, codeRuleID=113, defaultTip=抽奖还未开始, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=147, interfaceID=114, interfaceType=ActExec, code=14103001, codeRuleID=113, defaultTip=领取时间还未开始, needAlarm=NoNeedAlarm, priority=0), ReturnCodeConfigItem(id=148, interfaceID=0, interfaceType=YYBClient, code=0, codeRuleID=108, defaultTip=成功, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=149, interfaceID=0, interfaceType=ActExec, code=1001002, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=150, interfaceID=0, interfaceType=ActExec, code=1001003, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=151, interfaceID=0, interfaceType=ActExec, code=1001004, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=152, interfaceID=0, interfaceType=ActExec, code=1001005, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=153, interfaceID=0, interfaceType=ActExec, code=1001006, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=154, interfaceID=0, interfaceType=ActExec, code=1001007, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=155, interfaceID=0, interfaceType=ActExec, code=1001008, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=156, interfaceID=0, interfaceType=ActExec, code=1001009, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfigItem(id=157, interfaceID=0, interfaceType=ActExec, code=1001010, codeRuleID=113, defaultTip=系统繁忙，请稍后再试～, needAlarm=NoNeedAlarm, priority=10), ReturnCodeConfi
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][][][|12:45.15.675|[KLog][]:getCGIHost
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][ActExecRequester][][|12:45.15.675|[KLog][ActExecRequester]:generateDoLotteryReqBody body: {"activity_iid": "aiid_8e78673c-08ba-444d-b23b-e16f392adc73","component_iid": "iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95\"}"},"qualifier_params": []}
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][HTTPRequester][][|12:45.15.676|[KLog][HTTPRequester]:request start: HTTPReq(interfaceKeyword=/trpc.component_plat.obtain.Obtain/GetObtainInfo, interfaceType=ActExec, baseURL=https://ovactapi.iwan.yyb.qq.com, url=/trpc.activity_plat.controller.ControllerService/Exec, method=post, data={"activity_iid": "aiid_8e78673c-08ba-444d-b23b-e16f392adc73","component_iid": "iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95\"}"},"qualifier_params": []}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=yyb-obtain, componentID=moka-ui-obtain_dffbb467, headers={})
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][PageRequestEngine][][|12:45.15.676|[KLog][PageRequestEngine]:sendRequest requestId: -418024248, url: https://ovactapi.iwan.yyb.qq.com/trpc.activity_plat.controller.ControllerService/Exec, POST, body: {"activity_iid": "aiid_8e78673c-08ba-444d-b23b-e16f392adc73","component_iid": "iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95\"}"},"qualifier_params": []}, retry: false
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][HttpProtocolInterceptor][][|12:45.15.676|[KLog][HttpProtocolInterceptor]:intercept url: https://ovactapi.iwan.yyb.qq.com/trpc.activity_plat.controller.ControllerService/Exec, body: {"activity_iid": "aiid_8e78673c-08ba-444d-b23b-e16f392adc73","component_iid": "iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95\"}"},"qualifier_params": []}
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][JceProtocolRequest][][|12:45.15.677|[KLog][JceProtocolRequest]:start sendJceRequest seqId=4
[I][2025-04-17 +80 12:45:16.680][20137, 1411][kuikly_thread-5][market][PageRequestEngine][][|12:45.15.677|[KLog][PageRequestEngine]:http protocol interceptor, url:https://ovactapi.iwan.yyb.qq.com/trpc.activity_plat.controller.ControllerService/Exec, method:POST, body:{"activity_iid": "aiid_8e78673c-08ba-444d-b23b-e16f392adc73","component_iid": "iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95","component_type": 12,"invocation": {"name": "/trpc.component_plat.obtain.Obtain/GetObtainInfo","data": "{\"obtain_iid\": \"iid_obtain_239c70f3-9f9b-4c10-8449-1a2487830e95\"}"},"qualifier_params": []}



[KLog][HttpRequest]:doSendHttpRequest message url: ];https://gftact.qq.com/cmd/getGFTPrivateArgeementHttp?check_token=A230697B4AF07EB670CC2FB295619B8B, header: {activity_iid=aiid_8e78673c-08ba-444d-b23b-e16f392adc73, authority=gftact.qq.com, origin=https://ovact.iwan.yyb.qq.com, referer=https://ovact.iwan.yyb.qq.com/moka-act/j2TU8mCQ2dU0ublhlLCGEGN0s5/page1/index.html?page=index&ovscroll=0&download_pkgnames=com.tencent.dhm1&book_appids=54367612&only_openid=1&kuiklyVersion=23&kuiklyPageName=Nativej2TU8mCQ2dU0ublhlLCGEGN0s5&activityKey=j2TU8mCQ2dU0ublhlLCGEGN0s5, env=prod, cookie=encrypt_q36id=LTI1MzQ1Njc4OTAx3FMWDFRb1Juz6mJm/8eQFdsg8z65U+mh5ohyalSKNHHb3UiL2PBgB1GN/mpQ6nPgH3PMsewYNtz0Kdk45i93tQ==; openid=o4f6JuG1ed1N2Rs7XtRtwLclI1XY; accesstoken=91_4Axb1ZjmrDjtlV8vzWo43Dd8ZsUvnwoHYmLWjY3JRZGmI6xwHuK1ohGC3JCYUAMGlT_sHMFzRKv6KbC09FFm5bgc-9svUKGwfe9Dqcyfv6I; skey=; skey_datetime=; uin=; sid=; vkey=; guid=1989915505835716416; via=; isforeground=1; qid=5ab0d2c93ac2e7a1; q36id=dbb33b0d43805ca56af64cf9100018614b15; qopenid=null; qaccesstoken=null; openappid=0; sdkVersion=34; abiList=["arm64-v8a","armeabi-v7a","armeabi" logintype=WX; mobileqopenid=; mobileqaccesstoken=; mobileqpaytoken=; caller=13}, body: {"appid": 54367612}, response: {"statusCode": 0,"code": 200,"headers": {"Date": "Thu, 17 Apr 2025 04:45:18 GMT","Content-Type": "application\/json;charset=UTF-8","Content-Length": "1909","Connection": "keep-alive","Access-Control-Allow-Credentials": "true","Access-Control-Allow-Origin": "https:\/\/ovact.iwan.yyb.qq.com","Server": "HTTP Load Balancer\/1.0"},"body": "{\"code\":0,\"data\":{\"errMsg\":\"\",\"gft_private_argeement\":{\"author\":\"北京儒意景秀网络科技有限公司\",\"desc\":\"《群星纪元》是一款星际科幻题材的战争策略手游，通过虚幻引擎4打造了一个恢弘的未来战场。游戏融合了即时战略和塔防等多种玩法元素，您将扮演一名指挥官，带领部队踏上异星领土，建立坚固的基地防线，抵抗变异虫潮的入侵。与盟友联合建造强大的部队，探索征服不同的星球，共同扩张势力版图。\",\"developer\":\"北海卡布姆科技有限公司\",\"name\":\"群星纪元\",\"permissions\":\"android.permission.INTERNET;android.permission.ACCESS_NETWORK_STATE;android.permission.WAKE_LOCK;com.android.vending.CHECK_LICENSE;android.permission.ACCESS_WIFI_STATE;android.permission.MODIFY_AUDIO_SETTINGS;android.permission.VIBRATE;com.asus.msa.SupplementaryDID.ACCESS;android.permission.RECORD_AUDIO;android.permission.BLUETOOTH;android.permission.BLUETOOTH_ADMIN;com.huawei.permission.ACCESS_HW_KEYSTORE;com.hihonor.permission.ACCESS_HW_KEYSTORE;android.permission.QUERY_ALL_PACKAGES;android.permission.REQUEST_INSTALL_PACKAGES;android.permission.CHANGE_NETWORK_STATE;com.tencent.dhm1.permission.XGPUSH_RECEIVE;android.permission.SCHEDULE_EXACT_ALARM;android.permission.POST_NOTIFICATIONS;com.huawei.android.launcher.permission.CHANGE_BADGE;com.vivo.notification.permission.BADGE_ICON;android.permission.RECEIVE_USER_PRESENT;android.permission.RESTART_PACKAGES;android.permission.GET_TASKS;com.tencent.dhm1.permission.MIPUSH_RECEIVE;com.meizu.flyme.permission.PUSH;com.meizu.flyme.push.permission.RECEIVE;com.tencent.dhm1.push.permission.MESSAGE;com.meizu.c2dm.permission.RECEIVE;com.tencent.dhm1.permission.C2D_MESSAGE\",\"privacyAgreement\":\"https:\/\/cn-open.jx.ruyi.cn\/client\/terms\/privacy-policy\/9or\",\"publishTime\":1709284692,\"versionName\":\"2.47.2062.1\"},\"ret\":0},\"errmsg\":\"succ\",\"subcode\":0}\n","message": "OK","requestTime": 1744865116736,"responseTime": 1744865116847}