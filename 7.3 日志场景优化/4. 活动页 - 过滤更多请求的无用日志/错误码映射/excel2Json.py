# Excel表转化为jsonl格式
# 部分Excel表 内容如下：
# id(id)	返回码(code)	返回码描述(desc)
# 127	-99000	接口超时
# 128	-99001	 不支持 SSR 环境
# 129	-99002	非应用宝环境访问
# 130	-99003	应用宝版本过低
# 131	-99004	网络异常
# 132	-99999	未知错误
# 133	100005	无效的 taskID 参数
# 134	403	鉴权失败
# 135	0	成功
# jsonl格式
# {"id":1, "query": "erro_code=-99000", "value": "erro_code=-99000 表示 接口超时"}

import pandas as pd
import json

def excel_to_json_lines(filepath, output_path):
    df = pd.read_excel(filepath)

    with open(output_path, 'w', encoding='utf-8') as f:
        i = 0
        for _, row in df.iterrows():
            # id_val = int(row['id(id)'])
            code = str(row['错误码']).strip()
            desc = str(row['描述']).strip()
            recommend = str(row['处理建议']).strip()

            recommend = f"处理建议：{recommend}" if recommend else ""

            item = {
                "id": i+1,
                "query": f"erro_code={code}",
                "value": f"erro_code={code} 表示 {desc}。{recommend}"
            }
            # 每条数据写一行json字符串
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

if __name__ == "__main__":
    input_file = "/Users/<USER>/Documents/需求/调研/7.3 日志场景优化/4. 活动页 - 过滤更多请求的无用日志/错误码映射/视频错误码.xlsx"  # Excel文件路径
    output_file = "/Users/<USER>/Documents/需求/调研/7.3 日志场景优化/4. 活动页 - 过滤更多请求的无用日志/错误码映射/视频错误码.json"          # 输出文件路径
    excel_to_json_lines(input_file, output_file)