- tag：YybLotteryViewModel
  * 配置消息id：
    com.tencent.assistantkuikly.component.yybcomponents.yyblottery.YybLotteryViewModel#queryActPoints
    KLog.i(TAG, "queryActPoints actPointGroupID：${config.actPointGroupID}")  
  * 后台回包
    KLog.i(TAG, "queryActPoints $rawResponse")
- tag：YybLotteryView
  com.tencent.assistantkuikly.component.yybcomponents.yyblottery.YybLotteryView
  KLog.i(TAG, "update points, data: $data") 【如：update points, data: {"actPointGroupID": "zrbrhzgpcx","points": 17}】
  KLog.i(TAG, "body click lottery pointCount:${ctx.viewModel.pointCount}")

2025-04-21 08:20:53.631 I YybLotteryViewModel|08:20.53.625|KLog YybLotteryViewModel]:queryActPoints actPointGroupID：vsmgnv1pn1
2025-04-21 08:20:53.717 I YybLotteryViewModel|08:20.53.714|KLog YybLotteryViewModel]:queryActPoints {"headers": {"Trpc-Func-Msg": "","Trpc-Func-Ret": "0"},"body": "{\"data\":\"{\\\"latest_update_time\\\":\\\"1744255458752\\\",\\\"user_point\\\":\\\"0\\\"}\", \"server_ts\":null, \"code\":0, \"msg\":\"\", \"ret\":0}"}
2025-04-21 08:20:53.733 I YybLotteryView|08:20.53.731|KLog YybLotteryView]:update points, data: {"actPointGroupID": "zrbrhzgpcx","points": 17}