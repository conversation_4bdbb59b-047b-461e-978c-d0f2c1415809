import re
import json
from typing import List, Dict, Optional, Callable, Any

def parse_log_line(log_line: str) -> Optional[Dict[str, str]]:
    """
    解析单条日志，返回字典包含时间、级别、tag、内容。
    日志格式示例：
    "2025-04-19 00:01:52.930 I ReceivingRewardViewModel|00:01.52.926|KLog ReceivingRewardViewModel]:doQueryLotteryResult item: ..."
    """
    parts = log_line.split(' ', 4)
    if len(parts) < 5:
        return None
    return {
        'time': parts[0] + ' ' + parts[1],
        'level': parts[2],
        'tag': parts[3],
        'content': parts[4]
    }

def unique_json_append(data_list: List[Dict], item: Any, seen: set) -> bool:
    """
    将 item 转成 json 字符串去重后追加到 data_list。
    返回是否添加成功。
    """
    item_str = json.dumps(item, sort_keys=True, ensure_ascii=False)
    if item_str not in seen:
        seen.add(item_str)
        data_list.append(item)
        return True
    return False

def format_log_to_info(
    filtered_logs: List[str],
    keyword: str,
    extract_func: Callable[[str], Optional[Any]],
    filter_func: Optional[Callable[[Any], bool]] = None,
    log_desc: str = ""
) -> str:
    """
    通用日志格式化函数。
    :param filtered_logs: 日志列表
    :param keyword: 过滤关键字
    :param extract_func: 提取函数，传入日志内容，返回结构化数据或 None
    :param filter_func: 额外过滤函数，传入提取结果，返回bool，默认不过滤
    :param log_desc: 日志描述，用于打印
    :return: JSON字符串
    """
    all_data = []
    seen = set()

    for log in filtered_logs:
        parsed = parse_log_line(log)
        if not parsed:
            continue
        content = parsed['content']
        if keyword not in content:
            continue
        info = extract_func(content)
        if info is None:
            continue
        if filter_func and not filter_func(info):
            continue
        unique_json_append(all_data, info, seen)

    app_logger.info(f"{log_desc}共提取{len(all_data)}条不重复记录")
    return json.dumps(all_data, ensure_ascii=False, indent=2)

# ----------------- 提取函数 -----------------

def extract_download_info(log_content: str) -> Optional[Dict]:
    """
    提取 DownloadInfo 信息，返回字典。
    示例日志内容：
    DownloadInfo{downloadState=PAUSED, appId=53987000, apkId=129515448, downloadTicket=129515448, packageName='com.xiaoe.client', name='小鹅通学员版', versionCode=905, versionName='5.12.0', apkUrlList=[https://xiaoetong-1252524126.cdn.xiaoeknow.com/APP_builder_files/XiaoeApp-v5.8.5-881-xiaoe.apk]}
    """
    match = re.search(r'DownloadInfo\{(.+)\}$', log_content)
    if not match:
        return None

    content = match.group(1)
    fields = ["appId", "apkId", "downloadTicket", "packageName", "name", "versionCode",
              "versionName", "apkUrlList", "scene", "statScene"]

    result = {}
    for field in fields:
        if field == "apkUrlList":
            m = re.search(r'apkUrlList=\[([^\]]*)\]', content)
            if m:
                urls = [url.strip() for url in m.group(1).split(',') if url.strip()]
                result[field] = urls
            else:
                result[field] = []
        else:
            # 先匹配字符串类型（带单引号）
            m_str = re.search(rf"{field}='([^']*)'", content)
            if m_str:
                result[field] = m_str.group(1)
                continue
            # 匹配数字或其他非逗号字符串
            m_val = re.search(rf"{field}=([^,]+)", content)
            if m_val:
                val = m_val.group(1).strip()
                # 尝试转数字
                if val.isdigit():
                    val = int(val)
                else:
                    try:
                        val = float(val)
                    except ValueError:
                        pass
                result[field] = val
            else:
                result[field] = None

    # 判断 versionName 是否包含在 apkUrlList 第一个 URL 中
    if result.get('apkUrlList') and result.get('versionName'):
        result["isInfoMatch"] = 'true' if result['versionName'] in result['apkUrlList'][0] else 'false'
    else:
        result["isInfoMatch"] = 'false'

    return result

# def extract_lottery_item_info(log_content: str) -> Optional[Dict]:
#     """
#     提取活动页 我的奖品item 信息。
#     示例日志内容：
#     doQueryLotteryResult item: PropertyActBtnConfig(name=应用宝专属基地装扮（7天）*1..., time=2025-04-17 10:06:15, orderId=GF-25-20250417100615-18ekl4, propertyBaseType=2, propertyPlatId=4, propertyPlatType=0, orderStatus=1, hasUserAddr=false, ...)
#     """
#     match = re.search(r'PropertyActBtnConfig\((.*?)\)(?:,|$)', log_content, re.DOTALL)
#     if not match:
#         return None

#     content = match.group(1)
#     fields = ['name', 'orderId', 'orderStatus', 'sendErrorCode']
#     results = {}

#     for field in fields:
#         if field == 'name':
#             # name 字段可能包含逗号，匹配到下一个字段名或结尾
#             m = re.search(rf'{field}=(.*?)(?=, \w+=|$)', content)
#         else:
#             m = re.search(rf'{field}=([^,]*)(?:,|$)', content)
#         results[field] = m.group(1).strip() if m else None

#     return results


def extract_lottery_item_info(log_content: str) -> Optional[List[Dict]]:
    """
    提取活动页 我的奖品item 信息，只返回 orderStatus=3 的列表。
    示例日志内容：
    doQueryLotteryResult item: PropertyActBtnConfig(name=应用宝专属基地装扮（7天）*1..., time=2025-04-17 10:06:15, orderId=GF-25-20250417100615-18ekl4, propertyBaseType=2, propertyPlatId=4, propertyPlatType=0, orderStatus=3, hasUserAddr=false, ...)
    """
    # 匹配所有 PropertyActBtnConfig(...) 的内容
    items = re.findall(r'PropertyActBtnConfig\((.*?)\)(?:,|$)', log_content, re.DOTALL)
    if not items:
        return None

    results = []
    fields = ['name', 'orderId', 'orderStatus', 'sendErrorCode']

    for content in items:
        record = {}
        for field in fields:
            if field == 'name':
                # name 字段可能包含逗号，匹配到下一个字段名或结尾
                m = re.search(rf'{field}=(.*?)(?=, \w+=|$)', content)
            else:
                m = re.search(rf'{field}=([^,]*)(?:,|$)', content)
            record[field] = m.group(1).strip() if m else None

        # 只保留 orderStatus=3 的记录
        if record.get('orderStatus') == '3':
            results.append(record)

    return results if results else None


def extract_obtain_present_info(log_content: str) -> Optional[List[Dict]]:
    """
    提取奖品领取结果信息，返回列表。
    示例日志内容：
    ReceiveResult(showResult=ShowResult(title=领取奖励失败, desc=亲密玫瑰x1, img=, btnText=确认, highestPriority=false, customData={presentsInfo=[PresentInfo(presentTitle=预约五五登录礼包, presentDesc=亲密玫瑰x1)]}), code=-1, msg=您的账号存在风险，暂不支持参与该活动, orderId=[])
    """
    code_msg_match = re.search(r'code=(-?\d+), msg=([^,}\]]+)', log_content)
    if not code_msg_match:
        return None

    code = int(code_msg_match.group(1))
    if code == 0:
        return None

    msg = code_msg_match.group(2).strip()
    presents = re.findall(r'PresentInfo\(presentTitle=([^,]+), presentDesc=([^\)]+)\)', log_content)

    results = []
    for presentTitle, presentDesc in presents:
        results.append({
            'code': code,
            'msg': msg,
            'presentTitle': presentTitle.strip(),
            'presentDesc': presentDesc.strip()
        })

    return results

# ----------------- 调用示例 -----------------

def format_log_to_download_info(self, filtered_logs: List[str]) -> str:
    return format_log_to_info(
        filtered_logs,
        keyword="DownloadInfo",
        extract_func=self.extract_download_info,
        log_desc="下载场景 DownloadInfo"
    )

def format_log_to_lottery_item_info(self, filtered_logs: List[str]) -> str:
    # 只保留 orderStatus == '3' 的记录
    def filter_order_status_3(info):
        return info.get('orderStatus') == '3'

    return format_log_to_info(
        filtered_logs,
        keyword="doQueryLotteryResult item",
        extract_func=self.extract_lottery_item_info,
        filter_func=filter_order_status_3,
        log_desc="活动页 我的奖品item"
    )

def format_log_to_obtain_present_info(self, filtered_logs: List[str]) -> str:
    return format_log_to_info(
        filtered_logs,
        keyword="doShowResult instance",
        extract_func=self.extract_obtain_present_info,
        log_desc="活动页 奖品领取结果"
    )