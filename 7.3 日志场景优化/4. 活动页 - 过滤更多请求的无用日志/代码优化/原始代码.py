    # 将下载场景的 DownloadInfo 转化成 json。只保存不同的 DownloadInfo
    # [YYBWebsocketServer] downloadApk :DownloadInfo{downloadState=PAUSED, appId=53987000, apkId=129515448, downloadTicket=129515448, packageName='com.xiaoe.client', name='小鹅通学员版', versionCode=905, versionName='5.12.0', apkUrlList=[https://xiaoetong-1252524126.cdn.xiaoeknow.com/APP_builder_files/XiaoeApp-v5.8.5-881-xiaoe.apk]}
    def format_log_to_download_info(self, filteredLogs):
        all_data = []
        seen = set()  # 用来存json字符串，做去重
        open_id = ''

        for log in filteredLogs:
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            # 提取下载信息
            if "DownloadInfo" in log_content:
                download_info = self.extract_download_info(log_content)
                if download_info:
                    # 转成json字符串，排序键，保证同样内容字符串一致
                    info_str = json.dumps(download_info, sort_keys=True, ensure_ascii=False)
                    if info_str not in seen:
                        seen.add(info_str)
                        all_data.append(download_info)
        
        app_logger.info(f"共提取{len(all_data)}条不重复记录")
        return json.dumps(all_data, ensure_ascii=False, indent=2)
    
    def format_log_to_lottery_item_info(self, filteredLogs):
        all_data = []
        seen = set()  # 用来存json字符串，做去重
        open_id = ''

        for log in filteredLogs:
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            
            # 提取 活动页 我的奖品item 信息
            if "doQueryLotteryResult item" in log_content:
                lottery_item_info = self.extract_lottery_item_info(log_content)
                if lottery_item_info:
                    # 转成json字符串，排序键，保证同样内容字符串一致
                    info_str = json.dumps(lottery_item_info, sort_keys=True, ensure_ascii=False)
                    order_status = lottery_item_info.get('orderStatus')
                    if info_str not in seen and order_status == '3':
                        seen.add(info_str)
                        all_data.append(lottery_item_info)
        
        app_logger.info(f"共提取{len(all_data)}条不重复记录")
        return json.dumps(all_data, ensure_ascii=False, indent=2)

    def format_log_to_obtain_present_info(self, filteredLogs):
        all_data = []
        seen = set()  # 用来存json字符串，做去重
        open_id = ''

        for log in filteredLogs:
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            # 提取 奖品领取结果 信息
            if "doShowResult instance" in log_content:
                obtain_present_info = self.extract_obtain_present_info(log_content)
                if obtain_present_info:
                    # 转成json字符串，排序键，保证同样内容字符串一致
                    info_str = json.dumps(obtain_present_info, sort_keys=True, ensure_ascii=False)
                    if info_str not in seen:
                        seen.add(info_str)
                        all_data.append(obtain_present_info)
        
        app_logger.info(f"共提取{len(all_data)}条不重复记录")
        return json.dumps(all_data, ensure_ascii=False, indent=2)
    
    
    
    def extract_download_info(self, log_content):
        match = re.search(r'DownloadInfo\{(.+)\}$', log_content)
        if not match:
            return None
        
        fields = ["appId", "apkId", "downloadTicket", "packageName", "name", "versionCode", 
                "versionName", "apkUrlList", "scene", "statScene"]

        content = match.group(1)
        result = {}
        for field in fields:
            if field == "apkUrlList":
                pattern = re.compile(r'apkUrlList=\[([^\]]*)\]')
                m = pattern.search(content)
                if m:
                    urls_str = m.group(1).strip()
                    urls = [url.strip() for url in urls_str.split(',') if url.strip()]
                    result[field] = urls
                else:
                    result[field] = []
            else:
                pattern_str = re.compile(rf'{field}=\'([^\']*)\'')
                m_str = pattern_str.search(content)
                if m_str:
                    result[field] = m_str.group(1)
                    continue
                pattern_val = re.compile(rf'{field}=([^,]+)')
                m_val = pattern_val.search(content)
                if m_val:
                    val = m_val.group(1).strip()
                    if val.isdigit():
                        val = int(val)
                    else:
                        try:
                            val = float(val)
                        except:
                            pass
                    result[field] = val
                else:
                    result[field] = None
        if result['versionName'] in result['apkUrlList'][0]:
            result["isInfoMatch"] = 'true'
        else:
            result["isInfoMatch"] = 'false'
        return result
    
    # 活动页 我的奖品item 回包格式化 json
    # 示例日志内容
    # 2025-04-19 00:01:52.930 I ReceivingRewardViewModel|00:01.52.926|KLog ReceivingRewardViewModel]:doQueryLotteryResult item: PropertyActBtnConfig(name=应用宝专属基地装扮（7天）*1..., time=2025-04-17 10:06:15, orderId=GF-25-20250417100615-18ekl4, propertyBaseType=2, propertyPlatId=4, propertyPlatType=0, orderStatus=1, hasUserAddr=false, 。。。)
    def extract_lottery_item_info(self, log_content):
        pattern = r'PropertyActBtnConfig\((.*?)\)(?:,|$)'
        match = re.search(pattern, log_content, re.DOTALL)
        if not match:
            return None

        content = match.group(1)

        # 要提取的字段
        fields = ['name', 'orderId', 'orderStatus', 'sendErrorCode']

        # 构造正则表达式，匹配字段名=字段值，字段值可能包含中文、数字、字母、符号，直到遇到逗号或字符串结尾
        # name 字段可能包含逗号或空格，使用非贪婪匹配
        results = {}
        for field in fields:
            # 这里对 name 字段做特殊处理，匹配到逗号前的所有内容（非贪婪）
            if field == 'name':
                regex = rf'{field}=(.*?)(?:, [a-zA-Z]+=|$)'
            else:
                regex = rf'{field}=([^,]*)(?:,|$)'
            m = re.search(regex, content)
            if m:
                value = m.group(1).strip()
                results[field] = value
            else:
                results[field] = None

        return results
    
    # 部分日志内容示例
    # ReceiveResult(showResult=ShowResult(title=领取奖励失败, desc=亲密玫瑰x1, img=, btnText=确认, highestPriority=false, customData={presentsInfo=[PresentInfo(presentTitle=预约五五登录礼包, presentDesc=亲密玫瑰x1)]}), code=-1, msg=您的账号存在风险，暂不支持参与该活动, orderId=[]
    def extract_obtain_present_info(self, log_content):
        """
        处理单条日志（receiveResult=ReceiveResult(...)），提取 code != 0 的 presentTitle、presentDesc、code、msg，
        返回 JSON 格式字符串（列表形式，可能有多个 PresentInfo）。

        :param log_content: 单条日志字符串
        :return: JSON 格式字符串
        """
        # 匹配 code 和 msg
        code_msg_pattern = re.compile(r'code=(-?\d+), msg=([^,}]+)')
        # 匹配 PresentInfo(presentTitle=xxx, presentDesc=xxx)
        present_pattern = re.compile(r'PresentInfo\(presentTitle=([^,]+), presentDesc=([^\)]+)\)')

        code_msg_match = code_msg_pattern.search(log_content)
        if not code_msg_match:
            return None

        code = int(code_msg_match.group(1))
        if code == 0:
            return None

        msg = code_msg_match.group(2).strip()

        presents = present_pattern.findall(log_content)
        results = []
        for presentTitle, presentDesc in presents:
            results.append({
                'code': code,
                'msg': msg,
                'presentTitle': presentTitle.strip(),
                'presentDesc': presentDesc.strip()
            })

        return results