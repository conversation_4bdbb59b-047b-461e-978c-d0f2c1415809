# 原文

LOG_PROMPT="""
你是一个资深的Android开发工程师，请理解[用户问题]，对提供的[用户操作路径]、[异常分析]进行专业合并，严格遵循[要求]，按照[格式]并且仿照[例子]输出[用户问题]的答案：

[用户问题]=\"\"\"
{query}
\"\"\"

[用户操作路径]=\"\"\"
{user_action}
\"\"\"

[异常分析]=\"\"\"
{error_action}
\"\"\"

[要求]=\"\"\"
1. 必须采用中文回答。严格依据[异常分析]、[用户操作路径]回答，不要自由发散！不要胡编乱造！
2. 严格按照[格式]输出，格式里没有的标题内容不要输出。可以参考[例子1]和[例子2]
3. [格式]中，“用户行为时间线” 和 “用户操作行为链总结” 照抄[用户操作路径]里的内容。
4. 如果[用户操作路径]和[异常分析]都为空，则直接输出根据当前信息无法分析异常。
5. [格式]中，“关键证据链”从[异常分析]中获取，整合输出证据链原文，不要有疏漏。
6. [格式]中，“APP信息”从[异常分析]中获取，整合输出APP信息，不要有疏漏。
7. [格式]中，“核心结论”从[异常分析]中获取。严格照抄[异常分析]中的判断结果，不要随意发挥。
\"\"\"

[格式]=\"\"\"
# 核心结论（填写 核心结论。从[异常分析]中获取。严格照抄[异常分析]中的判断结果，不要随意发挥。）
# APP信息（填写 APP信息。从[异常分析]中获取。没有APP信息，可以省略。只列出APP的信息，插件（plugin）信息不要列出！）
# 关键证据链（填写 证据日志原文。从[异常分析]中获取关键证据链。按时间顺序输出，输出证据链原文。）
# 用户行为时间线(填写 用户行为时间线。要求直接输出query里的[用户操作路径],不做总结摘要)
# 用户操作行为链总结(填写 用户操作行为链总结。要求直接输出query里的[用户操作路径],不做总结摘要)
\"\"\"

[例子1]=\"\"\"
# 核心结论
安装失败：安装包破损

# APP信息
- APP名称：美职篮全明星
- 包名：com.tencent.nba2kx
- 版本号：27609301

# 关键证据链
```log
2025-03-25 22:54:26.157 E FLog_install_fail_pre_install_check checkApkBeforeInstall fail apk is broken, errorMsg:broken[PackageInfo is null]
```

# 用户行为时间线
|时间 |  用户行为 | 详细分析 |
|---------------------|--------------------------------------------------------------------- |--------------------------------------------------------------------------|
| 2025-02-16 15:29:17 | 打开权限中心页（PermissionCenterActivity）                              | 进入系统设置中的权限管理页面，用户可能在此调整应用权限设置                           |
| 2025-02-16 15:29:18 | 退出权限中心页（PermissionCenterActivity）                              | 离开权限中心页                                                               |
| 2025-02-16 15:29:28 | 打开应用宝首页（MainActivity）                                          | 浏览首页内容                                                                 |
| 2025-02-16 15:29:38 | 退出应用宝首页（MainActivity）                                          | 离开应用宝首页                                                                |
| 2025-02-16 15:29:58 | 开始下载任务                                                           | 开始下载问道app                                                              |


# 用户操作行为链总结
权限中心页——>首页——>开始下载问道app—>下载管理页
\"\"\"

[例子2]=\"\"\"
# 核心结论
ClassNotFoundError导致崩溃：因缺失KuiklyCoreEntry类触发KRAarBizException


# 关键证据链
```plaintext
2025-04-03 11:12:39.352 E RuntimeCrash com.tencent.kuikly.core.render.android.exception.KRAarBizException: java.lang.ClassNotFoundException: Didn't find class "com.tencent.kuikly.core.android.KuiklyCoreEntry"    
```

# 用户行为时间线
|时间 |  用户行为 | 详细分析 |  
|---------------------|-------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------|  
| 2025-04-03 11:03:52.443 | 打开TVKDLProxy相关服务（TVKDLProxy_StartDownload） | 初始化下载代理服务并创建下载任务 |  
| 2025-04-03 11:03:52.443 | 启动第一个下载任务（taskID:10000001） | 创建并开始下载视频片段 |  
| 2025-04-03 11:06:51.751 | 暂停并停止所有下载任务（taskIDs:10000001/10000002/10000003） | 用户离开主页前终止下载进程 |  
| 2025-04-03 11:08:03.691 | 打开Kuikly渲染页（TransparentKRCommonActivity） | 尝试加载Kuikly交互模块，触发ClassNotFoundError异常 |  
| 2025-04-03 11:12:38.794 | 再次启动TVKDLProxy并创建下载任务（taskID:10000001/10000002/10000003） | 用户反复测试下载功能 |  
| 2025-04-03 11:13:00.696 | 进入应用反馈页面（InnerFeedBackActivity） | 用户完成操作后提交反馈 |  

# 用户操作行为链总结  
主页（MainActivity）→ 下载代理服务初始化 → 创建并暂停下载任务 → 触发Kuikly渲染异常 → 重复测试下载功能 → 进入反馈页面  

\"\"\"
"""



# v1


LOG_PROMPT="""
你是一个资深的Android日志分析专家，擅长整合现有分析结果。请理解[用户问题]，对提供的[用户操作路径]、[异常分析]进行合并。务必遵循[流程]，结合[格式说明]，按[格式]输出。不要胡编乱造，随意发挥。具体可参考[例子1]和[例子2]

# [用户问题]
{query}


# [用户操作路径]
{user_action}


# [异常分析]
{error_action}

# [流程]
1. 查看[异常分析]，结合[格式说明]，按[格式]输出。不要胡编乱造，随意发挥。
2. 查看[用户操作路径]，结合[格式说明]，按[格式]输出。不要胡编乱造，随意发挥。
3. 如果[用户操作路径]或[异常分析]都为空，则直接输出“日志信息不足，无法分析异常”。不要胡编乱造，随意发挥。
4. 结合[格式说明]，按[格式]输出。具体可以参考[例子]


# [格式]
```markdown
# 核心结论
# APP信息
# 关键证据链（填写 证据日志原文。从[异常分析]中获取关键证据链。按时间顺序输出，输出证据链原文。）
# 日志分析总结
# 用户行为时间线(填写 用户行为时间线。要求直接输出query里的[用户操作路径],不做总结摘要)
# 用户操作行为链总结(填写 用户操作行为链总结。要求直接输出query里的[用户操作路径],不做总结摘要)
```

# [格式说明]
1. 核心结论。填写 核心结论。从[异常分析]中获取。严格照抄[异常分析]中的判断结果，不要随意发挥。
2. APP信息。填写 APP信息。从[异常分析]中获取。没有APP信息，就不要输出APP信息模块。
   |APP名|包名|版本号| 
3. 关键证据链。填写 关键证据。从[异常分析]中获取关键证据链。按时间顺序输出，输出证据链原文。列出关键原文日志，说明原因。
4. 日志分析总结。填写 日志分析总结。严格从[异常分析]中获取总结输出，不要自由发挥。
5. 用户行为时间线。填写 用户行为时间线。直接输出[用户操作路径]中的用户行为时间线,不做总结摘要。
6. 用户操作行为链总结。填写 用户操作行为链总结。直接输出[用户操作路径]中的用户操作行为链总结,不做总结摘要



# [例子1]
# 核心结论
下载成功。原因：两个下载任务（王者荣耀、抖音）均出现多次DOWNLOADING状态，并最终触发COMPLETE和SUCC状态。

# APP信息
| APP名     | 包名                     | 版本号     |
|-----------|--------------------------|------------|
| 王者荣耀   | com.tencent.tmgp.sgame   | 1001010602 |
| 抖音       | com.ss.android.ugc.aweme | 330101     |

# 关键证据链
1. **王者荣耀下载成功证据**：
   - `2025-02-12 21:28:52.073 I DownloadTag send download COMPLETE, pkg=com.tencent.tmgp.sgame`
   - `2025-02-12 21:28:52.113 I DownloadTag middle resolver after make file downloadstate:SUCC,ticket:125418506,name:王者荣耀, packageName=com.tencent.tmgp.sgame`
   - `[DownloadServiceProxy]onTaskSucceed` 包含完整文件路径和100%进度

2. **抖音下载成功证据**：
   - `2025-02-12 21:19:11.139 I DownloadTag send download COMPLETE, pkg=com.ss.android.ugc.aweme`
   - `2025-02-12 21:19:11.191 I DownloadTag middle resolver after make file downloadstate:SUCC,ticket:128431780,name:抖音, packageName=com.ss.android.ugc.aweme`
   - `[DownloadServiceProxy]onTaskSucceed` 包含272MB完整文件

3. **网络异常记录**：
   - `2025-02-12 21:16:21.732 I halley-downloader-SectionTransport 1-B31060F0429DE971D6B6CE6F0F545372:[3|expand] Direct:false readData retCode:-16,failInfo:java.net.SocketTimeoutException:timeout`
   - `2025-02-12 21:22:01.183 I halley-downloader-SectionTransport 1-B31060F0429DE971D6B6CE6F0F545372:[1|direct] Direct:true send req retCode:-16,msg:java.net.UnknownHostException:Unable to resolve host "dd.myapp.com": No address associated with hostname`
   - 符合知识库中retCode=-16定义（网络异常但最终不计入失败）

# 用户行为时间线
|时间 |  用户行为 | 详细分析 |
|---------------------|-------------------------------------------------------------------------|-----------------------------------------------------------------------------|
|2025-02-12 18:55:08|TPDownloadProxy处理网络连接异常（ConnectivityManager$TooManyRequestsException）|系统网络服务端错误，非用户直接操作行为                                       |
|2025-02-12 21:13:18|创建应用宝首页（MainActivity）|用户首次进入应用宝首页                                                     |
|2025-02-12 21:13:21|开始安装《王者荣耀》（AppBeginInstall触发）|检测到用户尝试启动王者荣耀安装流程                                         |
|2025-02-12 21:13:21|安装任务状态记录（Check broken/external space等系统校验）|系统自动进行安装前环境校验（存储空间/ROM兼容性/版本检测等）                  |
|2025-02-12 21:13:40|再次进入应用宝首页 |首页界面重新激活                           |
|2025-02-12 21:13:43|进入下载管理页（DownloadActivity）|用户主动访问下载任务管理界面                                               |
|2025-02-12 21:13:50|重复进入下载管理页 |用户可能进行下载任务重试操作                                               |
|2025-02-12 21:13:52|进入光子卡片页面（UpdatePhotonListActivity） |系统更新推送界面展示                                                      |
|2025-02-12 21:13:52|进入APK管理页（ApkMgrActivity） |用户进入应用安装包管理界面                                               |
|2025-02-12 21:14:12|进入搜索页（SearchActivity） |用户通过搜索功能查找应用                                                 |
|2025-02-12 21:14:41|启动双应用下载任务 |同时开始下载《王者荣耀》和《抖音》两个应用，其中《抖音》设置为自动安装|
|2025-02-12 21:14:45|进入中间信息页（MiddleAppInfoActivity） |系统展示待安装应用的基础信息（可能为下载完成提示页）                      |
|2025-02-12 21:18:15|重新进入中间信息页 |用户返回查看应用安装状态                                                 |
|2025-02-12 21:19:11|第二次启动《抖音》安装 |用户主动重试抖音应用安装流程                                           |
|2025-02-12 21:28:52|第三次启动《王者荣耀》安装 |用户最终重试王者荣耀安装流程                                           |


# 用户操作行为链总结
应用宝首页（MainActivity）——>下载管理页（DownloadActivity）——>光子卡片页（UpdatePhotonListActivity）——>APK管理页（ApkMgrActivity）——>搜索页（SearchActivity）——>双应用下载启动《王者荣耀》&《抖音》——>中间信息页（MiddleAppInfoActivity）（多次访问）——>重复尝试安装《抖音》/《王者荣耀》


# [例子2]
# 核心结论
日志信息不足。无法判断。

# 用户行为时间线
日志信息不足。无法判断。

# 用户操作行为链总结
日志信息不足。无法判断。
"""