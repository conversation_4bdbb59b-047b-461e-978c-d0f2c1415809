AUTO_DOWNLOAD_PROMPT="""
你是一名Android自动下载日志分析专家，擅长逐行阅读日志，通过日志轨迹还原自动下载流程，精准分析日志。请理解[用户问题]，合理使用[用户操作路径]，对提供的[用户自动下载日志]进行专业分析。结合[知识库]和你的专业知识，务必根据[日志分析流程]分析。最后结合[格式说明]，严格按[格式]输出。

# [用户问题]
{query}

# [用户操作路径]
{user_action}

# [知识库]
{rag}
1. “PreUpdateAppEngine onRequest Success startDownload” 表示 触发了静默下载
2. “tryAutoDownload PreUpdateAppEngine 没有需要更新的数据” 表示 没有触发静默下载。
3. “preUpdate switch is close” 表示 静默下载能力关闭
4. “PreUpdateAppEngine 当前已经请求过” 表示 静默下载不允许请求。
5. “PreUpdateAppEngine onRequest Success pkg” 表示 静默下载请求到数据
6. ”GetUpdatePreDownloadResponse is null sep=” 表示 静默下载请求的数据为空
7. “PreUpdateAppEngine no data“ 表示 静默下载请求的数据为空


# [用户自动下载日志]
{log_content}

# [下载APP信息]
{download_info}


# [日志分析流程]
逐行分析 [用户自动下载日志]，一切信息从[用户自动下载日志]获取，不得编造。分析流程如下：
1. 逐行分析[用户自动下载日志]分析是否触发了自动下载。相关关键字如下：
    - “PreUpdateAppEngine onRequest Success startDownload” 表示 触发了静默下载
    - “tryAutoDownload PreUpdateAppEngine 没有需要更新的数据” 表示 没有触发静默下载。
    - “preUpdate switch is close” 表示 静默下载能力关闭
    - “PreUpdateAppEngine 当前已经请求过” 表示 静默下载不允许请求。
    - “PreUpdateAppEngine onRequest Success pkg” 表示 静默下载请求到数据
    - “GetUpdatePreDownloadResponse is null sep=” 表示 静默下载请求的数据为空
    - “PreUpdateAppEngine no data” 表示 静默下载请求的数据为空
2. 从 [用户自动下载日志] 中获取 自动下载的APP信息。若日志中没有相关信息，认为日志信息不足，不要编造。对应关键字如下：
    - packageName（包名）
    - name（APP名）
    - versionCode（版本号）
    - scene(场景)
    - statScene
    - versionName(版本名)
    - apkUrlList（下载链接）
    - downloadTicket（下载唯一标识）
3. 自动下载场景分析。首先从[下载APP信息]查看 scene字段 和 statScene字段。结合[知识库]分析在哪个场景 触发自动下载的。再结合 [用户自动下载日志]，查看 在哪个场景 触发自动下载的。[用户自动下载日志]中，相关场景关键字如下：
   - “PreUpdateAppEngine” 表示 回流静默下载 场景
   - “UpdateBookingLongConnEngine” 表示 预约更新 场景
   - “BookingAutoDownloadLongConnEngine” 表示 预约下载 场景（长连接轮询）
   - “BookingPreDownPullEngine” 表示 预约下载 场景（主动拉取配置）
   - “ExternalCall” 表示 外call进入下载
4. 有多个应用下载，必须分别处理，不能混淆。
5. 遇到 “ExternalCall” 表示 外call进入下载，需要指出外call下载多少次，以及外call下载的APP信息。
6. 如果日志信息不足请说明，不要胡编乱造。
7. 最后将结论和日志原文一一对应总结，结合[格式说明]，按[格式]输出。具体输出格式可参考[例子]。


# [格式]
# 自动下载结果判断
# APP信息
# 关键证据链
# 自动下载流程分析
# 用户下载日志总结



# [格式说明]
1. 自动下载结果判断。填写 自动下载结果判断 以及 自动下载场景，并说明原因。
2. APP信息。填写APP信息。包括 包名（packageName/pkg）等以表格输出。如果没有从[用户自动下载日志]获取到信息，标明“日志信息不足”。
   |APP名|包名| 版本号| downloadTicket|scene|statScene|versionName(版本名)|apkUrlList（下载链接）|
3. 关键证据链。填写 原文日志，从[用户自动下载日志]中寻找证据。列出关键原文日志，说明原因。注意需要按时间顺序填写。
4. 自动下载流程分析。填写自动下载流程。
   |时间|自动下载行为|详细分析| 
5. 用户自动下载日志总结。填写 自动下载的app详细信息，自动下载状态，自动下载结论。

"""



# 安装结果判断  
安装失败。原因：安装流程触发多次"安装回调,success=false"，且系统安装界面启动失败。未发现包破损/网络错误/存储不足证据，但存在安装器进程异常（如`SystemInstallThread`异常终止）及安装会话无进度更新，推测可能为系统兼容性或权限拦截问题。

---

# APP信息  
|APP名|包名| 版本号|  
|-------|--------------|---------|  
|京东|com.jingdong.app.mall|100783|  

---

# 关键证据链  
1. **首次安装启动异常**  
   - `2025-04-24 16:09:13.622 I InstallUninstallTask is broken result = false; msg = `  
   - **说明**：系统安装线程异常终止，进程无法完成安装。

2. **安装器进程错误**  
   - `2025-04-24 16:09:14.478 E sys_install_debug startActivity apkPath... flg=0x10000001 cmp=com.android.packageinstaller/.InstallStart`  
   - **说明**：启动系统安装界面失败（`cmp`指向无效组件），可能权限不足。

3. **安装会话无进度更新**  
   - `2025-04-24 16:09:34.083 I InstallSessionObserver onProgressChanged, progress=0.0`  
   - **说明**：安装进度始终为0%，会话无有效进展。

4. **用户重复触发安装**  
   - `2025-04-24 16:09:35.693 I InstallRetryMgr [京东] event add:AppBeginInstall now: 3`  
   - **说明**：用户从中间页面反复触发安装，但均失败。

---

# 安装流程分析  
|时间|安装行为|详细分析|  
|---------------------|--------------------------|-----------------------------------------|  
|2025-04-24 16:09:07|MD5校验|头部（4E06EC7C...）与尾部（AD9C2471...）校验通过，排除APK篡改|  
|2025-04-24 16:09:13|安装器启动|系统进程`SystemInstallThread`异常终止，无法继续安装|  
|2025-04-24 16:09:14|系统安装界面失败|通过`Intent`尝试启动`com.android.packageinstaller.InstallStart`失败|  
|2025-04-24 16:09:15|安装回调失败|系统返回`success=false`，但未提供具体错误信息|  
|2025-04-24 16:09:16|安装计时器超限|会话创建后5秒内无进度更新，触发重试逻辑|  
|2025-04-24 16:09:21|安装器进程二次异常|用户从中间页重新触发安装，仍出现进程终止|  
|2025-04-24 16:12:08|安装队列超时|超过150秒无进度更新，安装任务被标记为无效|  

---

# 用户安装日志总结  
1. **应用信息**：目标APP为“京东”（包名`com.jingdong.app.mall`），版本号100783，文件MD5校验通过。  
2. **失败特征**：  
   - 系统安装界面启动失败（`cmp`组件无效）。  
   - 安装进程`SystemInstallThread`异常终止，导致安装失败。  
   - 安装回调`success=false`但无明确错误码。  
3. **结论**：安装失败源于系统级问题（如权限拦截或组件异常），非应用包自身缺陷。
